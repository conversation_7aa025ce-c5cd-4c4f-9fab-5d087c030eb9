import{a as r,i as d,s as g,f,y as h,w as v,z as y,A as u,d as m,C as o,x as _,D as c,l as b}from"../lite.js";function q(l){let e,a=(l[0]?l[0]:"")+"",i;return{c(){e=h("div"),i=v(a),y(e,"class","svelte-1ayixqk"),u(e,"table",l[1]==="table"),u(e,"gallery",l[1]==="gallery"),u(e,"selected",l[2])},m(t,s){m(t,e,s),o(e,i)},p(t,[s]){s&1&&a!==(a=(t[0]?t[0]:"")+"")&&_(i,a),s&2&&u(e,"table",t[1]==="table"),s&2&&u(e,"gallery",t[1]==="gallery"),s&4&&u(e,"selected",t[2])},i:c,o:c,d(t){t&&b(e)}}}function C(l,e,a){let{value:i}=e,{type:t}=e,{selected:s=!1}=e;return l.$$set=n=>{"value"in n&&a(0,i=n.value),"type"in n&&a(1,t=n.type),"selected"in n&&a(2,s=n.selected)},[i,t,s]}class p extends r{constructor(e){super(),d(this,e,C,q,g,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{p as default};
//# sourceMappingURL=Example-D1A46ypO.js.map
