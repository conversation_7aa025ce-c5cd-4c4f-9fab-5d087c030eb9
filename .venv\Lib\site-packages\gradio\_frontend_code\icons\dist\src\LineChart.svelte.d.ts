/** @typedef {typeof __propDef.props}  LineChartProps */
/** @typedef {typeof __propDef.events}  LineChartEvents */
/** @typedef {typeof __propDef.slots}  LineChartSlots */
export default class LineChart extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type LineChartProps = typeof __propDef.props;
export type LineChartEvents = typeof __propDef.events;
export type LineChartSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
