import{a as W,i as X,at as Z,f,B as y,c as I,m as M,k as V,t as Y,n as D,Y as x,S as $,aq as F,b as T,y as O,z as v,d as q,a0 as p,a6 as ee,l as C,as as te,w as K,x as L,A as N,C as B,M as E,V as se}from"../lite.js";import{B as le}from"./BlockTitle-DvFB_De3.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";function G(t,e,s){const n=t.slice();return n[22]=e[s][0],n[23]=e[s][1],n[25]=s,n}function ie(t){let e;return{c(){e=K(t[9])},m(s,n){q(s,e,n)},p(s,n){n&512&&L(e,s[9])},d(s){s&&C(e)}}}function H(t){let e,s,n,a,u,k,m,r=t[22]+"",d,o,l,c;function b(){return t[19](t[23])}function w(...h){return t[20](t[25],t[23],...h)}function _(...h){return t[21](t[23],t[25],...h)}return{c(){e=O("label"),s=O("input"),k=T(),m=O("span"),d=K(r),o=T(),s.disabled=t[14],s.checked=n=t[0].includes(t[23]),v(s,"type","checkbox"),v(s,"name",a=t[23]?.toString()),v(s,"title",u=t[23]?.toString()),v(s,"class","svelte-1e02hys"),v(m,"class","ml-2 svelte-1e02hys"),v(e,"class","svelte-1e02hys"),N(e,"disabled",t[14]),N(e,"selected",t[0].includes(t[23]))},m(h,g){q(h,e,g),B(e,s),B(e,k),B(e,m),B(m,d),B(e,o),l||(c=[E(s,"change",b),E(s,"input",w),E(s,"keydown",_)],l=!0)},p(h,g){t=h,g&16384&&(s.disabled=t[14]),g&33&&n!==(n=t[0].includes(t[23]))&&(s.checked=n),g&32&&a!==(a=t[23]?.toString())&&v(s,"name",a),g&32&&u!==(u=t[23]?.toString())&&v(s,"title",u),g&32&&r!==(r=t[22]+"")&&L(d,r),g&16384&&N(e,"disabled",t[14]),g&33&&N(e,"selected",t[0].includes(t[23]))},d(h){h&&C(e),l=!1,se(c)}}}function ne(t){let e,s,n,a,u,k;const m=[{autoscroll:t[1].autoscroll},{i18n:t[1].i18n},t[13]];let r={};for(let l=0;l<m.length;l+=1)r=x(r,m[l]);e=new $({props:r}),e.$on("clear_status",t[18]),n=new le({props:{root:t[12],show_label:t[11],info:t[10],$$slots:{default:[ie]},$$scope:{ctx:t}}});let d=F(t[5]),o=[];for(let l=0;l<d.length;l+=1)o[l]=H(G(t,d,l));return{c(){I(e.$$.fragment),s=T(),I(n.$$.fragment),a=T(),u=O("div");for(let l=0;l<o.length;l+=1)o[l].c();v(u,"class","wrap svelte-1e02hys"),v(u,"data-testid","checkbox-group")},m(l,c){M(e,l,c),q(l,s,c),M(n,l,c),q(l,a,c),q(l,u,c);for(let b=0;b<o.length;b+=1)o[b]&&o[b].m(u,null);k=!0},p(l,c){const b=c&8194?p(m,[c&2&&{autoscroll:l[1].autoscroll},c&2&&{i18n:l[1].i18n},c&8192&&ee(l[13])]):{};e.$set(b);const w={};if(c&4096&&(w.root=l[12]),c&2048&&(w.show_label=l[11]),c&1024&&(w.info=l[10]),c&67109376&&(w.$$scope={dirty:c,ctx:l}),n.$set(w),c&49187){d=F(l[5]);let _;for(_=0;_<d.length;_+=1){const h=G(l,d,_);o[_]?o[_].p(h,c):(o[_]=H(h),o[_].c(),o[_].m(u,null))}for(;_<o.length;_+=1)o[_].d(1);o.length=d.length}},i(l){k||(V(e.$$.fragment,l),V(n.$$.fragment,l),k=!0)},o(l){Y(e.$$.fragment,l),Y(n.$$.fragment,l),k=!1},d(l){l&&(C(s),C(a),C(u)),D(e,l),D(n,l),te(o,l)}}}function ae(t){let e,s;return e=new y({props:{visible:t[4],elem_id:t[2],elem_classes:t[3],type:"fieldset",container:t[6],scale:t[7],min_width:t[8],$$slots:{default:[ne]},$$scope:{ctx:t}}}),{c(){I(e.$$.fragment)},m(n,a){M(e,n,a),s=!0},p(n,[a]){const u={};a&16&&(u.visible=n[4]),a&4&&(u.elem_id=n[2]),a&8&&(u.elem_classes=n[3]),a&64&&(u.container=n[6]),a&128&&(u.scale=n[7]),a&256&&(u.min_width=n[8]),a&67141155&&(u.$$scope={dirty:a,ctx:n}),e.$set(u)},i(n){s||(V(e.$$.fragment,n),s=!0)},o(n){Y(e.$$.fragment,n),s=!1},d(n){D(e,n)}}}function ue(t,e,s){let n,{gradio:a}=e,{elem_id:u=""}=e,{elem_classes:k=[]}=e,{visible:m=!0}=e,{value:r=[]}=e,{choices:d}=e,{container:o=!0}=e,{scale:l=null}=e,{min_width:c=void 0}=e,{label:b=a.i18n("checkbox.checkbox_group")}=e,{info:w=void 0}=e,{show_label:_=!0}=e,{root:h}=e,{loading_status:g}=e,{interactive:j=!0}=e,{old_value:J=r.slice()}=e;function z(i){r.includes(i)?s(0,r=r.filter(S=>S!==i)):s(0,r=[...r,i]),a.dispatch("input")}const P=()=>a.dispatch("clear_status",g),Q=i=>z(i),R=(i,S,A)=>a.dispatch("select",{index:i,value:S,selected:A.currentTarget.checked}),U=(i,S,A)=>{A.key==="Enter"&&(z(i),a.dispatch("select",{index:S,value:i,selected:!r.includes(i)}))};return t.$$set=i=>{"gradio"in i&&s(1,a=i.gradio),"elem_id"in i&&s(2,u=i.elem_id),"elem_classes"in i&&s(3,k=i.elem_classes),"visible"in i&&s(4,m=i.visible),"value"in i&&s(0,r=i.value),"choices"in i&&s(5,d=i.choices),"container"in i&&s(6,o=i.container),"scale"in i&&s(7,l=i.scale),"min_width"in i&&s(8,c=i.min_width),"label"in i&&s(9,b=i.label),"info"in i&&s(10,w=i.info),"show_label"in i&&s(11,_=i.show_label),"root"in i&&s(12,h=i.root),"loading_status"in i&&s(13,g=i.loading_status),"interactive"in i&&s(17,j=i.interactive),"old_value"in i&&s(16,J=i.old_value)},t.$$.update=()=>{t.$$.dirty&131072&&s(14,n=!j),t.$$.dirty&65539&&JSON.stringify(J)!==JSON.stringify(r)&&(s(16,J=r),a.dispatch("change"))},[r,a,u,k,m,d,o,l,c,b,w,_,h,g,n,z,J,j,P,Q,R,U]}class fe extends W{constructor(e){super(),X(this,e,ue,ae,Z,{gradio:1,elem_id:2,elem_classes:3,visible:4,value:0,choices:5,container:6,scale:7,min_width:8,label:9,info:10,show_label:11,root:12,loading_status:13,interactive:17,old_value:16})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),f()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get choices(){return this.$$.ctx[5]}set choices(e){this.$$set({choices:e}),f()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[8]}set min_width(e){this.$$set({min_width:e}),f()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),f()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),f()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),f()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),f()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),f()}get interactive(){return this.$$.ctx[17]}set interactive(e){this.$$set({interactive:e}),f()}get old_value(){return this.$$.ctx[16]}set old_value(e){this.$$set({old_value:e}),f()}}export{fe as default};
//# sourceMappingURL=Index-VnffNhas.js.map
