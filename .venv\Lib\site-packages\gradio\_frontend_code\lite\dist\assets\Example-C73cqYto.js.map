{"version": 3, "file": "Example-C73cqYto.js", "sources": ["../../../markdown/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let sanitize_html: boolean;\n\texport let line_breaks: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let root: string;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass=\"prose\"\n>\n\t<MarkdownCode\n\t\tmessage={value ? value : \"\"}\n\t\t{latex_delimiters}\n\t\t{sanitize_html}\n\t\t{line_breaks}\n\t\tchatbot={false}\n\t\t{root}\n\t/>\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "value", "$$props", "type", "selected", "sanitize_html", "line_breaks", "latex_delimiters", "root"], "mappings": "gOAuBWA,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,qEAIhB,qFAVGC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAcKC,EAAAF,EAAAG,CAAA,wDAPML,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,oIANbC,EAAAC,EAAA,QAAAF,OAAS,OAAO,aACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,0IAftB,GAAA,CAAA,MAAAM,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF,EACX,CAAA,cAAAG,CAAA,EAAAH,EACA,CAAA,YAAAI,CAAA,EAAAJ,EACA,CAAA,iBAAAK,CAAA,EAAAL,EAKA,CAAA,KAAAM,CAAA,EAAAN"}