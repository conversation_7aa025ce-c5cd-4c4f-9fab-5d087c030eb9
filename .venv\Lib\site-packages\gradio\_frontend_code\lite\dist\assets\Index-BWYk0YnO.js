import{a as m,i as r,s as d,f as _,B as b,c as g,m as h,k as u,t as f,n as $,q as v,u as k,r as q,v as B}from"../lite.js";function p(n){let e;const l=n[3].default,s=v(l,n,n[4],null);return{c(){s&&s.c()},m(t,i){s&&s.m(t,i),e=!0},p(t,i){s&&s.p&&(!e||i&16)&&k(s,l,t,t[4],e?B(l,t[4],i,null):q(t[4]),null)},i(t){e||(u(s,t),e=!0)},o(t){f(s,t),e=!1},d(t){s&&s.d(t)}}}function w(n){let e,l;return e=new b({props:{elem_id:n[0],elem_classes:n[1],visible:n[2],explicit_call:!0,$$slots:{default:[p]},$$scope:{ctx:n}}}),{c(){g(e.$$.fragment)},m(s,t){h(e,s,t),l=!0},p(s,[t]){const i={};t&1&&(i.elem_id=s[0]),t&2&&(i.elem_classes=s[1]),t&4&&(i.visible=s[2]),t&16&&(i.$$scope={dirty:t,ctx:s}),e.$set(i)},i(s){l||(u(e.$$.fragment,s),l=!0)},o(s){f(e.$$.fragment,s),l=!1},d(s){$(e,s)}}}function C(n,e,l){let{$$slots:s={},$$scope:t}=e,{elem_id:i}=e,{elem_classes:o}=e,{visible:c=!0}=e;return n.$$set=a=>{"elem_id"in a&&l(0,i=a.elem_id),"elem_classes"in a&&l(1,o=a.elem_classes),"visible"in a&&l(2,c=a.visible),"$$scope"in a&&l(4,t=a.$$scope)},[i,o,c,s,t]}class S extends m{constructor(e){super(),r(this,e,C,w,d,{elem_id:0,elem_classes:1,visible:2})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),_()}}export{S as default};
//# sourceMappingURL=Index-BWYk0YnO.js.map
