/** @typedef {typeof __propDef.props}  MicrophoneProps */
/** @typedef {typeof __propDef.events}  MicrophoneEvents */
/** @typedef {typeof __propDef.slots}  MicrophoneSlots */
export default class Microphone extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type MicrophoneProps = typeof __propDef.props;
export type MicrophoneEvents = typeof __propDef.events;
export type MicrophoneSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
