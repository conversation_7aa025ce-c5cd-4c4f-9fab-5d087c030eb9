import{a as d,i as w,s as p,f as m,q as $,y as j,b as q,e as B,z as g,A as u,d as h,u as I,r as v,v as z,k as _,h as A,t as c,j as C,l as b,c as N,m as S,n as T}from"../lite.js";import{I as D}from"./Info-BVYOtGfA.js";function k(l){let e,i;return e=new D({props:{root:l[2],info:l[1]}}),{c(){N(e.$$.fragment)},m(o,n){S(e,o,n),i=!0},p(o,n){const r={};n&4&&(r.root=o[2]),n&2&&(r.info=o[1]),e.$set(r)},i(o){i||(_(e.$$.fragment,o),i=!0)},o(o){c(e.$$.fragment,o),i=!1},d(o){T(e,o)}}}function E(l){let e,i,o,n;const r=l[4].default,f=$(r,l,l[3],null);let t=l[1]&&k(l);return{c(){e=j("span"),f&&f.c(),i=q(),t&&t.c(),o=B(),g(e,"data-testid","block-info"),g(e,"class","svelte-1gfkn6j"),u(e,"sr-only",!l[0]),u(e,"hide",!l[0]),u(e,"has-info",l[1]!=null)},m(s,a){h(s,e,a),f&&f.m(e,null),h(s,i,a),t&&t.m(s,a),h(s,o,a),n=!0},p(s,[a]){f&&f.p&&(!n||a&8)&&I(f,r,s,s[3],n?z(r,s[3],a,null):v(s[3]),null),(!n||a&1)&&u(e,"sr-only",!s[0]),(!n||a&1)&&u(e,"hide",!s[0]),(!n||a&2)&&u(e,"has-info",s[1]!=null),s[1]?t?(t.p(s,a),a&2&&_(t,1)):(t=k(s),t.c(),_(t,1),t.m(o.parentNode,o)):t&&(A(),c(t,1,1,()=>{t=null}),C())},i(s){n||(_(f,s),_(t),n=!0)},o(s){c(f,s),c(t),n=!1},d(s){s&&(b(e),b(i),b(o)),f&&f.d(s),t&&t.d(s)}}}function F(l,e,i){let{$$slots:o={},$$scope:n}=e,{show_label:r=!0}=e,{info:f=void 0}=e,{root:t}=e;return l.$$set=s=>{"show_label"in s&&i(0,r=s.show_label),"info"in s&&i(1,f=s.info),"root"in s&&i(2,t=s.root),"$$scope"in s&&i(3,n=s.$$scope)},[r,f,t,n,o]}class J extends d{constructor(e){super(),w(this,e,F,E,p,{show_label:0,info:1,root:2})}get show_label(){return this.$$.ctx[0]}set show_label(e){this.$$set({show_label:e}),m()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),m()}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),m()}}export{J as B};
//# sourceMappingURL=BlockTitle-DvFB_De3.js.map
