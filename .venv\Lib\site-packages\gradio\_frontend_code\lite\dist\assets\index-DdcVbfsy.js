import{a as de,i as me,s as ue,f as h,O as Q,a7 as R,c as V,b as G,y as X,z as H,m as B,d as j,C as oe,h as y,t as p,j as $,k as v,a8 as Z,l as I,n as S,o as Te,p as E,w as _e,x as fe,D as re,e as be,q as Ye,u as Ae,r as Fe,v as Ge,B as ge,Y as we,S as pe,a0 as ve,a6 as ke}from"../lite.js";import{U as He}from"./Upload-Do_omv-N.js";import{B as Ke}from"./BlockLabel-DWW9BWN3.js";import{V as Me}from"./Video-4rA7HTQG.js";import{S as Qe}from"./SelectSource-kJI_8u2f.js";import"./Image-BPQ6A_U-.js";/* empty css                                                   */import{W as Re}from"./ImageUploader-BK9kfkZd.js";/* empty css                                              */import{p as ce,a as Xe}from"./Video-DBVExGTx.js";import{l as tl}from"./Video-DBVExGTx.js";import{P as Ze,V as ye}from"./VideoPreview-B8hyIiIs.js";import{default as sl}from"./Example-Bwj3U3qU.js";import{U as $e}from"./UploadText-Chjc4Zy7.js";/* empty css                                             */import"./Upload-CYshamIj.js";import"./file-url-CoOyVRgq.js";import"./FullscreenButton-DsVuMC2h.js";import"./Minimize-DOBO88I3.js";import"./IconButtonWrapper-BqpIgNIH.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-DIboSv6l.js";import"./Square-CkbFMpLj.js";import"./index-B9I6rkKj.js";import"./StreamingBar-lVbwTGD1.js";import"./hls-CnVhpNcu.js";import"./Empty-Bzq0Ew6m.js";import"./ShareButton-Be-vgu5O.js";import"./Community-BFnPJcwx.js";import"./utils-BsGrhMNe.js";import"./Download-RUpc9r8A.js";import"./DownloadLink-dHe4pFcz.js";import"./Trim-CDsEvQ4G.js";import"./Play-BIkNyEKH.js";import"./Undo-50qkik3g.js";import"./ModifyUpload-b77W1M2_.js";import"./Edit-fMGAgLsI.js";function xe(l){let e,s=(l[0].orig_name||l[0].url)+"",t,n,i,u=ce(l[0].size)+"",f;return{c(){e=X("div"),t=_e(s),n=G(),i=X("div"),f=_e(u),H(e,"class","file-name svelte-14jis2k"),H(i,"class","file-size svelte-14jis2k")},m(a,c){j(a,e,c),oe(e,t),j(a,n,c),j(a,i,c),oe(i,f)},p(a,c){c[0]&1&&s!==(s=(a[0].orig_name||a[0].url)+"")&&fe(t,s),c[0]&1&&u!==(u=ce(a[0].size)+"")&&fe(f,u)},i:re,o:re,d(a){a&&(I(e),I(n),I(i))}}}function et(l){let e=l[0]?.url,s,t,n=he(l);return{c(){n.c(),s=be()},m(i,u){n.m(i,u),j(i,s,u),t=!0},p(i,u){u[0]&1&&ue(e,e=i[0]?.url)?(y(),p(n,1,1,re),$(),n=he(i),n.c(),v(n,1),n.m(s.parentNode,s)):n.p(i,u)},i(i){t||(v(n),t=!0)},o(i){p(n),t=!1},d(i){i&&I(s),n.d(i)}}}function tt(l){let e,s,t,n;const i=[st,lt],u=[];function f(a,c){return a[1]==="upload"?0:a[1]==="webcam"?1:-1}return~(s=f(l))&&(t=u[s]=i[s](l)),{c(){e=X("div"),t&&t.c(),H(e,"class","upload-container svelte-14jis2k")},m(a,c){j(a,e,c),~s&&u[s].m(e,null),n=!0},p(a,c){let o=s;s=f(a),s===o?~s&&u[s].p(a,c):(t&&(y(),p(u[o],1,1,()=>{u[o]=null}),$()),~s?(t=u[s],t?t.p(a,c):(t=u[s]=i[s](a),t.c()),v(t,1),t.m(e,null)):t=null)},i(a){n||(v(t),n=!0)},o(a){p(t),n=!1},d(a){a&&I(e),~s&&u[s].d()}}}function he(l){let e,s;return e=new Ze({props:{upload:l[15],root:l[11],interactive:!0,autoplay:l[10],src:l[0].url,subtitle:l[3]?.url,is_stream:!1,mirror:l[8]&&l[1]==="webcam",label:l[5],handle_change:l[24],handle_reset_value:l[13],loop:l[17],value:l[0],i18n:l[12],show_download_button:l[6],handle_clear:l[23],has_change_history:l[20]}}),e.$on("play",l[33]),e.$on("pause",l[34]),e.$on("stop",l[35]),e.$on("end",l[36]),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&32768&&(i.upload=t[15]),n[0]&2048&&(i.root=t[11]),n[0]&1024&&(i.autoplay=t[10]),n[0]&1&&(i.src=t[0].url),n[0]&8&&(i.subtitle=t[3]?.url),n[0]&258&&(i.mirror=t[8]&&t[1]==="webcam"),n[0]&32&&(i.label=t[5]),n[0]&8192&&(i.handle_reset_value=t[13]),n[0]&131072&&(i.loop=t[17]),n[0]&1&&(i.value=t[0]),n[0]&4096&&(i.i18n=t[12]),n[0]&64&&(i.show_download_button=t[6]),n[0]&1048576&&(i.has_change_history=t[20]),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function lt(l){let e,s;return e=new Re({props:{root:l[11],mirror_webcam:l[8],include_audio:l[9],webcam_constraints:l[18],mode:"video",i18n:l[12],upload:l[15],stream_every:1}}),e.$on("error",l[30]),e.$on("capture",l[25]),e.$on("start_recording",l[31]),e.$on("stop_recording",l[32]),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&2048&&(i.root=t[11]),n[0]&256&&(i.mirror_webcam=t[8]),n[0]&512&&(i.include_audio=t[9]),n[0]&262144&&(i.webcam_constraints=t[18]),n[0]&4096&&(i.i18n=t[12]),n[0]&32768&&(i.upload=t[15]),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function st(l){let e,s,t,n;function i(a){l[27](a)}function u(a){l[28](a)}let f={filetype:"video/x-m4v,video/*",max_file_size:l[14],root:l[11],upload:l[15],stream_handler:l[16],aria_label:l[12]("video.drop_to_upload"),$$slots:{default:[nt]},$$scope:{ctx:l}};return l[19]!==void 0&&(f.dragging=l[19]),l[2]!==void 0&&(f.uploading=l[2]),e=new He({props:f}),Q.push(()=>R(e,"dragging",i)),Q.push(()=>R(e,"uploading",u)),e.$on("load",l[22]),e.$on("error",l[29]),{c(){V(e.$$.fragment)},m(a,c){B(e,a,c),n=!0},p(a,c){const o={};c[0]&16384&&(o.max_file_size=a[14]),c[0]&2048&&(o.root=a[11]),c[0]&32768&&(o.upload=a[15]),c[0]&65536&&(o.stream_handler=a[16]),c[0]&4096&&(o.aria_label=a[12]("video.drop_to_upload")),c[1]&128&&(o.$$scope={dirty:c,ctx:a}),!s&&c[0]&524288&&(s=!0,o.dragging=a[19],Z(()=>s=!1)),!t&&c[0]&4&&(t=!0,o.uploading=a[2],Z(()=>t=!1)),e.$set(o)},i(a){n||(v(e.$$.fragment,a),n=!0)},o(a){p(e.$$.fragment,a),n=!1},d(a){S(e,a)}}}function nt(l){let e;const s=l[26].default,t=Ye(s,l,l[38],null);return{c(){t&&t.c()},m(n,i){t&&t.m(n,i),e=!0},p(n,i){t&&t.p&&(!e||i[1]&128)&&Ae(t,s,n,n[38],e?Ge(s,n[38],i,null):Fe(n[38]),null)},i(n){e||(v(t,n),e=!0)},o(n){p(t,n),e=!1},d(n){t&&t.d(n)}}}function it(l){let e,s,t,n,i,u,f,a,c,o;e=new Ke({props:{show_label:l[7],Icon:Me,label:l[5]||"Video"}});const d=[tt,et,xe],w=[];function b(m,k){return m[0]===null||m[0].url===void 0?0:(n==null&&(n=!!Xe()),n?1:m[0].size?2:-1)}~(i=b(l))&&(u=w[i]=d[i](l));function D(m){l[37](m)}let O={sources:l[4],handle_clear:l[23]};return l[1]!==void 0&&(O.active_source=l[1]),a=new Qe({props:O}),Q.push(()=>R(a,"active_source",D)),{c(){V(e.$$.fragment),s=G(),t=X("div"),u&&u.c(),f=G(),V(a.$$.fragment),H(t,"data-testid","video"),H(t,"class","video-container svelte-14jis2k")},m(m,k){B(e,m,k),j(m,s,k),j(m,t,k),~i&&w[i].m(t,null),oe(t,f),B(a,t,null),o=!0},p(m,k){const N={};k[0]&128&&(N.show_label=m[7]),k[0]&32&&(N.label=m[5]||"Video"),e.$set(N);let P=i;i=b(m),i===P?~i&&w[i].p(m,k):(u&&(y(),p(w[P],1,1,()=>{w[P]=null}),$()),~i?(u=w[i],u?u.p(m,k):(u=w[i]=d[i](m),u.c()),v(u,1),u.m(t,f)):u=null);const U={};k[0]&16&&(U.sources=m[4]),!c&&k[0]&2&&(c=!0,U.active_source=m[1],Z(()=>c=!1)),a.$set(U)},i(m){o||(v(e.$$.fragment,m),v(u),v(a.$$.fragment,m),o=!0)},o(m){p(e.$$.fragment,m),p(u),p(a.$$.fragment,m),o=!1},d(m){m&&(I(s),I(t)),S(e,m),~i&&w[i].d(),S(a)}}}function at(l,e,s){let{$$slots:t={},$$scope:n}=e,{value:i=null}=e,{subtitle:u=null}=e,{sources:f=["webcam","upload"]}=e,{label:a=void 0}=e,{show_download_button:c=!1}=e,{show_label:o=!0}=e,{mirror_webcam:d=!1}=e,{include_audio:w}=e,{autoplay:b}=e,{root:D}=e,{i18n:O}=e,{active_source:m="webcam"}=e,{handle_reset_value:k=()=>{}}=e,{max_file_size:N=null}=e,{upload:P}=e,{stream_handler:U}=e,{loop:W}=e,{uploading:g=!1}=e,{webcam_constraints:L=null}=e,T=!1;const z=Te();function K({detail:r}){s(0,i=r),z("change",r),z("upload",r)}function Y(){s(0,i=null),z("change",null),z("clear")}function J(r){s(20,T=!0),z("change",r)}function A({detail:r}){z("change",r)}let q=!1;function F(r){q=r,s(19,q)}function C(r){g=r,s(2,g)}const x=({detail:r})=>z("error",r);function M(r){E.call(this,l,r)}function ee(r){E.call(this,l,r)}function te(r){E.call(this,l,r)}function le(r){E.call(this,l,r)}function se(r){E.call(this,l,r)}function ne(r){E.call(this,l,r)}function ie(r){E.call(this,l,r)}function ae(r){m=r,s(1,m)}return l.$$set=r=>{"value"in r&&s(0,i=r.value),"subtitle"in r&&s(3,u=r.subtitle),"sources"in r&&s(4,f=r.sources),"label"in r&&s(5,a=r.label),"show_download_button"in r&&s(6,c=r.show_download_button),"show_label"in r&&s(7,o=r.show_label),"mirror_webcam"in r&&s(8,d=r.mirror_webcam),"include_audio"in r&&s(9,w=r.include_audio),"autoplay"in r&&s(10,b=r.autoplay),"root"in r&&s(11,D=r.root),"i18n"in r&&s(12,O=r.i18n),"active_source"in r&&s(1,m=r.active_source),"handle_reset_value"in r&&s(13,k=r.handle_reset_value),"max_file_size"in r&&s(14,N=r.max_file_size),"upload"in r&&s(15,P=r.upload),"stream_handler"in r&&s(16,U=r.stream_handler),"loop"in r&&s(17,W=r.loop),"uploading"in r&&s(2,g=r.uploading),"webcam_constraints"in r&&s(18,L=r.webcam_constraints),"$$scope"in r&&s(38,n=r.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&524288&&z("drag",q)},[i,m,g,u,f,a,c,o,d,w,b,D,O,k,N,P,U,W,L,q,T,z,K,Y,J,A,t,F,C,x,M,ee,te,le,se,ne,ie,ae,n]}class ot extends de{constructor(e){super(),me(this,e,at,it,ue,{value:0,subtitle:3,sources:4,label:5,show_download_button:6,show_label:7,mirror_webcam:8,include_audio:9,autoplay:10,root:11,i18n:12,active_source:1,handle_reset_value:13,max_file_size:14,upload:15,stream_handler:16,loop:17,uploading:2,webcam_constraints:18},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get subtitle(){return this.$$.ctx[3]}set subtitle(e){this.$$set({subtitle:e}),h()}get sources(){return this.$$.ctx[4]}set sources(e){this.$$set({sources:e}),h()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),h()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),h()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),h()}get mirror_webcam(){return this.$$.ctx[8]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),h()}get include_audio(){return this.$$.ctx[9]}set include_audio(e){this.$$set({include_audio:e}),h()}get autoplay(){return this.$$.ctx[10]}set autoplay(e){this.$$set({autoplay:e}),h()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),h()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),h()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),h()}get handle_reset_value(){return this.$$.ctx[13]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),h()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),h()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),h()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),h()}get loop(){return this.$$.ctx[17]}set loop(e){this.$$set({loop:e}),h()}get uploading(){return this.$$.ctx[2]}set uploading(e){this.$$set({uploading:e}),h()}get webcam_constraints(){return this.$$.ctx[18]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),h()}}const rt=ot;function ut(l){let e,s;return e=new ge({props:{visible:l[4],variant:l[0]===null&&l[24]==="upload"?"dashed":"solid",border_mode:l[27]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[12],scale:l[13],min_width:l[14],allow_overflow:!1,$$slots:{default:[ct]},$$scope:{ctx:l}}}),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&16&&(i.visible=t[4]),n[0]&16777217&&(i.variant=t[0]===null&&t[24]==="upload"?"dashed":"solid"),n[0]&134217728&&(i.border_mode=t[27]?"focus":"base"),n[0]&4&&(i.elem_id=t[2]),n[0]&8&&(i.elem_classes=t[3]),n[0]&512&&(i.height=t[9]),n[0]&1024&&(i.width=t[10]),n[0]&4096&&(i.container=t[12]),n[0]&8192&&(i.scale=t[13]),n[0]&16384&&(i.min_width=t[14]),n[0]&267815394|n[1]&16777216&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function _t(l){let e,s;return e=new ge({props:{visible:l[4],variant:l[0]===null&&l[24]==="upload"?"dashed":"solid",border_mode:l[27]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[12],scale:l[13],min_width:l[14],allow_overflow:!1,$$slots:{default:[ht]},$$scope:{ctx:l}}}),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&16&&(i.visible=t[4]),n[0]&16777217&&(i.variant=t[0]===null&&t[24]==="upload"?"dashed":"solid"),n[0]&134217728&&(i.border_mode=t[27]?"focus":"base"),n[0]&4&&(i.elem_id=t[2]),n[0]&8&&(i.elem_classes=t[3]),n[0]&512&&(i.height=t[9]),n[0]&1024&&(i.width=t[10]),n[0]&4096&&(i.container=t[12]),n[0]&8192&&(i.scale=t[13]),n[0]&16384&&(i.min_width=t[14]),n[0]&105349410|n[1]&16777216&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function ft(l){let e,s;return e=new $e({props:{i18n:l[18].i18n,type:"video"}}),{c(){V(e.$$.fragment)},m(t,n){B(e,t,n),s=!0},p(t,n){const i={};n[0]&262144&&(i.i18n=t[18].i18n),e.$set(i)},i(t){s||(v(e.$$.fragment,t),s=!0)},o(t){p(e.$$.fragment,t),s=!1},d(t){S(e,t)}}}function ct(l){let e,s,t,n,i;const u=[{autoscroll:l[18].autoscroll},{i18n:l[18].i18n},l[1]];let f={};for(let o=0;o<u.length;o+=1)f=we(f,u[o]);e=new pe({props:f}),e.$on("clear_status",l[42]);function a(o){l[45](o)}let c={value:l[25],subtitle:l[26],label:l[5],show_label:l[8],show_download_button:l[17],sources:l[6],active_source:l[24],mirror_webcam:l[20],include_audio:l[21],autoplay:l[15],root:l[7],loop:l[22],webcam_constraints:l[11],handle_reset_value:l[28],i18n:l[18].i18n,max_file_size:l[18].max_file_size,upload:l[43],stream_handler:l[44],$$slots:{default:[ft]},$$scope:{ctx:l}};return l[23]!==void 0&&(c.uploading=l[23]),t=new rt({props:c}),Q.push(()=>R(t,"uploading",a)),t.$on("change",l[29]),t.$on("drag",l[46]),t.$on("error",l[30]),t.$on("clear",l[47]),t.$on("play",l[48]),t.$on("pause",l[49]),t.$on("upload",l[50]),t.$on("stop",l[51]),t.$on("end",l[52]),t.$on("start_recording",l[53]),t.$on("stop_recording",l[54]),{c(){V(e.$$.fragment),s=G(),V(t.$$.fragment)},m(o,d){B(e,o,d),j(o,s,d),B(t,o,d),i=!0},p(o,d){const w=d[0]&262146?ve(u,[d[0]&262144&&{autoscroll:o[18].autoscroll},d[0]&262144&&{i18n:o[18].i18n},d[0]&2&&ke(o[1])]):{};e.$set(w);const b={};d[0]&33554432&&(b.value=o[25]),d[0]&67108864&&(b.subtitle=o[26]),d[0]&32&&(b.label=o[5]),d[0]&256&&(b.show_label=o[8]),d[0]&131072&&(b.show_download_button=o[17]),d[0]&64&&(b.sources=o[6]),d[0]&16777216&&(b.active_source=o[24]),d[0]&1048576&&(b.mirror_webcam=o[20]),d[0]&2097152&&(b.include_audio=o[21]),d[0]&32768&&(b.autoplay=o[15]),d[0]&128&&(b.root=o[7]),d[0]&4194304&&(b.loop=o[22]),d[0]&2048&&(b.webcam_constraints=o[11]),d[0]&262144&&(b.i18n=o[18].i18n),d[0]&262144&&(b.max_file_size=o[18].max_file_size),d[0]&262144&&(b.upload=o[43]),d[0]&262144&&(b.stream_handler=o[44]),d[0]&262144|d[1]&16777216&&(b.$$scope={dirty:d,ctx:o}),!n&&d[0]&8388608&&(n=!0,b.uploading=o[23],Z(()=>n=!1)),t.$set(b)},i(o){i||(v(e.$$.fragment,o),v(t.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),p(t.$$.fragment,o),i=!1},d(o){o&&I(s),S(e,o),S(t,o)}}}function ht(l){let e,s,t,n;const i=[{autoscroll:l[18].autoscroll},{i18n:l[18].i18n},l[1]];let u={};for(let f=0;f<i.length;f+=1)u=we(u,i[f]);return e=new pe({props:u}),e.$on("clear_status",l[34]),t=new ye({props:{value:l[25],subtitle:l[26],label:l[5],show_label:l[8],autoplay:l[15],loop:l[22],show_share_button:l[16],show_download_button:l[17],i18n:l[18].i18n,upload:l[35]}}),t.$on("play",l[36]),t.$on("pause",l[37]),t.$on("stop",l[38]),t.$on("end",l[39]),t.$on("share",l[40]),t.$on("error",l[41]),{c(){V(e.$$.fragment),s=G(),V(t.$$.fragment)},m(f,a){B(e,f,a),j(f,s,a),B(t,f,a),n=!0},p(f,a){const c=a[0]&262146?ve(i,[a[0]&262144&&{autoscroll:f[18].autoscroll},a[0]&262144&&{i18n:f[18].i18n},a[0]&2&&ke(f[1])]):{};e.$set(c);const o={};a[0]&33554432&&(o.value=f[25]),a[0]&67108864&&(o.subtitle=f[26]),a[0]&32&&(o.label=f[5]),a[0]&256&&(o.show_label=f[8]),a[0]&32768&&(o.autoplay=f[15]),a[0]&4194304&&(o.loop=f[22]),a[0]&65536&&(o.show_share_button=f[16]),a[0]&131072&&(o.show_download_button=f[17]),a[0]&262144&&(o.i18n=f[18].i18n),a[0]&262144&&(o.upload=f[35]),t.$set(o)},i(f){n||(v(e.$$.fragment,f),v(t.$$.fragment,f),n=!0)},o(f){p(e.$$.fragment,f),p(t.$$.fragment,f),n=!1},d(f){f&&I(s),S(e,f),S(t,f)}}}function dt(l){let e,s,t,n;const i=[_t,ut],u=[];function f(a,c){return a[19]?1:0}return e=f(l),s=u[e]=i[e](l),{c(){s.c(),t=be()},m(a,c){u[e].m(a,c),j(a,t,c),n=!0},p(a,c){let o=e;e=f(a),e===o?u[e].p(a,c):(y(),p(u[o],1,1,()=>{u[o]=null}),$(),s=u[e],s?s.p(a,c):(s=u[e]=i[e](a),s.c()),v(s,1),s.m(t.parentNode,t))},i(a){n||(v(s),n=!0)},o(a){p(s),n=!1},d(a){a&&I(t),u[e].d(a)}}}function mt(l,e,s){let{elem_id:t=""}=e,{elem_classes:n=[]}=e,{visible:i=!0}=e,{value:u=null}=e,f=null,{label:a}=e,{sources:c}=e,{root:o}=e,{show_label:d}=e,{loading_status:w}=e,{height:b}=e,{width:D}=e,{webcam_constraints:O=null}=e,{container:m=!1}=e,{scale:k=null}=e,{min_width:N=void 0}=e,{autoplay:P=!1}=e,{show_share_button:U=!0}=e,{show_download_button:W}=e,{gradio:g}=e,{interactive:L}=e,{mirror_webcam:T}=e,{include_audio:z}=e,{loop:K=!1}=e,{input_ready:Y}=e,J=!1,A=null,q=null,F,C=u;const x=()=>{C===null||u===C||s(0,u=C)};let M=!1;function ee({detail:_}){_!=null?s(0,u={video:_,subtitles:null}):s(0,u=null)}function te({detail:_}){const[We,Le]=_.includes("Invalid file type")?["warning","complete"]:["error","error"];s(1,w=w||{}),s(1,w.status=Le,w),s(1,w.message=_,w),g.dispatch(We,_)}const le=()=>g.dispatch("clear_status",w),se=(..._)=>g.client.upload(..._),ne=()=>g.dispatch("play"),ie=()=>g.dispatch("pause"),ae=()=>g.dispatch("stop"),r=()=>g.dispatch("end"),ze=({detail:_})=>g.dispatch("share",_),Ve=({detail:_})=>g.dispatch("error",_),Be=()=>g.dispatch("clear_status",w),Se=(..._)=>g.client.upload(..._),je=(..._)=>g.client.stream(..._);function Ie(_){J=_,s(23,J)}const Ne=({detail:_})=>s(27,M=_),Pe=()=>g.dispatch("clear"),Ue=()=>g.dispatch("play"),Oe=()=>g.dispatch("pause"),qe=()=>g.dispatch("upload"),Ce=()=>g.dispatch("stop"),De=()=>g.dispatch("end"),Ee=()=>g.dispatch("start_recording"),Je=()=>g.dispatch("stop_recording");return l.$$set=_=>{"elem_id"in _&&s(2,t=_.elem_id),"elem_classes"in _&&s(3,n=_.elem_classes),"visible"in _&&s(4,i=_.visible),"value"in _&&s(0,u=_.value),"label"in _&&s(5,a=_.label),"sources"in _&&s(6,c=_.sources),"root"in _&&s(7,o=_.root),"show_label"in _&&s(8,d=_.show_label),"loading_status"in _&&s(1,w=_.loading_status),"height"in _&&s(9,b=_.height),"width"in _&&s(10,D=_.width),"webcam_constraints"in _&&s(11,O=_.webcam_constraints),"container"in _&&s(12,m=_.container),"scale"in _&&s(13,k=_.scale),"min_width"in _&&s(14,N=_.min_width),"autoplay"in _&&s(15,P=_.autoplay),"show_share_button"in _&&s(16,U=_.show_share_button),"show_download_button"in _&&s(17,W=_.show_download_button),"gradio"in _&&s(18,g=_.gradio),"interactive"in _&&s(19,L=_.interactive),"mirror_webcam"in _&&s(20,T=_.mirror_webcam),"include_audio"in _&&s(21,z=_.include_audio),"loop"in _&&s(22,K=_.loop),"input_ready"in _&&s(31,Y=_.input_ready)},l.$$.update=()=>{l.$$.dirty[0]&8388608&&s(31,Y=!J),l.$$.dirty[0]&1|l.$$.dirty[1]&4&&u&&C===null&&s(33,C=u),l.$$.dirty[0]&16777280&&c&&!F&&s(24,F=c[0]),l.$$.dirty[0]&1&&(u!=null?(s(25,A=u.video),s(26,q=u.subtitles)):(s(25,A=null),s(26,q=null))),l.$$.dirty[0]&262145|l.$$.dirty[1]&2&&JSON.stringify(u)!==JSON.stringify(f)&&(s(32,f=u),g.dispatch("change"))},[u,w,t,n,i,a,c,o,d,b,D,O,m,k,N,P,U,W,g,L,T,z,K,J,F,A,q,M,x,ee,te,Y,f,C,le,se,ne,ie,ae,r,ze,Ve,Be,Se,je,Ie,Ne,Pe,Ue,Oe,qe,Ce,De,Ee,Je]}class bt extends de{constructor(e){super(),me(this,e,mt,dt,ue,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,webcam_constraints:11,container:12,scale:13,min_width:14,autoplay:15,show_share_button:16,show_download_button:17,gradio:18,interactive:19,mirror_webcam:20,include_audio:21,loop:22,input_ready:31},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),h()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),h()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),h()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),h()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),h()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),h()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),h()}get webcam_constraints(){return this.$$.ctx[11]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),h()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),h()}get autoplay(){return this.$$.ctx[15]}set autoplay(e){this.$$set({autoplay:e}),h()}get show_share_button(){return this.$$.ctx[16]}set show_share_button(e){this.$$set({show_share_button:e}),h()}get show_download_button(){return this.$$.ctx[17]}set show_download_button(e){this.$$set({show_download_button:e}),h()}get gradio(){return this.$$.ctx[18]}set gradio(e){this.$$set({gradio:e}),h()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),h()}get mirror_webcam(){return this.$$.ctx[20]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),h()}get include_audio(){return this.$$.ctx[21]}set include_audio(e){this.$$set({include_audio:e}),h()}get loop(){return this.$$.ctx[22]}set loop(e){this.$$set({loop:e}),h()}get input_ready(){return this.$$.ctx[31]}set input_ready(e){this.$$set({input_ready:e}),h()}}const $t=bt;export{sl as BaseExample,rt as BaseInteractiveVideo,Ze as BasePlayer,ye as BaseStaticVideo,$t as default,tl as loaded,Xe as playable,ce as prettyBytes};
//# sourceMappingURL=index-DdcVbfsy.js.map
