import{a as P,i as R,s as S,f as k,y as v,b as I,z as y,A,d,C as h,h as T,t as N,j as B,k as U,l as m,c as z,m as D,n as G,w as p,e as H,x as w}from"../lite.js";import{U as F,I as J}from"./Upload-CYshamIj.js";const K=/^(#\s*)(.+)$/m;function L(s){const e=s.trim(),l=e.match(K);if(!l)return[!1,e||!1];const[t,,i]=l,n=i.trim();if(e===t)return[n,!1];const o=l.index!==void 0?l.index+t.length:0,f=e.substring(o).trim()||!1;return[n,f]}function M(s){let e,l;return e=new F({}),{c(){z(e.$$.fragment)},m(t,i){D(e,t,i),l=!0},i(t){l||(U(e.$$.fragment,t),l=!0)},o(t){N(e.$$.fragment,t),l=!1},d(t){G(e,t)}}}function O(s){let e,l;return e=new J({}),{c(){z(e.$$.fragment)},m(t,i){D(e,t,i),l=!0},i(t){l||(U(e.$$.fragment,t),l=!0)},o(t){N(e.$$.fragment,t),l=!1},d(t){G(e,t)}}}function Q(s){let e=s[1](s[7][s[0]]||s[7].file)+"",l,t,i,n=s[3]!=="short"&&C(s);return{c(){l=p(e),t=I(),n&&n.c(),i=H()},m(o,a){d(o,l,a),d(o,t,a),n&&n.m(o,a),d(o,i,a)},p(o,a){a&3&&e!==(e=o[1](o[7][o[0]]||o[7].file)+"")&&w(l,e),o[3]!=="short"?n?n.p(o,a):(n=C(o),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},d(o){o&&(m(l),m(t),m(i)),n&&n.d(o)}}}function V(s){let e,l,t=s[6]&&E(s),i=s[5]&&q(s);return{c(){t&&t.c(),e=I(),i&&i.c(),l=H()},m(n,o){t&&t.m(n,o),d(n,e,o),i&&i.m(n,o),d(n,l,o)},p(n,o){n[6]?t?t.p(n,o):(t=E(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),n[5]?i?i.p(n,o):(i=q(n),i.c(),i.m(l.parentNode,l)):i&&(i.d(1),i=null)},d(n){n&&(m(e),m(l)),t&&t.d(n),i&&i.d(n)}}}function C(s){let e,l,t=s[1]("common.or")+"",i,n,o,a=(s[2]||s[1]("upload_text.click_to_upload"))+"",f;return{c(){e=v("span"),l=p("- "),i=p(t),n=p(" -"),o=I(),f=p(a),y(e,"class","or svelte-12ioyct")},m(_,u){d(_,e,u),h(e,l),h(e,i),h(e,n),d(_,o,u),d(_,f,u)},p(_,u){u&2&&t!==(t=_[1]("common.or")+"")&&w(i,t),u&6&&a!==(a=(_[2]||_[1]("upload_text.click_to_upload"))+"")&&w(f,a)},d(_){_&&(m(e),m(o),m(f))}}}function E(s){let e,l;return{c(){e=v("h2"),l=p(s[6]),y(e,"class","svelte-12ioyct")},m(t,i){d(t,e,i),h(e,l)},p(t,i){i&64&&w(l,t[6])},d(t){t&&m(e)}}}function q(s){let e,l;return{c(){e=v("p"),l=p(s[5]),y(e,"class","svelte-12ioyct")},m(t,i){d(t,e,i),h(e,l)},p(t,i){i&32&&w(l,t[5])},d(t){t&&m(e)}}}function W(s){let e,l,t,i,n,o;const a=[O,M],f=[];function _(c,b){return c[0]==="clipboard"?0:1}t=_(s),i=f[t]=a[t](s);function u(c,b){return c[6]||c[5]?V:Q}let g=u(s),r=g(s);return{c(){e=v("div"),l=v("span"),i.c(),n=I(),r.c(),y(l,"class","icon-wrap svelte-12ioyct"),A(l,"hovered",s[4]),y(e,"class","wrap svelte-12ioyct")},m(c,b){d(c,e,b),h(e,l),f[t].m(l,null),h(e,n),r.m(e,null),o=!0},p(c,[b]){let j=t;t=_(c),t!==j&&(T(),N(f[j],1,1,()=>{f[j]=null}),B(),i=f[t],i||(i=f[t]=a[t](c),i.c()),U(i,1),i.m(l,null)),(!o||b&16)&&A(l,"hovered",c[4]),g===(g=u(c))&&r?r.p(c,b):(r.d(1),r=g(c),r&&(r.c(),r.m(e,null)))},i(c){o||(U(i),o=!0)},o(c){N(i),o=!1},d(c){c&&m(e),f[t].d(),r.d()}}}function X(s,e,l){let t,i,{type:n="file"}=e,{i18n:o}=e,{message:a=void 0}=e,{mode:f="full"}=e,{hovered:_=!1}=e,{placeholder:u=void 0}=e;const g={image:"upload_text.drop_image",video:"upload_text.drop_video",audio:"upload_text.drop_audio",file:"upload_text.drop_file",csv:"upload_text.drop_csv",gallery:"upload_text.drop_gallery",clipboard:"upload_text.paste_clipboard"};return s.$$set=r=>{"type"in r&&l(0,n=r.type),"i18n"in r&&l(1,o=r.i18n),"message"in r&&l(2,a=r.message),"mode"in r&&l(3,f=r.mode),"hovered"in r&&l(4,_=r.hovered),"placeholder"in r&&l(8,u=r.placeholder)},s.$$.update=()=>{s.$$.dirty&256&&l(6,[t,i]=u?L(u):[!1,!1],t,(l(5,i),l(8,u)))},[n,o,a,f,_,i,t,g,u]}class x extends P{constructor(e){super(),R(this,e,X,W,S,{type:0,i18n:1,message:2,mode:3,hovered:4,placeholder:8})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),k()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),k()}get message(){return this.$$.ctx[2]}set message(e){this.$$set({message:e}),k()}get mode(){return this.$$.ctx[3]}set mode(e){this.$$set({mode:e}),k()}get hovered(){return this.$$.ctx[4]}set hovered(e){this.$$set({hovered:e}),k()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),k()}}export{x as U};
//# sourceMappingURL=UploadText-Chjc4Zy7.js.map
