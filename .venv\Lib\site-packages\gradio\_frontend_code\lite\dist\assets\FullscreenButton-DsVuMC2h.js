import{a as w,i as b,s as v,E as m,z as i,d as _,C as p,D as f,l as d,f as F,b as M,e as z,k as a,h as g,t as u,j as h,o as B,a5 as C,I,c as x,m as y,n as E}from"../lite.js";import{M as j,a as q}from"./Minimize-DOBO88I3.js";function D(l){let e,r,t,s;return{c(){e=m("svg"),r=m("rect"),t=m("circle"),s=m("polyline"),i(r,"x","3"),i(r,"y","3"),i(r,"width","18"),i(r,"height","18"),i(r,"rx","2"),i(r,"ry","2"),i(t,"cx","8.5"),i(t,"cy","8.5"),i(t,"r","1.5"),i(s,"points","21 15 16 10 5 21"),i(e,"xmlns","http://www.w3.org/2000/svg"),i(e,"width","100%"),i(e,"height","100%"),i(e,"viewBox","0 0 24 24"),i(e,"fill","none"),i(e,"stroke","currentColor"),i(e,"stroke-width","1.5"),i(e,"stroke-linecap","round"),i(e,"stroke-linejoin","round"),i(e,"class","feather feather-image")},m(n,o){_(n,e,o),p(e,r),p(e,t),p(e,s)},p:f,i:f,o:f,d(n){n&&d(e)}}}class A extends w{constructor(e){super(),b(this,e,null,D,v,{})}}function $(l){let e,r;return e=new I({props:{Icon:j,label:"View in full screen"}}),e.$on("click",l[1]),{c(){x(e.$$.fragment)},m(t,s){y(e,t,s),r=!0},p:f,i(t){r||(a(e.$$.fragment,t),r=!0)},o(t){u(e.$$.fragment,t),r=!1},d(t){E(e,t)}}}function k(l){let e,r;return e=new I({props:{Icon:q,label:"Exit full screen"}}),e.$on("click",l[1]),{c(){x(e.$$.fragment)},m(t,s){y(e,t,s),r=!0},p:f,i(t){r||(a(e.$$.fragment,t),r=!0)},o(t){u(e.$$.fragment,t),r=!1},d(t){E(e,t)}}}function N(l){let e,r,t,s=!l[0]&&$(l),n=l[0]&&k(l);return{c(){s&&s.c(),e=M(),n&&n.c(),r=z()},m(o,c){s&&s.m(o,c),_(o,e,c),n&&n.m(o,c),_(o,r,c),t=!0},p(o,[c]){o[0]?s&&(g(),u(s,1,1,()=>{s=null}),h()):s?(s.p(o,c),c&1&&a(s,1)):(s=$(o),s.c(),a(s,1),s.m(e.parentNode,e)),o[0]?n?(n.p(o,c),c&1&&a(n,1)):(n=k(o),n.c(),a(n,1),n.m(r.parentNode,r)):n&&(g(),u(n,1,1,()=>{n=null}),h())},i(o){t||(a(s),a(n),t=!0)},o(o){u(s),u(n),t=!1},d(o){o&&(d(e),d(r)),s&&s.d(o),n&&n.d(o)}}}function L(l,e,r){let{container:t=void 0}=e;const s=B();let n=!1;C(()=>{document.addEventListener("fullscreenchange",()=>{r(0,n=!!document.fullscreenElement),s("fullscreenchange",n)})});const o=async()=>{t&&(n?(await document.exitFullscreen(),r(0,n=!n)):await t.requestFullscreen())};return l.$$set=c=>{"container"in c&&r(2,t=c.container)},[n,o,t]}class G extends w{constructor(e){super(),b(this,e,L,N,v,{container:2})}get container(){return this.$$.ctx[2]}set container(e){this.$$set({container:e}),F()}}export{G as F,A as I};
//# sourceMappingURL=FullscreenButton-DsVuMC2h.js.map
