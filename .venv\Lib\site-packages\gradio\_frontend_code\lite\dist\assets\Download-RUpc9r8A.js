import{a as i,i as p,s as v,E as n,z as a,d as h,C as c,D as l,l as d}from"../lite.js";function m(o){let t,e;return{c(){t=n("svg"),e=n("path"),a(e,"fill","currentColor"),a(e,"d","M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z"),a(t,"xmlns","http://www.w3.org/2000/svg"),a(t,"width","100%"),a(t,"height","100%"),a(t,"viewBox","0 0 32 32")},m(s,r){h(s,t,r),c(t,e)},p:l,i:l,o:l,d(s){s&&d(t)}}}class u extends i{constructor(t){super(),p(this,t,null,m,v,{})}}export{u as D};
//# sourceMappingURL=Download-RUpc9r8A.js.map
