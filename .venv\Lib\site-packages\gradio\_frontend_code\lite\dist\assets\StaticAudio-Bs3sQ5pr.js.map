{"version": 3, "file": "StaticAudio-Bs3sQ5pr.js", "sources": ["../../../audio/static/StaticAudio.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport {\n\t\tShareButton,\n\t\tIconButton,\n\t\tBlockLabel,\n\t\tIconButtonWrapper\n\t} from \"@gradio/atoms\";\n\timport { Download, Music } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport AudioPlayer from \"../player/AudioPlayer.svelte\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\n\texport let value: null | FileData = null;\n\texport let label: string;\n\texport let show_label = true;\n\texport let show_download_button = true;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any> = {};\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let editable = true;\n\texport let loop: boolean;\n\texport let display_icon_button_wrapper_top_corner = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={false}\n\tlabel={label || i18n(\"audio.audio\")}\n/>\n\n{#if value !== null}\n\t<IconButtonWrapper\n\t\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\n\t>\n\t\t{#if show_download_button}\n\t\t\t<DownloadLink\n\t\t\t\thref={value.is_stream\n\t\t\t\t\t? value.url?.replace(\"playlist.m3u8\", \"playlist-file\")\n\t\t\t\t\t: value.url}\n\t\t\t\tdownload={value.orig_name || value.path}\n\t\t\t>\n\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t</DownloadLink>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\t{i18n}\n\t\t\t\ton:error\n\t\t\t\ton:share\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value.url, \"url\");\n\t\t\t\t\treturn `<audio controls src=\"${url}\"></audio>`;\n\t\t\t\t}}\n\t\t\t\t{value}\n\t\t\t/>\n\t\t{/if}\n\t</IconButtonWrapper>\n\n\t<AudioPlayer\n\t\t{value}\n\t\t{label}\n\t\t{i18n}\n\t\t{waveform_settings}\n\t\t{waveform_options}\n\t\t{editable}\n\t\t{loop}\n\t\ton:pause\n\t\ton:play\n\t\ton:stop\n\t\ton:load\n\t/>\n{:else}\n\t<Empty size=\"small\">\n\t\t<Music />\n\t</Empty>\n{/if}\n"], "names": ["ctx", "dirty", "downloadlink_changes", "Download", "iconbutton_changes", "create_if_block_2", "create_if_block_1", "Music", "value", "$$props", "label", "show_label", "show_download_button", "show_share_button", "i18n", "waveform_settings", "waveform_options", "editable", "loop", "display_icon_button_wrapper_top_corner", "dispatch", "createEventDispatcher", "uploadToHuggingFace"], "mappings": "sjCAmDsBA,EAAsC,EAAA,2XAAtCA,EAAsC,EAAA,4kBAIlD,KAAAA,EAAM,CAAA,EAAA,UACTA,KAAM,KAAK,QAAQ,gBAAiB,eAAe,EACnDA,KAAM,IACC,SAAAA,EAAM,CAAA,EAAA,WAAaA,KAAM,8GAH7BC,EAAA,IAAAC,EAAA,KAAAF,EAAM,CAAA,EAAA,UACTA,KAAM,KAAK,QAAQ,gBAAiB,eAAe,EACnDA,KAAM,KACCC,EAAA,IAAAC,EAAA,SAAAF,EAAM,CAAA,EAAA,WAAaA,KAAM,yLAEjBG,EAAiB,MAAAH,KAAK,iBAAiB,oEAAtBC,EAAA,KAAAG,EAAA,MAAAJ,KAAK,iBAAiB,wbAPtDA,EAAoB,CAAA,GAAAK,EAAAL,CAAA,IAUpBA,EAAiB,CAAA,GAAAM,EAAAN,CAAA,0GAVjBA,EAAoB,CAAA,wGAUpBA,EAAiB,CAAA,uQAnBjBO,QACC,SACAP,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,wCAG9B,OAAAA,OAAU,KAAI,0LAHXA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,kSA7BvB,MAAAQ,EAAyB,IAAA,EAAAC,EACzB,CAAA,MAAAC,CAAA,EAAAD,GACA,WAAAE,EAAa,EAAA,EAAAF,GACb,qBAAAG,EAAuB,EAAA,EAAAH,GACvB,kBAAAI,EAAoB,EAAA,EAAAJ,EACpB,CAAA,KAAAK,CAAA,EAAAL,EACA,CAAA,kBAAAM,EAAA,EAAA,EAAAN,EACA,CAAA,iBAAAO,EAAA,CACV,wBAAyB,EAAA,CAAA,EAAAP,GAEf,SAAAQ,EAAW,EAAA,EAAAR,EACX,CAAA,KAAAS,CAAA,EAAAT,GACA,uCAAAU,EAAyC,EAAA,EAAAV,QAE9CW,EAAWC,YAqCIb,GACZA,gCACWc,EAAoBd,EAAM,GAAU,CAClB,aAFf,utBA9BpBA,GAASY,EAAS,SAAUZ,CAAK"}