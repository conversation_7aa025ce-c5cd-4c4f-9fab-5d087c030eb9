import{a as u,i as c,s as f,f as i,q as d,y as m,z as p,W as _,d as h,u as y,r as v,v as g,k as q,t as $,l as b}from"../lite.js";function j(a){let e,o,l;const r=a[3].default,s=d(r,a,a[2],null);return{c(){e=m("div"),s&&s.c(),p(e,"class",o=_(`icon-button-wrapper ${a[0]?"top-panel":""} ${a[1]?"display-top-corner":"hide-top-corner"}`)+" svelte-1jx2rq3")},m(t,n){h(t,e,n),s&&s.m(e,null),l=!0},p(t,[n]){s&&s.p&&(!l||n&4)&&y(s,r,t,t[2],l?g(r,t[2],n,null):v(t[2]),null),(!l||n&3&&o!==(o=_(`icon-button-wrapper ${t[0]?"top-panel":""} ${t[1]?"display-top-corner":"hide-top-corner"}`)+" svelte-1jx2rq3"))&&p(e,"class",o)},i(t){l||(q(s,t),l=!0)},o(t){$(s,t),l=!1},d(t){t&&b(e),s&&s.d(t)}}}function w(a,e,o){let{$$slots:l={},$$scope:r}=e,{top_panel:s=!0}=e,{display_top_corner:t=!1}=e;return a.$$set=n=>{"top_panel"in n&&o(0,s=n.top_panel),"display_top_corner"in n&&o(1,t=n.display_top_corner),"$$scope"in n&&o(2,r=n.$$scope)},[s,t,r,l]}class W extends u{constructor(e){super(),c(this,e,w,j,f,{top_panel:0,display_top_corner:1})}get top_panel(){return this.$$.ctx[0]}set top_panel(e){this.$$set({top_panel:e}),i()}get display_top_corner(){return this.$$.ctx[1]}set display_top_corner(e){this.$$set({display_top_corner:e}),i()}}export{W as I};
//# sourceMappingURL=IconButtonWrapper-BqpIgNIH.js.map
