import{a as U,i as j,s as q,E as g,z as s,d as v,C as b,D as k,l as $,f as B,e as F,k as d,h as z,t as p,j as S,y,b as T,c as A,A as C,m as I,M as N,n as P}from"../lite.js";import{U as G,I as H}from"./Upload-CYshamIj.js";function J(c){let e,l,t,i,a;return{c(){e=g("svg"),l=g("path"),t=g("path"),i=g("line"),a=g("line"),s(l,"d","M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"),s(t,"d","M19 10v2a7 7 0 0 1-14 0v-2"),s(i,"x1","12"),s(i,"y1","19"),s(i,"x2","12"),s(i,"y2","23"),s(a,"x1","8"),s(a,"y1","23"),s(a,"x2","16"),s(a,"y2","23"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"width","100%"),s(e,"height","100%"),s(e,"viewBox","0 0 24 24"),s(e,"fill","none"),s(e,"stroke","currentColor"),s(e,"stroke-width","2"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"class","feather feather-mic")},m(n,o){v(n,e,o),b(e,l),b(e,t),b(e,i),b(e,a)},p:k,i:k,o:k,d(n){n&&$(e)}}}class K extends U{constructor(e){super(),j(this,e,null,J,q,{})}}function L(c){let e,l,t;return{c(){e=g("svg"),l=g("path"),t=g("path"),s(l,"fill","currentColor"),s(l,"d","M12 2c-4.963 0-9 4.038-9 9c0 3.328 1.82 6.232 4.513 7.79l-2.067 1.378A1 1 0 0 0 6 22h12a1 1 0 0 0 .555-1.832l-2.067-1.378C19.18 17.232 21 14.328 21 11c0-4.962-4.037-9-9-9zm0 16c-3.859 0-7-3.141-7-7c0-3.86 3.141-7 7-7s7 3.14 7 7c0 3.859-3.141 7-7 7z"),s(t,"fill","currentColor"),s(t,"d","M12 6c-2.757 0-5 2.243-5 5s2.243 5 5 5s5-2.243 5-5s-2.243-5-5-5zm0 8c-1.654 0-3-1.346-3-3s1.346-3 3-3s3 1.346 3 3s-1.346 3-3 3z"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"width","100%"),s(e,"height","100%"),s(e,"viewBox","0 0 24 24")},m(i,a){v(i,e,a),b(e,l),b(e,t)},p:k,i:k,o:k,d(i){i&&$(e)}}}class O extends U{constructor(e){super(),j(this,e,null,L,q,{})}}function Q(c){let e,l,t;return{c(){e=g("svg"),l=g("circle"),t=g("animateTransform"),s(t,"attributeName","transform"),s(t,"type","rotate"),s(t,"from","0 25 25"),s(t,"to","360 25 25"),s(t,"repeatCount","indefinite"),s(l,"cx","25"),s(l,"cy","25"),s(l,"r","20"),s(l,"fill","none"),s(l,"stroke-width","3.0"),s(l,"stroke-linecap","round"),s(l,"stroke-dasharray","94.2477796076938 94.2477796076938"),s(l,"stroke-dashoffset","0"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"width","100%"),s(e,"height","100%"),s(e,"viewBox","0 0 50 50"),s(e,"class","svelte-184ngxt")},m(i,a){v(i,e,a),b(e,l),b(l,t)},p:k,i:k,o:k,d(i){i&&$(e)}}}class ee extends U{constructor(e){super(),j(this,e,null,Q,q,{})}}function W(c){let e,l=c[1].includes("upload"),t,i=c[1].includes("microphone"),a,n=c[1].includes("webcam"),o,w=c[1].includes("clipboard"),M,f=l&&D(c),u=i&&E(c),m=n&&R(c),r=w&&V(c);return{c(){e=y("span"),f&&f.c(),t=T(),u&&u.c(),a=T(),m&&m.c(),o=T(),r&&r.c(),s(e,"class","source-selection svelte-snayfm"),s(e,"data-testid","source-select")},m(h,_){v(h,e,_),f&&f.m(e,null),b(e,t),u&&u.m(e,null),b(e,a),m&&m.m(e,null),b(e,o),r&&r.m(e,null),M=!0},p(h,_){_&2&&(l=h[1].includes("upload")),l?f?(f.p(h,_),_&2&&d(f,1)):(f=D(h),f.c(),d(f,1),f.m(e,t)):f&&(z(),p(f,1,1,()=>{f=null}),S()),_&2&&(i=h[1].includes("microphone")),i?u?(u.p(h,_),_&2&&d(u,1)):(u=E(h),u.c(),d(u,1),u.m(e,a)):u&&(z(),p(u,1,1,()=>{u=null}),S()),_&2&&(n=h[1].includes("webcam")),n?m?(m.p(h,_),_&2&&d(m,1)):(m=R(h),m.c(),d(m,1),m.m(e,o)):m&&(z(),p(m,1,1,()=>{m=null}),S()),_&2&&(w=h[1].includes("clipboard")),w?r?(r.p(h,_),_&2&&d(r,1)):(r=V(h),r.c(),d(r,1),r.m(e,null)):r&&(z(),p(r,1,1,()=>{r=null}),S())},i(h){M||(d(f),d(u),d(m),d(r),M=!0)},o(h){p(f),p(u),p(m),p(r),M=!1},d(h){h&&$(e),f&&f.d(),u&&u.d(),m&&m.d(),r&&r.d()}}}function D(c){let e,l,t,i,a;return l=new G({}),{c(){e=y("button"),A(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Upload file"),C(e,"selected",c[0]==="upload"||!c[0])},m(n,o){v(n,e,o),I(l,e,null),t=!0,i||(a=N(e,"click",c[6]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="upload"||!n[0])},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),P(l),i=!1,a()}}}function E(c){let e,l,t,i,a;return l=new K({}),{c(){e=y("button"),A(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Record audio"),C(e,"selected",c[0]==="microphone")},m(n,o){v(n,e,o),I(l,e,null),t=!0,i||(a=N(e,"click",c[7]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="microphone")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),P(l),i=!1,a()}}}function R(c){let e,l,t,i,a;return l=new O({}),{c(){e=y("button"),A(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Capture from camera"),C(e,"selected",c[0]==="webcam")},m(n,o){v(n,e,o),I(l,e,null),t=!0,i||(a=N(e,"click",c[8]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="webcam")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),P(l),i=!1,a()}}}function V(c){let e,l,t,i,a;return l=new H({}),{c(){e=y("button"),A(l.$$.fragment),s(e,"class","icon svelte-snayfm"),s(e,"aria-label","Paste from clipboard"),C(e,"selected",c[0]==="clipboard")},m(n,o){v(n,e,o),I(l,e,null),t=!0,i||(a=N(e,"click",c[9]),i=!0)},p(n,o){(!t||o&1)&&C(e,"selected",n[0]==="clipboard")},i(n){t||(d(l.$$.fragment,n),t=!0)},o(n){p(l.$$.fragment,n),t=!1},d(n){n&&$(e),P(l),i=!1,a()}}}function X(c){let e,l,t=c[2].length>1&&W(c);return{c(){t&&t.c(),e=F()},m(i,a){t&&t.m(i,a),v(i,e,a),l=!0},p(i,[a]){i[2].length>1?t?(t.p(i,a),a&4&&d(t,1)):(t=W(i),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(z(),p(t,1,1,()=>{t=null}),S())},i(i){l||(d(t),l=!0)},o(i){p(t),l=!1},d(i){i&&$(e),t&&t.d(i)}}}function Y(c,e,l){let t,{sources:i}=e,{active_source:a}=e,{handle_clear:n=()=>{}}=e,{handle_select:o=()=>{}}=e;async function w(r){n(),l(0,a=r),o(r)}const M=()=>w("upload"),f=()=>w("microphone"),u=()=>w("webcam"),m=()=>w("clipboard");return c.$$set=r=>{"sources"in r&&l(1,i=r.sources),"active_source"in r&&l(0,a=r.active_source),"handle_clear"in r&&l(4,n=r.handle_clear),"handle_select"in r&&l(5,o=r.handle_select)},c.$$.update=()=>{c.$$.dirty&2&&l(2,t=[...new Set(i)])},[a,i,t,w,n,o,M,f,u,m]}class te extends U{constructor(e){super(),j(this,e,Y,X,q,{sources:1,active_source:0,handle_clear:4,handle_select:5})}get sources(){return this.$$.ctx[1]}set sources(e){this.$$set({sources:e}),B()}get active_source(){return this.$$.ctx[0]}set active_source(e){this.$$set({active_source:e}),B()}get handle_clear(){return this.$$.ctx[4]}set handle_clear(e){this.$$set({handle_clear:e}),B()}get handle_select(){return this.$$.ctx[5]}set handle_select(e){this.$$set({handle_select:e}),B()}}export{K as M,te as S,O as W,ee as a};
//# sourceMappingURL=SelectSource-kJI_8u2f.js.map
