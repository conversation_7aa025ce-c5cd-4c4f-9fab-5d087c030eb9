import{a as y,i as d,s as g,f as n,y as m,z as v,A as i,d as _,D as f,l as b}from"../lite.js";/* empty css                                              */function x(a){let e;return{c(){e=m("div"),e.textContent=`${a[2]}`,v(e,"class","svelte-1ayixqk"),i(e,"table",a[0]==="table"),i(e,"gallery",a[0]==="gallery"),i(e,"selected",a[1])},m(t,s){_(t,e,s)},p(t,[s]){s&1&&i(e,"table",t[0]==="table"),s&1&&i(e,"gallery",t[0]==="gallery"),s&2&&i(e,"selected",t[1])},i:f,o:f,d(t){t&&b(e)}}}function A(a,e,t){let{value:s}=e,{type:r}=e,{selected:u=!1}=e,{choices:c}=e,h=(s?Array.isArray(s)?s:[s]:[]).map(l=>c.find(o=>o[1]===l)?.[0]).filter(l=>l!==void 0).join(", ");return a.$$set=l=>{"value"in l&&t(3,s=l.value),"type"in l&&t(0,r=l.type),"selected"in l&&t(1,u=l.selected),"choices"in l&&t(4,c=l.choices)},[r,u,h,s,c]}class z extends y{constructor(e){super(),d(this,e,A,x,g,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),n()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),n()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),n()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),n()}}export{z as default};
//# sourceMappingURL=Example-DicIXVkr.js.map
