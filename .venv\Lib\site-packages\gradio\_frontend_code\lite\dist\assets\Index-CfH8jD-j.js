import{a as se,i as ie,s as le,f,B as ne,c as p,m as j,k as g,t as b,n as B,o as oe,Y as ae,S as re,b as _e,e as ue,d as L,a0 as fe,a6 as he,h as ce,j as me,l as N,O,a7 as P,a8 as R}from"../lite.js";import{U as we}from"./UploadText-Chjc4Zy7.js";import de from"./Gallery-CrHDoI2O.js";import{B as ge}from"./FileUpload-BtOs7LC8.js";/* empty css                                              */import"./Upload-CYshamIj.js";import"./BlockLabel-DWW9BWN3.js";import"./Empty-Bzq0Ew6m.js";import"./ShareButton-Be-vgu5O.js";import"./Community-BFnPJcwx.js";import"./utils-BsGrhMNe.js";import"./Download-RUpc9r8A.js";import"./FullscreenButton-DsVuMC2h.js";import"./Minimize-DOBO88I3.js";import"./Play-BIkNyEKH.js";import"./IconButtonWrapper-BqpIgNIH.js";/* empty css                                             */import"./ModifyUpload-b77W1M2_.js";import"./Edit-fMGAgLsI.js";import"./Undo-50qkik3g.js";import"./DownloadLink-dHe4pFcz.js";import"./file-url-CoOyVRgq.js";import"./Image-BPQ6A_U-.js";/* empty css                                                   */import"./Video-DBVExGTx.js";import"./hls-CnVhpNcu.js";import"./File-C5WPisji.js";import"./Upload-Do_omv-N.js";function be(i){let e,l,s,n;function a(o){i[31](o)}function d(o){i[32](o)}let w={label:i[4],show_label:i[3],columns:i[13],rows:i[14],height:i[15],preview:i[16],object_fit:i[18],interactive:i[20],allow_preview:i[17],show_share_button:i[19],show_download_button:i[21],i18n:i[22].i18n,_fetch:i[30],show_fullscreen_button:i[23]};return i[1]!==void 0&&(w.selected_index=i[1]),i[0]!==void 0&&(w.value=i[0]),e=new de({props:w}),O.push(()=>P(e,"selected_index",a)),O.push(()=>P(e,"value",d)),e.$on("change",i[33]),e.$on("select",i[34]),e.$on("share",i[35]),e.$on("error",i[36]),e.$on("preview_open",i[37]),e.$on("preview_close",i[38]),{c(){p(e.$$.fragment)},m(o,u){j(e,o,u),n=!0},p(o,u){const _={};u[0]&16&&(_.label=o[4]),u[0]&8&&(_.show_label=o[3]),u[0]&8192&&(_.columns=o[13]),u[0]&16384&&(_.rows=o[14]),u[0]&32768&&(_.height=o[15]),u[0]&65536&&(_.preview=o[16]),u[0]&262144&&(_.object_fit=o[18]),u[0]&1048576&&(_.interactive=o[20]),u[0]&131072&&(_.allow_preview=o[17]),u[0]&524288&&(_.show_share_button=o[19]),u[0]&2097152&&(_.show_download_button=o[21]),u[0]&4194304&&(_.i18n=o[22].i18n),u[0]&4194304&&(_._fetch=o[30]),u[0]&8388608&&(_.show_fullscreen_button=o[23]),!l&&u[0]&2&&(l=!0,_.selected_index=o[1],R(()=>l=!1)),!s&&u[0]&1&&(s=!0,_.value=o[0],R(()=>s=!1)),e.$set(_)},i(o){n||(g(e.$$.fragment,o),n=!0)},o(o){b(e.$$.fragment,o),n=!1},d(o){B(e,o)}}}function ve(i){let e,l;return e=new ge({props:{value:null,root:i[5],label:i[4],max_file_size:i[22].max_file_size,file_count:"multiple",file_types:i[9],i18n:i[22].i18n,upload:i[26],stream_handler:i[27],$$slots:{default:[ke]},$$scope:{ctx:i}}}),e.$on("upload",i[28]),e.$on("error",i[29]),{c(){p(e.$$.fragment)},m(s,n){j(e,s,n),l=!0},p(s,n){const a={};n[0]&32&&(a.root=s[5]),n[0]&16&&(a.label=s[4]),n[0]&4194304&&(a.max_file_size=s[22].max_file_size),n[0]&512&&(a.file_types=s[9]),n[0]&4194304&&(a.i18n=s[22].i18n),n[0]&4194304&&(a.upload=s[26]),n[0]&4194304&&(a.stream_handler=s[27]),n[0]&4194304|n[1]&512&&(a.$$scope={dirty:n,ctx:s}),e.$set(a)},i(s){l||(g(e.$$.fragment,s),l=!0)},o(s){b(e.$$.fragment,s),l=!1},d(s){B(e,s)}}}function ke(i){let e,l;return e=new we({props:{i18n:i[22].i18n,type:"gallery"}}),{c(){p(e.$$.fragment)},m(s,n){j(e,s,n),l=!0},p(s,n){const a={};n[0]&4194304&&(a.i18n=s[22].i18n),e.$set(a)},i(s){l||(g(e.$$.fragment,s),l=!0)},o(s){b(e.$$.fragment,s),l=!1},d(s){B(e,s)}}}function pe(i){let e,l,s,n,a,d;const w=[{autoscroll:i[22].autoscroll},{i18n:i[22].i18n},i[2]];let o={};for(let r=0;r<w.length;r+=1)o=ae(o,w[r]);e=new re({props:o}),e.$on("clear_status",i[25]);const u=[ve,be],_=[];function m(r,h){return r[20]&&r[24]?0:1}return s=m(i),n=_[s]=u[s](i),{c(){p(e.$$.fragment),l=_e(),n.c(),a=ue()},m(r,h){j(e,r,h),L(r,l,h),_[s].m(r,h),L(r,a,h),d=!0},p(r,h){const z=h[0]&4194308?fe(w,[h[0]&4194304&&{autoscroll:r[22].autoscroll},h[0]&4194304&&{i18n:r[22].i18n},h[0]&4&&he(r[2])]):{};e.$set(z);let v=s;s=m(r),s===v?_[s].p(r,h):(ce(),b(_[v],1,1,()=>{_[v]=null}),me(),n=_[s],n?n.p(r,h):(n=_[s]=u[s](r),n.c()),g(n,1),n.m(a.parentNode,a))},i(r){d||(g(e.$$.fragment,r),g(n),d=!0)},o(r){b(e.$$.fragment,r),b(n),d=!1},d(r){r&&(N(l),N(a)),B(e,r),_[s].d(r)}}}function je(i){let e,l;return e=new ne({props:{visible:i[8],variant:"solid",padding:!1,elem_id:i[6],elem_classes:i[7],container:i[10],scale:i[11],min_width:i[12],allow_overflow:!1,height:typeof i[15]=="number"?i[15]:void 0,$$slots:{default:[pe]},$$scope:{ctx:i}}}),{c(){p(e.$$.fragment)},m(s,n){j(e,s,n),l=!0},p(s,n){const a={};n[0]&256&&(a.visible=s[8]),n[0]&64&&(a.elem_id=s[6]),n[0]&128&&(a.elem_classes=s[7]),n[0]&1024&&(a.container=s[10]),n[0]&2048&&(a.scale=s[11]),n[0]&4096&&(a.min_width=s[12]),n[0]&32768&&(a.height=typeof s[15]=="number"?s[15]:void 0),n[0]&33546815|n[1]&512&&(a.$$scope={dirty:n,ctx:s}),e.$set(a)},i(s){l||(g(e.$$.fragment,s),l=!0)},o(s){b(e.$$.fragment,s),l=!1},d(s){B(e,s)}}}async function Be(i){return(await Promise.all(i.map(async l=>{if(l.path?.toLowerCase().endsWith(".svg")&&l.url){const n=await(await fetch(l.url)).text();return{...l,url:`data:image/svg+xml,${encodeURIComponent(n)}`}}return l}))).map(l=>l.mime_type?.includes("video")?{video:l,caption:null}:{image:l,caption:null})}function ze(i,e,l){let s,{loading_status:n}=e,{show_label:a}=e,{label:d}=e,{root:w}=e,{elem_id:o=""}=e,{elem_classes:u=[]}=e,{visible:_=!0}=e,{value:m=null}=e,{file_types:r=["image","video"]}=e,{container:h=!0}=e,{scale:z=null}=e,{min_width:v=void 0}=e,{columns:C=[2]}=e,{rows:U=void 0}=e,{height:S="auto"}=e,{preview:A}=e,{allow_preview:G=!0}=e,{selected_index:k=null}=e,{object_fit:I="cover"}=e,{show_share_button:q=!1}=e,{interactive:D}=e,{show_download_button:E=!1}=e,{gradio:c}=e,{show_fullscreen_button:F=!0}=e;const T=oe(),W=()=>c.dispatch("clear_status",n),Y=(...t)=>c.client.upload(...t),H=(...t)=>c.client.stream(...t),J=async t=>{const te=Array.isArray(t.detail)?t.detail:[t.detail];l(0,m=await Be(te)),c.dispatch("upload",m),c.dispatch("change",m)},K=({detail:t})=>{l(2,n=n||{}),l(2,n.status="error",n),c.dispatch("error",t)},M=(...t)=>c.client.fetch(...t);function Q(t){k=t,l(1,k)}function V(t){m=t,l(0,m)}const X=()=>c.dispatch("change",m),Z=t=>c.dispatch("select",t.detail),y=t=>c.dispatch("share",t.detail),x=t=>c.dispatch("error",t.detail),$=()=>c.dispatch("preview_open"),ee=()=>c.dispatch("preview_close");return i.$$set=t=>{"loading_status"in t&&l(2,n=t.loading_status),"show_label"in t&&l(3,a=t.show_label),"label"in t&&l(4,d=t.label),"root"in t&&l(5,w=t.root),"elem_id"in t&&l(6,o=t.elem_id),"elem_classes"in t&&l(7,u=t.elem_classes),"visible"in t&&l(8,_=t.visible),"value"in t&&l(0,m=t.value),"file_types"in t&&l(9,r=t.file_types),"container"in t&&l(10,h=t.container),"scale"in t&&l(11,z=t.scale),"min_width"in t&&l(12,v=t.min_width),"columns"in t&&l(13,C=t.columns),"rows"in t&&l(14,U=t.rows),"height"in t&&l(15,S=t.height),"preview"in t&&l(16,A=t.preview),"allow_preview"in t&&l(17,G=t.allow_preview),"selected_index"in t&&l(1,k=t.selected_index),"object_fit"in t&&l(18,I=t.object_fit),"show_share_button"in t&&l(19,q=t.show_share_button),"interactive"in t&&l(20,D=t.interactive),"show_download_button"in t&&l(21,E=t.show_download_button),"gradio"in t&&l(22,c=t.gradio),"show_fullscreen_button"in t&&l(23,F=t.show_fullscreen_button)},i.$$.update=()=>{i.$$.dirty[0]&1&&l(24,s=m===null?!0:m.length===0),i.$$.dirty[0]&2&&T("prop_change",{selected_index:k})},[m,k,n,a,d,w,o,u,_,r,h,z,v,C,U,S,A,G,I,q,D,E,c,F,s,W,Y,H,J,K,M,Q,V,X,Z,y,x,$,ee]}class $e extends se{constructor(e){super(),ie(this,e,ze,je,le,{loading_status:2,show_label:3,label:4,root:5,elem_id:6,elem_classes:7,visible:8,value:0,file_types:9,container:10,scale:11,min_width:12,columns:13,rows:14,height:15,preview:16,allow_preview:17,selected_index:1,object_fit:18,show_share_button:19,interactive:20,show_download_button:21,gradio:22,show_fullscreen_button:23},null,[-1,-1])}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),f()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),f()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),f()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[8]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get file_types(){return this.$$.ctx[9]}set file_types(e){this.$$set({file_types:e}),f()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),f()}get columns(){return this.$$.ctx[13]}set columns(e){this.$$set({columns:e}),f()}get rows(){return this.$$.ctx[14]}set rows(e){this.$$set({rows:e}),f()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),f()}get preview(){return this.$$.ctx[16]}set preview(e){this.$$set({preview:e}),f()}get allow_preview(){return this.$$.ctx[17]}set allow_preview(e){this.$$set({allow_preview:e}),f()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),f()}get object_fit(){return this.$$.ctx[18]}set object_fit(e){this.$$set({object_fit:e}),f()}get show_share_button(){return this.$$.ctx[19]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get interactive(){return this.$$.ctx[20]}set interactive(e){this.$$set({interactive:e}),f()}get show_download_button(){return this.$$.ctx[21]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get gradio(){return this.$$.ctx[22]}set gradio(e){this.$$set({gradio:e}),f()}get show_fullscreen_button(){return this.$$.ctx[23]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}}export{de as BaseGallery,$e as default};
//# sourceMappingURL=Index-CfH8jD-j.js.map
