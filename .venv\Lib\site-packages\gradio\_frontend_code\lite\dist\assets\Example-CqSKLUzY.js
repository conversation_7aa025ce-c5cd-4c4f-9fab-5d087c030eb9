import{a as r,i as d,s as g,f as u,y as h,w as y,z as v,A as n,d as m,C as o,x as _,D as c,l as b}from"../lite.js";function x(a){let e,l;return{c(){e=h("div"),l=y(a[0]),v(e,"class","svelte-1ayixqk"),n(e,"table",a[1]==="table"),n(e,"gallery",a[1]==="gallery"),n(e,"selected",a[2])},m(t,s){m(t,e,s),o(e,l)},p(t,[s]){s&1&&_(l,t[0]),s&2&&n(e,"table",t[1]==="table"),s&2&&n(e,"gallery",t[1]==="gallery"),s&4&&n(e,"selected",t[2])},i:c,o:c,d(t){t&&b(e)}}}function q(a,e,l){let{value:t}=e,{type:s}=e,{selected:f=!1}=e;return a.$$set=i=>{"value"in i&&l(0,t=i.value),"type"in i&&l(1,s=i.type),"selected"in i&&l(2,f=i.selected)},[t,s,f]}class k extends r{constructor(e){super(),d(this,e,q,x,g,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{k as default};
//# sourceMappingURL=Example-CqSKLUzY.js.map
