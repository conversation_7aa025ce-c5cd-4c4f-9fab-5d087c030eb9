import{a_ as v,a$ as I,a as P,i as N,s as H,f as g,y as K,z as S,A as b,d as X,D as y,l as q,b0 as B,K as F,O as U}from"../lite.js";var V=function(e,r,t){for(var n=t,l=0,s=e.length;n<r.length;){var c=r[n];if(l<=0&&r.slice(n,n+s)===e)return n;c==="\\"?n++:c==="{"?l++:c==="}"&&l--,n++}return-1},W=function(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},j=/^\\begin{/,G=function(e,r){for(var t,n=[],l=new RegExp("("+r.map(o=>W(o.left)).join("|")+")");t=e.search(l),t!==-1;){t>0&&(n.push({type:"text",data:e.slice(0,t)}),e=e.slice(t));var s=r.findIndex(o=>e.startsWith(o.left));if(t=V(r[s].right,e,r[s].left.length),t===-1)break;var c=e.slice(0,t+r[s].right.length),h=j.test(c)?c:e.slice(r[s].left.length,t);n.push({type:"math",data:h,rawData:c,display:r[s].display}),e=e.slice(t+r[s].right.length)}return e!==""&&n.push({type:"text",data:e}),n},J=function(e,r){var t=G(e,r.delimiters);if(t.length===1&&t[0].type==="text")return null;for(var n=document.createDocumentFragment(),l=0;l<t.length;l++)if(t[l].type==="text")n.appendChild(document.createTextNode(t[l].data));else{var s=document.createElement("span"),c=t[l].data;r.displayMode=t[l].display;try{r.preProcess&&(c=r.preProcess(c)),v.render(c,s,r)}catch(h){if(!(h instanceof v.ParseError))throw h;r.errorCallback("KaTeX auto-render: Failed to parse `"+t[l].data+"` with ",h),n.appendChild(document.createTextNode(t[l].rawData));continue}n.appendChild(s)}return n},Q=function a(e,r){for(var t=0;t<e.childNodes.length;t++){var n=e.childNodes[t];if(n.nodeType===3){for(var l=n.textContent,s=n.nextSibling,c=0;s&&s.nodeType===Node.TEXT_NODE;)l+=s.textContent,s=s.nextSibling,c++;var h=J(l,r);if(h){for(var o=0;o<c;o++)n.nextSibling.remove();t+=h.childNodes.length-1,e.replaceChild(h,n)}else t+=c}else n.nodeType===1&&function(){var _=" "+n.className+" ",u=r.ignoredTags.indexOf(n.nodeName.toLowerCase())===-1&&r.ignoredClasses.every(d=>_.indexOf(" "+d+" ")===-1);u&&a(n,r)}()}},Y=function(e,r){if(!e)throw new Error("No element provided to render");var t={};for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n]);t.delimiters=t.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],t.ignoredTags=t.ignoredTags||["script","noscript","style","textarea","pre","code","option"],t.ignoredClasses=t.ignoredClasses||[],t.errorCallback=t.errorCallback||console.error,t.macros=t.macros||{},Q(e,t)};const Z=(a,e)=>{try{return!!a&&new URL(a).origin!==new URL(e).origin}catch{return!1}};function E(a,e){const r=new I,t=new DOMParser().parseFromString(a,"text/html");return M(t.body,"A",n=>{n instanceof HTMLElement&&"target"in n&&Z(n.getAttribute("href"),e)&&(n.setAttribute("target","_blank"),n.setAttribute("rel","noopener noreferrer"))}),r.sanitize(t).body.innerHTML}function M(a,e,r){a&&(a.nodeName===e||typeof e=="function")&&r(a);const t=a?.childNodes||[];for(let n=0;n<t.length;n++)M(t[n],e,r)}function $(a){let e;return{c(){e=K("span"),S(e,"class","md svelte-7ddecg"),b(e,"chatbot",a[0]),b(e,"prose",a[1])},m(r,t){X(r,e,t),e.innerHTML=a[3],a[11](e)},p(r,[t]){t&8&&(e.innerHTML=r[3]),t&1&&b(e,"chatbot",r[0]),t&2&&b(e,"prose",r[1])},i:y,o:y,d(r){r&&q(e),a[11](null)}}}function T(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ee(a,e){const r=e.map(n=>({open:new RegExp(`<(${n})(\\s+[^>]*)?>`,"gi"),close:new RegExp(`</(${n})>`,"gi")}));let t=a;return r.forEach(n=>{t=t.replace(n.open,(l,s,c)=>`&lt;${s}${c||""}&gt;`),t=t.replace(n.close,(l,s)=>`&lt;/${s}&gt;`)}),t}function te(a,e,r){let{chatbot:t=!0}=e,{message:n}=e,{sanitize_html:l=!0}=e,{latex_delimiters:s=[]}=e,{render_markdown:c=!0}=e,{line_breaks:h=!0}=e,{header_links:o=!1}=e,{root:_}=e,{allow_tags:u=null}=e,d,w;const p=B({header_links:o,line_breaks:h,latex_delimiters:s||[]});function L(i){let f=i;if(c){const m=[];s.forEach((x,k)=>{const R=T(x.left),A=T(x.right),C=new RegExp(`${R}([\\s\\S]+?)${A}`,"g");f=f.replace(C,(z,re)=>(m.push(z),`%%%LATEX_BLOCK_${m.length-1}%%%`))}),f=p.parse(f),f=f.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(x,k)=>m[parseInt(k,10)])}return u&&(f=ee(f,u)),l&&E&&(f=E(f,_)),f}async function D(i){s.length>0&&i&&s.every(m=>i.includes(m.left)&&i.includes(m.right))&&Y(d,{delimiters:s,throwOnError:!1})}F(async()=>{d&&document.body.contains(d)?await D(n):console.error("Element is not in the DOM")});function O(i){U[i?"unshift":"push"](()=>{d=i,r(2,d)})}return a.$$set=i=>{"chatbot"in i&&r(0,t=i.chatbot),"message"in i&&r(4,n=i.message),"sanitize_html"in i&&r(5,l=i.sanitize_html),"latex_delimiters"in i&&r(6,s=i.latex_delimiters),"render_markdown"in i&&r(1,c=i.render_markdown),"line_breaks"in i&&r(7,h=i.line_breaks),"header_links"in i&&r(8,o=i.header_links),"root"in i&&r(9,_=i.root),"allow_tags"in i&&r(10,u=i.allow_tags)},a.$$.update=()=>{a.$$.dirty&16&&(n&&n.trim()?r(3,w=L(n)):r(3,w=""))},[t,c,d,w,n,l,s,h,o,_,u,O]}class ae extends P{constructor(e){super(),N(this,e,te,$,H,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,root:9,allow_tags:10})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),g()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),g()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),g()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),g()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),g()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),g()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),g()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),g()}get allow_tags(){return this.$$.ctx[10]}set allow_tags(e){this.$$set({allow_tags:e}),g()}}export{ae as M};
//# sourceMappingURL=MarkdownCode-DVjr71R6.js.map
