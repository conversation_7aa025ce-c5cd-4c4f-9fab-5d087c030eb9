import{a as be,i as ve,s as we,E as lt,z as $,$ as re,d as v,C as f,D as ge,l as b,f as P,y as z,b as U,w as m,c as H,m as V,M as Ee,x as D,k as I,t as O,n as W,o as De,ar as Fe,aq as oe,e as me,A as it,h as pe,j as de,as as Ne,L as Un,B as Xe,O as ze,a5 as Re,N as Ze,p as Ue,ao as Ie,ap as at,bp as yt,bq as hl,br as gl,V as bl,bs as vl,bt as kl,at as wl,au as jt,a7 as Je,a0 as ht,a6 as gt,a8 as bt,X as qt,Y as Ye,Z as yl,q as Cl,u as $l,r as El,v as zl,b3 as Nl,bu as Ll,b8 as jl,b9 as ql,bv as Al,aB as Tl,bw as At,bx as Tt,ba as Sl}from"../lite.js";import{B as ct}from"./Button-BiPFvbFD.js";import{c as Pl}from"./Dropdown-Inzjy6u3.js";/* empty css                                              */import{T as Il}from"./Toast-DP7Rbu7A.js";import{G as Ml}from"./utils-BsGrhMNe.js";import"./Image-BPQ6A_U-.js";import"./file-url-CoOyVRgq.js";/* empty css                                                   */import"./index-B9I6rkKj.js";import"./BlockTitle-DvFB_De3.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";import"./DropdownArrow-DIboSv6l.js";function Ol(l){let e,t,n,s;return{c(){e=lt("svg"),t=lt("g"),n=lt("path"),s=lt("path"),$(n,"d","M3.789,0.09C3.903,-0.024 4.088,-0.024 4.202,0.09L4.817,0.705C4.931,0.819 4.931,1.004 4.817,1.118L1.118,4.817C1.004,4.931 0.819,4.931 0.705,4.817L0.09,4.202C-0.024,4.088 -0.024,3.903 0.09,3.789L3.789,0.09Z"),$(s,"d","M4.825,3.797C4.934,3.907 4.934,4.084 4.825,4.193L4.193,4.825C4.084,4.934 3.907,4.934 3.797,4.825L0.082,1.11C-0.027,1.001 -0.027,0.823 0.082,0.714L0.714,0.082C0.823,-0.027 1.001,-0.027 1.11,0.082L4.825,3.797Z"),$(e,"width","100%"),$(e,"height","100%"),$(e,"viewBox","0 0 5 5"),$(e,"version","1.1"),$(e,"xmlns","http://www.w3.org/2000/svg"),$(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),$(e,"xml:space","preserve"),re(e,"fill","currentColor"),re(e,"fill-rule","evenodd"),re(e,"clip-rule","evenodd"),re(e,"stroke-linejoin","round"),re(e,"stroke-miterlimit","2")},m(i,r){v(i,e,r),f(e,t),f(t,n),f(t,s)},p:ge,i:ge,o:ge,d(i){i&&b(e)}}}class Ct extends be{constructor(e){super(),ve(this,e,null,Ol,we,{})}}function Fl(l){let e,t,n,s,i,r,a,c,o,u,_,g,d,y,h;return g=new Ct({}),{c(){e=z("div"),t=z("h1"),t.textContent="API Docs",n=U(),s=z("p"),i=m(`No API Routes found for
		`),r=z("code"),a=m(l[0]),c=U(),o=z("p"),o.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,u=U(),_=z("button"),H(g.$$.fragment),$(r,"class","svelte-e1ha0f"),$(s,"class","attention svelte-e1ha0f"),$(e,"class","wrap prose svelte-e1ha0f"),$(_,"class","svelte-e1ha0f")},m(E,p){v(E,e,p),f(e,t),f(e,n),f(e,s),f(s,i),f(s,r),f(r,a),f(e,c),f(e,o),v(E,u,p),v(E,_,p),V(g,_,null),d=!0,y||(h=Ee(_,"click",l[2]),y=!0)},p(E,[p]){(!d||p&1)&&D(a,E[0])},i(E){d||(I(g.$$.fragment,E),d=!0)},o(E){O(g.$$.fragment,E),d=!1},d(E){E&&(b(e),b(u),b(_)),W(g),y=!1,h()}}}function Ul(l,e,t){const n=De();let{root:s}=e;const i=()=>n("close");return l.$$set=r=>{"root"in r&&t(0,s=r.root)},[s,n,i]}class Dl extends be{constructor(e){super(),ve(this,e,Ul,Fl,we,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),P()}}const Dn="data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e";function Rl(l){let e,t,n;return{c(){e=z("div"),t=U(),n=z("p"),n.textContent="API Recorder",$(e,"class","loading-dot self-baseline svelte-1i1gjw2"),$(n,"class","self-baseline btn-text svelte-1i1gjw2")},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p:ge,d(s){s&&(b(e),b(t),b(n))}}}function St(l){let e;return{c(){e=m("s")},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function Bl(l){let e,t,n,s,i,r,a,c,o,u,_,g,d,y,h,E,p,N,j,w,L,C,k;_=new ct({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[Rl]},$$scope:{ctx:l}}}),_.$on("click",l[3]);let T=l[1]>1&&St();return w=new Ct({}),{c(){e=z("h2"),t=z("img"),s=U(),i=z("div"),r=m(`API documentation
		`),a=z("div"),c=m(l[0]),o=U(),u=z("span"),H(_.$$.fragment),g=U(),d=z("p"),y=z("span"),h=m(l[1]),E=m(" API endpoint"),T&&T.c(),p=z("br"),N=U(),j=z("button"),H(w.$$.fragment),Fe(t.src,n=Dn)||$(t,"src",n),$(t,"alt",""),$(t,"class","svelte-1i1gjw2"),$(a,"class","url svelte-1i1gjw2"),$(i,"class","title svelte-1i1gjw2"),$(y,"class","url svelte-1i1gjw2"),$(u,"class","counts svelte-1i1gjw2"),$(e,"class","svelte-1i1gjw2"),$(j,"class","svelte-1i1gjw2")},m(S,q){v(S,e,q),f(e,t),f(e,s),f(e,i),f(i,r),f(i,a),f(a,c),f(e,o),f(e,u),V(_,u,null),f(u,g),f(u,d),f(d,y),f(y,h),f(d,E),T&&T.m(d,null),f(d,p),v(S,N,q),v(S,j,q),V(w,j,null),L=!0,C||(k=Ee(j,"click",l[4]),C=!0)},p(S,[q]){(!L||q&1)&&D(c,S[0]);const M={};q&32&&(M.$$scope={dirty:q,ctx:S}),_.$set(M),(!L||q&2)&&D(h,S[1]),S[1]>1?T||(T=St(),T.c(),T.m(d,p)):T&&(T.d(1),T=null)},i(S){L||(I(_.$$.fragment,S),I(w.$$.fragment,S),L=!0)},o(S){O(_.$$.fragment,S),O(w.$$.fragment,S),L=!1},d(S){S&&(b(e),b(N),b(j)),W(_),T&&T.d(),W(w),C=!1,k()}}}function Gl(l,e,t){let{root:n}=e,{api_count:s}=e;const i=De(),r=()=>i("close",{api_recorder_visible:!0}),a=()=>i("close");return l.$$set=c=>{"root"in c&&t(0,n=c.root),"api_count"in c&&t(1,s=c.api_count)},[n,s,i,r,a]}class Hl extends be{constructor(e){super(),ve(this,e,Gl,Bl,we,{root:0,api_count:1})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),P()}get api_count(){return this.$$.ctx[1]}set api_count(e){this.$$set({api_count:e}),P()}}function $e(l,e,t=null){return e===void 0?t==="py"?"None":null:l===null&&t==="py"?"None":e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"||t==="bash"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):e.startsWith("Literal['")?'"'+l+'"':t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:(t==="bash"&&(l=vt(l)),t==="py"&&(l=kt(l)),Vl(l))}function Rn(l){if(typeof l=="object"&&l!==null&&l.hasOwnProperty("url")&&l.hasOwnProperty("meta")&&typeof l.meta=="object"&&l.meta!==null&&l.meta._type==="gradio.FileData")return!0;if(typeof l=="object"&&l!==null){for(let e in l)if(typeof l[e]=="object"&&Rn(l[e]))return!0}return!1}function vt(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?{path:l.url}:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=vt(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=vt(l[e])}),l)}function kt(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?`handle_file('${l.url}')`:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=kt(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=kt(l[e])}),l)}function Vl(l){let e=JSON.stringify(l,(s,i)=>i===null?"UNQUOTEDNone":typeof i=="string"&&i.startsWith("handle_file(")&&i.endsWith(")")?`UNQUOTED${i}`:i);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(s,i)=>`handle_file(${i})`);const n=/"UNQUOTEDNone"/g;return e.replace(n,"None")}function Pt(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].python_type,n[6]=e[t].component,n[7]=e[t].parameter_name,n[8]=e[t].parameter_has_default,n[9]=e[t].parameter_default,n[11]=t,n}function It(l){let e;return{c(){e=m("s")},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function Wl(l){let e=(l[2][l[11]].type||"any")+"",t;return{c(){t=m(e)},m(n,s){v(n,t,s)},p(n,s){s&4&&e!==(e=(n[2][n[11]].type||"any")+"")&&D(t,e)},d(n){n&&b(t)}}}function Zl(l){let e=l[5].type+"",t,n,s=l[8]&&l[9]===null&&Mt();return{c(){t=m(e),s&&s.c(),n=me()},m(i,r){v(i,t,r),s&&s.m(i,r),v(i,n,r)},p(i,r){r&2&&e!==(e=i[5].type+"")&&D(t,e),i[8]&&i[9]===null?s||(s=Mt(),s.c(),s.m(n.parentNode,n)):s&&(s.d(1),s=null)},d(i){i&&(b(t),b(n)),s&&s.d(i)}}}function Mt(l){let e;return{c(){e=m(` |
							None`)},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function Ql(l){let e,t,n=$e(l[9],l[5].type,"py")+"",s;return{c(){e=z("span"),e.textContent="Default: ",t=z("span"),s=m(n),$(t,"class","code svelte-1yt946s"),re(t,"font-size","var(--text-sm)")},m(i,r){v(i,e,r),v(i,t,r),f(t,s)},p(i,r){r&2&&n!==(n=$e(i[9],i[5].type,"py")+"")&&D(s,n)},d(i){i&&(b(e),b(t))}}}function Jl(l){let e;return{c(){e=z("span"),e.textContent="Required",re(e,"font-weight","bold")},m(t,n){v(t,e,n)},p:ge,d(t){t&&b(e)}}}function Ot(l){let e,t,n,s,i,r=(l[3]!=="bash"&&l[7]?l[7]:"["+l[11]+"]")+"",a,c,o,u,_,g,d,y=l[4]+"",h,E,p=l[6]+"",N,j,w=l[5].description+"",L,C;function k(F,G){return F[3]==="python"?Zl:Wl}let T=k(l),S=T(l);function q(F,G){return!F[8]||F[3]=="bash"?Jl:Ql}let M=q(l),R=M(l);return{c(){e=z("hr"),t=U(),n=z("div"),s=z("p"),i=z("span"),a=m(r),c=U(),o=z("span"),S.c(),u=U(),R.c(),_=U(),g=z("p"),d=m('The input value that is provided in the "'),h=m(y),E=m('" '),N=m(p),j=m(`
				component. `),L=m(w),C=U(),$(e,"class","hr svelte-1yt946s"),$(i,"class","code svelte-1yt946s"),re(i,"margin-right","10px"),$(o,"class","code highlight svelte-1yt946s"),re(o,"margin-right","10px"),re(s,"white-space","nowrap"),re(s,"overflow-x","auto"),$(g,"class","desc svelte-1yt946s"),re(n,"margin","10px")},m(F,G){v(F,e,G),v(F,t,G),v(F,n,G),f(n,s),f(s,i),f(i,a),f(s,c),f(s,o),S.m(o,null),f(s,u),R.m(s,null),f(n,_),f(n,g),f(g,d),f(g,h),f(g,E),f(g,N),f(g,j),f(g,L),f(n,C)},p(F,G){G&10&&r!==(r=(F[3]!=="bash"&&F[7]?F[7]:"["+F[11]+"]")+"")&&D(a,r),T===(T=k(F))&&S?S.p(F,G):(S.d(1),S=T(F),S&&(S.c(),S.m(o,null))),M===(M=q(F))&&R?R.p(F,G):(R.d(1),R=M(F),R&&(R.c(),R.m(s,null))),G&2&&y!==(y=F[4]+"")&&D(h,y),G&2&&p!==(p=F[6]+"")&&D(N,p),G&2&&w!==(w=F[5].description+"")&&D(L,w)},d(F){F&&(b(e),b(t),b(n)),S.d(),R.d()}}}function Ft(l){let e,t,n;return t=new Un({props:{margin:!1}}),{c(){e=z("div"),H(t.$$.fragment),$(e,"class","load-wrap")},m(s,i){v(s,e,i),V(t,e,null),n=!0},i(s){n||(I(t.$$.fragment,s),n=!0)},o(s){O(t.$$.fragment,s),n=!1},d(s){s&&b(e),W(t)}}}function Yl(l){let e,t,n,s=l[1].length+"",i,r,a,c,o,u,_,g,d=l[1].length!=1&&It(),y=oe(l[1]),h=[];for(let p=0;p<y.length;p+=1)h[p]=Ot(Pt(l,y,p));let E=l[0]&&Ft();return{c(){e=z("h4"),t=z("div"),t.innerHTML='<div class="toggle-dot svelte-1yt946s"></div>',n=m(`
	Accepts `),i=m(s),r=m(" parameter"),d&&d.c(),a=m(":"),c=U(),o=z("div");for(let p=0;p<h.length;p+=1)h[p].c();u=U(),E&&E.c(),_=me(),$(t,"class","toggle-icon svelte-1yt946s"),$(e,"class","svelte-1yt946s"),it(o,"hide",l[0])},m(p,N){v(p,e,N),f(e,t),f(e,n),f(e,i),f(e,r),d&&d.m(e,null),f(e,a),v(p,c,N),v(p,o,N);for(let j=0;j<h.length;j+=1)h[j]&&h[j].m(o,null);v(p,u,N),E&&E.m(p,N),v(p,_,N),g=!0},p(p,[N]){if((!g||N&2)&&s!==(s=p[1].length+"")&&D(i,s),p[1].length!=1?d||(d=It(),d.c(),d.m(e,a)):d&&(d.d(1),d=null),N&14){y=oe(p[1]);let j;for(j=0;j<y.length;j+=1){const w=Pt(p,y,j);h[j]?h[j].p(w,N):(h[j]=Ot(w),h[j].c(),h[j].m(o,null))}for(;j<h.length;j+=1)h[j].d(1);h.length=y.length}(!g||N&1)&&it(o,"hide",p[0]),p[0]?E?N&1&&I(E,1):(E=Ft(),E.c(),I(E,1),E.m(_.parentNode,_)):E&&(pe(),O(E,1,1,()=>{E=null}),de())},i(p){g||(I(E),g=!0)},o(p){O(E),g=!1},d(p){p&&(b(e),b(c),b(o),b(u),b(_)),d&&d.d(),Ne(h,p),E&&E.d(p)}}}function Xl(l,e,t){let{is_running:n}=e,{endpoint_returns:s}=e,{js_returns:i}=e,{current_language:r}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,s=a.endpoint_returns),"js_returns"in a&&t(2,i=a.js_returns),"current_language"in a&&t(3,r=a.current_language)},[n,s,i,r]}class Kl extends be{constructor(e){super(),ve(this,e,Xl,Yl,we,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),P()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),P()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),P()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),P()}}function xl(l){let e;return{c(){e=m(l[0])},m(t,n){v(t,e,n)},p(t,n){n&1&&D(e,t[0])},d(t){t&&b(e)}}}function es(l){let e,t;return e=new ct({props:{size:"sm",$$slots:{default:[xl]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,[s]){const i={};s&9&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function ts(l,e,t){let{code:n}=e,s="copy";function i(){navigator.clipboard.writeText(n),t(0,s="copied!"),setTimeout(()=>{t(0,s="copy")},1500)}return l.$$set=r=>{"code"in r&&t(2,n=r.code)},[s,i,n]}class Ae extends be{constructor(e){super(),ve(this,e,ts,es,we,{code:2})}get code(){return this.$$.ctx[2]}set code(e){this.$$set({code:e}),P()}}function ns(l){let e,t,n,s,i,r;return t=new Ae({props:{code:Rt}}),{c(){e=z("div"),H(t.$$.fragment),n=U(),s=z("div"),i=z("pre"),i.textContent=`$ ${Rt}`,$(e,"class","copy svelte-hq8ezf"),$(i,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),V(t,e,null),v(a,n,c),v(a,s,c),f(s,i),r=!0},p:ge,i(a){r||(I(t.$$.fragment,a),r=!0)},o(a){O(t.$$.fragment,a),r=!1},d(a){a&&(b(e),b(n),b(s)),W(t)}}}function ls(l){let e,t,n,s,i,r;return t=new Ae({props:{code:Dt}}),{c(){e=z("div"),H(t.$$.fragment),n=U(),s=z("div"),i=z("pre"),i.textContent=`$ ${Dt}`,$(e,"class","copy svelte-hq8ezf"),$(i,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),V(t,e,null),v(a,n,c),v(a,s,c),f(s,i),r=!0},p:ge,i(a){r||(I(t.$$.fragment,a),r=!0)},o(a){O(t.$$.fragment,a),r=!1},d(a){a&&(b(e),b(n),b(s)),W(t)}}}function ss(l){let e,t,n,s,i,r;return t=new Ae({props:{code:Ut}}),{c(){e=z("div"),H(t.$$.fragment),n=U(),s=z("div"),i=z("pre"),i.textContent=`$ ${Ut}`,$(e,"class","copy svelte-hq8ezf"),$(i,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),V(t,e,null),v(a,n,c),v(a,s,c),f(s,i),r=!0},p:ge,i(a){r||(I(t.$$.fragment,a),r=!0)},o(a){O(t.$$.fragment,a),r=!1},d(a){a&&(b(e),b(n),b(s)),W(t)}}}function is(l){let e,t,n,s;const i=[ss,ls,ns],r=[];function a(c,o){return c[0]==="python"?0:c[0]==="javascript"?1:c[0]==="bash"?2:-1}return~(t=a(l))&&(n=r[t]=i[t](l)),{c(){e=z("code"),n&&n.c(),$(e,"class","svelte-hq8ezf")},m(c,o){v(c,e,o),~t&&r[t].m(e,null),s=!0},p(c,o){let u=t;t=a(c),t===u?~t&&r[t].p(c,o):(n&&(pe(),O(r[u],1,1,()=>{r[u]=null}),de()),~t?(n=r[t],n?n.p(c,o):(n=r[t]=i[t](c),n.c()),I(n,1),n.m(e,null)):n=null)},i(c){s||(I(n),s=!0)},o(c){O(n),s=!1},d(c){c&&b(e),~t&&r[t].d()}}}function rs(l){let e,t;return e=new Xe({props:{$$slots:{default:[is]},$$scope:{ctx:l}}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,[s]){const i={};s&3&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}let Ut="pip install gradio_client",Dt="npm i -D @gradio/client",Rt="curl --version";function os(l,e,t){let{current_language:n}=e;return l.$$set=s=>{"current_language"in s&&t(0,n=s.current_language)},[n]}class as extends be{constructor(e){super(),ve(this,e,os,rs,we,{current_language:0})}get current_language(){return this.$$.ctx[0]}set current_language(e){this.$$set({current_language:e}),P()}}function cs(l){let e,t,n,s;return{c(){e=z("h3"),t=m(`fn_index:
		`),n=z("span"),s=m(l[1]),$(n,"class","post svelte-41kcm6"),$(e,"class","svelte-41kcm6")},m(i,r){v(i,e,r),f(e,t),f(e,n),f(n,s)},p(i,r){r&2&&D(s,i[1])},d(i){i&&b(e)}}}function fs(l){let e,t,n,s="/"+l[0],i;return{c(){e=z("h3"),t=m(`api_name:
		`),n=z("span"),i=m(s),$(n,"class","post svelte-41kcm6"),$(e,"class","svelte-41kcm6")},m(r,a){v(r,e,a),f(e,t),f(e,n),f(n,i)},p(r,a){a&1&&s!==(s="/"+r[0])&&D(i,s)},d(r){r&&b(e)}}}function us(l){let e;function t(i,r){return i[2]?fs:cs}let n=t(l),s=n(l);return{c(){s.c(),e=me()},m(i,r){s.m(i,r),v(i,e,r)},p(i,[r]){n===(n=t(i))&&s?s.p(i,r):(s.d(1),s=n(i),s&&(s.c(),s.m(e.parentNode,e)))},i:ge,o:ge,d(i){i&&b(e),s.d(i)}}}function _s(l,e,t){let{api_name:n=null}=e,{fn_index:s=null}=e,{named:i}=e;return l.$$set=r=>{"api_name"in r&&t(0,n=r.api_name),"fn_index"in r&&t(1,s=r.fn_index),"named"in r&&t(2,i=r.named)},[n,s,i]}class Bn extends be{constructor(e){super(),ve(this,e,_s,us,we,{api_name:0,fn_index:1,named:2})}get api_name(){return this.$$.ctx[0]}set api_name(e){this.$$set({api_name:e}),P()}get fn_index(){return this.$$.ctx[1]}set fn_index(e){this.$$set({fn_index:e}),P()}get named(){return this.$$.ctx[2]}set named(e){this.$$set({named:e}),P()}}function Bt(l,e,t){const n=l.slice();return n[28]=e[t].label,n[23]=e[t].parameter_name,n[29]=e[t].type,n[21]=e[t].python_type,n[30]=e[t].component,n[22]=e[t].example_input,n[31]=e[t].serializer,n[27]=t,n}function Gt(l,e,t){const n=l.slice();return n[28]=e[t].label,n[23]=e[t].parameter_name,n[29]=e[t].type,n[21]=e[t].python_type,n[30]=e[t].component,n[22]=e[t].example_input,n[31]=e[t].serializer,n[27]=t,n}function Ht(l,e,t){const n=l.slice();return n[30]=e[t].component,n[22]=e[t].example_input,n[27]=t,n}function Vt(l,e,t){const n=l.slice();return n[21]=e[t].python_type,n[22]=e[t].example_input,n[23]=e[t].parameter_name,n[24]=e[t].parameter_has_default,n[25]=e[t].parameter_default,n[27]=t,n}function ps(l){let e,t;return e=new Bn({props:{named:l[5],fn_index:l[1]}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&32&&(i.named=n[5]),s[0]&2&&(i.fn_index=n[1]),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function ds(l){let e,t;return e=new Bn({props:{named:l[5],api_name:l[0].api_name}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&32&&(i.named=n[5]),s[0]&1&&(i.api_name=n[0].api_name),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function ms(l){let e,t;return e=new Xe({props:{$$slots:{default:[bs]},$$scope:{ctx:l}}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&7185|s[1]&16&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function hs(l){let e,t;return e=new Xe({props:{$$slots:{default:[Cs]},$$scope:{ctx:l}}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&639|s[1]&16&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function gs(l){let e,t;return e=new Xe({props:{$$slots:{default:[Es]},$$scope:{ctx:l}}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&349|s[1]&16&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function Wt(l){let e;return{c(){e=m(",")},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function Zt(l){let e,t=$e(l[22],l[21].type,"bash")+"",n,s,i=l[27]<l[4].length-1&&Wt();return{c(){e=m(`
							`),n=m(t),i&&i.c(),s=me()},m(r,a){v(r,e,a),v(r,n,a),i&&i.m(r,a),v(r,s,a)},p(r,a){a[0]&16&&t!==(t=$e(r[22],r[21].type,"bash")+"")&&D(n,t),r[27]<r[4].length-1?i||(i=Wt(),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(r){r&&(b(e),b(n),b(s)),i&&i.d(r)}}}function bs(l){let e,t,n,s,i,r,a,c,o,u,_=l[0].api_name+"",g,d,y="{",h,E,p,N="}",j,w,L="{",C,k,T="}",S,q,M,R,F,G=l[0].api_name+"",K,he,x;n=new Ae({props:{code:l[10]?.innerText}});let Y=oe(l[4]),ne=[];for(let Q=0;Q<Y.length;Q+=1)ne[Q]=Zt(Bt(l,Y,Q));return{c(){e=z("code"),t=z("div"),H(n.$$.fragment),s=U(),i=z("div"),r=z("pre"),a=m("curl -X POST "),c=m(l[11]),o=m(l[12]),u=m("/call/"),g=m(_),d=m(` -s -H "Content-Type: application/json" -d '`),h=m(y),E=m(`
  "data": [`);for(let Q=0;Q<ne.length;Q+=1)ne[Q].c();p=m(`
]`),j=m(N),w=m(`' \\
  | awk -F'"' '`),C=m(L),k=m(" print $4"),S=m(T),q=m(`'  \\
  | read EVENT_ID; curl -N `),M=m(l[11]),R=m(l[12]),F=m("/call/"),K=m(G),he=m("/$EVENT_ID"),$(t,"class","copy svelte-114qcyq"),$(r,"class","svelte-114qcyq"),$(e,"class","svelte-114qcyq")},m(Q,se){v(Q,e,se),f(e,t),V(n,t,null),f(e,s),f(e,i),f(i,r),f(r,a),f(r,c),f(r,o),f(r,u),f(r,g),f(r,d),f(r,h),f(r,E);for(let ke=0;ke<ne.length;ke+=1)ne[ke]&&ne[ke].m(r,null);f(r,p),f(r,j),f(r,w),f(r,C),f(r,k),f(r,S),f(r,q),f(r,M),f(r,R),f(r,F),f(r,K),f(r,he),l[19](i),x=!0},p(Q,se){const ke={};if(se[0]&1024&&(ke.code=Q[10]?.innerText),n.$set(ke),(!x||se[0]&2048)&&D(c,Q[11]),(!x||se[0]&4096)&&D(o,Q[12]),(!x||se[0]&1)&&_!==(_=Q[0].api_name+"")&&D(g,_),se[0]&16){Y=oe(Q[4]);let ye;for(ye=0;ye<Y.length;ye+=1){const Ke=Bt(Q,Y,ye);ne[ye]?ne[ye].p(Ke,se):(ne[ye]=Zt(Ke),ne[ye].c(),ne[ye].m(r,p))}for(;ye<ne.length;ye+=1)ne[ye].d(1);ne.length=Y.length}(!x||se[0]&2048)&&D(M,Q[11]),(!x||se[0]&4096)&&D(R,Q[12]),(!x||se[0]&1)&&G!==(G=Q[0].api_name+"")&&D(K,G)},i(Q){x||(I(n.$$.fragment,Q),x=!0)},o(Q){O(n.$$.fragment,Q),x=!1},d(Q){Q&&b(e),W(n),Ne(ne,Q),l[19](null)}}}function Qt(l){let e,t,n,s=l[22].url+"",i,r,a=l[30]+"",c,o,u,_;return{c(){e=m(`
const response_`),t=m(l[27]),n=m(' = await fetch("'),i=m(s),r=m(`");
const example`),c=m(a),o=m(" = await response_"),u=m(l[27]),_=m(`.blob();
						`)},m(g,d){v(g,e,d),v(g,t,d),v(g,n,d),v(g,i,d),v(g,r,d),v(g,c,d),v(g,o,d),v(g,u,d),v(g,_,d)},p:ge,d(g){g&&(b(e),b(t),b(n),b(i),b(r),b(c),b(o),b(u),b(_))}}}function Jt(l){let e,t,n;return{c(){e=m(', {auth: ["'),t=m(l[6]),n=m('", **password**]}')},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p(s,i){i[0]&64&&D(t,s[6])},d(s){s&&(b(e),b(t),b(n))}}}function vs(l){let e;return{c(){e=m(l[1])},m(t,n){v(t,e,n)},p(t,n){n[0]&2&&D(e,t[1])},d(t){t&&b(e)}}}function ks(l){let e,t,n=l[0].api_name+"",s,i;return{c(){e=z("span"),t=m('"/'),s=m(n),i=m('"'),$(e,"class","api-name svelte-114qcyq")},m(r,a){v(r,e,a),f(e,t),f(e,s),f(e,i)},p(r,a){a[0]&1&&n!==(n=r[0].api_name+"")&&D(s,n)},d(r){r&&b(e)}}}function ws(l){let e,t,n=l[23]+"",s,i,r=$e(l[22],l[21].type,"js")+"",a,c;return{c(){e=m(`		
		`),t=z("span"),s=m(n),i=m(": "),a=m(r),c=m(", "),$(t,"class","example-inputs")},m(o,u){v(o,e,u),v(o,t,u),f(t,s),f(t,i),f(t,a),v(o,c,u)},p(o,u){u[0]&16&&n!==(n=o[23]+"")&&D(s,n),u[0]&16&&r!==(r=$e(o[22],o[21].type,"js")+"")&&D(a,r)},d(o){o&&(b(e),b(t),b(c))}}}function ys(l){let e,t,n=l[23]+"",s,i,r=l[30]+"",a,c,o;return{c(){e=m(`
				`),t=z("span"),s=m(n),i=m(": example"),a=m(r),c=m(", "),o=z("span"),o.innerHTML="",$(t,"class","example-inputs"),$(o,"class","desc svelte-114qcyq")},m(u,_){v(u,e,_),v(u,t,_),f(t,s),f(t,i),f(t,a),v(u,c,_),v(u,o,_)},p(u,_){_[0]&16&&n!==(n=u[23]+"")&&D(s,n),_[0]&16&&r!==(r=u[30]+"")&&D(a,r)},d(u){u&&(b(e),b(t),b(c),b(o))}}}function Yt(l){let e,t;function n(r,a){return a[0]&16&&(e=null),e==null&&(e=!!r[14].includes(r[30])),e?ys:ws}let s=n(l,[-1,-1]),i=s(l);return{c(){i.c(),t=me()},m(r,a){i.m(r,a),v(r,t,a)},p(r,a){s===(s=n(r,a))&&i?i.p(r,a):(i.d(1),i=s(r),i&&(i.c(),i.m(t.parentNode,t)))},d(r){r&&b(t),i.d(r)}}}function Cs(l){let e,t,n,s,i,r,a,c,o,u,_=(l[3]||l[2])+"",g,d,y,h,E,p;n=new Ae({props:{code:l[9]?.innerText}});let N=oe(l[15]),j=[];for(let q=0;q<N.length;q+=1)j[q]=Qt(Ht(l,N,q));let w=l[6]!==null&&Jt(l);function L(q,M){return q[5]?ks:vs}let C=L(l),k=C(l),T=oe(l[4]),S=[];for(let q=0;q<T.length;q+=1)S[q]=Yt(Gt(l,T,q));return{c(){e=z("code"),t=z("div"),H(n.$$.fragment),s=U(),i=z("div"),r=z("pre"),a=m(`import { Client } from "@gradio/client";
`);for(let q=0;q<j.length;q+=1)j[q].c();c=m(`
const client = await Client.connect(`),o=z("span"),u=m('"'),g=m(_),d=m('"'),w&&w.c(),y=m(`);
const result = await client.predict(`),k.c(),h=m(", { ");for(let q=0;q<S.length;q+=1)S[q].c();E=m(`
});

console.log(result.data);
`),$(t,"class","copy svelte-114qcyq"),$(o,"class","token string svelte-114qcyq"),$(r,"class","svelte-114qcyq"),$(e,"class","svelte-114qcyq")},m(q,M){v(q,e,M),f(e,t),V(n,t,null),f(e,s),f(e,i),f(i,r),f(r,a);for(let R=0;R<j.length;R+=1)j[R]&&j[R].m(r,null);f(r,c),f(r,o),f(o,u),f(o,g),f(o,d),w&&w.m(r,null),f(r,y),k.m(r,null),f(r,h);for(let R=0;R<S.length;R+=1)S[R]&&S[R].m(r,null);f(r,E),l[18](i),p=!0},p(q,M){const R={};if(M[0]&512&&(R.code=q[9]?.innerText),n.$set(R),M[0]&32768){N=oe(q[15]);let F;for(F=0;F<N.length;F+=1){const G=Ht(q,N,F);j[F]?j[F].p(G,M):(j[F]=Qt(G),j[F].c(),j[F].m(r,c))}for(;F<j.length;F+=1)j[F].d(1);j.length=N.length}if((!p||M[0]&12)&&_!==(_=(q[3]||q[2])+"")&&D(g,_),q[6]!==null?w?w.p(q,M):(w=Jt(q),w.c(),w.m(r,y)):w&&(w.d(1),w=null),C===(C=L(q))&&k?k.p(q,M):(k.d(1),k=C(q),k&&(k.c(),k.m(r,h))),M[0]&16400){T=oe(q[4]);let F;for(F=0;F<T.length;F+=1){const G=Gt(q,T,F);S[F]?S[F].p(G,M):(S[F]=Yt(G),S[F].c(),S[F].m(r,E))}for(;F<S.length;F+=1)S[F].d(1);S.length=T.length}},i(q){p||(I(n.$$.fragment,q),p=!0)},o(q){O(n.$$.fragment,q),p=!1},d(q){q&&b(e),W(n),Ne(j,q),w&&w.d(),k.d(),Ne(S,q),l[18](null)}}}function $s(l){let e;return{c(){e=m(", handle_file")},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function Xt(l){let e,t,n;return{c(){e=m(', auth=("'),t=m(l[6]),n=m('", **password**)')},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p(s,i){i[0]&64&&D(t,s[6])},d(s){s&&(b(e),b(t),b(n))}}}function Kt(l){let e,t=l[23]?l[23]+"=":"",n,s,i=$e(l[24]?l[25]:l[22],l[21].type,"py")+"",r,a;return{c(){e=m(`
		`),n=m(t),s=z("span"),r=m(i),a=m(",")},m(c,o){v(c,e,o),v(c,n,o),v(c,s,o),f(s,r),v(c,a,o)},p(c,o){o[0]&16&&t!==(t=c[23]?c[23]+"=":"")&&D(n,t),o[0]&16&&i!==(i=$e(c[24]?c[25]:c[22],c[21].type,"py")+"")&&D(r,i)},d(c){c&&(b(e),b(n),b(s),b(a))}}}function Es(l){let e,t,n,s,i,r,a,c,o,u,_,g,d,y=(l[3]||l[2])+"",h,E,p,N,j,w,L,C,k=l[0].api_name+"",T,S,q,M,R,F;n=new Ae({props:{code:l[8]?.innerText}});let G=l[13]&&$s(),K=l[6]!==null&&Xt(l),he=oe(l[4]),x=[];for(let Y=0;Y<he.length;Y+=1)x[Y]=Kt(Vt(l,he,Y));return{c(){e=z("code"),t=z("div"),H(n.$$.fragment),s=U(),i=z("div"),r=z("pre"),a=z("span"),a.textContent="from",c=m(" gradio_client "),o=z("span"),o.textContent="import",u=m(" Client"),G&&G.c(),_=m(`

client = Client(`),g=z("span"),d=m('"'),h=m(y),E=m('"'),K&&K.c(),p=m(`)
result = client.`),N=z("span"),N.textContent="predict",j=m("(");for(let Y=0;Y<x.length;Y+=1)x[Y].c();w=m(`
		api_name=`),L=z("span"),C=m('"/'),T=m(k),S=m('"'),q=m(`
)
`),M=z("span"),M.textContent="print",R=m("(result)"),$(t,"class","copy svelte-114qcyq"),$(a,"class","highlight"),$(o,"class","highlight"),$(g,"class","token string svelte-114qcyq"),$(N,"class","highlight"),$(L,"class","api-name svelte-114qcyq"),$(M,"class","highlight"),$(r,"class","svelte-114qcyq"),$(e,"class","svelte-114qcyq")},m(Y,ne){v(Y,e,ne),f(e,t),V(n,t,null),f(e,s),f(e,i),f(i,r),f(r,a),f(r,c),f(r,o),f(r,u),G&&G.m(r,null),f(r,_),f(r,g),f(g,d),f(g,h),f(g,E),K&&K.m(r,null),f(r,p),f(r,N),f(r,j);for(let Q=0;Q<x.length;Q+=1)x[Q]&&x[Q].m(r,null);f(r,w),f(r,L),f(L,C),f(L,T),f(L,S),f(r,q),f(r,M),f(r,R),l[17](i),F=!0},p(Y,ne){const Q={};if(ne[0]&256&&(Q.code=Y[8]?.innerText),n.$set(Q),(!F||ne[0]&12)&&y!==(y=(Y[3]||Y[2])+"")&&D(h,y),Y[6]!==null?K?K.p(Y,ne):(K=Xt(Y),K.c(),K.m(r,p)):K&&(K.d(1),K=null),ne[0]&16){he=oe(Y[4]);let se;for(se=0;se<he.length;se+=1){const ke=Vt(Y,he,se);x[se]?x[se].p(ke,ne):(x[se]=Kt(ke),x[se].c(),x[se].m(r,w))}for(;se<x.length;se+=1)x[se].d(1);x.length=he.length}(!F||ne[0]&1)&&k!==(k=Y[0].api_name+"")&&D(T,k)},i(Y){F||(I(n.$$.fragment,Y),F=!0)},o(Y){O(n.$$.fragment,Y),F=!1},d(Y){Y&&b(e),W(n),G&&G.d(),K&&K.d(),Ne(x,Y),l[17](null)}}}function zs(l){let e,t,n,s,i,r,a;const c=[ds,ps],o=[];function u(y,h){return y[5]?0:1}t=u(l),n=o[t]=c[t](l);const _=[gs,hs,ms],g=[];function d(y,h){return y[7]==="python"?0:y[7]==="javascript"?1:y[7]==="bash"?2:-1}return~(i=d(l))&&(r=g[i]=_[i](l)),{c(){e=z("div"),n.c(),s=U(),r&&r.c(),$(e,"class","container svelte-114qcyq")},m(y,h){v(y,e,h),o[t].m(e,null),f(e,s),~i&&g[i].m(e,null),a=!0},p(y,h){let E=t;t=u(y),t===E?o[t].p(y,h):(pe(),O(o[E],1,1,()=>{o[E]=null}),de(),n=o[t],n?n.p(y,h):(n=o[t]=c[t](y),n.c()),I(n,1),n.m(e,s));let p=i;i=d(y),i===p?~i&&g[i].p(y,h):(r&&(pe(),O(g[p],1,1,()=>{g[p]=null}),de()),~i?(r=g[i],r?r.p(y,h):(r=g[i]=_[i](y),r.c()),I(r,1),r.m(e,null)):r=null)},i(y){a||(I(n),I(r),a=!0)},o(y){O(n),O(r),a=!1},d(y){y&&b(e),o[t].d(),~i&&g[i].d()}}}function Ns(l,e,t){let n,s,{dependency:i}=e,{dependency_index:r}=e,{root:a}=e,{api_prefix:c}=e,{space_id:o}=e,{endpoint_parameters:u}=e,{named:_}=e,{username:g}=e,{current_language:d}=e,y,h,E,p=u.some(k=>Rn(k.example_input)),N=["Audio","File","Image","Video"],j=u.filter(k=>N.includes(k.component));function w(k){ze[k?"unshift":"push"](()=>{y=k,t(8,y)})}function L(k){ze[k?"unshift":"push"](()=>{h=k,t(9,h)})}function C(k){ze[k?"unshift":"push"](()=>{E=k,t(10,E)})}return l.$$set=k=>{"dependency"in k&&t(0,i=k.dependency),"dependency_index"in k&&t(1,r=k.dependency_index),"root"in k&&t(2,a=k.root),"api_prefix"in k&&t(16,c=k.api_prefix),"space_id"in k&&t(3,o=k.space_id),"endpoint_parameters"in k&&t(4,u=k.endpoint_parameters),"named"in k&&t(5,_=k.named),"username"in k&&t(6,g=k.username),"current_language"in k&&t(7,d=k.current_language)},l.$$.update=()=>{l.$$.dirty[0]&65536&&t(12,n=c||"/"),l.$$.dirty[0]&4&&t(11,s=a.replace(/\/$/,""))},[i,r,a,o,u,_,g,d,y,h,E,s,n,p,N,j,c,w,L,C]}class Ls extends be{constructor(e){super(),ve(this,e,Ns,zs,we,{dependency:0,dependency_index:1,root:2,api_prefix:16,space_id:3,endpoint_parameters:4,named:5,username:6,current_language:7},null,[-1,-1])}get dependency(){return this.$$.ctx[0]}set dependency(e){this.$$set({dependency:e}),P()}get dependency_index(){return this.$$.ctx[1]}set dependency_index(e){this.$$set({dependency_index:e}),P()}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),P()}get api_prefix(){return this.$$.ctx[16]}set api_prefix(e){this.$$set({api_prefix:e}),P()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),P()}get endpoint_parameters(){return this.$$.ctx[4]}set endpoint_parameters(e){this.$$set({endpoint_parameters:e}),P()}get named(){return this.$$.ctx[5]}set named(e){this.$$set({named:e}),P()}get username(){return this.$$.ctx[6]}set username(e){this.$$set({username:e}),P()}get current_language(){return this.$$.ctx[7]}set current_language(e){this.$$set({current_language:e}),P()}}function xt(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function en(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function tn(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function js(l){let e,t,n,s,i,r;n=new Ae({props:{code:l[6]?.innerText}});let a=oe(l[9]),c=[];for(let o=0;o<a.length;o+=1)c[o]=nn(xt(l,a,o));return{c(){e=z("code"),t=z("div"),H(n.$$.fragment),s=U(),i=z("div");for(let o=0;o<c.length;o+=1)c[o].c();$(t,"class","copy svelte-j71ub0"),$(e,"class","svelte-j71ub0")},m(o,u){v(o,e,u),f(e,t),V(n,t,null),f(e,s),f(e,i);for(let _=0;_<c.length;_+=1)c[_]&&c[_].m(i,null);l[16](i),r=!0},p(o,u){const _={};if(u&64&&(_.code=o[6]?.innerText),n.$set(_),u&513){a=oe(o[9]);let g;for(g=0;g<a.length;g+=1){const d=xt(o,a,g);c[g]?c[g].p(d,u):(c[g]=nn(d),c[g].c(),c[g].m(i,null))}for(;g<c.length;g+=1)c[g].d(1);c.length=a.length}},i(o){r||(I(n.$$.fragment,o),r=!0)},o(o){O(n.$$.fragment,o),r=!1},d(o){o&&b(e),W(n),Ne(c,o),l[16](null)}}}function qs(l){let e,t,n,s,i,r,a,c,o,u,_,g,d;n=new Ae({props:{code:l[5]?.innerText}});let y=l[2]!==null&&ln(l),h=oe(l[8]),E=[];for(let p=0;p<h.length;p+=1)E[p]=rn(en(l,h,p));return{c(){e=z("code"),t=z("div"),H(n.$$.fragment),s=U(),i=z("div"),r=z("pre"),a=m(`import { Client } from "@gradio/client";

const app = await Client.connect(`),c=z("span"),o=m('"'),u=m(l[0]),_=m('"'),y&&y.c(),g=m(`);
					`);for(let p=0;p<E.length;p+=1)E[p].c();$(t,"class","copy svelte-j71ub0"),$(c,"class","token string svelte-j71ub0"),$(r,"class","svelte-j71ub0"),$(e,"class","svelte-j71ub0")},m(p,N){v(p,e,N),f(e,t),V(n,t,null),f(e,s),f(e,i),f(i,r),f(r,a),f(r,c),f(c,o),f(c,u),f(c,_),y&&y.m(r,null),f(r,g);for(let j=0;j<E.length;j+=1)E[j]&&E[j].m(r,null);l[15](i),d=!0},p(p,N){const j={};if(N&32&&(j.code=p[5]?.innerText),n.$set(j),(!d||N&1)&&D(u,p[0]),p[2]!==null?y?y.p(p,N):(y=ln(p),y.c(),y.m(r,g)):y&&(y.d(1),y=null),N&256){h=oe(p[8]);let w;for(w=0;w<h.length;w+=1){const L=en(p,h,w);E[w]?E[w].p(L,N):(E[w]=rn(L),E[w].c(),E[w].m(r,null))}for(;w<E.length;w+=1)E[w].d(1);E.length=h.length}},i(p){d||(I(n.$$.fragment,p),d=!0)},o(p){O(n.$$.fragment,p),d=!1},d(p){p&&b(e),W(n),y&&y.d(),Ne(E,p),l[15](null)}}}function As(l){let e,t,n,s,i,r,a,c,o,u,_,g,d,y,h,E;n=new Ae({props:{code:l[4]}});let p=l[2]!==null&&on(l),N=oe(l[7]),j=[];for(let w=0;w<N.length;w+=1)j[w]=an(tn(l,N,w));return{c(){e=z("code"),t=z("div"),H(n.$$.fragment),s=U(),i=z("div"),r=z("pre"),a=z("span"),a.textContent="from",c=m(" gradio_client "),o=z("span"),o.textContent="import",u=m(` Client, file

client = Client(`),_=z("span"),g=m('"'),d=m(l[0]),y=m('"'),p&&p.c(),h=m(`)
`);for(let w=0;w<j.length;w+=1)j[w].c();$(t,"class","copy svelte-j71ub0"),$(a,"class","highlight"),$(o,"class","highlight"),$(_,"class","token string svelte-j71ub0"),$(r,"class","svelte-j71ub0"),$(e,"class","svelte-j71ub0")},m(w,L){v(w,e,L),f(e,t),V(n,t,null),f(e,s),f(e,i),f(i,r),f(r,a),f(r,c),f(r,o),f(r,u),f(r,_),f(_,g),f(_,d),f(_,y),p&&p.m(r,null),f(r,h);for(let C=0;C<j.length;C+=1)j[C]&&j[C].m(r,null);l[14](i),E=!0},p(w,L){const C={};if(L&16&&(C.code=w[4]),n.$set(C),(!E||L&1)&&D(d,w[0]),w[2]!==null?p?p.p(w,L):(p=on(w),p.c(),p.m(r,h)):p&&(p.d(1),p=null),L&128){N=oe(w[7]);let k;for(k=0;k<N.length;k+=1){const T=tn(w,N,k);j[k]?j[k].p(T,L):(j[k]=an(T),j[k].c(),j[k].m(r,null))}for(;k<j.length;k+=1)j[k].d(1);j.length=N.length}},i(w){E||(I(n.$$.fragment,w),E=!0)},o(w){O(n.$$.fragment,w),E=!1},d(w){w&&b(e),W(n),p&&p.d(),Ne(j,w),l[14](null)}}}function nn(l){let e,t,n,s,i=l[21]+"",r,a,c="{",o,u,_=l[20]+"",g,d,y="}",h,E,p="{",N,j,w="}",L,C,k,T,S=l[21]+"",q,M,R,F;return{c(){e=z("pre"),t=m("curl -X POST "),n=m(l[0]),s=m("call/"),r=m(i),a=m(` -s -H "Content-Type: application/json" -d '`),o=m(c),u=m(` 
	"data": [`),g=m(_),d=m("]"),h=m(y),E=m(`' \\
  | awk -F'"' '`),N=m(p),j=m(" print $4"),L=m(w),C=m(`' \\
  | read EVENT_ID; curl -N `),k=m(l[0]),T=m("call/"),q=m(S),M=m("/$EVENT_ID"),R=U(),F=z("br"),$(e,"class","svelte-j71ub0")},m(G,K){v(G,e,K),f(e,t),f(e,n),f(e,s),f(e,r),f(e,a),f(e,o),f(e,u),f(e,g),f(e,d),f(e,h),f(e,E),f(e,N),f(e,j),f(e,L),f(e,C),f(e,k),f(e,T),f(e,q),f(e,M),v(G,R,K),v(G,F,K)},p(G,K){K&1&&D(n,G[0]),K&512&&i!==(i=G[21]+"")&&D(r,i),K&512&&_!==(_=G[20]+"")&&D(g,_),K&1&&D(k,G[0]),K&512&&S!==(S=G[21]+"")&&D(q,S)},d(G){G&&(b(e),b(R),b(F))}}}function ln(l){let e,t,n;return{c(){e=m(', {auth: ["'),t=m(l[2]),n=m('", **password**]}')},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p(s,i){i&4&&D(t,s[2])},d(s){s&&(b(e),b(t),b(n))}}}function sn(l){let e,t=l[20]+"",n;return{c(){e=m(", "),n=m(t)},m(s,i){v(s,e,i),v(s,n,i)},p(s,i){i&256&&t!==(t=s[20]+"")&&D(n,t)},d(s){s&&(b(e),b(n))}}}function rn(l){let e,t,n,s=l[21]+"",i,r,a,c=l[20]&&sn(l);return{c(){e=m(`
await client.predict(`),t=z("span"),n=m(`
  "/`),i=m(s),r=m('"'),c&&c.c(),a=m(`);
						`),$(t,"class","api-name svelte-j71ub0")},m(o,u){v(o,e,u),v(o,t,u),f(t,n),f(t,i),f(t,r),c&&c.m(o,u),v(o,a,u)},p(o,u){u&256&&s!==(s=o[21]+"")&&D(i,s),o[20]?c?c.p(o,u):(c=sn(o),c.c(),c.m(a.parentNode,a)):c&&(c.d(1),c=null)},d(o){o&&(b(e),b(t),b(a)),c&&c.d(o)}}}function on(l){let e,t,n;return{c(){e=m(', auth=("'),t=m(l[2]),n=m('", **password**)')},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p(s,i){i&4&&D(t,s[2])},d(s){s&&(b(e),b(t),b(n))}}}function an(l){let e,t,n,s=l[20]+"",i,r,a,c,o=l[21]+"",u,_,g;return{c(){e=m(`
client.`),t=z("span"),n=m(`predict(
`),i=m(s),r=m("  api_name="),a=z("span"),c=m('"/'),u=m(o),_=m('"'),g=m(`
)
`),$(a,"class","api-name svelte-j71ub0"),$(t,"class","highlight")},m(d,y){v(d,e,y),v(d,t,y),f(t,n),f(t,i),f(t,r),f(t,a),f(a,c),f(a,u),f(a,_),f(t,g)},p(d,y){y&128&&s!==(s=d[20]+"")&&D(i,s),y&128&&o!==(o=d[21]+"")&&D(u,o)},d(d){d&&(b(e),b(t))}}}function Ts(l){let e,t,n,s;const i=[As,qs,js],r=[];function a(c,o){return c[1]==="python"?0:c[1]==="javascript"?1:c[1]==="bash"?2:-1}return~(e=a(l))&&(t=r[e]=i[e](l)),{c(){t&&t.c(),n=me()},m(c,o){~e&&r[e].m(c,o),v(c,n,o),s=!0},p(c,o){let u=e;e=a(c),e===u?~e&&r[e].p(c,o):(t&&(pe(),O(r[u],1,1,()=>{r[u]=null}),de()),~e?(t=r[e],t?t.p(c,o):(t=r[e]=i[e](c),t.c()),I(t,1),t.m(n.parentNode,n)):t=null)},i(c){s||(I(t),s=!0)},o(c){O(t),s=!1},d(c){c&&b(n),~e&&r[e].d(c)}}}function Ss(l){let e,t,n;return t=new Xe({props:{border_mode:"focus",$$slots:{default:[Ts]},$$scope:{ctx:l}}}),{c(){e=z("div"),H(t.$$.fragment),$(e,"class","container svelte-j71ub0")},m(s,i){v(s,e,i),V(t,e,null),n=!0},p(s,[i]){const r={};i&268436479&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){n||(I(t.$$.fragment,s),n=!0)},o(s){O(t.$$.fragment,s),n=!1},d(s){s&&b(e),W(t)}}}function Ps(l,e,t){let{dependencies:n}=e,{short_root:s}=e,{root:i}=e,{api_prefix:r=""}=e,{current_language:a}=e,{username:c}=e,o,u,_,g,{api_calls:d=[]}=e;async function y(){return await(await fetch(i.replace(/\/$/,"")+r+"/info/?all_endpoints=true")).json()}let h,E=[],p=[],N=[];function j(k,T){const S=`/${n[k.fn_index].api_name}`,M=k.data.filter(R=>typeof R<"u").map((R,F)=>{if(h[S]){const G=h[S].parameters[F];if(!G)return;const K=G.parameter_name,he=G.python_type.type;if(T==="py")return`  ${K}=${$e(R,he,"py")}`;if(T==="js")return`    ${K}: ${$e(R,he,"js")}`;if(T==="bash")return`    ${$e(R,he,"bash")}`}return`  ${$e(R,void 0,T)}`}).filter(R=>typeof R<"u").join(`,
`);if(M){if(T==="py")return`${M},
`;if(T==="js")return`{
${M},
}`;if(T==="bash")return`
${M}
`}return T==="py"?"":`
`}Re(async()=>{h=(await y()).named_endpoints;let T=d.map(R=>j(R,"py")),S=d.map(R=>j(R,"js")),q=d.map(R=>j(R,"bash")),M=d.map(R=>n[R.fn_index].api_name||"");t(7,E=T.map((R,F)=>({call:R,api_name:M[F]}))),t(8,p=S.map((R,F)=>({call:R,api_name:M[F]}))),t(9,N=q.map((R,F)=>({call:R,api_name:M[F]}))),await Ze(),t(4,u=o.innerText)});function w(k){ze[k?"unshift":"push"](()=>{o=k,t(3,o)})}function L(k){ze[k?"unshift":"push"](()=>{_=k,t(5,_)})}function C(k){ze[k?"unshift":"push"](()=>{g=k,t(6,g)})}return l.$$set=k=>{"dependencies"in k&&t(10,n=k.dependencies),"short_root"in k&&t(0,s=k.short_root),"root"in k&&t(11,i=k.root),"api_prefix"in k&&t(12,r=k.api_prefix),"current_language"in k&&t(1,a=k.current_language),"username"in k&&t(2,c=k.username),"api_calls"in k&&t(13,d=k.api_calls)},[s,a,c,o,u,_,g,E,p,N,n,i,r,d,w,L,C]}class Is extends be{constructor(e){super(),ve(this,e,Ps,Ss,we,{dependencies:10,short_root:0,root:11,api_prefix:12,current_language:1,username:2,api_calls:13})}get dependencies(){return this.$$.ctx[10]}set dependencies(e){this.$$set({dependencies:e}),P()}get short_root(){return this.$$.ctx[0]}set short_root(e){this.$$set({short_root:e}),P()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),P()}get api_prefix(){return this.$$.ctx[12]}set api_prefix(e){this.$$set({api_prefix:e}),P()}get current_language(){return this.$$.ctx[1]}set current_language(e){this.$$set({current_language:e}),P()}get username(){return this.$$.ctx[2]}set username(e){this.$$set({username:e}),P()}get api_calls(){return this.$$.ctx[13]}set api_calls(e){this.$$set({api_calls:e}),P()}}const Ms="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",Os="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",Fs="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e";function cn(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function Us(l){let e;return{c(){e=m("1 element")},m(t,n){v(t,e,n)},p:ge,d(t){t&&b(e)}}}function Ds(l){let e=l[3]=="python"?"tuple":"list",t,n,s=l[1].length+"",i,r;return{c(){t=m(e),n=m(" of "),i=m(s),r=m(`
		elements`)},m(a,c){v(a,t,c),v(a,n,c),v(a,i,c),v(a,r,c)},p(a,c){c&8&&e!==(e=a[3]=="python"?"tuple":"list")&&D(t,e),c&2&&s!==(s=a[1].length+"")&&D(i,s)},d(a){a&&(b(t),b(n),b(i),b(r))}}}function fn(l){let e;return{c(){e=z("span"),e.textContent=`[${l[10]}]`,$(e,"class","code svelte-16h224k")},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function Rs(l){let e=l[2][l[10]].type+"",t;return{c(){t=m(e)},m(n,s){v(n,t,s)},p(n,s){s&4&&e!==(e=n[2][n[10]].type+"")&&D(t,e)},d(n){n&&b(t)}}}function Bs(l){let e=l[6].type+"",t;return{c(){t=m(e)},m(n,s){v(n,t,s)},p(n,s){s&2&&e!==(e=n[6].type+"")&&D(t,e)},d(n){n&&b(t)}}}function un(l){let e,t,n,s,i,r,a,c,o,u=l[4]+"",_,g,d=l[7]+"",y,h,E,p=l[1].length>1&&fn(l);function N(L,C){return L[3]==="python"?Bs:Rs}let j=N(l),w=j(l);return{c(){e=z("hr"),t=U(),n=z("div"),s=z("p"),p&&p.c(),i=U(),r=z("span"),w.c(),a=U(),c=z("p"),o=m('The output value that appears in the "'),_=m(u),g=m('" '),y=m(d),h=m(`
				component.`),E=U(),$(e,"class","hr svelte-16h224k"),$(r,"class","code highlight svelte-16h224k"),$(c,"class","desc svelte-16h224k"),re(n,"margin","10px")},m(L,C){v(L,e,C),v(L,t,C),v(L,n,C),f(n,s),p&&p.m(s,null),f(s,i),f(s,r),w.m(r,null),f(n,a),f(n,c),f(c,o),f(c,_),f(c,g),f(c,y),f(c,h),f(n,E)},p(L,C){L[1].length>1?p||(p=fn(L),p.c(),p.m(s,i)):p&&(p.d(1),p=null),j===(j=N(L))&&w?w.p(L,C):(w.d(1),w=j(L),w&&(w.c(),w.m(r,null))),C&2&&u!==(u=L[4]+"")&&D(_,u),C&2&&d!==(d=L[7]+"")&&D(y,d)},d(L){L&&(b(e),b(t),b(n)),p&&p.d(),w.d()}}}function _n(l){let e,t,n;return t=new Un({props:{margin:!1}}),{c(){e=z("div"),H(t.$$.fragment),$(e,"class","load-wrap")},m(s,i){v(s,e,i),V(t,e,null),n=!0},i(s){n||(I(t.$$.fragment,s),n=!0)},o(s){O(t.$$.fragment,s),n=!1},d(s){s&&b(e),W(t)}}}function Gs(l){let e,t,n,s,i,r,a,c;function o(h,E){return h[1].length>1?Ds:Us}let u=o(l),_=u(l),g=oe(l[1]),d=[];for(let h=0;h<g.length;h+=1)d[h]=un(cn(l,g,h));let y=l[0]&&_n();return{c(){e=z("h4"),t=z("div"),t.innerHTML='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n=m(`
	Returns `),_.c(),s=U(),i=z("div");for(let h=0;h<d.length;h+=1)d[h].c();r=U(),y&&y.c(),a=me(),$(t,"class","toggle-icon svelte-16h224k"),$(e,"class","svelte-16h224k"),it(i,"hide",l[0])},m(h,E){v(h,e,E),f(e,t),f(e,n),_.m(e,null),v(h,s,E),v(h,i,E);for(let p=0;p<d.length;p+=1)d[p]&&d[p].m(i,null);v(h,r,E),y&&y.m(h,E),v(h,a,E),c=!0},p(h,[E]){if(u===(u=o(h))&&_?_.p(h,E):(_.d(1),_=u(h),_&&(_.c(),_.m(e,null))),E&14){g=oe(h[1]);let p;for(p=0;p<g.length;p+=1){const N=cn(h,g,p);d[p]?d[p].p(N,E):(d[p]=un(N),d[p].c(),d[p].m(i,null))}for(;p<d.length;p+=1)d[p].d(1);d.length=g.length}(!c||E&1)&&it(i,"hide",h[0]),h[0]?y?E&1&&I(y,1):(y=_n(),y.c(),I(y,1),y.m(a.parentNode,a)):y&&(pe(),O(y,1,1,()=>{y=null}),de())},i(h){c||(I(y),c=!0)},o(h){O(y),c=!1},d(h){h&&(b(e),b(s),b(i),b(r),b(a)),_.d(),Ne(d,h),y&&y.d(h)}}}function Hs(l,e,t){let{is_running:n}=e,{endpoint_returns:s}=e,{js_returns:i}=e,{current_language:r}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,s=a.endpoint_returns),"js_returns"in a&&t(2,i=a.js_returns),"current_language"in a&&t(3,r=a.current_language)},[n,s,i,r]}class Vs extends be{constructor(e){super(),ve(this,e,Hs,Gs,we,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),P()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),P()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),P()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),P()}}function pn(l,e,t){const n=l.slice();return n[19]=e[t],n[21]=t,n}function dn(l,e,t){const n=l.slice();return n[22]=e[t][0],n[23]=e[t][1],n}function mn(l){let e,t,n,s;const i=[Zs,Ws],r=[];function a(c,o){return c[9]?0:1}return e=a(l),t=r[e]=i[e](l),{c(){t.c(),n=me()},m(c,o){r[e].m(c,o),v(c,n,o),s=!0},p(c,o){t.p(c,o)},i(c){s||(I(t),s=!0)},o(c){O(t),s=!1},d(c){c&&b(n),r[e].d(c)}}}function Ws(l){let e,t;return e=new Dl({props:{root:l[0]}}),e.$on("close",l[16]),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s&1&&(i.root=n[0]),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function Zs(l){let e,t,n,s,i,r,a,c,o,u,_,g,d;t=new Hl({props:{root:l[3]||l[0],api_count:l[9]}}),t.$on("close",l[13]);let y=oe(l[10]),h=[];for(let C=0;C<y.length;C+=1)h[C]=hn(dn(l,y,C));const E=[Js,Qs],p=[];function N(C,k){return C[5].length?0:1}u=N(l),_=p[u]=E[u](l);let j=oe(l[1]),w=[];for(let C=0;C<j.length;C+=1)w[C]=wn(pn(l,j,C));const L=C=>O(w[C],1,1,()=>{w[C]=null});return{c(){e=z("div"),H(t.$$.fragment),n=U(),s=z("div"),i=z("div"),i.innerHTML=`<p style="font-size: var(--text-lg);">Choose a language to see the code snippets for interacting with the
					API.</p>`,r=U(),a=z("div"),c=z("div");for(let C=0;C<h.length;C+=1)h[C].c();o=U(),_.c(),g=U();for(let C=0;C<w.length;C+=1)w[C].c();$(e,"class","banner-wrap svelte-1y3yua6"),$(i,"class","client-doc svelte-1y3yua6"),$(c,"class","snippets svelte-1y3yua6"),$(a,"class","endpoint svelte-1y3yua6"),$(s,"class","docs-wrap svelte-1y3yua6")},m(C,k){v(C,e,k),V(t,e,null),v(C,n,k),v(C,s,k),f(s,i),f(s,r),f(s,a),f(a,c);for(let T=0;T<h.length;T+=1)h[T]&&h[T].m(c,null);f(a,o),p[u].m(a,null),f(a,g);for(let T=0;T<w.length;T+=1)w[T]&&w[T].m(a,null);d=!0},p(C,k){const T={};if(k&9&&(T.root=C[3]||C[0]),t.$set(T),k&1088){y=oe(C[10]);let q;for(q=0;q<y.length;q+=1){const M=dn(C,y,q);h[q]?h[q].p(M,k):(h[q]=hn(M),h[q].c(),h[q].m(c,null))}for(;q<h.length;q+=1)h[q].d(1);h.length=y.length}let S=u;if(u=N(C),u===S?p[u].p(C,k):(pe(),O(p[S],1,1,()=>{p[S]=null}),de(),_=p[u],_?_.p(C,k):(_=p[u]=E[u](C),_.c()),I(_,1),_.m(a,g)),k&479){j=oe(C[1]);let q;for(q=0;q<j.length;q+=1){const M=pn(C,j,q);w[q]?(w[q].p(M,k),I(w[q],1)):(w[q]=wn(M),w[q].c(),I(w[q],1),w[q].m(a,null))}for(pe(),q=j.length;q<w.length;q+=1)L(q);de()}},i(C){if(!d){I(t.$$.fragment,C),I(_);for(let k=0;k<j.length;k+=1)I(w[k]);d=!0}},o(C){O(t.$$.fragment,C),O(_),w=w.filter(Boolean);for(let k=0;k<w.length;k+=1)O(w[k]);d=!1},d(C){C&&(b(e),b(n),b(s)),W(t),Ne(h,C),p[u].d(),Ne(w,C)}}}function hn(l){let e,t,n,s,i=l[22]+"",r,a,c,o,u;function _(){return l[14](l[22])}return{c(){e=z("li"),t=z("img"),s=U(),r=m(i),a=U(),Fe(t.src,n=l[23])||$(t,"src",n),$(t,"alt",""),$(t,"class","svelte-1y3yua6"),$(e,"class",c="snippet "+(l[6]===l[22]?"current-lang":"inactive-lang")+" svelte-1y3yua6")},m(g,d){v(g,e,d),f(e,t),f(e,s),f(e,r),f(e,a),o||(u=Ee(e,"click",_),o=!0)},p(g,d){l=g,d&64&&c!==(c="snippet "+(l[6]===l[22]?"current-lang":"inactive-lang")+" svelte-1y3yua6")&&$(e,"class",c)},d(g){g&&b(e),o=!1,u()}}}function Qs(l){let e,t,n,s,i,r,a,c,o,u;function _(E,p){return E[6]=="python"||E[6]=="javascript"?Xs:Ys}let g=_(l),d=g(l);n=new as({props:{current_language:l[6]}});let y=l[3]&&gn(l);c=new ct({props:{size:"sm",variant:"secondary",$$slots:{default:[Ks]},$$scope:{ctx:l}}}),c.$on("click",l[15]);let h=l[6]=="bash"&&bn(l);return{c(){e=z("p"),d.c(),t=U(),H(n.$$.fragment),s=U(),i=z("p"),r=m(`2. Find the API endpoint below corresponding to your desired
						function in the app. Copy the code snippet, replacing the
						placeholder values with your own input data.
						`),y&&y.c(),a=m(`

						Or use the
						`),H(c.$$.fragment),o=m(`
						to automatically generate your API requests.
						`),h&&h.c(),$(e,"class","padded svelte-1y3yua6"),$(i,"class","padded svelte-1y3yua6")},m(E,p){v(E,e,p),d.m(e,null),v(E,t,p),V(n,E,p),v(E,s,p),v(E,i,p),f(i,r),y&&y.m(i,null),f(i,a),V(c,i,null),f(i,o),h&&h.m(i,null),u=!0},p(E,p){g===(g=_(E))&&d?d.p(E,p):(d.d(1),d=g(E),d&&(d.c(),d.m(e,null)));const N={};p&64&&(N.current_language=E[6]),n.$set(N),E[3]?y?y.p(E,p):(y=gn(E),y.c(),y.m(i,a)):y&&(y.d(1),y=null);const j={};p&67108864&&(j.$$scope={dirty:p,ctx:E}),c.$set(j),E[6]=="bash"?h?h.p(E,p):(h=bn(E),h.c(),h.m(i,null)):h&&(h.d(1),h=null)},i(E){u||(I(n.$$.fragment,E),I(c.$$.fragment,E),u=!0)},o(E){O(n.$$.fragment,E),O(c.$$.fragment,E),u=!1},d(E){E&&(b(e),b(t),b(s),b(i)),d.d(),W(n,E),y&&y.d(),W(c),h&&h.d()}}}function Js(l){let e,t,n,s,i,r=l[5].length+"",a,c,o,u,_,g,d,y,h,E,p,N,j,w;return h=new Is({props:{current_language:l[6],api_calls:l[5],dependencies:l[1],root:l[0],api_prefix:l[2].api_prefix,short_root:l[3]||l[0],username:l[4]}}),{c(){e=z("div"),t=z("p"),n=m("🪄 Recorded API Calls "),s=z("span"),i=m("["),a=m(r),c=m("]"),o=U(),u=z("p"),_=m(`Here is the code snippet to replay the most recently recorded API
							calls using the `),g=m(l[6]),d=m(`
							client.`),y=U(),H(h.$$.fragment),E=U(),p=z("p"),p.textContent=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,N=U(),j=z("p"),j.textContent="API Documentation",$(s,"class","api-count svelte-1y3yua6"),$(t,"id","num-recorded-api-calls"),re(t,"font-size","var(--text-lg)"),re(t,"font-weight","bold"),re(t,"margin","10px 0px"),re(j,"font-size","var(--text-lg)"),re(j,"font-weight","bold"),re(j,"margin","30px 0px 10px")},m(L,C){v(L,e,C),f(e,t),f(t,n),f(t,s),f(s,i),f(s,a),f(s,c),f(e,o),f(e,u),f(u,_),f(u,g),f(u,d),f(e,y),V(h,e,null),f(e,E),f(e,p),v(L,N,C),v(L,j,C),w=!0},p(L,C){(!w||C&32)&&r!==(r=L[5].length+"")&&D(a,r),(!w||C&64)&&D(g,L[6]);const k={};C&64&&(k.current_language=L[6]),C&32&&(k.api_calls=L[5]),C&2&&(k.dependencies=L[1]),C&1&&(k.root=L[0]),C&4&&(k.api_prefix=L[2].api_prefix),C&9&&(k.short_root=L[3]||L[0]),C&16&&(k.username=L[4]),h.$set(k)},i(L){w||(I(h.$$.fragment,L),w=!0)},o(L){O(h.$$.fragment,L),w=!1},d(L){L&&(b(e),b(N),b(j)),W(h)}}}function Ys(l){let e;return{c(){e=m("1. Confirm that you have cURL installed on your system.")},m(t,n){v(t,e,n)},p:ge,d(t){t&&b(e)}}}function Xs(l){let e,t,n,s,i,r,a,c;return{c(){e=m(`1. Install the
							`),t=z("span"),n=m(l[6]),s=m(`
							client (`),i=z("a"),r=m("docs"),c=m(") if you don't already have it installed."),re(t,"text-transform","capitalize"),$(i,"href",a=l[6]=="python"?ot:rt),$(i,"target","_blank"),$(i,"class","svelte-1y3yua6")},m(o,u){v(o,e,u),v(o,t,u),f(t,n),v(o,s,u),v(o,i,u),f(i,r),v(o,c,u)},p(o,u){u&64&&D(n,o[6]),u&64&&a!==(a=o[6]=="python"?ot:rt)&&$(i,"href",a)},d(o){o&&(b(e),b(t),b(s),b(i),b(c))}}}function gn(l){let e,t,n,s,i;return{c(){e=m(`If this is a private Space, you may need to pass your
							Hugging Face token as well (`),t=z("a"),n=m("read more"),i=m(")."),$(t,"href",s=l[6]=="python"?ot+st:l[6]=="javascript"?rt+st:wt),$(t,"class","underline svelte-1y3yua6"),$(t,"target","_blank")},m(r,a){v(r,e,a),v(r,t,a),f(t,n),v(r,i,a)},p(r,a){a&64&&s!==(s=r[6]=="python"?ot+st:r[6]=="javascript"?rt+st:wt)&&$(t,"href",s)},d(r){r&&(b(e),b(t),b(i))}}}function Ks(l){let e,t,n;return{c(){e=z("div"),t=U(),n=z("p"),n.textContent="API Recorder",$(e,"class","loading-dot svelte-1y3yua6"),$(n,"class","self-baseline svelte-1y3yua6")},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p:ge,d(s){s&&(b(e),b(t),b(n))}}}function bn(l){let e,t,n,s,i,r,a,c,o,u,_,g,d,y,h,E,p,N,j,w,L,C,k,T,S=l[4]!==null&&vn();return{c(){e=z("br"),t=m(" "),n=z("br"),s=m(`Making a
							prediction and getting a result requires
							`),i=z("strong"),i.textContent="2 requests",r=m(`: a
							`),a=z("code"),a.textContent="POST",c=m(`
							and a `),o=z("code"),o.textContent="GET",u=m(" request. The "),_=z("code"),_.textContent="POST",g=m(` request
							returns an `),d=z("code"),d.textContent="EVENT_ID",y=m(`, which is used in the second
							`),h=z("code"),h.textContent="GET",E=m(` request to fetch the results. In these snippets,
							we've used `),p=z("code"),p.textContent="awk",N=m(" and "),j=z("code"),j.textContent="read",w=m(` to parse the
							results, combining these two requests into one command for ease of
							use. `),S&&S.c(),L=m(` See
							`),C=z("a"),k=m("curl docs"),T=m("."),$(a,"class","svelte-1y3yua6"),$(o,"class","svelte-1y3yua6"),$(_,"class","svelte-1y3yua6"),$(d,"class","svelte-1y3yua6"),$(h,"class","svelte-1y3yua6"),$(p,"class","svelte-1y3yua6"),$(j,"class","svelte-1y3yua6"),$(C,"href",wt),$(C,"target","_blank"),$(C,"class","svelte-1y3yua6")},m(q,M){v(q,e,M),v(q,t,M),v(q,n,M),v(q,s,M),v(q,i,M),v(q,r,M),v(q,a,M),v(q,c,M),v(q,o,M),v(q,u,M),v(q,_,M),v(q,g,M),v(q,d,M),v(q,y,M),v(q,h,M),v(q,E,M),v(q,p,M),v(q,N,M),v(q,j,M),v(q,w,M),S&&S.m(q,M),v(q,L,M),v(q,C,M),f(C,k),v(q,T,M)},p(q,M){q[4]!==null?S||(S=vn(),S.c(),S.m(L.parentNode,L)):S&&(S.d(1),S=null)},d(q){q&&(b(e),b(t),b(n),b(s),b(i),b(r),b(a),b(c),b(o),b(u),b(_),b(g),b(d),b(y),b(h),b(E),b(p),b(N),b(j),b(w),b(L),b(C),b(T)),S&&S.d(q)}}}function vn(l){let e;return{c(){e=m(`Note: connecting to an authenticated app requires an additional
								request.`)},m(t,n){v(t,e,n)},d(t){t&&b(e)}}}function kn(l){let e,t,n,s,i,r,a,c;return t=new Ls({props:{named:!0,endpoint_parameters:l[7].named_endpoints["/"+l[19].api_name].parameters,dependency:l[19],dependency_index:l[21],current_language:l[6],root:l[0],space_id:l[3],username:l[4],api_prefix:l[2].api_prefix}}),s=new Kl({props:{endpoint_returns:l[7].named_endpoints["/"+l[19].api_name].parameters,js_returns:l[8].named_endpoints["/"+l[19].api_name].parameters,is_running:yn,current_language:l[6]}}),r=new Vs({props:{endpoint_returns:l[7].named_endpoints["/"+l[19].api_name].returns,js_returns:l[8].named_endpoints["/"+l[19].api_name].returns,is_running:yn,current_language:l[6]}}),{c(){e=z("div"),H(t.$$.fragment),n=U(),H(s.$$.fragment),i=U(),H(r.$$.fragment),a=U(),$(e,"class","endpoint-container svelte-1y3yua6")},m(o,u){v(o,e,u),V(t,e,null),f(e,n),V(s,e,null),f(e,i),V(r,e,null),f(e,a),c=!0},p(o,u){const _={};u&130&&(_.endpoint_parameters=o[7].named_endpoints["/"+o[19].api_name].parameters),u&2&&(_.dependency=o[19]),u&64&&(_.current_language=o[6]),u&1&&(_.root=o[0]),u&8&&(_.space_id=o[3]),u&16&&(_.username=o[4]),u&4&&(_.api_prefix=o[2].api_prefix),t.$set(_);const g={};u&130&&(g.endpoint_returns=o[7].named_endpoints["/"+o[19].api_name].parameters),u&258&&(g.js_returns=o[8].named_endpoints["/"+o[19].api_name].parameters),u&64&&(g.current_language=o[6]),s.$set(g);const d={};u&130&&(d.endpoint_returns=o[7].named_endpoints["/"+o[19].api_name].returns),u&258&&(d.js_returns=o[8].named_endpoints["/"+o[19].api_name].returns),u&64&&(d.current_language=o[6]),r.$set(d)},i(o){c||(I(t.$$.fragment,o),I(s.$$.fragment,o),I(r.$$.fragment,o),c=!0)},o(o){O(t.$$.fragment,o),O(s.$$.fragment,o),O(r.$$.fragment,o),c=!1},d(o){o&&b(e),W(t),W(s),W(r)}}}function wn(l){let e,t,n=l[19].show_api&&l[7].named_endpoints["/"+l[19].api_name]&&kn(l);return{c(){n&&n.c(),e=me()},m(s,i){n&&n.m(s,i),v(s,e,i),t=!0},p(s,i){s[19].show_api&&s[7].named_endpoints["/"+s[19].api_name]?n?(n.p(s,i),i&130&&I(n,1)):(n=kn(s),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(pe(),O(n,1,1,()=>{n=null}),de())},i(s){t||(I(n),t=!0)},o(s){O(n),t=!1},d(s){s&&b(e),n&&n.d(s)}}}function xs(l){let e,t,n=l[7]&&mn(l);return{c(){n&&n.c(),e=me()},m(s,i){n&&n.m(s,i),v(s,e,i),t=!0},p(s,[i]){s[7]?n?(n.p(s,i),i&128&&I(n,1)):(n=mn(s),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(pe(),O(n,1,1,()=>{n=null}),de())},i(s){t||(I(n),t=!0)},o(s){O(n),t=!1},d(s){s&&b(e),n&&n.d(s)}}}const rt="https://www.gradio.app/guides/getting-started-with-the-js-client",ot="https://www.gradio.app/guides/getting-started-with-the-python-client",wt="https://www.gradio.app/guides/querying-gradio-apps-with-curl",st="#connecting-to-a-hugging-face-space";let yn=!1;function ei(l,e,t){let{dependencies:n}=e,{root:s}=e,{app:i}=e,{space_id:r}=e,{root_node:a}=e,{username:c}=e,o=n.filter(C=>C.show_api).length;s===""&&(s=location.protocol+"//"+location.host+location.pathname),s.endsWith("/")||(s+="/");let{api_calls:u=[]}=e,_="python";const g=[["python",Ms],["javascript",Os],["bash",Fs]];async function d(){return await(await fetch(s.replace(/\/$/,"")+i.api_prefix+"/info")).json()}async function y(){return await i.view_api()}let h,E;d().then(C=>{t(7,h=C)}),y().then(C=>{t(8,E=C)});const p=De();Re(()=>(document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0),()=>{document.body.style.overflow="auto"}));function N(C){Ue.call(this,l,C)}const j=C=>t(6,_=C),w=()=>p("close",{api_recorder_visible:!0});function L(C){Ue.call(this,l,C)}return l.$$set=C=>{"dependencies"in C&&t(1,n=C.dependencies),"root"in C&&t(0,s=C.root),"app"in C&&t(2,i=C.app),"space_id"in C&&t(3,r=C.space_id),"root_node"in C&&t(12,a=C.root_node),"username"in C&&t(4,c=C.username),"api_calls"in C&&t(5,u=C.api_calls)},[s,n,i,r,c,u,_,h,E,o,g,p,a,N,j,w,L]}class ti extends be{constructor(e){super(),ve(this,e,ei,xs,we,{dependencies:1,root:0,app:2,space_id:3,root_node:12,username:4,api_calls:5})}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),P()}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),P()}get app(){return this.$$.ctx[2]}set app(e){this.$$set({app:e}),P()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),P()}get root_node(){return this.$$.ctx[12]}set root_node(e){this.$$set({root_node:e}),P()}get username(){return this.$$.ctx[4]}set username(e){this.$$set({username:e}),P()}get api_calls(){return this.$$.ctx[5]}set api_calls(e){this.$$set({api_calls:e}),P()}}function Cn(l){let e,t,n=l[1][l[0][l[0].length-1].fn_index].api_name+"",s;return{c(){e=z("span"),t=m("/"),s=m(n),$(e,"class","api-name svelte-sy28j6")},m(i,r){v(i,e,r),f(e,t),f(e,s)},p(i,r){r&3&&n!==(n=i[1][i[0][i[0].length-1].fn_index].api_name+"")&&D(s,n)},d(i){i&&b(e)}}}function ni(l){let e,t,n,s,i,r,a,c=l[0].length+"",o,u,_,g=l[0].length>0&&Cn(l);return{c(){e=z("div"),t=U(),n=z("p"),n.textContent="Recording API Calls:",s=U(),i=z("p"),r=z("span"),a=m("["),o=m(c),u=m("]"),_=U(),g&&g.c(),$(e,"class","loading-dot self-baseline svelte-sy28j6"),$(n,"class","self-baseline svelte-sy28j6"),$(r,"class","api-count svelte-sy28j6"),$(i,"class","self-baseline api-section svelte-sy28j6")},m(d,y){v(d,e,y),v(d,t,y),v(d,n,y),v(d,s,y),v(d,i,y),f(i,r),f(r,a),f(r,o),f(r,u),f(i,_),g&&g.m(i,null)},p(d,y){y&1&&c!==(c=d[0].length+"")&&D(o,c),d[0].length>0?g?g.p(d,y):(g=Cn(d),g.c(),g.m(i,null)):g&&(g.d(1),g=null)},d(d){d&&(b(e),b(t),b(n),b(s),b(i)),g&&g.d()}}}function li(l){let e,t,n;return t=new ct({props:{size:"sm",variant:"secondary",$$slots:{default:[ni]},$$scope:{ctx:l}}}),{c(){e=z("div"),H(t.$$.fragment),$(e,"id","api-recorder")},m(s,i){v(s,e,i),V(t,e,null),n=!0},p(s,[i]){const r={};i&7&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){n||(I(t.$$.fragment,s),n=!0)},o(s){O(t.$$.fragment,s),n=!1},d(s){s&&b(e),W(t)}}}function si(l,e,t){let{api_calls:n=[]}=e,{dependencies:s}=e;return l.$$set=i=>{"api_calls"in i&&t(0,n=i.api_calls),"dependencies"in i&&t(1,s=i.dependencies)},[n,s]}class ii extends be{constructor(e){super(),ve(this,e,si,li,we,{api_calls:0,dependencies:1})}get api_calls(){return this.$$.ctx[0]}set api_calls(e){this.$$set({api_calls:e}),P()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),P()}}const Gn="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3c!--%20Outer%20gear%20teeth%20(gray)%20--%3e%3cpath%20d='M19.14%2012.94c.04-.3.06-.61.06-.94%200-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24%200-.43.17-.47.41l-.36%202.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47%200-.59.22L2.74%208.87c-.12.21-.08.47.12.61l2.03%201.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03%201.58c-.18.14-.23.41-.12.61l1.92%203.32c.12.22.37.29.59.22l2.39-.96c.5.38%201.03.7%201.62.94l.36%202.54c.05.24.24.41.48.41h3.84c.24%200%20.44-.17.47-.41l.36-2.54c.59-.24%201.13-.56%201.62-.94l2.39.96c.22.08.47%200%20.59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12%2015.6c-1.98%200-3.6-1.62-3.6-3.6s1.62-3.6%203.6-3.6%203.6%201.62%203.6%203.6-1.62%203.6-3.6%203.6z'%20fill='%23808080'/%3e%3c!--%20Inner%20circle%20(now%20gray)%20--%3e%3ccircle%20cx='12'%20cy='12'%20r='2.5'%20fill='%23808080'/%3e%3c/svg%3e";function ri(l){let e,t,n,s,i,r=l[1]("common.settings")+"",a,c,o,u,_,g,d,y,h,E;return d=new Ct({}),{c(){e=z("h2"),t=z("img"),s=U(),i=z("div"),a=m(r),c=U(),o=z("div"),u=m(l[0]),_=U(),g=z("button"),H(d.$$.fragment),Fe(t.src,n=Gn)||$(t,"src",n),$(t,"alt",""),$(t,"class","svelte-1b2d8fn"),$(o,"class","url svelte-1b2d8fn"),$(i,"class","title svelte-1b2d8fn"),$(e,"class","svelte-1b2d8fn"),$(g,"class","svelte-1b2d8fn")},m(p,N){v(p,e,N),f(e,t),f(e,s),f(e,i),f(i,a),f(i,c),f(i,o),f(o,u),v(p,_,N),v(p,g,N),V(d,g,null),y=!0,h||(E=Ee(g,"click",l[3]),h=!0)},p(p,[N]){(!y||N&2)&&r!==(r=p[1]("common.settings")+"")&&D(a,r),(!y||N&1)&&D(u,p[0])},i(p){y||(I(d.$$.fragment,p),y=!0)},o(p){O(d.$$.fragment,p),y=!1},d(p){p&&(b(e),b(_),b(g)),W(d),h=!1,E()}}}function oi(l,e,t){let n;Ie(l,at,a=>t(1,n=a));let{root:s}=e;const i=De();yt();const r=()=>i("close");return l.$$set=a=>{"root"in a&&t(0,s=a.root)},[s,n,i,r]}class ai extends be{constructor(e){super(),ve(this,e,oi,ri,we,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),P()}}function $n(l){let e,t,n=l[5]("common.display_theme")+"",s,i,r,a,c,o,u,_,g,d,y,h,E,p,N,j;return{c(){e=z("div"),t=z("h2"),s=m(n),i=U(),r=z("p"),a=z("li"),c=z("button"),c.textContent="☀︎  Light",u=U(),_=z("li"),g=z("button"),g.textContent="⏾   Dark",y=U(),h=z("li"),E=z("button"),E.textContent="🖥︎  System",$(t,"class","svelte-npavty"),$(c,"class","svelte-npavty"),$(a,"class",o="theme-button "+(l[4]==="light"?"current-theme":"inactive-theme")+" svelte-npavty"),$(g,"class","svelte-npavty"),$(_,"class",d="theme-button "+(l[4]==="dark"?"current-theme":"inactive-theme")+" svelte-npavty"),$(E,"class","svelte-npavty"),$(h,"class",p="theme-button "+(l[4]==="system"?"current-theme":"inactive-theme")+" svelte-npavty"),$(r,"class","padded theme-buttons svelte-npavty"),$(e,"class","banner-wrap svelte-npavty")},m(w,L){v(w,e,L),f(e,t),f(t,s),f(e,i),f(e,r),f(r,a),f(a,c),f(r,u),f(r,_),f(_,g),f(r,y),f(r,h),f(h,E),N||(j=[Ee(a,"click",l[9]),Ee(_,"click",l[10]),Ee(h,"click",l[11])],N=!0)},p(w,L){L&32&&n!==(n=w[5]("common.display_theme")+"")&&D(s,n),L&16&&o!==(o="theme-button "+(w[4]==="light"?"current-theme":"inactive-theme")+" svelte-npavty")&&$(a,"class",o),L&16&&d!==(d="theme-button "+(w[4]==="dark"?"current-theme":"inactive-theme")+" svelte-npavty")&&$(_,"class",d),L&16&&p!==(p="theme-button "+(w[4]==="system"?"current-theme":"inactive-theme")+" svelte-npavty")&&$(h,"class",p)},d(w){w&&b(e),N=!1,bl(j)}}}function ci(l){let e,t,n;return{c(){e=m(`Progressive Web App is not enabled for this app. To enable it, start your
			Gradio app with `),t=z("code"),t.textContent="launch(pwa=True)",n=m(".")},m(s,i){v(s,e,i),v(s,t,i),v(s,n,i)},p:ge,d(s){s&&(b(e),b(t),b(n))}}}function fi(l){let e,t,n,s;return{c(){e=m("You can install this app as a Progressive Web App on your device. Visit "),t=z("a"),n=m(l[0]),s=m(" and click the install button in the URL address bar of your browser."),$(t,"href",l[0]),$(t,"target","_blank"),$(t,"class","svelte-npavty")},m(i,r){v(i,e,r),v(i,t,r),f(t,n),v(i,s,r)},p(i,r){r&1&&D(n,i[0]),r&1&&$(t,"href",i[0])},d(i){i&&(b(e),b(t),b(s))}}}function ui(l){let e,t,n,s,i,r,a=l[5]("common.language")+"",c,o,u,_,g,d,y,h=l[5]("common.pwa")+"",E,p,N,j;t=new ai({props:{root:l[0]}}),t.$on("close",l[8]);let w=l[1]===null&&$n(l);_=new Pl({props:{label:"Language",choices:hl,show_label:!1,root:l[0],value:l[3]}}),_.$on("change",l[7]);function L(T,S){return T[2]?fi:ci}let C=L(l),k=C(l);return{c(){e=z("div"),H(t.$$.fragment),n=U(),w&&w.c(),s=U(),i=z("div"),r=z("h2"),c=m(a),o=U(),u=z("p"),H(_.$$.fragment),g=U(),d=z("div"),y=z("h2"),E=m(h),p=U(),N=z("p"),k.c(),$(e,"class","banner-wrap svelte-npavty"),$(r,"class","svelte-npavty"),$(u,"class","padded svelte-npavty"),$(i,"class","banner-wrap svelte-npavty"),$(y,"class","svelte-npavty"),$(N,"class","padded svelte-npavty"),$(d,"class","banner-wrap svelte-npavty")},m(T,S){v(T,e,S),V(t,e,null),v(T,n,S),w&&w.m(T,S),v(T,s,S),v(T,i,S),f(i,r),f(r,c),f(i,o),f(i,u),V(_,u,null),v(T,g,S),v(T,d,S),f(d,y),f(y,E),f(d,p),f(d,N),k.m(N,null),j=!0},p(T,[S]){const q={};S&1&&(q.root=T[0]),t.$set(q),T[1]===null?w?w.p(T,S):(w=$n(T),w.c(),w.m(s.parentNode,s)):w&&(w.d(1),w=null),(!j||S&32)&&a!==(a=T[5]("common.language")+"")&&D(c,a);const M={};S&1&&(M.root=T[0]),S&8&&(M.value=T[3]),_.$set(M),(!j||S&32)&&h!==(h=T[5]("common.pwa")+"")&&D(E,h),C===(C=L(T))&&k?k.p(T,S):(k.d(1),k=C(T),k&&(k.c(),k.m(N,null)))},i(T){j||(I(t.$$.fragment,T),I(_.$$.fragment,T),j=!0)},o(T){O(t.$$.fragment,T),O(_.$$.fragment,T),j=!1},d(T){T&&(b(e),b(n),b(s),b(i),b(g),b(d)),W(t),w&&w.d(T),W(_),k.d()}}}function _i(l,e,t){let n;Ie(l,at,h=>t(5,n=h));let{root:s}=e,{space_id:i}=e,{pwa_enabled:r}=e;s===""&&(s=location.protocol+"//"+location.host+location.pathname),s.endsWith("/")||(s+="/");function a(h){const E=new URL(window.location.href);h==="system"?(E.searchParams.delete("__theme"),t(4,o="system")):(E.searchParams.set("__theme",h),t(4,o=h)),window.location.href=E.toString()}Re(()=>{document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0);const E=new URL(window.location.href).searchParams.get("__theme");return t(4,o=E||"system"),()=>{document.body.style.overflow="auto"}});let c,o="system";gl.subscribe(h=>{h&&t(3,c=h)});function u(h){const E=h.detail;vl(E)}yt();function _(h){Ue.call(this,l,h)}const g=()=>a("light"),d=()=>a("dark"),y=()=>a("system");return l.$$set=h=>{"root"in h&&t(0,s=h.root),"space_id"in h&&t(1,i=h.space_id),"pwa_enabled"in h&&t(2,r=h.pwa_enabled)},[s,i,r,c,o,n,a,u,_,g,d,y]}class pi extends be{constructor(e){super(),ve(this,e,_i,ui,we,{root:0,space_id:1,pwa_enabled:2})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),P()}get space_id(){return this.$$.ctx[1]}set space_id(e){this.$$set({space_id:e}),P()}get pwa_enabled(){return this.$$.ctx[2]}set pwa_enabled(e){this.$$set({pwa_enabled:e}),P()}}const di=kl(at);function mi(l){let e;const t=l[12].default,n=Cl(t,l,l[16],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,i){n&&n.p&&(!e||i&65536)&&$l(n,t,s,s[16],e?zl(t,s[16],i,null):El(s[16]),null)},i(s){e||(I(n,s),e=!0)},o(s){O(n,s),e=!1},d(s){n&&n.d(s)}}}function hi(l){let e,t,n,s;const i=[{elem_id:l[5]},{elem_classes:l[6]},{target:l[3]},{visible:l[7]},l[9],{theme_mode:l[4]},{root:l[2]}];function r(o){l[14](o)}var a=l[8];function c(o,u){let _={$$slots:{default:[mi]},$$scope:{ctx:o}};for(let g=0;g<i.length;g+=1)_=Ye(_,i[g]);return u!==void 0&&u&764&&(_=Ye(_,ht(i,[u&32&&{elem_id:o[5]},u&64&&{elem_classes:o[6]},u&8&&{target:o[3]},u&128&&{visible:o[7]},u&512&&gt(o[9]),u&16&&{theme_mode:o[4]},u&4&&{root:o[2]}]))),o[1]!==void 0&&(_.value=o[1]),{props:_}}return a&&(e=jt(a,c(l)),l[13](e),ze.push(()=>Je(e,"value",r)),e.$on("prop_change",l[15])),{c(){e&&H(e.$$.fragment),n=me()},m(o,u){e&&V(e,o,u),v(o,n,u),s=!0},p(o,[u]){if(a!==(a=o[8])){if(e){pe();const _=e;O(_.$$.fragment,1,0,()=>{W(_,1)}),de()}a?(e=jt(a,c(o,u)),o[13](e),ze.push(()=>Je(e,"value",r)),e.$on("prop_change",o[15]),H(e.$$.fragment),I(e.$$.fragment,1),V(e,n.parentNode,n)):e=null}else if(a){const _=u&764?ht(i,[u&32&&{elem_id:o[5]},u&64&&{elem_classes:o[6]},u&8&&{target:o[3]},u&128&&{visible:o[7]},u&512&&gt(o[9]),u&16&&{theme_mode:o[4]},u&4&&{root:o[2]}]):{};u&65536&&(_.$$scope={dirty:u,ctx:o}),!t&&u&2&&(t=!0,_.value=o[1],bt(()=>t=!1)),e.$set(_)}},i(o){s||(e&&I(e.$$.fragment,o),s=!0)},o(o){e&&O(e.$$.fragment,o),s=!1},d(o){o&&b(n),l[13](null),e&&W(e,o)}}}function gi(l,e,t){const n=["root","component","target","theme_mode","instance","value","elem_id","elem_classes","_id","visible"];let s=qt(e,n),{$$slots:i={},$$scope:r}=e,{root:a}=e,{component:c}=e,{target:o}=e,{theme_mode:u}=e,{instance:_}=e,{value:g}=e,{elem_id:d}=e,{elem_classes:y}=e,{_id:h}=e,{visible:E}=e;const p=(k,T,S)=>new CustomEvent("prop_change",{detail:{id:k,prop:T,value:S}});function N(k){return new Proxy(k,{construct(S,q){const M=new S(...q),R=Object.keys(M.$$.props);function F(G){return function(K){if(!o)return;const he=p(h,G,K);o.dispatchEvent(he)}}return R.forEach(G=>{ze.push(()=>Je(M,G,F(G)))}),M}})}const j=N(c);function w(k){ze[k?"unshift":"push"](()=>{_=k,t(0,_)})}function L(k){g=k,t(1,g)}function C(k){Ue.call(this,l,k)}return l.$$set=k=>{e=Ye(Ye({},e),yl(k)),t(9,s=qt(e,n)),"root"in k&&t(2,a=k.root),"component"in k&&t(10,c=k.component),"target"in k&&t(3,o=k.target),"theme_mode"in k&&t(4,u=k.theme_mode),"instance"in k&&t(0,_=k.instance),"value"in k&&t(1,g=k.value),"elem_id"in k&&t(5,d=k.elem_id),"elem_classes"in k&&t(6,y=k.elem_classes),"_id"in k&&t(11,h=k._id),"visible"in k&&t(7,E=k.visible),"$$scope"in k&&t(16,r=k.$$scope)},[_,g,a,o,u,d,y,E,j,s,c,h,i,w,L,C,r]}class bi extends be{constructor(e){super(),ve(this,e,gi,hi,wl,{root:2,component:10,target:3,theme_mode:4,instance:0,value:1,elem_id:5,elem_classes:6,_id:11,visible:7})}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),P()}get component(){return this.$$.ctx[10]}set component(e){this.$$set({component:e}),P()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),P()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),P()}get instance(){return this.$$.ctx[0]}set instance(e){this.$$set({instance:e}),P()}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),P()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),P()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),P()}get _id(){return this.$$.ctx[11]}set _id(e){this.$$set({_id:e}),P()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),P()}}function En(l,e,t){const n=l.slice();return n[15]=e[t],n}function zn(l){let e=[],t=new Map,n,s,i=oe(l[0].children);const r=a=>a[15].id;for(let a=0;a<i.length;a+=1){let c=En(l,i,a),o=r(c);t.set(o,e[a]=Nn(o,c))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=me()},m(a,c){for(let o=0;o<e.length;o+=1)e[o]&&e[o].m(a,c);v(a,n,c),s=!0},p(a,c){c&63&&(i=oe(a[0].children),pe(),e=jl(e,c,r,1,a,i,t,n.parentNode,ql,Nn,n,En),de())},i(a){if(!s){for(let c=0;c<i.length;c+=1)I(e[c]);s=!0}},o(a){for(let c=0;c<e.length;c+=1)O(e[c]);s=!1},d(a){a&&b(n);for(let c=0;c<e.length;c+=1)e[c].d(a)}}}function Nn(l,e){let t,n,s;return n=new Hn({props:{node:e[15],component:e[15].component,target:e[2],id:e[15].id,root:e[1],theme_mode:e[3],max_file_size:e[4],client:e[5]}}),n.$on("destroy",e[9]),n.$on("mount",e[10]),{key:l,first:null,c(){t=me(),H(n.$$.fragment),this.first=t},m(i,r){v(i,t,r),V(n,i,r),s=!0},p(i,r){e=i;const a={};r&1&&(a.node=e[15]),r&1&&(a.component=e[15].component),r&4&&(a.target=e[2]),r&1&&(a.id=e[15].id),r&2&&(a.root=e[1]),r&8&&(a.theme_mode=e[3]),r&16&&(a.max_file_size=e[4]),r&32&&(a.client=e[5]),n.$set(a)},i(i){s||(I(n.$$.fragment,i),s=!0)},o(i){O(n.$$.fragment,i),s=!1},d(i){i&&b(t),W(n,i)}}}function vi(l){let e,t,n=l[0].children&&l[0].children.length&&zn(l);return{c(){n&&n.c(),e=me()},m(s,i){n&&n.m(s,i),v(s,e,i),t=!0},p(s,i){s[0].children&&s[0].children.length?n?(n.p(s,i),i&1&&I(n,1)):(n=zn(s),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(pe(),O(n,1,1,()=>{n=null}),de())},i(s){t||(I(n),t=!0)},o(s){O(n),t=!1},d(s){s&&b(e),n&&n.d(s)}}}function ki(l){let e,t,n,s;const i=[{_id:l[0]?.id},{component:l[0].component},{elem_id:"elem_id"in l[0].props&&l[0].props.elem_id||`component-${l[0].id}`},{elem_classes:"elem_classes"in l[0].props&&l[0].props.elem_classes||[]},{target:l[2]},l[0].props,{theme_mode:l[3]},{root:l[1]},{visible:typeof l[0].props.visible=="boolean"?l[0].props.visible:!0}];function r(o){l[11](o)}function a(o){l[12](o)}let c={$$slots:{default:[vi]},$$scope:{ctx:l}};for(let o=0;o<i.length;o+=1)c=Ye(c,i[o]);return l[0].instance!==void 0&&(c.instance=l[0].instance),l[0].props.value!==void 0&&(c.value=l[0].props.value),e=new bi({props:c}),ze.push(()=>Je(e,"instance",r)),ze.push(()=>Je(e,"value",a)),{c(){H(e.$$.fragment)},m(o,u){V(e,o,u),s=!0},p(o,[u]){const _=u&15?ht(i,[u&1&&{_id:o[0]?.id},u&1&&{component:o[0].component},u&1&&{elem_id:"elem_id"in o[0].props&&o[0].props.elem_id||`component-${o[0].id}`},u&1&&{elem_classes:"elem_classes"in o[0].props&&o[0].props.elem_classes||[]},u&4&&{target:o[2]},u&1&&gt(o[0].props),u&8&&{theme_mode:o[3]},u&2&&{root:o[1]},u&1&&{visible:typeof o[0].props.visible=="boolean"?o[0].props.visible:!0}]):{};u&262207&&(_.$$scope={dirty:u,ctx:o}),!t&&u&1&&(t=!0,_.instance=o[0].instance,bt(()=>t=!1)),!n&&u&1&&(n=!0,_.value=o[0].props.value,bt(()=>n=!1)),e.$set(_)},i(o){s||(I(e.$$.fragment,o),s=!0)},o(o){O(e.$$.fragment,o),s=!1},d(o){W(e,o)}}}function wi(l,e,t){let{root:n}=e,{node:s}=e,{parent:i=null}=e,{target:r}=e,{theme_mode:a}=e,{version:c}=e,{autoscroll:o}=e,{max_file_size:u}=e,{client:_}=e;const g=De();let d=[];Re(()=>{g("mount",s.id);for(const N of d)g("mount",N.id);return()=>{g("destroy",s.id);for(const N of d)g("mount",N.id)}}),Nl("BLOCK_KEY",i);function y(N){Ue.call(this,l,N)}function h(N){Ue.call(this,l,N)}function E(N){l.$$.not_equal(s.instance,N)&&(s.instance=N,t(0,s),t(14,d),t(2,r),t(3,a),t(7,c),t(1,n),t(8,o),t(4,u),t(5,_))}function p(N){l.$$.not_equal(s.props.value,N)&&(s.props.value=N,t(0,s),t(14,d),t(2,r),t(3,a),t(7,c),t(1,n),t(8,o),t(4,u),t(5,_))}return l.$$set=N=>{"root"in N&&t(1,n=N.root),"node"in N&&t(0,s=N.node),"parent"in N&&t(6,i=N.parent),"target"in N&&t(2,r=N.target),"theme_mode"in N&&t(3,a=N.theme_mode),"version"in N&&t(7,c=N.version),"autoscroll"in N&&t(8,o=N.autoscroll),"max_file_size"in N&&t(4,u=N.max_file_size),"client"in N&&t(5,_=N.client)},l.$$.update=()=>{l.$$.dirty&1&&s&&t(0,s.children=s.children&&s.children.filter(N=>{const j=s.type!=="statustracker";return j||d.push(N),j}),s),l.$$.dirty&1&&s&&s.type==="form"&&(s.children?.every(N=>!N.props.visible)?t(0,s.props.visible=!1,s):t(0,s.props.visible=!0,s)),l.$$.dirty&447&&t(0,s.props.gradio=new Ml(s.id,r,a,c,n,o,u,di,_,Ll),s)},[s,n,r,a,u,_,i,c,o,y,h,E,p]}class Hn extends be{constructor(e){super(),ve(this,e,wi,ki,we,{root:1,node:0,parent:6,target:2,theme_mode:3,version:7,autoscroll:8,max_file_size:4,client:5})}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),P()}get node(){return this.$$.ctx[0]}set node(e){this.$$set({node:e}),P()}get parent(){return this.$$.ctx[6]}set parent(e){this.$$set({parent:e}),P()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),P()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),P()}get version(){return this.$$.ctx[7]}set version(e){this.$$set({version:e}),P()}get autoscroll(){return this.$$.ctx[8]}set autoscroll(e){this.$$set({autoscroll:e}),P()}get max_file_size(){return this.$$.ctx[4]}set max_file_size(e){this.$$set({max_file_size:e}),P()}get client(){return this.$$.ctx[5]}set client(e){this.$$set({client:e}),P()}}function Ln(l){let e,t;return e=new Hn({props:{node:l[0],root:l[1],target:l[2],theme_mode:l[3],version:l[4],autoscroll:l[5],max_file_size:l[6],client:l[7]}}),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s&1&&(i.node=n[0]),s&2&&(i.root=n[1]),s&4&&(i.target=n[2]),s&8&&(i.theme_mode=n[3]),s&16&&(i.version=n[4]),s&32&&(i.autoscroll=n[5]),s&64&&(i.max_file_size=n[6]),s&128&&(i.client=n[7]),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function yi(l){let e,t,n=l[0]&&Ln(l);return{c(){n&&n.c(),e=me()},m(s,i){n&&n.m(s,i),v(s,e,i),t=!0},p(s,[i]){s[0]?n?(n.p(s,i),i&1&&I(n,1)):(n=Ln(s),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(pe(),O(n,1,1,()=>{n=null}),de())},i(s){t||(I(n),t=!0)},o(s){O(n),t=!1},d(s){s&&b(e),n&&n.d(s)}}}function Ci(l,e,t){let{rootNode:n}=e,{root:s}=e,{target:i}=e,{theme_mode:r}=e,{version:a}=e,{autoscroll:c}=e,{max_file_size:o=null}=e,{client:u}=e;const _=De();return Re(()=>{_("mount")}),l.$$set=g=>{"rootNode"in g&&t(0,n=g.rootNode),"root"in g&&t(1,s=g.root),"target"in g&&t(2,i=g.target),"theme_mode"in g&&t(3,r=g.theme_mode),"version"in g&&t(4,a=g.version),"autoscroll"in g&&t(5,c=g.autoscroll),"max_file_size"in g&&t(6,o=g.max_file_size),"client"in g&&t(7,u=g.client)},[n,s,i,r,a,c,o,u]}class $i extends be{constructor(e){super(),ve(this,e,Ci,yi,we,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}get rootNode(){return this.$$.ctx[0]}set rootNode(e){this.$$set({rootNode:e}),P()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),P()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),P()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),P()}get version(){return this.$$.ctx[4]}set version(e){this.$$set({version:e}),P()}get autoscroll(){return this.$$.ctx[5]}set autoscroll(e){this.$$set({autoscroll:e}),P()}get max_file_size(){return this.$$.ctx[6]}set max_file_size(e){this.$$set({max_file_size:e}),P()}get client(){return this.$$.ctx[7]}set client(e){this.$$set({client:e}),P()}}const Ei="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",{document:Qe}=Tl;function jn(l){return Qe.title=l[2],{c:ge,m:ge,d:ge}}function qn(l){let e,t=`<style>${At(l[15],l[12])}</style>`,n;return{c(){e=new Sl(!1),n=me(),e.a=n},m(s,i){e.m(t,s,i),v(s,n,i)},p(s,i){i[0]&36864&&t!==(t=`<style>${At(s[15],s[12])}</style>`)&&e.p(t)},d(s){s&&(b(n),e.d())}}}function An(l){let e,t;return e=new $i({props:{rootNode:l[16],root:l[0],target:l[3],theme_mode:l[9],version:l[12],autoscroll:l[4],max_file_size:l[14],client:l[10]}}),e.$on("mount",l[30]),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&65536&&(i.rootNode=n[16]),s[0]&1&&(i.root=n[0]),s[0]&8&&(i.target=n[3]),s[0]&512&&(i.theme_mode=n[9]),s[0]&4096&&(i.version=n[12]),s[0]&16&&(i.autoscroll=n[4]),s[0]&16384&&(i.max_file_size=n[14]),s[0]&1024&&(i.client=n[10]),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function Tn(l){let e,t,n,s=l[22]("common.built_with_gradio")+"",i,r,a,c,o,u,_,g,d,y=l[22]("common.settings")+"",h,E,p,N,j,w,L,C=l[5]&&Sn(l);return{c(){e=z("footer"),C&&C.c(),t=U(),n=z("a"),i=m(s),r=U(),a=z("img"),u=U(),_=z("div"),_.textContent="·",g=U(),d=z("button"),h=m(y),E=U(),p=z("img"),Fe(a.src,c=Ei)||$(a,"src",c),$(a,"alt",o=l[22]("common.logo")),$(a,"class","svelte-1byz9vf"),$(n,"href","https://gradio.app"),$(n,"class","built-with svelte-1byz9vf"),$(n,"target","_blank"),$(n,"rel","noreferrer"),$(_,"class","divider svelte-1byz9vf"),Fe(p.src,N=Gn)||$(p,"src",N),$(p,"alt",j=l[22]("common.settings")),$(p,"class","svelte-1byz9vf"),$(d,"class","settings svelte-1byz9vf"),$(e,"class","svelte-1byz9vf")},m(k,T){v(k,e,T),C&&C.m(e,null),f(e,t),f(e,n),f(n,i),f(n,r),f(n,a),f(e,u),f(e,_),f(e,g),f(e,d),f(d,h),f(d,E),f(d,p),w||(L=Ee(d,"click",l[44]),w=!0)},p(k,T){k[5]?C?C.p(k,T):(C=Sn(k),C.c(),C.m(e,t)):C&&(C.d(1),C=null),T[0]&4194304&&s!==(s=k[22]("common.built_with_gradio")+"")&&D(i,s),T[0]&4194304&&o!==(o=k[22]("common.logo"))&&$(a,"alt",o),T[0]&4194304&&y!==(y=k[22]("common.settings")+"")&&D(h,y),T[0]&4194304&&j!==(j=k[22]("common.settings"))&&$(p,"alt",j)},d(k){k&&b(e),C&&C.d(),w=!1,L()}}}function Sn(l){let e,t=l[22]("errors.use_via_api")+"",n,s,i,r,a,c,o,u,_;return{c(){e=z("button"),n=m(t),s=U(),i=z("img"),c=U(),o=z("div"),o.textContent="·",Fe(i.src,r=Dn)||$(i,"src",r),$(i,"alt",a=l[22]("common.logo")),$(i,"class","svelte-1byz9vf"),$(e,"class","show-api svelte-1byz9vf"),$(o,"class","divider show-api-divider svelte-1byz9vf")},m(g,d){v(g,e,d),f(e,n),f(e,s),f(e,i),v(g,c,d),v(g,o,d),u||(_=Ee(e,"click",l[43]),u=!0)},p(g,d){d[0]&4194304&&t!==(t=g[22]("errors.use_via_api")+"")&&D(n,t),d[0]&4194304&&a!==(a=g[22]("common.logo"))&&$(i,"alt",a)},d(g){g&&(b(e),b(c),b(o)),u=!1,_()}}}function Pn(l){let e,t,n,s,i;return t=new ii({props:{api_calls:l[20],dependencies:l[1]}}),{c(){e=z("div"),H(t.$$.fragment),$(e,"id","api-recorder-container"),$(e,"class","svelte-1byz9vf")},m(r,a){v(r,e,a),V(t,e,null),n=!0,s||(i=Ee(e,"click",l[45]),s=!0)},p(r,a){const c={};a[0]&1048576&&(c.api_calls=r[20]),a[0]&2&&(c.dependencies=r[1]),t.$set(c)},i(r){n||(I(t.$$.fragment,r),n=!0)},o(r){O(t.$$.fragment,r),n=!1},d(r){r&&b(e),W(t),s=!1,i()}}}function In(l){let e,t,n,s,i,r,a,c;return i=new ti({props:{root_node:l[16],dependencies:l[1],root:l[0],app:l[10],space_id:l[11],api_calls:l[20],username:l[13]}}),i.$on("close",l[47]),{c(){e=z("div"),t=z("div"),n=U(),s=z("div"),H(i.$$.fragment),$(t,"class","backdrop svelte-1byz9vf"),$(s,"class","api-docs-wrap svelte-1byz9vf"),$(e,"class","api-docs svelte-1byz9vf")},m(o,u){v(o,e,u),f(e,t),f(e,n),f(e,s),V(i,s,null),r=!0,a||(c=Ee(t,"click",l[46]),a=!0)},p(o,u){const _={};u[0]&65536&&(_.root_node=o[16]),u[0]&2&&(_.dependencies=o[1]),u[0]&1&&(_.root=o[0]),u[0]&1024&&(_.app=o[10]),u[0]&2048&&(_.space_id=o[11]),u[0]&1048576&&(_.api_calls=o[20]),u[0]&8192&&(_.username=o[13]),i.$set(_)},i(o){r||(I(i.$$.fragment,o),r=!0)},o(o){O(i.$$.fragment,o),r=!1},d(o){o&&b(e),W(i),a=!1,c()}}}function Mn(l){let e,t,n,s,i,r,a,c;return i=new pi({props:{pwa_enabled:l[10].config.pwa,root:l[0],space_id:l[11]}}),i.$on("close",l[49]),{c(){e=z("div"),t=z("div"),n=U(),s=z("div"),H(i.$$.fragment),$(t,"class","backdrop svelte-1byz9vf"),$(s,"class","api-docs-wrap svelte-1byz9vf"),$(e,"class","api-docs svelte-1byz9vf")},m(o,u){v(o,e,u),f(e,t),f(e,n),f(e,s),V(i,s,null),r=!0,a||(c=Ee(t,"click",l[48]),a=!0)},p(o,u){const _={};u[0]&1024&&(_.pwa_enabled=o[10].config.pwa),u[0]&1&&(_.root=o[0]),u[0]&2048&&(_.space_id=o[11]),i.$set(_)},i(o){r||(I(i.$$.fragment,o),r=!0)},o(o){O(i.$$.fragment,o),r=!1},d(o){o&&b(e),W(i),a=!1,c()}}}function On(l){let e,t;return e=new Il({props:{messages:l[21]}}),e.$on("close",l[29]),{c(){H(e.$$.fragment)},m(n,s){V(e,n,s),t=!0},p(n,s){const i={};s[0]&2097152&&(i.messages=n[21]),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){O(e.$$.fragment,n),t=!1},d(n){W(e,n)}}}function zi(l){let e,t,n,s,i,r,a,c,o,u,_,g,d=l[7]&&jn(l),y=l[15]&&qn(l),h=l[16]&&l[10].config&&An(l),E=l[6]&&Tn(l),p=l[19]&&Pn(l),N=l[17]&&l[16]&&In(l),j=l[18]&&l[16]&&l[10].config&&Mn(l),w=l[21]&&On(l);return{c(){d&&d.c(),e=me(),y&&y.c(),t=me(),n=U(),s=z("div"),i=z("div"),h&&h.c(),r=U(),E&&E.c(),a=U(),p&&p.c(),c=U(),N&&N.c(),o=U(),j&&j.c(),u=U(),w&&w.c(),_=me(),$(i,"class","contain svelte-1byz9vf"),re(i,"flex-grow",l[8]?"1":"auto"),$(s,"class","wrap svelte-1byz9vf"),re(s,"min-height",l[8]?"100%":"auto")},m(L,C){d&&d.m(Qe.head,null),f(Qe.head,e),y&&y.m(Qe.head,null),f(Qe.head,t),v(L,n,C),v(L,s,C),f(s,i),h&&h.m(i,null),f(s,r),E&&E.m(s,null),v(L,a,C),p&&p.m(L,C),v(L,c,C),N&&N.m(L,C),v(L,o,C),j&&j.m(L,C),v(L,u,C),w&&w.m(L,C),v(L,_,C),g=!0},p(L,C){L[7]?d||(d=jn(L),d.c(),d.m(e.parentNode,e)):d&&(d.d(1),d=null),L[15]?y?y.p(L,C):(y=qn(L),y.c(),y.m(t.parentNode,t)):y&&(y.d(1),y=null),L[16]&&L[10].config?h?(h.p(L,C),C[0]&66560&&I(h,1)):(h=An(L),h.c(),I(h,1),h.m(i,null)):h&&(pe(),O(h,1,1,()=>{h=null}),de()),C[0]&256&&re(i,"flex-grow",L[8]?"1":"auto"),L[6]?E?E.p(L,C):(E=Tn(L),E.c(),E.m(s,null)):E&&(E.d(1),E=null),C[0]&256&&re(s,"min-height",L[8]?"100%":"auto"),L[19]?p?(p.p(L,C),C[0]&524288&&I(p,1)):(p=Pn(L),p.c(),I(p,1),p.m(c.parentNode,c)):p&&(pe(),O(p,1,1,()=>{p=null}),de()),L[17]&&L[16]?N?(N.p(L,C),C[0]&196608&&I(N,1)):(N=In(L),N.c(),I(N,1),N.m(o.parentNode,o)):N&&(pe(),O(N,1,1,()=>{N=null}),de()),L[18]&&L[16]&&L[10].config?j?(j.p(L,C),C[0]&328704&&I(j,1)):(j=Mn(L),j.c(),I(j,1),j.m(u.parentNode,u)):j&&(pe(),O(j,1,1,()=>{j=null}),de()),L[21]?w?(w.p(L,C),C[0]&2097152&&I(w,1)):(w=On(L),w.c(),I(w,1),w.m(_.parentNode,_)):w&&(pe(),O(w,1,1,()=>{w=null}),de())},i(L){g||(I(h),I(p),I(N),I(j),I(w),g=!0)},o(L){O(h),O(p),O(N),O(j),O(w),g=!1},d(L){L&&(b(n),b(s),b(a),b(c),b(o),b(u),b(_)),d&&d.d(L),b(e),y&&y.d(L),b(t),h&&h.d(),E&&E.d(),p&&p.d(L),N&&N.d(L),j&&j.d(L),w&&w.d(L)}}}const Ni=/^'([^]+)'$/,Li=15,ji=10;function Fn(l){return"detail"in l}function qi(l,e,t){let n,s,i,r,a;Ie(l,at,A=>t(22,r=A)),yt();let{root:c}=e,{components:o}=e,{layout:u}=e,{dependencies:_}=e,{title:g="Gradio"}=e,{target:d}=e,{autoscroll:y}=e,{show_api:h=!0}=e,{show_footer:E=!0}=e,{control_page_title:p=!1}=e,{app_mode:N}=e,{theme_mode:j}=e,{app:w}=e,{space_id:L}=e,{version:C}=e,{js:k}=e,{fill_height:T=!1}=e,{ready:S}=e,{username:q}=e,{api_prefix:M=""}=e,{max_file_size:R=void 0}=e,{initial_layout:F=void 0}=e,{css:G=null}=e,{layout:K,targets:he,update_value:x,get_data:Y,modify_stream:ne,get_stream_state:Q,set_time_limit:se,loading_status:ke,scheduled_updates:ye,create_layout:Ke,rerender_layout:Vn}=Al(F);Ie(l,K,A=>t(16,a=A)),Ie(l,he,A=>t(56,s=A)),Ie(l,ke,A=>t(42,n=A)),Ie(l,ye,A=>t(57,i=A));let ft=_;async function Wn(){await Ke({components:o,layout:u,dependencies:_,root:c+M,app:w,options:{fill_height:T}})}let{search_params:Be}=e,ut=Be.get("view")==="api"&&h,xe=Be.get("view")==="settings",Ge=Be.get("view")==="api-recorder"&&h;function He(A){t(19,Ge=!1),t(17,ut=A);let J=new URLSearchParams(window.location.search);A?J.set("view","api"):J.delete("view"),history.replaceState(null,"","?"+J.toString())}function et(A){let J=new URLSearchParams(window.location.search);A?J.set("view","settings"):J.delete("view"),history.replaceState(null,"","?"+J.toString()),t(18,xe=!xe)}let tt=[],{render_complete:Ve=!1}=e;async function _t(A,J){const X=_.find(ce=>ce.id===J);if(!X)return;const ie=X.outputs,B=A?.map((ce,fe)=>({id:ie[fe],prop:"value_is_output",value:!0}));x(B),await Ze();const te=[];A?.forEach((ce,fe)=>{if(typeof ce=="object"&&ce!==null&&ce.__type__==="update")for(const[Te,Oe]of Object.entries(ce))Te!=="__type__"&&te.push({id:ie[fe],prop:Te,value:Oe});else te.push({id:ie[fe],prop:"value",value:ce})}),x(te),await Ze()}let Le=new Map,ae=[];function je(A,J,X,ie,B=10,te=!0){return{title:A,message:J,fn_index:X,type:ie,id:++Zn,duration:B,visible:te}}function $t(A,J,X){t(21,ae=[je(A,J,-1,X),...ae])}let Zn=-1,pt=!1;const Qn=r("blocks.long_requests_queue"),Jn=r("blocks.connection_can_break"),Yn=r("blocks.lost_connection"),Xn=r("blocks.waiting_for_inputs");let dt=!1,Et=!1,zt=!1,We=[];function Me(A,J=null,X=null){let ie=()=>{};function B(){ie()}i?ie=ye.subscribe(te=>{te||Ze().then(()=>{Nt(A,J,X),B()})}):Nt(A,J,X)}async function Kn(A,J,X){return A===J&&X&&X.is_value_data===!0?X.value:Y(A)}async function Nt(A,J=null,X=null){const ie=_.find(Z=>Z.id===A);if(ie===void 0)return;const B=ie;if(We.length>0){for(const Z of We)if(B.inputs.includes(Z)){$t("Warning",Xn,"warning");return}}const te=ke.get_status_for_fn(A);t(21,ae=ae.filter(({fn_index:Z})=>Z!==A)),(te==="pending"||te==="generating")&&(B.pending_request=!0);let ce=[];B.render_id!=null&&_.forEach((Z,Ce)=>{Z.rendered_in===B.render_id&&ce.push(Ce)}),ce.reverse().forEach(Z=>{_.splice(Z,1)});let fe={fn_index:A,data:await Promise.all(B.inputs.map(Z=>Kn(Z,J,X))),event_data:B.collects_event_data?X:null,trigger_id:J};B.frontend_fn&&typeof B.frontend_fn!="boolean"?B.frontend_fn(fe.data.concat(await Promise.all(B.outputs.map(Z=>Y(Z))))).then(Z=>{B.backend_fn?(fe.data=Z,Te(B,fe)):_t(Z,A)}):B.types.cancel&&B.cancels?await Promise.all(B.cancels.map(async Z=>{const Ce=Le.get(Z);return Ce?.cancel(),Ce})):B.backend_fn&&(B.js_implementation&&new Tt(`let result = await (${B.js_implementation})(...arguments);
						return (!Array.isArray(result)) ? [result] : result;`)(...fe.data).then(Ce=>{_t(Ce,A),fe.js_implementation=!0}).catch(Ce=>{console.error(Ce),fe.js_implementation=!1}),Te(B,fe));function Te(Z,Ce){Z.trigger_mode==="once"?Z.pending_request||Oe(Ce,Z.connection=="stream"):Z.trigger_mode==="multiple"?Oe(Ce,Z.connection=="stream"):Z.trigger_mode==="always_last"&&(Z.pending_request?Z.final_event=Ce:Oe(Ce,Z.connection=="stream"))}async function Oe(Z,Ce=!1){Ge&&t(20,tt=[...tt,JSON.parse(JSON.stringify(Z))]);let mt;if(w.set_current_payload(Z),Ce)if(!Le.has(A))B.inputs.forEach(le=>ne(le,"waiting"));else{if(Le.has(A)&&B.inputs.some(le=>Q(le)==="waiting"))return;if(Le.has(A)&&B.inputs.some(le=>Q(le)==="open")){await w.send_ws_message(`${w.config.root+w.config.api_prefix}/stream/${Le.get(A).event_id()}`,{...Z,session_hash:w.session_hash});return}}try{mt=w.submit(Z.fn_index,Z.data,Z.event_data,Z.trigger_id)}catch(le){if(w.closed)return;t(21,ae=[je("Error",String(le),0,"error"),...ae]),ke.update({status:"error",fn_index:0,eta:0,queue:!1,queue_position:null}),nt(n);return}Le.set(A,mt);for await(const le of mt){if(Z.js_implementation)return;le.type==="data"?ul(le):le.type==="render"?_l(le):le.type==="status"?ml(le):le.type==="log"&&pl(le)}function ul(le){const{data:_e,fn_index:ee}=le;B.pending_request&&B.final_event&&(B.pending_request=!1,Oe(B.final_event,B.connection=="stream")),B.pending_request=!1,_t(_e,ee),nt(n)}function _l(le){const{data:_e}=le;let ee=_e.components,ue=_e.layout,qe=_e.dependencies,Se=_e.render_id;qe.forEach(Pe=>{_.push(Pe)}),Vn({components:ee,layout:ue,root:c+M,dependencies:_,render_id:Se})}function pl(le){const{title:_e,log:ee,fn_index:ue,level:qe,duration:Se,visible:Pe}=le;t(21,ae=[je(_e,ee,ue,qe,Se,Pe),...ae])}function dl(le,_e,ee){le.original_msg==="process_starts"&&ee.connection==="stream"&&ne(_e,"open")}function ml(le){const{fn_index:_e,...ee}=le;if(ee.stage==="streaming"&&ee.time_limit&&B.inputs.forEach(ue=>{se(ue,ee.time_limit)}),B.inputs.forEach(ue=>{dl(le,ue,B)}),ke.update({...ee,time_limit:ee.time_limit,status:ee.stage,progress:ee.progress_data,fn_index:_e}),nt(n),!Et&&L!==null&&ee.position!==void 0&&ee.position>=2&&ee.eta!==void 0&&ee.eta>Li&&(Et=!0,t(21,ae=[je("Warning",Qn,_e,"warning"),...ae])),!zt&&dt&&ee.eta!==void 0&&ee.eta>ji&&(zt=!0,t(21,ae=[je("Warning",Jn,_e,"warning"),...ae])),ee.stage==="complete"||ee.stage==="generating"){const ue=new Set;ee.changed_state_ids?.forEach(qe=>{_.filter(Se=>Se.targets.some(([Pe,Ai])=>Pe===qe)).forEach(Se=>{ue.add(Se)})}),ue.forEach(qe=>{Me(qe.id,Z.trigger_id)})}if(ee.stage==="complete"&&(_.forEach(async ue=>{ue.trigger_after===_e&&Me(ue.id,Z.trigger_id)}),B.inputs.forEach(ue=>{ne(ue,"closed")}),Le.delete(A)),ee.broken&&dt&&pt)window.setTimeout(()=>{t(21,ae=[je("Error",Yn,_e,"error"),...ae])},0),Me(B.id,Z.trigger_id,X),pt=!1;else if(ee.stage==="error"){if(ee.message){const ue=ee.message.replace(Ni,(Se,Pe)=>Pe),qe=ee.title??"Error";t(21,ae=[je(qe,ue,_e,"error",ee.duration,ee.visible),...ae])}_.map(async ue=>{ue.trigger_after===_e&&!ue.trigger_only_on_success&&Me(ue.id,Z.trigger_id)})}}}}function xn(A,J){if(L===null)return;const X=new URL(`https://huggingface.co/spaces/${L}/discussions/new`);A!==void 0&&A.length>0&&X.searchParams.set("title",A),X.searchParams.set("description",J),window.open(X.toString(),"_blank")}function el(A){const J=A.detail;t(21,ae=ae.filter(X=>X.id!==J))}const tl=A=>!!(A&&new URL(A,location.href).origin!==location.origin);async function nl(){k&&await new Tt(`let result = await (${k})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await Ze();for(var A=d.getElementsByTagName("a"),J=0;J<A.length;J++){const X=A[J].getAttribute("target"),ie=A[J].getAttribute("href");tl(ie)&&X!=="_blank"&&A[J].setAttribute("target","_blank")}Lt(),!(!d||Ve)&&(d.addEventListener("prop_change",X=>{if(!Fn(X))throw new Error("not a custom event");const{id:ie,prop:B,value:te}=X.detail;x([{id:ie,prop:B,value:te}]),B==="input_ready"&&te===!1&&We.push(ie),B==="input_ready"&&te===!0&&(We=We.filter(ce=>ce!==ie))}),d.addEventListener("gradio",X=>{if(!Fn(X))throw new Error("not a custom event");const{id:ie,event:B,data:te}=X.detail;if(B==="share"){const{title:ce,description:fe}=te;xn(ce,fe)}else B==="error"?t(21,ae=[je("Error",te,-1,B),...ae]):B==="warning"?t(21,ae=[je("Warning",te,-1,B),...ae]):B=="clear_status"?ll(ie,"complete",te):B=="close_stream"?s[ie]?.[te]?.forEach(fe=>{if(Le.has(fe)){const Te=`${w.config.root+w.config.api_prefix}/stream/${Le.get(fe).event_id()}`;w.post_data(`${Te}/close`,{}),w.close_ws(Te)}}):s[ie]?.[B]?.forEach(fe=>{requestAnimationFrame(()=>{Me(fe,ie,te)})})}),t(31,Ve=!0))}const Lt=()=>{_.forEach(A=>{A.targets.some(J=>J[1]==="load")&&Me(A.id)})};function ll(A,J,X){X.status=J,x([{id:A,prop:"loading_status",value:X}])}function nt(A){let J=[];Object.entries(A).forEach(([B,te])=>{if(w.closed&&te.status==="error")return;let ce=_.find(fe=>fe.id==te.fn_index);ce!==void 0&&(te.scroll_to_output=ce.scroll_to_output,te.show_progress=ce.show_progress,J.push({id:parseInt(B),prop:"loading_status",value:te}))});const X=ke.get_inputs_to_update(),ie=Array.from(X).map(([B,te])=>({id:B,prop:"pending",value:te==="pending"}));x([...J,...ie])}Re(()=>{document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&(pt=!0)}),dt=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)});const sl=()=>{He(!ut)},il=()=>{et(!xe)},rl=()=>{He(!0),t(19,Ge=!1)},ol=()=>{He(!1)},al=A=>{He(!1),t(20,tt=[]),t(19,Ge=A.detail?.api_recorder_visible)},cl=()=>{et(!1)},fl=A=>{et(!1)};return l.$$set=A=>{"root"in A&&t(0,c=A.root),"components"in A&&t(33,o=A.components),"layout"in A&&t(34,u=A.layout),"dependencies"in A&&t(1,_=A.dependencies),"title"in A&&t(2,g=A.title),"target"in A&&t(3,d=A.target),"autoscroll"in A&&t(4,y=A.autoscroll),"show_api"in A&&t(5,h=A.show_api),"show_footer"in A&&t(6,E=A.show_footer),"control_page_title"in A&&t(7,p=A.control_page_title),"app_mode"in A&&t(8,N=A.app_mode),"theme_mode"in A&&t(9,j=A.theme_mode),"app"in A&&t(10,w=A.app),"space_id"in A&&t(11,L=A.space_id),"version"in A&&t(12,C=A.version),"js"in A&&t(35,k=A.js),"fill_height"in A&&t(36,T=A.fill_height),"ready"in A&&t(32,S=A.ready),"username"in A&&t(13,q=A.username),"api_prefix"in A&&t(37,M=A.api_prefix),"max_file_size"in A&&t(14,R=A.max_file_size),"initial_layout"in A&&t(38,F=A.initial_layout),"css"in A&&t(15,G=A.css),"search_params"in A&&t(39,Be=A.search_params),"render_complete"in A&&t(31,Ve=A.render_complete)},l.$$.update=()=>{l.$$.dirty[0]&1035|l.$$.dirty[1]&44&&Wn(),l.$$.dirty[0]&65536&&t(32,S=!!a),l.$$.dirty[0]&2|l.$$.dirty[1]&1025&&_!==ft&&Ve&&(Lt(),t(41,ft=_)),l.$$.dirty[1]&2048&&nt(n)},[c,_,g,d,y,h,E,p,N,j,w,L,C,q,R,G,a,ut,xe,Ge,tt,ae,r,K,he,ke,ye,He,et,el,nl,Ve,S,o,u,k,T,M,F,Be,$t,ft,n,sl,il,rl,ol,al,cl,fl]}class Wi extends be{constructor(e){super(),ve(this,e,qi,zi,we,{root:0,components:33,layout:34,dependencies:1,title:2,target:3,autoscroll:4,show_api:5,show_footer:6,control_page_title:7,app_mode:8,theme_mode:9,app:10,space_id:11,version:12,js:35,fill_height:36,ready:32,username:13,api_prefix:37,max_file_size:14,initial_layout:38,css:15,search_params:39,render_complete:31,add_new_message:40},null,[-1,-1,-1])}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),P()}get components(){return this.$$.ctx[33]}set components(e){this.$$set({components:e}),P()}get layout(){return this.$$.ctx[34]}set layout(e){this.$$set({layout:e}),P()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),P()}get title(){return this.$$.ctx[2]}set title(e){this.$$set({title:e}),P()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),P()}get autoscroll(){return this.$$.ctx[4]}set autoscroll(e){this.$$set({autoscroll:e}),P()}get show_api(){return this.$$.ctx[5]}set show_api(e){this.$$set({show_api:e}),P()}get show_footer(){return this.$$.ctx[6]}set show_footer(e){this.$$set({show_footer:e}),P()}get control_page_title(){return this.$$.ctx[7]}set control_page_title(e){this.$$set({control_page_title:e}),P()}get app_mode(){return this.$$.ctx[8]}set app_mode(e){this.$$set({app_mode:e}),P()}get theme_mode(){return this.$$.ctx[9]}set theme_mode(e){this.$$set({theme_mode:e}),P()}get app(){return this.$$.ctx[10]}set app(e){this.$$set({app:e}),P()}get space_id(){return this.$$.ctx[11]}set space_id(e){this.$$set({space_id:e}),P()}get version(){return this.$$.ctx[12]}set version(e){this.$$set({version:e}),P()}get js(){return this.$$.ctx[35]}set js(e){this.$$set({js:e}),P()}get fill_height(){return this.$$.ctx[36]}set fill_height(e){this.$$set({fill_height:e}),P()}get ready(){return this.$$.ctx[32]}set ready(e){this.$$set({ready:e}),P()}get username(){return this.$$.ctx[13]}set username(e){this.$$set({username:e}),P()}get api_prefix(){return this.$$.ctx[37]}set api_prefix(e){this.$$set({api_prefix:e}),P()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),P()}get initial_layout(){return this.$$.ctx[38]}set initial_layout(e){this.$$set({initial_layout:e}),P()}get css(){return this.$$.ctx[15]}set css(e){this.$$set({css:e}),P()}get search_params(){return this.$$.ctx[39]}set search_params(e){this.$$set({search_params:e}),P()}get render_complete(){return this.$$.ctx[31]}set render_complete(e){this.$$set({render_complete:e}),P()}get add_new_message(){return this.$$.ctx[40]}}export{Wi as default};
//# sourceMappingURL=Blocks-CJZ_ykWE.js.map
