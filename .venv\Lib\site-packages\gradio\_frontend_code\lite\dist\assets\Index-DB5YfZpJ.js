import{a as J,i as K,s as L,f as r,y as N,b as O,c as I,z as k,A as q,$ as Y,d as D,C as Q,m as B,T as R,k as b,h as U,t as w,j as V,l as W,n as M,o as X,I as Z,B as y,Y as x,S as $,a0 as p,a6 as ee}from"../lite.js";import{c as E,a as te}from"./utils-BsGrhMNe.js";import{C as F}from"./Check-DbzZ-PD_.js";import{C as G}from"./Copy-DcTA0nce.js";import{M as se}from"./MarkdownCode-DVjr71R6.js";import{I as ie}from"./IconButtonWrapper-BqpIgNIH.js";import{default as ze}from"./Example-C73cqYto.js";function H(t){let e,i;return e=new ie({props:{$$slots:{default:[ne]},$$scope:{ctx:t}}}),{c(){I(e.$$.fragment)},m(s,a){B(e,s,a),i=!0},p(s,a){const _={};a&270336&&(_.$$scope={dirty:a,ctx:s}),e.$set(_)},i(s){i||(b(e.$$.fragment,s),i=!0)},o(s){w(e.$$.fragment,s),i=!1},d(s){M(e,s)}}}function ne(t){let e,i;return e=new Z({props:{Icon:t[13]?F:G,label:t[13]?"Copied conversation":"Copy conversation"}}),e.$on("click",t[14]),{c(){I(e.$$.fragment)},m(s,a){B(e,s,a),i=!0},p(s,a){const _={};a&8192&&(_.Icon=s[13]?F:G),a&8192&&(_.label=s[13]?"Copied conversation":"Copy conversation"),e.$set(_)},i(s){i||(b(e.$$.fragment,s),i=!0)},o(s){w(e.$$.fragment,s),i=!1},d(s){M(e,s)}}}function le(t){let e,i,s,a,_,c,g,l,o,f=t[10]&&H(t);return s=new se({props:{message:t[2],latex_delimiters:t[7],sanitize_html:t[5],line_breaks:t[6],chatbot:!1,header_links:t[8],root:t[11]}}),{c(){e=N("div"),f&&f.c(),i=O(),I(s.$$.fragment),k(e,"class",a="prose "+(t[0]?.join(" ")||"")+" svelte-lag733"),k(e,"data-testid","markdown"),k(e,"dir",_=t[4]?"rtl":"ltr"),k(e,"style",c=t[9]?`max-height: ${E(t[9])}; overflow-y: auto;`:""),q(e,"hide",!t[1]),Y(e,"min-height",t[3]&&t[12]?.status!=="pending"?E(t[3]):void 0)},m(n,m){D(n,e,m),f&&f.m(e,null),Q(e,i),B(s,e,null),g=!0,l||(o=R(te.call(null,e)),l=!0)},p(n,[m]){n[10]?f?(f.p(n,m),m&1024&&b(f,1)):(f=H(n),f.c(),b(f,1),f.m(e,i)):f&&(U(),w(f,1,1,()=>{f=null}),V());const d={};m&4&&(d.message=n[2]),m&128&&(d.latex_delimiters=n[7]),m&32&&(d.sanitize_html=n[5]),m&64&&(d.line_breaks=n[6]),m&256&&(d.header_links=n[8]),m&2048&&(d.root=n[11]),s.$set(d),(!g||m&1&&a!==(a="prose "+(n[0]?.join(" ")||"")+" svelte-lag733"))&&k(e,"class",a),(!g||m&16&&_!==(_=n[4]?"rtl":"ltr"))&&k(e,"dir",_),(!g||m&512&&c!==(c=n[9]?`max-height: ${E(n[9])}; overflow-y: auto;`:""))&&k(e,"style",c),(!g||m&3)&&q(e,"hide",!n[1]);const v=m&512;(m&4616||v)&&Y(e,"min-height",n[3]&&n[12]?.status!=="pending"?E(n[3]):void 0)},i(n){g||(b(f),b(s.$$.fragment,n),g=!0)},o(n){w(f),w(s.$$.fragment,n),g=!1},d(n){n&&W(e),f&&f.d(),M(s),l=!1,o()}}}function ae(t,e,i){let{elem_classes:s=[]}=e,{visible:a=!0}=e,{value:_}=e,{min_height:c=void 0}=e,{rtl:g=!1}=e,{sanitize_html:l=!0}=e,{line_breaks:o=!1}=e,{latex_delimiters:f}=e,{header_links:n=!1}=e,{height:m=void 0}=e,{show_copy_button:d=!1}=e,{root:v}=e,{loading_status:j=void 0}=e,z=!1,C;const T=X();async function S(){"clipboard"in navigator&&(await navigator.clipboard.writeText(_),T("copy",{value:_}),A())}function A(){i(13,z=!0),C&&clearTimeout(C),C=setTimeout(()=>{i(13,z=!1)},1e3)}return t.$$set=u=>{"elem_classes"in u&&i(0,s=u.elem_classes),"visible"in u&&i(1,a=u.visible),"value"in u&&i(2,_=u.value),"min_height"in u&&i(3,c=u.min_height),"rtl"in u&&i(4,g=u.rtl),"sanitize_html"in u&&i(5,l=u.sanitize_html),"line_breaks"in u&&i(6,o=u.line_breaks),"latex_delimiters"in u&&i(7,f=u.latex_delimiters),"header_links"in u&&i(8,n=u.header_links),"height"in u&&i(9,m=u.height),"show_copy_button"in u&&i(10,d=u.show_copy_button),"root"in u&&i(11,v=u.root),"loading_status"in u&&i(12,j=u.loading_status)},t.$$.update=()=>{t.$$.dirty&4&&T("change")},[s,a,_,c,g,l,o,f,n,m,d,v,j,z,S]}class he extends J{constructor(e){super(),K(this,e,ae,le,L,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8,height:9,show_copy_button:10,root:11,loading_status:12})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),r()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),r()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),r()}get rtl(){return this.$$.ctx[4]}set rtl(e){this.$$set({rtl:e}),r()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),r()}get line_breaks(){return this.$$.ctx[6]}set line_breaks(e){this.$$set({line_breaks:e}),r()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),r()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),r()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),r()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),r()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),r()}get loading_status(){return this.$$.ctx[12]}set loading_status(e){this.$$set({loading_status:e}),r()}}const _e=he;function re(t){let e,i,s,a,_;const c=[{autoscroll:t[8].autoscroll},{i18n:t[8].i18n},t[4],{variant:"center"}];let g={};for(let l=0;l<c.length;l+=1)g=x(g,c[l]);return e=new $({props:g}),e.$on("clear_status",t[17]),a=new _e({props:{value:t[3],elem_classes:t[1],visible:t[2],rtl:t[5],latex_delimiters:t[9],sanitize_html:t[6],line_breaks:t[7],header_links:t[10],show_copy_button:t[14],root:t[8].root,loading_status:t[4]}}),a.$on("change",t[18]),a.$on("copy",t[19]),{c(){I(e.$$.fragment),i=O(),s=N("div"),I(a.$$.fragment),k(s,"class","svelte-1ed2p3z"),q(s,"pending",t[4]?.status==="pending")},m(l,o){B(e,l,o),D(l,i,o),D(l,s,o),B(a,s,null),_=!0},p(l,o){const f=o&272?p(c,[o&256&&{autoscroll:l[8].autoscroll},o&256&&{i18n:l[8].i18n},o&16&&ee(l[4]),c[3]]):{};e.$set(f);const n={};o&8&&(n.value=l[3]),o&2&&(n.elem_classes=l[1]),o&4&&(n.visible=l[2]),o&32&&(n.rtl=l[5]),o&512&&(n.latex_delimiters=l[9]),o&64&&(n.sanitize_html=l[6]),o&128&&(n.line_breaks=l[7]),o&1024&&(n.header_links=l[10]),o&16384&&(n.show_copy_button=l[14]),o&256&&(n.root=l[8].root),o&16&&(n.loading_status=l[4]),a.$set(n),(!_||o&16)&&q(s,"pending",l[4]?.status==="pending")},i(l){_||(b(e.$$.fragment,l),b(a.$$.fragment,l),_=!0)},o(l){w(e.$$.fragment,l),w(a.$$.fragment,l),_=!1},d(l){l&&(W(i),W(s)),M(e,l),M(a)}}}function oe(t){let e,i;return e=new y({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],container:t[15],allow_overflow:!0,overflow_behavior:"auto",height:t[11],min_height:t[12],max_height:t[13],$$slots:{default:[re]},$$scope:{ctx:t}}}),{c(){I(e.$$.fragment)},m(s,a){B(e,s,a),i=!0},p(s,[a]){const _={};a&4&&(_.visible=s[2]),a&1&&(_.elem_id=s[0]),a&2&&(_.elem_classes=s[1]),a&32768&&(_.container=s[15]),a&2048&&(_.height=s[11]),a&4096&&(_.min_height=s[12]),a&8192&&(_.max_height=s[13]),a&1067006&&(_.$$scope={dirty:a,ctx:s}),e.$set(_)},i(s){i||(b(e.$$.fragment,s),i=!0)},o(s){w(e.$$.fragment,s),i=!1},d(s){M(e,s)}}}function ue(t,e,i){let{label:s}=e,{elem_id:a=""}=e,{elem_classes:_=[]}=e,{visible:c=!0}=e,{value:g=""}=e,{loading_status:l}=e,{rtl:o=!1}=e,{sanitize_html:f=!0}=e,{line_breaks:n=!1}=e,{gradio:m}=e,{latex_delimiters:d}=e,{header_links:v=!1}=e,{height:j}=e,{min_height:z}=e,{max_height:C}=e,{show_copy_button:T=!1}=e,{container:S=!1}=e;const A=()=>m.dispatch("clear_status",l),u=()=>m.dispatch("change"),P=h=>m.dispatch("copy",h.detail);return t.$$set=h=>{"label"in h&&i(16,s=h.label),"elem_id"in h&&i(0,a=h.elem_id),"elem_classes"in h&&i(1,_=h.elem_classes),"visible"in h&&i(2,c=h.visible),"value"in h&&i(3,g=h.value),"loading_status"in h&&i(4,l=h.loading_status),"rtl"in h&&i(5,o=h.rtl),"sanitize_html"in h&&i(6,f=h.sanitize_html),"line_breaks"in h&&i(7,n=h.line_breaks),"gradio"in h&&i(8,m=h.gradio),"latex_delimiters"in h&&i(9,d=h.latex_delimiters),"header_links"in h&&i(10,v=h.header_links),"height"in h&&i(11,j=h.height),"min_height"in h&&i(12,z=h.min_height),"max_height"in h&&i(13,C=h.max_height),"show_copy_button"in h&&i(14,T=h.show_copy_button),"container"in h&&i(15,S=h.container)},t.$$.update=()=>{t.$$.dirty&65792&&m.dispatch("change")},[a,_,c,g,l,o,f,n,m,d,v,j,z,C,T,S,s,A,u,P]}class ke extends J{constructor(e){super(),K(this,e,ue,oe,L,{label:16,elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9,header_links:10,height:11,min_height:12,max_height:13,show_copy_button:14,container:15})}get label(){return this.$$.ctx[16]}set label(e){this.$$set({label:e}),r()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),r()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),r()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),r()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),r()}get rtl(){return this.$$.ctx[5]}set rtl(e){this.$$set({rtl:e}),r()}get sanitize_html(){return this.$$.ctx[6]}set sanitize_html(e){this.$$set({sanitize_html:e}),r()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),r()}get gradio(){return this.$$.ctx[8]}set gradio(e){this.$$set({gradio:e}),r()}get latex_delimiters(){return this.$$.ctx[9]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),r()}get header_links(){return this.$$.ctx[10]}set header_links(e){this.$$set({header_links:e}),r()}get height(){return this.$$.ctx[11]}set height(e){this.$$set({height:e}),r()}get min_height(){return this.$$.ctx[12]}set min_height(e){this.$$set({min_height:e}),r()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),r()}get show_copy_button(){return this.$$.ctx[14]}set show_copy_button(e){this.$$set({show_copy_button:e}),r()}get container(){return this.$$.ctx[15]}set container(e){this.$$set({container:e}),r()}}export{ze as BaseExample,_e as BaseMarkdown,ke as default};
//# sourceMappingURL=Index-DB5YfZpJ.js.map
