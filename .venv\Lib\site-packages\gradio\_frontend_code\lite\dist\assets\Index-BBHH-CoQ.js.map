{"version": 3, "file": "Index-BBHH-CoQ.js", "sources": ["../../../dataframe/shared/EditableCell.svelte", "../../../dataframe/shared/VirtualTable.svelte", "../../../dataframe/shared/CellMenuIcons.svelte", "../../../dataframe/shared/CellMenu.svelte", "../../../dataframe/shared/icons/FilterIcon.svelte", "../../../dataframe/shared/Toolbar.svelte", "../../../dataframe/shared/icons/SortIcon.svelte", "../../../dataframe/shared/selection_utils.ts", "../../../dataframe/shared/utils/sort_utils.ts", "../../../dataframe/shared/utils/table_utils.ts", "../../../dataframe/shared/Table.svelte", "../../../dataframe/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let display_value: string | null = null;\n\texport let styling = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let clear_on_focus = false;\n\texport let line_breaks = true;\n\texport let editable = true;\n\texport let root: string;\n\texport let max_chars: number | null = null;\n\n\tconst dispatch = createEventDispatcher();\n\tlet is_expanded = false;\n\n\texport let el: HTMLInputElement | null;\n\t$: _value = value;\n\n\tfunction truncate_text(\n\t\ttext: string | number,\n\t\tmax_length: number | null = null\n\t): string {\n\t\tconst str = String(text);\n\t\tif (!max_length || str.length <= max_length) return str;\n\t\treturn str.slice(0, max_length) + \"...\";\n\t}\n\n\t$: display_text = is_expanded\n\t\t? value\n\t\t: truncate_text(display_value || value, max_chars);\n\n\tfunction use_focus(node: HTMLInputElement): any {\n\t\tif (clear_on_focus) {\n\t\t\t_value = \"\";\n\t\t}\n\n\t\trequestAnimationFrame(() => {\n\t\t\tnode.focus();\n\t\t});\n\n\t\treturn {};\n\t}\n\n\tfunction handle_blur({\n\t\tcurrentTarget\n\t}: Event & {\n\t\tcurrentTarget: HTMLInputElement;\n\t}): void {\n\t\tvalue = currentTarget.value;\n\t\tdispatch(\"blur\");\n\t}\n\n\tfunction handle_keydown(event: KeyboardEvent): void {\n\t\tif (event.key === \"Enter\") {\n\t\t\tif (edit) {\n\t\t\t\tvalue = _value;\n\t\t\t\tdispatch(\"blur\");\n\t\t\t} else if (!header) {\n\t\t\t\tis_expanded = !is_expanded;\n\t\t\t}\n\t\t}\n\t\tdispatch(\"keydown\", event);\n\t}\n\n\tfunction handle_click(): void {\n\t\tif (!edit && !header) {\n\t\t\tis_expanded = !is_expanded;\n\t\t}\n\t}\n</script>\n\n{#if edit}\n\t<input\n\t\trole=\"textbox\"\n\t\tbind:this={el}\n\t\tbind:value={_value}\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\ton:blur={handle_blur}\n\t\ton:mousedown|stopPropagation\n\t\ton:mouseup|stopPropagation\n\t\ton:click|stopPropagation\n\t\tuse:use_focus\n\t\ton:keydown={handle_keydown}\n\t/>\n{/if}\n\n<span\n\ton:click={handle_click}\n\ton:keydown={handle_keydown}\n\ttabindex=\"0\"\n\trole=\"button\"\n\tclass:edit\n\tclass:expanded={is_expanded}\n\tclass:multiline={header}\n\ton:focus|preventDefault\n\tstyle={styling}\n\tdata-editable={editable}\n\tplaceholder=\" \"\n>\n\t{#if datatype === \"html\"}\n\t\t{@html display_text}\n\t{:else if datatype === \"markdown\"}\n\t\t<MarkdownCode\n\t\t\tmessage={display_text.toLocaleString()}\n\t\t\t{latex_delimiters}\n\t\t\t{line_breaks}\n\t\t\tchatbot={false}\n\t\t\t{root}\n\t\t/>\n\t{:else}\n\t\t{editable ? display_text : display_value || display_text}\n\t{/if}\n</span>\n\n<style>\n\tinput {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tbottom: var(--size-2);\n\t\tleft: var(--size-2);\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t\tcursor: text;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\toutline: none;\n\t\t-webkit-user-select: text;\n\t\t-moz-user-select: text;\n\t\t-ms-user-select: text;\n\t\tuser-select: text;\n\t\tcursor: text;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\tspan.expanded {\n\t\theight: auto;\n\t\tmin-height: 100%;\n\t\twhite-space: pre-wrap;\n\t\tword-break: break-word;\n\t\twhite-space: normal;\n\t}\n\n\t.multiline {\n\t\twhite-space: pre-line;\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont-weight: var(--weight-bold);\n\t\twhite-space: normal;\n\t\tword-break: break-word;\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount, tick } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let items: any[][] = [];\n\n\texport let max_height: number;\n\texport let actual_height: number;\n\texport let table_scrollbar_width: number;\n\texport let start = 0;\n\texport let end = 20;\n\texport let selected: number | false;\n\texport let disable_scroll = false;\n\tlet height = \"100%\";\n\n\tlet average_height = 30;\n\tlet bottom = 0;\n\tlet contents: HTMLTableSectionElement;\n\tlet head_height = 0;\n\tlet foot_height = 0;\n\tlet height_map: number[] = [];\n\tlet mounted: boolean;\n\tlet rows: HTMLCollectionOf<HTMLTableRowElement>;\n\tlet top = 0;\n\tlet viewport: HTMLTableElement;\n\tlet viewport_height = 200;\n\tlet visible: { index: number; data: any[] }[] = [];\n\tlet viewport_box: DOMRectReadOnly;\n\n\t$: viewport_height = viewport_box?.height || 200;\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tconst raf = is_browser\n\t\t? window.requestAnimationFrame\n\t\t: (cb: (...args: any[]) => void) => cb();\n\n\t$: mounted && raf(() => refresh_height_map(sortedItems));\n\n\tlet content_height = 0;\n\tasync function refresh_height_map(_items: typeof items): Promise<void> {\n\t\tif (viewport_height === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\t// force header height calculation first\n\t\thead_height =\n\t\t\tviewport.querySelector(\".thead\")?.getBoundingClientRect().height || 0;\n\t\tawait tick();\n\n\t\tconst { scrollTop } = viewport;\n\t\ttable_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n\n\t\tcontent_height = top - (scrollTop - head_height);\n\t\tlet i = start;\n\n\t\twhile (content_height < max_height && i < _items.length) {\n\t\t\tlet row = rows[i - start];\n\t\t\tif (!row) {\n\t\t\t\tend = i + 1;\n\t\t\t\tawait tick(); // render the newly visible row\n\t\t\t\trow = rows[i - start];\n\t\t\t}\n\t\t\tlet _h = row?.getBoundingClientRect().height;\n\t\t\tif (!_h) {\n\t\t\t\t_h = average_height;\n\t\t\t}\n\t\t\tconst row_height = (height_map[i] = _h);\n\t\t\tcontent_height += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tend = i;\n\t\tconst remaining = _items.length - end;\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tcontent_height += scrollbar_height;\n\t\t}\n\n\t\tlet filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n\t\taverage_height =\n\t\t\tfiltered_height_map.reduce((a, b) => a + b, 0) /\n\t\t\tfiltered_height_map.length;\n\n\t\tbottom = remaining * average_height;\n\t\theight_map.length = _items.length;\n\t\tawait tick();\n\t\tif (!max_height) {\n\t\t\tactual_height = content_height + 1;\n\t\t} else if (content_height < max_height) {\n\t\t\tactual_height = content_height + 2;\n\t\t} else {\n\t\t\tactual_height = max_height;\n\t\t}\n\n\t\tawait tick();\n\t}\n\n\t$: scroll_and_render(selected);\n\n\tasync function scroll_and_render(n: number | false): Promise<void> {\n\t\traf(async () => {\n\t\t\tif (typeof n !== \"number\") return;\n\t\t\tconst direction = typeof n !== \"number\" ? false : is_in_view(n);\n\t\t\tif (direction === true) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (direction === \"back\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" });\n\t\t\t}\n\n\t\t\tif (direction === \"forwards\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" }, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction is_in_view(n: number): \"back\" | \"forwards\" | true {\n\t\tconst current = rows && rows[n - start];\n\t\tif (!current && n < start) {\n\t\t\treturn \"back\";\n\t\t}\n\t\tif (!current && n >= end - 1) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\tconst { top: viewport_top } = viewport.getBoundingClientRect();\n\t\tconst { top, bottom } = current.getBoundingClientRect();\n\n\t\tif (top - viewport_top < 37) {\n\t\t\treturn \"back\";\n\t\t}\n\n\t\tif (bottom - viewport_top > viewport_height) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tfunction get_computed_px_amount(elem: HTMLElement, property: string): number {\n\t\tif (!elem) {\n\t\t\treturn 0;\n\t\t}\n\t\tconst compStyle = getComputedStyle(elem);\n\n\t\tlet x = parseInt(compStyle.getPropertyValue(property));\n\t\treturn x;\n\t}\n\n\tasync function handle_scroll(e: Event): Promise<void> {\n\t\tconst scroll_top = viewport.scrollTop;\n\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tconst is_start_overflow = sortedItems.length < start;\n\n\t\tconst row_top_border = get_computed_px_amount(rows[1], \"border-top-width\");\n\n\t\tconst actual_border_collapsed_width = 0;\n\n\t\tif (is_start_overflow) {\n\t\t\tawait scroll_to_index(sortedItems.length - 1, { behavior: \"auto\" });\n\t\t}\n\n\t\tlet new_start = 0;\n\t\t// acquire height map for currently visible rows\n\t\tfor (let v = 0; v < rows.length; v += 1) {\n\t\t\theight_map[start + v] = rows[v].getBoundingClientRect().height;\n\t\t}\n\t\tlet i = 0;\n\t\t// start from top: thead, with its borders, plus the first border to afterwards neglect\n\t\tlet y = head_height + row_top_border / 2;\n\t\tlet row_heights = [];\n\t\t// loop items to find new start\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\trow_heights[i] = row_height;\n\t\t\t// we only want to jump if the full (incl. border) row is away\n\t\t\tif (y + row_height + actual_border_collapsed_width > scroll_top) {\n\t\t\t\t// this is the last index still inside the viewport\n\t\t\t\tnew_start = i;\n\t\t\t\ttop = y - (head_height + row_top_border / 2);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tnew_start = Math.max(0, new_start);\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t\tif (y > scroll_top + viewport_height) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tstart = new_start;\n\t\tend = i;\n\t\tconst remaining = sortedItems.length - end;\n\t\tif (end === 0) {\n\t\t\tend = 10;\n\t\t}\n\t\taverage_height = (y - head_height) / end;\n\t\tlet remaining_height = remaining * average_height; // 0\n\t\t// compute height map for remaining items\n\t\twhile (i < sortedItems.length) {\n\t\t\ti += 1;\n\t\t\theight_map[i] = average_height;\n\t\t}\n\t\tbottom = remaining_height;\n\t\tif (!isFinite(bottom)) {\n\t\t\tbottom = 200000;\n\t\t}\n\t}\n\n\texport async function scroll_to_index(\n\t\tindex: number,\n\t\topts: ScrollToOptions,\n\t\talign_end = false\n\t): Promise<void> {\n\t\tawait tick();\n\n\t\tconst _itemHeight = average_height;\n\n\t\tlet distance = index * _itemHeight;\n\t\tif (align_end) {\n\t\t\tdistance = distance - viewport_height + _itemHeight + head_height;\n\t\t}\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tdistance += scrollbar_height;\n\t\t}\n\n\t\tconst _opts = {\n\t\t\ttop: distance,\n\t\t\tbehavior: \"smooth\" as ScrollBehavior,\n\t\t\t...opts\n\t\t};\n\n\t\tviewport.scrollTo(_opts);\n\t}\n\n\t$: sortedItems = items;\n\n\t$: visible = is_browser\n\t\t? sortedItems.slice(start, end).map((data, i) => {\n\t\t\t\treturn { index: i + start, data };\n\t\t\t})\n\t\t: sortedItems\n\t\t\t\t.slice(0, (max_height / sortedItems.length) * average_height + 1)\n\t\t\t\t.map((data, i) => {\n\t\t\t\t\treturn { index: i + start, data };\n\t\t\t\t});\n\n\tonMount(() => {\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tmounted = true;\n\t\trefresh_height_map(items);\n\t});\n</script>\n\n<svelte-virtual-table-viewport>\n\t<div>\n\t\t<table\n\t\t\tclass=\"table\"\n\t\t\tclass:disable-scroll={disable_scroll}\n\t\t\tbind:this={viewport}\n\t\t\tbind:contentRect={viewport_box}\n\t\t\ton:scroll={handle_scroll}\n\t\t\tstyle=\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px; --max-height: {max_height}px\"\n\t\t>\n\t\t\t<thead class=\"thead\" bind:offsetHeight={head_height}>\n\t\t\t\t<slot name=\"thead\" />\n\t\t\t</thead>\n\t\t\t<tbody bind:this={contents} class=\"tbody\">\n\t\t\t\t{#if visible.length && visible[0].data.length}\n\t\t\t\t\t{#each visible as item (item.data[0].id)}\n\t\t\t\t\t\t<slot name=\"tbody\" item={item.data} index={item.index}>\n\t\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t\t</slot>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</tbody>\n\t\t\t<tfoot class=\"tfoot\" bind:offsetHeight={foot_height}>\n\t\t\t\t<slot name=\"tfoot\" />\n\t\t\t</tfoot>\n\t\t</table>\n\t</div>\n</svelte-virtual-table-viewport>\n\n<style type=\"text/css\">\n\ttable {\n\t\tposition: relative;\n\t\toverflow-y: scroll;\n\t\toverflow-x: scroll;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tmax-height: var(--max-height);\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\twidth: 100%;\n\t\tscroll-snap-type: x proximity;\n\t\tborder-collapse: separate;\n\t}\n\ttable :is(thead, tfoot, tbody) {\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\ttbody {\n\t\toverflow-x: scroll;\n\t\toverflow-y: hidden;\n\t}\n\n\ttable tbody {\n\t\tpadding-top: var(--bw-svt-p-top);\n\t\tpadding-bottom: var(--bw-svt-p-bottom);\n\t}\n\ttbody {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tborder: 0px solid currentColor;\n\t}\n\n\ttbody > :global(tr:last-child) {\n\t\tborder: none;\n\t}\n\n\ttable :global(td) {\n\t\tscroll-snap-align: start;\n\t}\n\n\ttbody > :global(tr:nth-child(even)) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttbody :global(td.frozen-column) {\n\t\tposition: sticky;\n\t\tz-index: var(--layer-2);\n\t}\n\n\ttbody :global(tr:nth-child(odd)) :global(td.frozen-column) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\ttbody :global(tr:nth-child(even)) :global(td.frozen-column) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttbody :global(td.always-frozen) {\n\t\tz-index: var(--layer-3);\n\t}\n\n\ttbody :global(td.last-frozen) {\n\t\tborder-right: 2px solid var(--border-color-primary);\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tz-index: var(--layer-3);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\tthead :global(th) {\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\tthead :global(th.frozen-column) {\n\t\tposition: sticky;\n\t\tz-index: var(--layer-4);\n\t}\n\n\tthead :global(th.always-frozen) {\n\t\tz-index: var(--layer-5);\n\t}\n\n\t.table.disable-scroll {\n\t\toverflow: hidden !important;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let icon: string;\n</script>\n\n{#if icon == \"add-column-right\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"4\"\n\t\t\ty=\"6\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12H19M16 8L19 12L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-column-left\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"16\"\n\t\t\ty=\"6\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12H5M8 8L5 12L8 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-row-above\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"6\"\n\t\t\ty=\"16\"\n\t\t\twidth=\"12\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V5M8 8L12 5L16 8\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-row-below\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"6\"\n\t\t\ty=\"4\"\n\t\t\twidth=\"12\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V19M8 16L12 19L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"delete-row\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"5\"\n\t\t\ty=\"10\"\n\t\t\twidth=\"14\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 7L16 17M16 7L8 17\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"delete-column\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"10\"\n\t\t\ty=\"5\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"14\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M7 8L17 16M17 8L7 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{/if}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport CellMenuIcons from \"./CellMenuIcons.svelte\";\n\timport type { I18nFormatter } from \"js/utils/src\";\n\n\texport let x: number;\n\texport let y: number;\n\texport let on_add_row_above: () => void;\n\texport let on_add_row_below: () => void;\n\texport let on_add_column_left: () => void;\n\texport let on_add_column_right: () => void;\n\texport let row: number;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let on_delete_row: () => void;\n\texport let on_delete_col: () => void;\n\texport let can_delete_rows: boolean;\n\texport let can_delete_cols: boolean;\n\n\texport let i18n: I18nFormatter;\n\tlet menu_element: HTMLDivElement;\n\n\t$: is_header = row === -1;\n\t$: can_add_rows = row_count[1] === \"dynamic\";\n\t$: can_add_columns = col_count[1] === \"dynamic\";\n\n\tonMount(() => {\n\t\tposition_menu();\n\t});\n\n\tfunction position_menu(): void {\n\t\tif (!menu_element) return;\n\n\t\tconst viewport_width = window.innerWidth;\n\t\tconst viewport_height = window.innerHeight;\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\n\t\tlet new_x = x - 30;\n\t\tlet new_y = y - 20;\n\n\t\tif (new_x + menu_rect.width > viewport_width) {\n\t\t\tnew_x = x - menu_rect.width + 10;\n\t\t}\n\n\t\tif (new_y + menu_rect.height > viewport_height) {\n\t\t\tnew_y = y - menu_rect.height + 10;\n\t\t}\n\n\t\tmenu_element.style.left = `${new_x}px`;\n\t\tmenu_element.style.top = `${new_y}px`;\n\t}\n</script>\n\n<div bind:this={menu_element} class=\"cell-menu\">\n\t{#if !is_header && can_add_rows}\n\t\t<button on:click={() => on_add_row_above()}>\n\t\t\t<CellMenuIcons icon=\"add-row-above\" />\n\t\t\t{i18n(\"dataframe.add_row_above\")}\n\t\t</button>\n\t\t<button on:click={() => on_add_row_below()}>\n\t\t\t<CellMenuIcons icon=\"add-row-below\" />\n\t\t\t{i18n(\"dataframe.add_row_below\")}\n\t\t</button>\n\t\t{#if can_delete_rows}\n\t\t\t<button on:click={on_delete_row} class=\"delete\">\n\t\t\t\t<CellMenuIcons icon=\"delete-row\" />\n\t\t\t\t{i18n(\"dataframe.delete_row\")}\n\t\t\t</button>\n\t\t{/if}\n\t{/if}\n\t{#if can_add_columns}\n\t\t<button on:click={() => on_add_column_left()}>\n\t\t\t<CellMenuIcons icon=\"add-column-left\" />\n\t\t\t{i18n(\"dataframe.add_column_left\")}\n\t\t</button>\n\t\t<button on:click={() => on_add_column_right()}>\n\t\t\t<CellMenuIcons icon=\"add-column-right\" />\n\t\t\t{i18n(\"dataframe.add_column_right\")}\n\t\t</button>\n\t\t{#if can_delete_cols}\n\t\t\t<button on:click={on_delete_col} class=\"delete\">\n\t\t\t\t<CellMenuIcons icon=\"delete-column\" />\n\t\t\t\t{i18n(\"dataframe.delete_column\")}\n\t\t\t</button>\n\t\t{/if}\n\t{/if}\n</div>\n\n<style>\n\t.cell-menu {\n\t\tposition: fixed;\n\t\tz-index: var(--layer-4);\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-1);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-1);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tmin-width: 150px;\n\t}\n\n\t.cell-menu button {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\ttext-align: left;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\ttransition:\n\t\t\tbackground-color 0.2s,\n\t\t\tcolor 0.2s;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t}\n\n\t.cell-menu button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.cell-menu button :global(svg) {\n\t\tfill: currentColor;\n\t\ttransition: fill 0.2s;\n\t}\n</style>\n", "<script lang=\"ts\">\n</script>\n\n<svg viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t<path\n\t\td=\"M4 4h16v2.67l-6.67 6.67v8L9.33 19v-5.66L2.67 6.67V4h1.33z\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { Maximize, Minimize, Copy } from \"@gradio/icons\";\n\timport { onDestroy } from \"svelte\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport FilterIcon from \"./icons/FilterIcon.svelte\";\n\n\texport let show_fullscreen_button = false;\n\texport let show_copy_button = false;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let is_fullscreen = false;\n\texport let on_copy: () => Promise<void>;\n\texport let on_commit_filter: () => void;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tsearch: string | null;\n\t}>();\n\n\tlet copied = false;\n\tlet timer: ReturnType<typeof setTimeout>;\n\texport let current_search_query: string | null = null;\n\n\t$: dispatch(\"search\", current_search_query);\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tawait on_copy();\n\t\tcopy_feedback();\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<div class=\"toolbar\" role=\"toolbar\" aria-label=\"Table actions\">\n\t<div class=\"toolbar-buttons\">\n\t\t{#if show_search !== \"none\"}\n\t\t\t<div class=\"search-container\">\n\t\t\t\t<input\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tbind:value={current_search_query}\n\t\t\t\t\tplaceholder=\"Search...\"\n\t\t\t\t\tclass=\"search-input\"\n\t\t\t\t/>\n\t\t\t\t{#if current_search_query && show_search === \"filter\"}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"toolbar-button check-button\"\n\t\t\t\t\t\ton:click={on_commit_filter}\n\t\t\t\t\t\taria-label=\"Apply filter and update dataframe values\"\n\t\t\t\t\t\ttitle=\"Apply filter and update dataframe values\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<FilterIcon />\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/if}\n\t\t{#if show_copy_button}\n\t\t\t<button\n\t\t\t\tclass=\"toolbar-button\"\n\t\t\t\ton:click={handle_copy}\n\t\t\t\taria-label={copied ? \"Copied to clipboard\" : \"Copy table data\"}\n\t\t\t\ttitle={copied ? \"Copied to clipboard\" : \"Copy table data\"}\n\t\t\t>\n\t\t\t\t{#if copied}\n\t\t\t\t\t<FilterIcon />\n\t\t\t\t{:else}\n\t\t\t\t\t<Copy />\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t\t{#if show_fullscreen_button}\n\t\t\t<button\n\t\t\t\tclass=\"toolbar-button\"\n\t\t\t\ton:click\n\t\t\t\taria-label={is_fullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n\t\t\t\ttitle={is_fullscreen ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n\t\t\t>\n\t\t\t\t{#if is_fullscreen}\n\t\t\t\t\t<Minimize />\n\t\t\t\t{:else}\n\t\t\t\t\t<Maximize />\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.toolbar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tflex: 0 0 auto;\n\t}\n\n\t.toolbar-buttons {\n\t\tdisplay: flex;\n\t\tgap: var(--size-1);\n\t\tflex-wrap: nowrap;\n\t}\n\n\t.toolbar-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: var(--size-6);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-1);\n\t\tborder: none;\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: transparent;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s;\n\t}\n\n\t.toolbar-button:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.toolbar-button :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\n\t.search-container {\n\t\tposition: relative;\n\t}\n\n\t.search-input {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-2);\n\t\tpadding-right: var(--size-8);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t\tbackground: var(--background-fill-secondary);\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.search-input:hover {\n\t\tborder-color: var(--border-color-secondary);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.search-input:focus {\n\t\toutline: none;\n\t\tborder-color: var(--color-accent);\n\t\tbackground: var(--background-fill-primary);\n\t\tbox-shadow: 0 0 0 1px var(--color-accent);\n\t}\n\n\t.check-button {\n\t\tposition: absolute;\n\t\tright: var(--size-1);\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tborder-radius: var(--radius-sm);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: var(--size-1);\n\t}\n\n\t.check-button :global(svg) {\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t}\n\n\t.check-button:hover {\n\t\tbackground: var(--color-accent-soft);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\ttype SortDirection = \"asc\" | \"des\";\n\texport let direction: SortDirection | null = null;\n\texport let i18n: I18nFormatter;\n\n\tconst dispatch = createEventDispatcher<{ sort: SortDirection }>();\n</script>\n\n<div class=\"sort-icons\" role=\"group\" aria-label={i18n(\"dataframe.sort_column\")}>\n\t<button\n\t\tclass=\"sort-button up\"\n\t\tclass:active={direction === \"asc\"}\n\t\ton:click={() => dispatch(\"sort\", \"asc\")}\n\t\taria-label={i18n(\"dataframe.sort_ascending\")}\n\t\taria-pressed={direction === \"asc\"}\n\t>\n\t\t<svg\n\t\t\tviewBox=\"0 0 24 24\"\n\t\t\tfill=\"none\"\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t>\n\t\t\t<path\n\t\t\t\td=\"M7 14l5-5 5 5\"\n\t\t\t\tstroke=\"currentColor\"\n\t\t\t\tstroke-width=\"2\"\n\t\t\t\tstroke-linecap=\"round\"\n\t\t\t\tstroke-linejoin=\"round\"\n\t\t\t/>\n\t\t</svg>\n\t</button>\n\t<button\n\t\tclass=\"sort-button down\"\n\t\tclass:active={direction === \"des\"}\n\t\ton:click={() => dispatch(\"sort\", \"des\")}\n\t\taria-label={i18n(\"dataframe.sort_descending\")}\n\t\taria-pressed={direction === \"des\"}\n\t>\n\t\t<svg\n\t\t\tviewBox=\"0 0 24 24\"\n\t\t\tfill=\"none\"\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t>\n\t\t\t<path\n\t\t\t\td=\"M7 10l5 5 5-5\"\n\t\t\t\tstroke=\"currentColor\"\n\t\t\t\tstroke-width=\"2\"\n\t\t\t\tstroke-linecap=\"round\"\n\t\t\t\tstroke-linejoin=\"round\"\n\t\t\t/>\n\t\t</svg>\n\t</button>\n</div>\n\n<style>\n\t.sort-icons {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 0;\n\t\tmargin-right: var(--spacing-md);\n\t}\n\n\t.sort-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 0;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\topacity: 0.5;\n\t\ttransition: opacity 150ms;\n\t}\n\n\t.sort-button:hover {\n\t\topacity: 0.8;\n\t}\n\n\t.sort-button.active {\n\t\topacity: 1;\n\t\tcolor: var(--color-accent);\n\t}\n\n\tsvg {\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t\tdisplay: block;\n\t}\n</style>\n", "import type { CellCoordinate, EditingState } from \"./types\";\n\nexport type CellData = { id: string; value: string | number };\n\nexport function is_cell_selected(\n\tcell: CellCoordinate,\n\tselected_cells: CellCoordinate[]\n): string {\n\tconst [row, col] = cell;\n\tif (!selected_cells.some(([r, c]) => r === row && c === col)) return \"\";\n\n\tconst up = selected_cells.some(([r, c]) => r === row - 1 && c === col);\n\tconst down = selected_cells.some(([r, c]) => r === row + 1 && c === col);\n\tconst left = selected_cells.some(([r, c]) => r === row && c === col - 1);\n\tconst right = selected_cells.some(([r, c]) => r === row && c === col + 1);\n\n\treturn `cell-selected${up ? \" no-top\" : \"\"}${down ? \" no-bottom\" : \"\"}${left ? \" no-left\" : \"\"}${right ? \" no-right\" : \"\"}`;\n}\n\nexport function get_range_selection(\n\tstart: CellCoordinate,\n\tend: CellCoordinate\n): CellCoordinate[] {\n\tconst [start_row, start_col] = start;\n\tconst [end_row, end_col] = end;\n\tconst min_row = Math.min(start_row, end_row);\n\tconst max_row = Math.max(start_row, end_row);\n\tconst min_col = Math.min(start_col, end_col);\n\tconst max_col = Math.max(start_col, end_col);\n\n\tconst cells: CellCoordinate[] = [];\n\tfor (let i = min_row; i <= max_row; i++) {\n\t\tfor (let j = min_col; j <= max_col; j++) {\n\t\t\tcells.push([i, j]);\n\t\t}\n\t}\n\treturn cells;\n}\n\nexport function handle_selection(\n\tcurrent: CellCoordinate,\n\tselected_cells: CellCoordinate[],\n\tevent: { shiftKey: boolean; metaKey: boolean; ctrlKey: boolean }\n): CellCoordinate[] {\n\tif (event.shiftKey && selected_cells.length > 0) {\n\t\treturn get_range_selection(\n\t\t\tselected_cells[selected_cells.length - 1],\n\t\t\tcurrent\n\t\t);\n\t}\n\n\tif (event.metaKey || event.ctrlKey) {\n\t\tconst is_cell_match = ([r, c]: CellCoordinate): boolean =>\n\t\t\tr === current[0] && c === current[1];\n\t\tconst index = selected_cells.findIndex(is_cell_match);\n\t\treturn index === -1\n\t\t\t? [...selected_cells, current]\n\t\t\t: selected_cells.filter((_, i) => i !== index);\n\t}\n\n\treturn [current];\n}\n\nexport function handle_delete_key(\n\tdata: CellData[][],\n\tselected_cells: CellCoordinate[]\n): CellData[][] {\n\tconst new_data = data.map((row) => [...row]);\n\tselected_cells.forEach(([row, col]) => {\n\t\tif (new_data[row] && new_data[row][col]) {\n\t\t\tnew_data[row][col] = { ...new_data[row][col], value: \"\" };\n\t\t}\n\t});\n\treturn new_data;\n}\n\nexport function handle_editing_state(\n\tcurrent: CellCoordinate,\n\tediting: EditingState,\n\tselected_cells: CellCoordinate[],\n\teditable: boolean\n): EditingState {\n\tconst [row, col] = current;\n\tif (!editable) return false;\n\n\tif (editing && editing[0] === row && editing[1] === col) return editing;\n\n\tif (\n\t\tselected_cells.length === 1 &&\n\t\tselected_cells[0][0] === row &&\n\t\tselected_cells[0][1] === col\n\t) {\n\t\treturn [row, col];\n\t}\n\n\treturn false;\n}\n\nexport function should_show_cell_menu(\n\tcell: CellCoordinate,\n\tselected_cells: CellCoordinate[],\n\teditable: boolean\n): boolean {\n\tconst [row, col] = cell;\n\treturn (\n\t\teditable &&\n\t\tselected_cells.length === 1 &&\n\t\tselected_cells[0][0] === row &&\n\t\tselected_cells[0][1] === col\n\t);\n}\n\nexport function get_next_cell_coordinates(\n\tcurrent: CellCoordinate,\n\tdata: CellData[][],\n\tshift_key: boolean\n): CellCoordinate | false {\n\tconst [row, col] = current;\n\tconst direction = shift_key ? -1 : 1;\n\n\tif (data[row]?.[col + direction]) {\n\t\treturn [row, col + direction];\n\t}\n\n\tconst next_row = row + (direction > 0 ? 1 : 0);\n\tconst prev_row = row + (direction < 0 ? -1 : 0);\n\n\tif (direction > 0 && data[next_row]?.[0]) {\n\t\treturn [next_row, 0];\n\t}\n\n\tif (direction < 0 && data[prev_row]?.[data[0].length - 1]) {\n\t\treturn [prev_row, data[0].length - 1];\n\t}\n\n\treturn false;\n}\n\nexport function move_cursor(\n\tkey: \"ArrowRight\" | \"ArrowLeft\" | \"ArrowDown\" | \"ArrowUp\",\n\tcurrent_coords: CellCoordinate,\n\tdata: CellData[][]\n): CellCoordinate | false {\n\tconst dir = {\n\t\tArrowRight: [0, 1],\n\t\tArrowLeft: [0, -1],\n\t\tArrowDown: [1, 0],\n\t\tArrowUp: [-1, 0]\n\t}[key];\n\n\tconst i = current_coords[0] + dir[0];\n\tconst j = current_coords[1] + dir[1];\n\n\tif (i < 0 && j <= 0) {\n\t\treturn false;\n\t}\n\n\tconst is_data = data[i]?.[j];\n\tif (is_data) {\n\t\treturn [i, j];\n\t}\n\treturn false;\n}\n\nexport function get_current_indices(\n\tid: string,\n\tdata: CellData[][]\n): [number, number] {\n\treturn data.reduce(\n\t\t(acc, arr, i) => {\n\t\t\tconst j = arr.reduce(\n\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t-1\n\t\t\t);\n\t\t\treturn j === -1 ? acc : [i, j];\n\t\t},\n\t\t[-1, -1]\n\t);\n}\n\nexport function handle_click_outside(\n\tevent: Event,\n\tparent: HTMLElement\n): boolean {\n\tconst [trigger] = event.composedPath() as HTMLElement[];\n\treturn !parent.contains(trigger);\n}\n\nexport function select_column(data: any[][], col: number): CellCoordinate[] {\n\treturn Array.from({ length: data.length }, (_, i) => [i, col]);\n}\n\nexport function select_row(data: any[][], row: number): CellCoordinate[] {\n\treturn Array.from({ length: data[0].length }, (_, i) => [row, i]);\n}\n\nexport function calculate_selection_positions(\n\tselected: CellCoordinate,\n\tdata: { id: string; value: string | number }[][],\n\tels: Record<string, { cell: HTMLTableCellElement | null }>,\n\tparent: HTMLElement,\n\ttable: HTMLElement\n): { col_pos: string; row_pos: string | undefined } {\n\tconst [row, col] = selected;\n\tif (!data[row]?.[col]) {\n\t\treturn { col_pos: \"0px\", row_pos: undefined };\n\t}\n\n\tlet offset = 0;\n\tfor (let i = 0; i < col; i++) {\n\t\toffset += parseFloat(\n\t\t\tgetComputedStyle(parent).getPropertyValue(`--cell-width-${i}`)\n\t\t);\n\t}\n\n\tconst cell_id = data[row][col].id;\n\tconst cell_el = els[cell_id]?.cell;\n\n\tif (!cell_el) {\n\t\t// if we cant get the row position, just return the column position which is static\n\t\treturn { col_pos: \"0px\", row_pos: undefined };\n\t}\n\n\tconst cell_rect = cell_el.getBoundingClientRect();\n\tconst table_rect = table.getBoundingClientRect();\n\tconst col_pos = `${cell_rect.left - table_rect.left + cell_rect.width / 2}px`;\n\tconst relative_top = cell_rect.top - table_rect.top;\n\tconst row_pos = `${relative_top + cell_rect.height / 2}px`;\n\treturn { col_pos, row_pos };\n}\n", "import type { Headers } from \"../types\";\n\nexport type SortDirection = \"asc\" | \"des\";\n\nexport function get_sort_status(\n\tname: string,\n\tsort_by: number | undefined,\n\tdirection: SortDirection | undefined,\n\theaders: Headers\n): \"none\" | \"ascending\" | \"descending\" {\n\tif (typeof sort_by !== \"number\") return \"none\";\n\tif (sort_by < 0 || sort_by >= headers.length) return \"none\";\n\tif (headers[sort_by] === name) {\n\t\tif (direction === \"asc\") return \"ascending\";\n\t\tif (direction === \"des\") return \"descending\";\n\t}\n\treturn \"none\";\n}\n\nexport function sort_data(\n\tdata: { id: string; value: string | number }[][],\n\tsort_by: number | undefined,\n\tsort_direction: SortDirection | undefined\n): number[] {\n\tif (!data || !data.length || !data[0]) {\n\t\treturn [];\n\t}\n\n\tif (\n\t\ttypeof sort_by === \"number\" &&\n\t\tsort_direction &&\n\t\tsort_by >= 0 &&\n\t\tsort_by < data[0].length\n\t) {\n\t\tconst row_indices = [...Array(data.length)].map((_, i) => i);\n\t\trow_indices.sort((row_a_idx, row_b_idx) => {\n\t\t\tconst row_a = data[row_a_idx];\n\t\t\tconst row_b = data[row_b_idx];\n\t\t\tif (\n\t\t\t\t!row_a ||\n\t\t\t\t!row_b ||\n\t\t\t\tsort_by >= row_a.length ||\n\t\t\t\tsort_by >= row_b.length\n\t\t\t)\n\t\t\t\treturn 0;\n\n\t\t\tconst val_a = row_a[sort_by].value;\n\t\t\tconst val_b = row_b[sort_by].value;\n\t\t\tconst comparison = val_a < val_b ? -1 : val_a > val_b ? 1 : 0;\n\t\t\treturn sort_direction === \"asc\" ? comparison : -comparison;\n\t\t});\n\t\treturn row_indices;\n\t}\n\treturn [...Array(data.length)].map((_, i) => i);\n}\n", "import type {\n\t<PERSON><PERSON>,\n\t<PERSON>ersWithIDs,\n\tTableCell,\n\tTableData,\n\tCountConfig,\n\tElementRefs,\n\tDataBinding\n} from \"../types\";\nimport { sort_data } from \"./sort_utils\";\nimport type { SortDirection } from \"./sort_utils\";\nimport { dsvFormat } from \"d3-dsv\";\n\nexport function make_cell_id(row: number, col: number): string {\n\treturn `cell-${row}-${col}`;\n}\n\nexport function make_header_id(col: number): string {\n\treturn `header-${col}`;\n}\n\nexport function process_data(\n\tinput_values: (string | number)[][],\n\trow_count: CountConfig,\n\tcol_count: CountConfig,\n\theaders: Headers,\n\tshow_row_numbers: boolean,\n\telement_refs: ElementRefs,\n\tdata_binding: DataBinding\n): TableData {\n\tconst data_row_length = input_values.length;\n\treturn Array(row_count[1] === \"fixed\" ? row_count[0] : data_row_length)\n\t\t.fill(0)\n\t\t.map((_, row) => {\n\t\t\treturn Array(\n\t\t\t\tcol_count[1] === \"fixed\"\n\t\t\t\t\t? col_count[0]\n\t\t\t\t\t: data_row_length > 0\n\t\t\t\t\t\t? input_values[0].length\n\t\t\t\t\t\t: headers.length\n\t\t\t)\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, col) => {\n\t\t\t\t\tconst cell_id = make_cell_id(row, col);\n\t\t\t\t\telement_refs[cell_id] = element_refs[cell_id] || {\n\t\t\t\t\t\tinput: null,\n\t\t\t\t\t\tcell: null\n\t\t\t\t\t};\n\t\t\t\t\tconst cell_obj: TableCell = {\n\t\t\t\t\t\tvalue: input_values?.[row]?.[col] ?? \"\",\n\t\t\t\t\t\tid: cell_id\n\t\t\t\t\t};\n\t\t\t\t\tdata_binding[cell_id] = cell_obj;\n\t\t\t\t\treturn cell_obj;\n\t\t\t\t});\n\t\t});\n}\n\nexport function make_headers(\n\tinput_headers: Headers,\n\tshow_row_numbers: boolean,\n\tcol_count: CountConfig,\n\telement_refs: ElementRefs\n): HeadersWithIDs[] {\n\tlet header_list = input_headers || [];\n\tif (show_row_numbers) {\n\t\theader_list = [\"\", ...header_list];\n\t}\n\tif (col_count[1] === \"fixed\" && header_list.length < col_count[0]) {\n\t\tconst fill_headers = Array(col_count[0] - header_list.length)\n\t\t\t.fill(\"\")\n\t\t\t.map((_, i) => `${i + header_list.length}`);\n\t\theader_list = header_list.concat(fill_headers);\n\t}\n\n\tif (!header_list || header_list.length === 0) {\n\t\treturn Array(col_count[0])\n\t\t\t.fill(0)\n\t\t\t.map((_, col) => {\n\t\t\t\tconst header_id = make_header_id(col);\n\t\t\t\telement_refs[header_id] = { cell: null, input: null };\n\t\t\t\treturn { id: header_id, value: JSON.stringify(col + 1) };\n\t\t\t});\n\t}\n\n\treturn header_list.map((header: string | null, col: number) => {\n\t\tconst header_id = make_header_id(col);\n\t\telement_refs[header_id] = { cell: null, input: null };\n\t\treturn { id: header_id, value: header ?? \"\" };\n\t});\n}\n\nexport function get_max(data: TableData): TableCell[] {\n\tif (!data || !data.length) return [];\n\tlet max = data[0].slice();\n\tfor (let i = 0; i < data.length; i++) {\n\t\tfor (let j = 0; j < data[i].length; j++) {\n\t\t\tif (`${max[j].value}`.length < `${data[i][j].value}`.length) {\n\t\t\t\tmax[j] = data[i][j];\n\t\t\t}\n\t\t}\n\t}\n\treturn max;\n}\n\nexport function sort_table_data(\n\tdata: TableData,\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tcol: number,\n\tdir: SortDirection\n): void {\n\tconst indices = sort_data(data, col, dir);\n\n\tconst new_data = indices.map((i: number) => data[i]);\n\tdata.splice(0, data.length, ...new_data);\n\n\tif (display_value) {\n\t\tconst new_display = indices.map((i: number) => display_value[i]);\n\t\tdisplay_value.splice(0, display_value.length, ...new_display);\n\t}\n\n\tif (styling) {\n\t\tconst new_styling = indices.map((i: number) => styling[i]);\n\t\tstyling.splice(0, styling.length, ...new_styling);\n\t}\n}\n\nexport async function copy_table_data(\n\tdata: TableData,\n\tselected_cells: [number, number][]\n): Promise<void> {\n\tconst csv = selected_cells.reduce(\n\t\t(acc: { [key: string]: { [key: string]: string } }, [row, col]) => {\n\t\t\tacc[row] = acc[row] || {};\n\t\t\tconst value = String(data[row][col].value);\n\t\t\tacc[row][col] =\n\t\t\t\tvalue.includes(\",\") || value.includes('\"') || value.includes(\"\\n\")\n\t\t\t\t\t? `\"${value.replace(/\"/g, '\"\"')}\"`\n\t\t\t\t\t: value;\n\t\t\treturn acc;\n\t\t},\n\t\t{}\n\t);\n\n\tconst rows = Object.keys(csv).sort((a, b) => +a - +b);\n\tconst cols = Object.keys(csv[rows[0]]).sort((a, b) => +a - +b);\n\tconst text = rows\n\t\t.map((r) => cols.map((c) => csv[r][c] || \"\").join(\",\"))\n\t\t.join(\"\\n\");\n\n\ttry {\n\t\tawait navigator.clipboard.writeText(text);\n\t} catch (err) {\n\t\tconsole.error(\"Copy failed:\", err);\n\t\tthrow new Error(\"Failed to copy to clipboard: \" + (err as Error).message);\n\t}\n}\n\n// File Import/Export\nexport function guess_delimiter(\n\ttext: string,\n\tpossibleDelimiters: string[]\n): string[] {\n\treturn possibleDelimiters.filter(weedOut);\n\n\tfunction weedOut(delimiter: string): boolean {\n\t\tvar cache = -1;\n\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\tfunction checkLength(line: string): boolean {\n\t\t\tif (!line) return true;\n\t\t\tvar length = line.split(delimiter).length;\n\t\t\tif (cache < 0) cache = length;\n\t\t\treturn cache === length && length > 1;\n\t\t}\n\t}\n}\n\nexport function data_uri_to_blob(data_uri: string): Blob {\n\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\tconst ab = new ArrayBuffer(byte_str.length);\n\tconst ia = new Uint8Array(ab);\n\tfor (let i = 0; i < byte_str.length; i++) {\n\t\tia[i] = byte_str.charCodeAt(i);\n\t}\n\treturn new Blob([ab], { type: mime_str });\n}\n\nexport function handle_file_upload(\n\tdata_uri: string,\n\tupdate_headers: (headers: Headers) => HeadersWithIDs[],\n\tupdate_values: (values: (string | number)[][]) => void\n): void {\n\tconst blob = data_uri_to_blob(data_uri);\n\tconst reader = new FileReader();\n\treader.addEventListener(\"loadend\", (e) => {\n\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\t\tconst [delimiter] = guess_delimiter(e.target.result, [\",\", \"\\t\"]);\n\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\t\tupdate_headers(head);\n\t\tupdate_values(rest);\n\t});\n\treader.readAsText(blob);\n}\n", "<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { dequal } from \"dequal/lite\";\n\timport { Upload } from \"@gradio/upload\";\n\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { type Client } from \"@gradio/client\";\n\timport VirtualTable from \"./VirtualTable.svelte\";\n\timport type {\n\t\tHeaders,\n\t\tHeadersWithIDs,\n\t\tDataframeValue,\n\t\tDatatype\n\t} from \"./utils\";\n\timport CellMenu from \"./CellMenu.svelte\";\n\timport Toolbar from \"./Toolbar.svelte\";\n\timport SortIcon from \"./icons/SortIcon.svelte\";\n\timport type { CellCoordinate, EditingState } from \"./types\";\n\timport {\n\t\tis_cell_selected,\n\t\thandle_selection,\n\t\thandle_delete_key,\n\t\tshould_show_cell_menu,\n\t\tget_next_cell_coordinates,\n\t\tget_range_selection,\n\t\tmove_cursor,\n\t\tget_current_indices,\n\t\thandle_click_outside as handle_click_outside_util,\n\t\tselect_column,\n\t\tselect_row,\n\t\tcalculate_selection_positions\n\t} from \"./selection_utils\";\n\timport {\n\t\tcopy_table_data,\n\t\tget_max,\n\t\thandle_file_upload,\n\t\tsort_table_data\n\t} from \"./utils/table_utils\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let headers: Headers = [];\n\texport let values: (string | number)[][] = [];\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\n\texport let max_height = 500;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let show_row_numbers = false;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let show_fullscreen_button = false;\n\texport let show_copy_button = false;\n\texport let value_is_output = false;\n\texport let max_chars: number | undefined = undefined;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let pinned_columns = 0;\n\n\tlet actual_pinned_columns = 0;\n\t$: actual_pinned_columns =\n\t\tpinned_columns && data?.[0]?.length\n\t\t\t? Math.min(pinned_columns, data[0].length)\n\t\t\t: 0;\n\n\tlet selected_cells: CellCoordinate[] = [];\n\t$: selected_cells = [...selected_cells];\n\tlet selected: CellCoordinate | false = false;\n\t$: selected =\n\t\tselected_cells.length > 0\n\t\t\t? selected_cells[selected_cells.length - 1]\n\t\t\t: false;\n\n\texport let display_value: string[][] | null = null;\n\texport let styling: string[][] | null = null;\n\tlet t_rect: DOMRectReadOnly;\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLInputElement }\n\t> = {};\n\tlet data_binding: Record<string, (typeof data)[0][0]> = {};\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: DataframeValue;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tsearch: string | null;\n\t}>();\n\n\tlet editing: EditingState = false;\n\tlet clear_on_focus = false;\n\tlet header_edit: number | false = false;\n\tlet selected_header: number | false = false;\n\tlet active_cell_menu: {\n\t\trow: number;\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null = null;\n\tlet active_header_menu: {\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null = null;\n\tlet is_fullscreen = false;\n\tlet dragging = false;\n\tlet copy_flash = false;\n\n\tlet color_accent_copied: string;\n\tonMount(() => {\n\t\tconst color = getComputedStyle(document.documentElement)\n\t\t\t.getPropertyValue(\"--color-accent\")\n\t\t\t.trim();\n\t\tcolor_accent_copied = color + \"40\"; // 80 is 50% opacity in hex\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--color-accent-copied\",\n\t\t\tcolor_accent_copied\n\t\t);\n\t});\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata?.[row]?.[col]?.value;\n\n\tfunction make_id(): string {\n\t\treturn Math.random().toString(36).substring(2, 15);\n\t}\n\n\tfunction make_headers(\n\t\t_head: Headers,\n\t\tcol_count: [number, \"fixed\" | \"dynamic\"],\n\t\tels: Record<\n\t\t\tstring,\n\t\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLInputElement }\n\t\t>\n\t): HeadersWithIDs {\n\t\tlet _h = _head || [];\n\t\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t\t.fill(\"\")\n\t\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t\t_h = _h.concat(fill);\n\t\t}\n\n\t\tif (!_h || _h.length === 0) {\n\t\t\treturn Array(col_count[0])\n\t\t\t\t.fill(0)\n\t\t\t\t.map((_, i) => {\n\t\t\t\t\tconst _id = make_id();\n\t\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t\t});\n\t\t}\n\n\t\treturn _h.map((h, i) => {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\treturn { id: _id, value: h ?? \"\" };\n\t\t});\n\t}\n\n\tfunction process_data(_values: (string | number)[][]): {\n\t\tvalue: string | number;\n\t\tid: string;\n\t}[][] {\n\t\tconst data_row_length = _values.length;\n\t\tif (data_row_length === 0) return [];\n\t\treturn Array(row_count[1] === \"fixed\" ? row_count[0] : data_row_length)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\treturn Array(\n\t\t\t\t\tcol_count[1] === \"fixed\"\n\t\t\t\t\t\t? col_count[0]\n\t\t\t\t\t\t: _values[0].length || headers.length\n\t\t\t\t)\n\t\t\t\t\t.fill(0)\n\t\t\t\t\t.map((_, j) => {\n\t\t\t\t\t\tconst id = make_id();\n\t\t\t\t\t\tels[id] = els[id] || { input: null, cell: null };\n\t\t\t\t\t\tconst obj = { value: _values?.[i]?.[j] ?? \"\", id };\n\t\t\t\t\t\tdata_binding[id] = obj;\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t});\n\t\t\t});\n\t}\n\n\tlet _headers = make_headers(headers, col_count, els);\n\tlet old_headers: string[] = headers;\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\t_headers = make_headers(headers, col_count, els);\n\t\t\told_headers = JSON.parse(JSON.stringify(headers));\n\t\t}\n\t}\n\n\tlet data: { id: string; value: string | number }[][] = [[]];\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\n\t$: if (!dequal(values, old_val)) {\n\t\tdata = process_data(values as (string | number)[][]);\n\t\told_val = JSON.parse(JSON.stringify(values)) as (string | number)[][];\n\t}\n\n\tlet previous_headers = _headers.map((h) => h.value);\n\tlet previous_data = data.map((row) => row.map((cell) => String(cell.value)));\n\n\tasync function trigger_change(): Promise<void> {\n\t\t// shouldnt trigger if data changed due to search\n\t\tif (current_search_query) return;\n\t\tconst current_headers = _headers.map((h) => h.value);\n\t\tconst current_data = data.map((row) =>\n\t\t\trow.map((cell) => String(cell.value))\n\t\t);\n\n\t\tif (\n\t\t\t!dequal(current_data, previous_data) ||\n\t\t\t!dequal(current_headers, previous_headers)\n\t\t) {\n\t\t\t// We dispatch the value as part of the change event to ensure that the value is updated\n\t\t\t// in the parent component and the updated value is passed into the user's function\n\t\t\tdispatch(\"change\", {\n\t\t\t\tdata: data.map((row) => row.map((cell) => cell.value)),\n\t\t\t\theaders: _headers.map((h) => h.value),\n\t\t\t\tmetadata: null // the metadata (display value, styling) cannot be changed by the user so we don't need to pass it up\n\t\t\t});\n\t\t\tif (!value_is_output) {\n\t\t\t\tdispatch(\"input\");\n\t\t\t}\n\t\t\tprevious_data = current_data;\n\t\t\tprevious_headers = current_headers;\n\t\t}\n\t}\n\n\tfunction get_sort_status(\n\t\tname: string,\n\t\t_sort?: number,\n\t\tdirection?: SortDirection\n\t): \"none\" | \"ascending\" | \"descending\" {\n\t\tif (!_sort) return \"none\";\n\t\tif (headers[_sort] === name) {\n\t\t\tif (direction === \"asc\") return \"ascending\";\n\t\t\tif (direction === \"des\") return \"descending\";\n\t\t}\n\t\treturn \"none\";\n\t}\n\n\t// eslint-disable-next-line complexity\n\tasync function handle_keydown(event: KeyboardEvent): Promise<void> {\n\t\tif (selected_header !== false && header_edit === false) {\n\t\t\tswitch (event.key) {\n\t\t\t\tcase \"ArrowDown\":\n\t\t\t\t\tselected = [0, selected_header];\n\t\t\t\t\tselected_cells = [[0, selected_header]];\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowLeft\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header > 0 ? selected_header - 1 : selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"ArrowRight\":\n\t\t\t\t\tselected_header =\n\t\t\t\t\t\tselected_header < _headers.length - 1\n\t\t\t\t\t\t\t? selected_header + 1\n\t\t\t\t\t\t\t: selected_header;\n\t\t\t\t\treturn;\n\t\t\t\tcase \"Escape\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tselected_header = false;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"Enter\":\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tif (editable) {\n\t\t\t\t\t\theader_edit = selected_header;\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (event.key === \"Delete\" || event.key === \"Backspace\") {\n\t\t\tif (!editable) return;\n\n\t\t\tif (editing) {\n\t\t\t\tconst [row, col] = editing;\n\t\t\t\tconst input_el = els[data[row][col].id].input;\n\t\t\t\tif (input_el && input_el.selectionStart !== input_el.selectionEnd) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (\n\t\t\t\t\tevent.key === \"Delete\" &&\n\t\t\t\t\tinput_el?.selectionStart !== input_el?.value.length\n\t\t\t\t) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (event.key === \"Backspace\" && input_el?.selectionStart !== 0) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tevent.preventDefault();\n\t\t\tif (selected_cells.length > 0) {\n\t\t\t\tdata = handle_delete_key(data, selected_cells);\n\t\t\t\tdispatch(\"change\", {\n\t\t\t\t\tdata: data.map((row) => row.map((cell) => cell.value)),\n\t\t\t\t\theaders: _headers.map((h) => h.value),\n\t\t\t\t\tmetadata: null\n\t\t\t\t});\n\t\t\t\tif (!value_is_output) {\n\t\t\t\t\tdispatch(\"input\");\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tif (event.key === \"c\" && (event.metaKey || event.ctrlKey)) {\n\t\t\tevent.preventDefault();\n\t\t\tif (selected_cells.length > 0) {\n\t\t\t\tawait handle_copy();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tif (!selected) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst [i, j] = selected;\n\n\t\tswitch (event.key) {\n\t\t\tcase \"ArrowRight\":\n\t\t\tcase \"ArrowLeft\":\n\t\t\tcase \"ArrowDown\":\n\t\t\tcase \"ArrowUp\":\n\t\t\t\tif (editing) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tconst next_coords = move_cursor(event.key, [i, j], data);\n\t\t\t\tif (next_coords) {\n\t\t\t\t\tif (event.shiftKey) {\n\t\t\t\t\t\tselected_cells = get_range_selection(\n\t\t\t\t\t\t\tselected_cells.length > 0 ? selected_cells[0] : [i, j],\n\t\t\t\t\t\t\tnext_coords\n\t\t\t\t\t\t);\n\t\t\t\t\t\tediting = false;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tselected_cells = [next_coords];\n\t\t\t\t\t\tediting = false;\n\t\t\t\t\t}\n\t\t\t\t\tselected = next_coords;\n\t\t\t\t} else if (\n\t\t\t\t\tnext_coords === false &&\n\t\t\t\t\tevent.key === \"ArrowUp\" &&\n\t\t\t\t\ti === 0\n\t\t\t\t) {\n\t\t\t\t\tselected_header = j;\n\t\t\t\t\tselected = false;\n\t\t\t\t\tselected_cells = [];\n\t\t\t\t\tediting = false;\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\tcase \"Escape\":\n\t\t\t\tif (!editable) break;\n\t\t\t\tevent.preventDefault();\n\t\t\t\tediting = false;\n\t\t\t\tbreak;\n\t\t\tcase \"Enter\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tif (editable) {\n\t\t\t\t\tif (event.shiftKey) {\n\t\t\t\t\t\tadd_row(i);\n\t\t\t\t\t\tawait tick();\n\t\t\t\t\t\tselected = [i + 1, j];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (dequal(editing, [i, j])) {\n\t\t\t\t\t\t\tconst cell_id = data[i][j].id;\n\t\t\t\t\t\t\tconst input_el = els[cell_id].input;\n\t\t\t\t\t\t\tif (input_el) {\n\t\t\t\t\t\t\t\tdata[i][j].value = input_el.value;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tediting = false;\n\t\t\t\t\t\t\tawait tick();\n\t\t\t\t\t\t\tselected = [i, j];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tediting = [i, j];\n\t\t\t\t\t\t\tclear_on_focus = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"Tab\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tediting = false;\n\t\t\t\tconst next_cell = get_next_cell_coordinates(\n\t\t\t\t\t[i, j],\n\t\t\t\t\tdata,\n\t\t\t\t\tevent.shiftKey\n\t\t\t\t);\n\t\t\t\tif (next_cell) {\n\t\t\t\t\tselected_cells = [next_cell];\n\t\t\t\t\tselected = next_cell;\n\t\t\t\t\tif (editable) {\n\t\t\t\t\t\tediting = next_cell;\n\t\t\t\t\t\tclear_on_focus = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tif (!editable) break;\n\t\t\t\tif (\n\t\t\t\t\t(!editing || (editing && dequal(editing, [i, j]))) &&\n\t\t\t\t\tevent.key.length === 1\n\t\t\t\t) {\n\t\t\t\t\tclear_on_focus = true;\n\t\t\t\t\tediting = [i, j];\n\t\t\t\t}\n\t\t}\n\t}\n\n\ttype SortDirection = \"asc\" | \"des\";\n\tlet sort_direction: SortDirection | undefined;\n\tlet sort_by: number | undefined;\n\n\tfunction handle_sort(col: number, direction: SortDirection): void {\n\t\tif (typeof sort_by !== \"number\" || sort_by !== col) {\n\t\t\tsort_direction = direction;\n\t\t\tsort_by = col;\n\t\t} else if (sort_by === col) {\n\t\t\tif (sort_direction === direction) {\n\t\t\t\tsort_direction = undefined;\n\t\t\t\tsort_by = undefined;\n\t\t\t} else {\n\t\t\t\tsort_direction = direction;\n\t\t\t}\n\t\t}\n\t}\n\n\tasync function edit_header(i: number, _select = false): Promise<void> {\n\t\tif (!editable || header_edit === i) return;\n\t\tselected = false;\n\t\tselected_cells = [];\n\t\tselected_header = i;\n\t\theader_edit = i;\n\t}\n\n\tfunction end_header_edit(event: CustomEvent<KeyboardEvent>): void {\n\t\tif (!editable) return;\n\n\t\tswitch (event.detail.key) {\n\t\t\tcase \"Escape\":\n\t\t\tcase \"Enter\":\n\t\t\tcase \"Tab\":\n\t\t\t\tevent.preventDefault();\n\t\t\t\tselected = false;\n\t\t\t\tselected_header = header_edit;\n\t\t\t\theader_edit = false;\n\t\t\t\tparent.focus();\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tasync function add_row(index?: number): Promise<void> {\n\t\tparent.focus();\n\n\t\tif (row_count[1] !== \"dynamic\") return;\n\n\t\tconst new_row = Array(data[0]?.length || headers.length)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\tconst _id = make_id();\n\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t});\n\n\t\tif (data.length === 0) {\n\t\t\tdata = [new_row];\n\t\t} else if (index !== undefined && index >= 0 && index <= data.length) {\n\t\t\tdata.splice(index, 0, new_row);\n\t\t} else {\n\t\t\tdata.push(new_row);\n\t\t}\n\n\t\tdata = data;\n\t\tselected = [index !== undefined ? index : data.length - 1, 0];\n\t}\n\n\t$: (data || _headers) && trigger_change();\n\n\tasync function add_col(index?: number): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\n\t\tconst insert_index = index !== undefined ? index : data[0].length;\n\n\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata[i].splice(insert_index, 0, { id: _id, value: \"\" });\n\t\t}\n\n\t\theaders.splice(insert_index, 0, `Header ${headers.length + 1}`);\n\n\t\tdata = data;\n\t\theaders = headers;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tedit_header(insert_index, true);\n\t\t\tconst new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n\t\t\tparent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n\t\t});\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tif (handle_click_outside_util(event, parent)) {\n\t\t\tediting = false;\n\t\t\tselected_cells = [];\n\t\t\theader_edit = false;\n\t\t\tselected_header = false;\n\t\t\tactive_cell_menu = null;\n\t\t\tactive_header_menu = null;\n\t\t}\n\t}\n\n\t$: max = get_max(data);\n\n\t$: cells[0] && set_cell_widths();\n\tlet cells: HTMLTableCellElement[] = [];\n\tlet parent: HTMLDivElement;\n\tlet table: HTMLTableElement;\n\n\tfunction set_cell_widths(): void {\n\t\tconst widths = cells.map((el) => el?.clientWidth || 0);\n\t\tif (widths.length === 0) return;\n\n\t\tif (show_row_numbers) {\n\t\t\tparent.style.setProperty(`--cell-width-row-number`, `${widths[0]}px`);\n\t\t}\n\t\tconst data_cells = show_row_numbers ? widths.slice(1) : widths;\n\t\tdata_cells.forEach((width, i) => {\n\t\t\tif (!column_widths[i]) {\n\t\t\t\tparent.style.setProperty(\n\t\t\t\t\t`--cell-width-${i}`,\n\t\t\t\t\t`${width - scrollbar_width / data_cells.length}px`\n\t\t\t\t);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction get_cell_width(index: number): string {\n\t\treturn column_widths[index] || `var(--cell-width-${index})`;\n\t}\n\n\tlet table_height: number =\n\t\tvalues.slice(0, (max_height / values.length) * 37).length * 37 + 37;\n\tlet scrollbar_width = 0;\n\n\tfunction sort_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null,\n\t\tcol?: number,\n\t\tdir?: SortDirection\n\t): void {\n\t\tlet id = null;\n\t\tif (selected && selected[0] in _data && selected[1] in _data[selected[0]]) {\n\t\t\tid = _data[selected[0]][selected[1]].id;\n\t\t}\n\t\tif (typeof col !== \"number\" || !dir) {\n\t\t\treturn;\n\t\t}\n\n\t\tsort_table_data(_data, _display_value, _styling, col, dir);\n\t\tdata = data;\n\n\t\tif (id) {\n\t\t\tconst [i, j] = get_current_indices(id, data);\n\t\t\tselected = [i, j];\n\t\t}\n\t}\n\n\t$: sort_data(data, display_value, styling, sort_by, sort_direction);\n\n\t$: selected_index = !!selected && selected[0];\n\n\tlet is_visible = false;\n\n\tonMount(() => {\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting && !is_visible) {\n\t\t\t\t\tset_cell_widths();\n\t\t\t\t\tdata = data;\n\t\t\t\t}\n\t\t\t\tis_visible = entry.isIntersecting;\n\t\t\t});\n\t\t});\n\n\t\tobserver.observe(parent);\n\t\tdocument.addEventListener(\"click\", handle_click_outside);\n\t\twindow.addEventListener(\"resize\", handle_resize);\n\t\tdocument.addEventListener(\"fullscreenchange\", handle_fullscreen_change);\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t\tdocument.removeEventListener(\"click\", handle_click_outside);\n\t\t\twindow.removeEventListener(\"resize\", handle_resize);\n\t\t\tdocument.removeEventListener(\n\t\t\t\t\"fullscreenchange\",\n\t\t\t\thandle_fullscreen_change\n\t\t\t);\n\t\t};\n\t});\n\n\tfunction handle_cell_click(\n\t\tevent: MouseEvent,\n\t\trow: number,\n\t\tcol: number\n\t): void {\n\t\tif (event.target instanceof HTMLAnchorElement) {\n\t\t\treturn;\n\t\t}\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tif (show_row_numbers && col === -1) return;\n\n\t\tclear_on_focus = false;\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t\tselected_header = false;\n\t\theader_edit = false;\n\n\t\tselected_cells = handle_selection([row, col], selected_cells, event);\n\t\tparent.focus();\n\n\t\tif (editable) {\n\t\t\tif (selected_cells.length === 1) {\n\t\t\t\tediting = [row, col];\n\t\t\t\ttick().then(() => {\n\t\t\t\t\tconst input_el = els[data[row][col].id].input;\n\t\t\t\t\tif (input_el) {\n\t\t\t\t\t\tinput_el.focus();\n\t\t\t\t\t\tinput_el.selectionStart = input_el.selectionEnd =\n\t\t\t\t\t\t\tinput_el.value.length;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tediting = false;\n\t\t\t}\n\t\t}\n\n\t\ttoggle_cell_button(row, col);\n\n\t\tdispatch(\"select\", {\n\t\t\tindex: [row, col],\n\t\t\tvalue: get_data_at(row, col),\n\t\t\trow_value: data[row].map((d) => d.value)\n\t\t});\n\t}\n\n\tfunction toggle_cell_menu(event: MouseEvent, row: number, col: number): void {\n\t\tevent.stopPropagation();\n\t\tif (\n\t\t\tactive_cell_menu &&\n\t\t\tactive_cell_menu.row === row &&\n\t\t\tactive_cell_menu.col === col\n\t\t) {\n\t\t\tactive_cell_menu = null;\n\t\t} else {\n\t\t\tconst cell = (event.target as HTMLElement).closest(\"td\");\n\t\t\tif (cell) {\n\t\t\t\tconst rect = cell.getBoundingClientRect();\n\t\t\t\tactive_cell_menu = { row, col, x: rect.right, y: rect.bottom };\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction add_row_at(index: number, position: \"above\" | \"below\"): void {\n\t\tconst row_index = position === \"above\" ? index : index + 1;\n\t\tadd_row(row_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction add_col_at(index: number, position: \"left\" | \"right\"): void {\n\t\tconst col_index = position === \"left\" ? index : index + 1;\n\t\tadd_col(col_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction handle_resize(): void {\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t\tselected_cells = [];\n\t\tselected = false;\n\t\tediting = false;\n\t\tset_cell_widths();\n\t}\n\n\tlet active_button: {\n\t\ttype: \"header\" | \"cell\";\n\t\trow?: number;\n\t\tcol: number;\n\t} | null = null;\n\n\tfunction toggle_header_button(col: number): void {\n\t\tactive_button =\n\t\t\tactive_button?.type === \"header\" && active_button.col === col\n\t\t\t\t? null\n\t\t\t\t: { type: \"header\", col };\n\t}\n\n\tfunction toggle_cell_button(row: number, col: number): void {\n\t\tactive_button =\n\t\t\tactive_button?.type === \"cell\" &&\n\t\t\tactive_button.row === row &&\n\t\t\tactive_button.col === col\n\t\t\t\t? null\n\t\t\t\t: { type: \"cell\", row, col };\n\t}\n\n\tfunction toggle_fullscreen(): void {\n\t\tif (!document.fullscreenElement) {\n\t\t\tparent.requestFullscreen();\n\t\t\tis_fullscreen = true;\n\t\t} else {\n\t\t\tdocument.exitFullscreen();\n\t\t\tis_fullscreen = false;\n\t\t}\n\t}\n\n\tfunction handle_fullscreen_change(): void {\n\t\tis_fullscreen = !!document.fullscreenElement;\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tawait copy_table_data(data, selected_cells);\n\t\tcopy_flash = true;\n\t\tsetTimeout(() => {\n\t\t\tcopy_flash = false;\n\t\t}, 800);\n\t}\n\n\tfunction toggle_header_menu(event: MouseEvent, col: number): void {\n\t\tevent.stopPropagation();\n\t\tif (active_header_menu && active_header_menu.col === col) {\n\t\t\tactive_header_menu = null;\n\t\t} else {\n\t\t\tconst header = (event.target as HTMLElement).closest(\"th\");\n\t\t\tif (header) {\n\t\t\t\tconst rect = header.getBoundingClientRect();\n\t\t\t\tactive_header_menu = { col, x: rect.right, y: rect.bottom };\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tasync function delete_row(index: number): Promise<void> {\n\t\tparent.focus();\n\t\tif (row_count[1] !== \"dynamic\") return;\n\t\tif (data.length <= 1) return;\n\t\tdata.splice(index, 1);\n\t\tdata = data;\n\t\tselected = false;\n\t}\n\n\tasync function delete_col(index: number): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\t\tif (_headers.length <= 1) return;\n\n\t\t_headers.splice(index, 1);\n\t\t_headers = _headers;\n\n\t\tif (data.length > 0) {\n\t\t\tdata.forEach((row) => {\n\t\t\t\trow.splice(index, 1);\n\t\t\t});\n\t\t\tdata = data;\n\t\t}\n\t\tselected = false;\n\t}\n\n\tfunction delete_row_at(index: number): void {\n\t\tdelete_row(index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction delete_col_at(index: number): void {\n\t\tdelete_col(index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tlet row_order: number[] = [];\n\n\t$: {\n\t\tif (\n\t\t\ttypeof sort_by === \"number\" &&\n\t\t\tsort_direction &&\n\t\t\tsort_by >= 0 &&\n\t\t\tsort_by < data[0].length\n\t\t) {\n\t\t\tconst indices = [...Array(data.length)].map((_, i) => i);\n\t\t\tconst sort_index = sort_by as number;\n\t\t\tindices.sort((a, b) => {\n\t\t\t\tconst row_a = data[a];\n\t\t\t\tconst row_b = data[b];\n\t\t\t\tif (\n\t\t\t\t\t!row_a ||\n\t\t\t\t\t!row_b ||\n\t\t\t\t\tsort_index >= row_a.length ||\n\t\t\t\t\tsort_index >= row_b.length\n\t\t\t\t)\n\t\t\t\t\treturn 0;\n\t\t\t\tconst val_a = row_a[sort_index].value;\n\t\t\t\tconst val_b = row_b[sort_index].value;\n\t\t\t\tconst comp = val_a < val_b ? -1 : val_a > val_b ? 1 : 0;\n\t\t\t\treturn sort_direction === \"asc\" ? comp : -comp;\n\t\t\t});\n\t\t\trow_order = indices;\n\t\t} else {\n\t\t\trow_order = [...Array(data.length)].map((_, i) => i);\n\t\t}\n\t}\n\n\tfunction handle_select_column(col: number): void {\n\t\tselected_cells = select_column(data, col);\n\t\tselected = selected_cells[0];\n\t\tediting = false;\n\t}\n\n\tfunction handle_select_row(row: number): void {\n\t\tselected_cells = select_row(data, row);\n\t\tselected = selected_cells[0];\n\t\tediting = false;\n\t}\n\n\tlet coords: CellCoordinate;\n\t$: if (selected !== false) coords = selected;\n\n\t$: if (selected !== false) {\n\t\tconst positions = calculate_selection_positions(\n\t\t\tselected,\n\t\t\tdata,\n\t\t\tels,\n\t\t\tparent,\n\t\t\ttable\n\t\t);\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--selected-col-pos\",\n\t\t\tpositions.col_pos\n\t\t);\n\t\tif (positions.row_pos) {\n\t\t\tdocument.documentElement.style.setProperty(\n\t\t\t\t\"--selected-row-pos\",\n\t\t\t\tpositions.row_pos\n\t\t\t);\n\t\t}\n\t}\n\n\tlet current_search_query: string | null = null;\n\n\tfunction handle_search(search_query: string | null): void {\n\t\tcurrent_search_query = search_query;\n\t\tdispatch(\"search\", search_query);\n\t}\n\n\tfunction commit_filter(): void {\n\t\tif (current_search_query && show_search === \"filter\") {\n\t\t\tdispatch(\"change\", {\n\t\t\t\tdata: data.map((row) => row.map((cell) => cell.value)),\n\t\t\t\theaders: _headers.map((h) => h.value),\n\t\t\t\tmetadata: null\n\t\t\t});\n\t\t\tif (!value_is_output) {\n\t\t\t\tdispatch(\"input\");\n\t\t\t}\n\t\t\tcurrent_search_query = null;\n\t\t}\n\t}\n\n\tfunction handle_header_click(event: MouseEvent, col: number): void {\n\t\tif (event.target instanceof HTMLAnchorElement) {\n\t\t\treturn;\n\t\t}\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tif (!editable) return;\n\n\t\tclear_on_focus = false;\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t\tselected = false;\n\t\tselected_cells = [];\n\t\tselected_header = col;\n\t\theader_edit = col;\n\n\t\tparent.focus();\n\t}\n</script>\n\n<svelte:window on:resize={() => set_cell_widths()} />\n\n<div class=\"table-container\">\n\t{#if (label && label.length !== 0 && show_label) || show_fullscreen_button || show_copy_button || show_search !== \"none\"}\n\t\t<div class=\"header-row\">\n\t\t\t{#if label && label.length !== 0 && show_label}\n\t\t\t\t<div class=\"label\">\n\t\t\t\t\t<p>{label}</p>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t<Toolbar\n\t\t\t\t{show_fullscreen_button}\n\t\t\t\t{is_fullscreen}\n\t\t\t\ton:click={toggle_fullscreen}\n\t\t\t\ton_copy={handle_copy}\n\t\t\t\t{show_copy_button}\n\t\t\t\t{show_search}\n\t\t\t\ton:search={(e) => handle_search(e.detail)}\n\t\t\t\ton_commit_filter={commit_filter}\n\t\t\t\t{current_search_query}\n\t\t\t/>\n\t\t</div>\n\t{/if}\n\t<div\n\t\tbind:this={parent}\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"height:{table_height}px;\"\n\t\tclass:menu-open={active_cell_menu || active_header_menu}\n\t\ton:keydown={(e) => handle_keydown(e)}\n\t\trole=\"grid\"\n\t\ttabindex=\"0\"\n\t>\n\t\t{#if selected !== false && selected_cells.length === 1}\n\t\t\t<button\n\t\t\t\tclass=\"selection-button selection-button-column\"\n\t\t\t\ton:click|stopPropagation={() => handle_select_column(coords[1])}\n\t\t\t\taria-label=\"Select column\"\n\t\t\t>\n\t\t\t\t&#8942;\n\t\t\t</button>\n\t\t\t<button\n\t\t\t\tclass=\"selection-button selection-button-row\"\n\t\t\t\ton:click|stopPropagation={() => handle_select_row(coords[0])}\n\t\t\t\taria-label=\"Select row\"\n\t\t\t>\n\t\t\t\t&#8942;\n\t\t\t</button>\n\t\t{/if}\n\t\t<table\n\t\t\tbind:contentRect={t_rect}\n\t\t\tbind:this={table}\n\t\t\tclass:fixed-layout={column_widths.length != 0}\n\t\t>\n\t\t\t{#if label && label.length !== 0}\n\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t{/if}\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass=\"row-number-header frozen-column always-frozen\"\n\t\t\t\t\t\t\tstyle=\"left: 0;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<div class=\"header-content\">\n\t\t\t\t\t\t\t\t\t<div class=\"header-text\"></div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<th\n\t\t\t\t\t\t\tclass:frozen-column={i < actual_pinned_columns}\n\t\t\t\t\t\t\tclass:last-frozen={i === actual_pinned_columns - 1}\n\t\t\t\t\t\t\tclass:focus={header_edit === i || selected_header === i}\n\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\tstyle=\"width: {get_cell_width(i)}; left: {i <\n\t\t\t\t\t\t\tactual_pinned_columns\n\t\t\t\t\t\t\t\t? i === 0\n\t\t\t\t\t\t\t\t\t? show_row_numbers\n\t\t\t\t\t\t\t\t\t\t? 'var(--cell-width-row-number)'\n\t\t\t\t\t\t\t\t\t\t: '0'\n\t\t\t\t\t\t\t\t\t: `calc(${show_row_numbers ? 'var(--cell-width-row-number) + ' : ''}${Array(\n\t\t\t\t\t\t\t\t\t\t\ti\n\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t.fill(0)\n\t\t\t\t\t\t\t\t\t\t\t.map((_, idx) => `var(--cell-width-${idx})`)\n\t\t\t\t\t\t\t\t\t\t\t.join(' + ')})`\n\t\t\t\t\t\t\t\t: 'auto'};\"\n\t\t\t\t\t\t\ton:click={(event) => handle_header_click(event, i)}\n\t\t\t\t\t\t\ton:mousedown={(event) => {\n\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<div class=\"header-content\">\n\t\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\t\t\t\t\t\ton:keydown={end_header_edit}\n\t\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{#if header_edit !== i}\n\t\t\t\t\t\t\t\t\t\t<div class=\"sort-buttons\">\n\t\t\t\t\t\t\t\t\t\t\t<SortIcon\n\t\t\t\t\t\t\t\t\t\t\t\tdirection={sort_by === i ? sort_direction : null}\n\t\t\t\t\t\t\t\t\t\t\t\ton:sort={({ detail }) => handle_sort(i, detail)}\n\t\t\t\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{#if editable}\n\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\tclass=\"cell-menu-button\"\n\t\t\t\t\t\t\t\t\t\ton:click={(event) => toggle_header_menu(event, i)}\n\t\t\t\t\t\t\t\t\t\ton:touchstart={(event) => {\n\t\t\t\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\t\t\t\tconst touch = event.touches[0];\n\t\t\t\t\t\t\t\t\t\t\tconst mouseEvent = new MouseEvent(\"click\", {\n\t\t\t\t\t\t\t\t\t\t\t\tclientX: touch.clientX,\n\t\t\t\t\t\t\t\t\t\t\t\tclientY: touch.clientY,\n\t\t\t\t\t\t\t\t\t\t\t\tbubbles: true,\n\t\t\t\t\t\t\t\t\t\t\t\tcancelable: true,\n\t\t\t\t\t\t\t\t\t\t\t\tview: window\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\ttoggle_header_menu(mouseEvent, i);\n\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t&#8942;\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</th>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t<tr>\n\t\t\t\t\t{#each max as { value, id }, j (id)}\n\t\t\t\t\t\t<td tabindex=\"-1\" bind:this={cells[j]}>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t\t<Upload\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\t{root}\n\t\t\ton:load={({ detail }) =>\n\t\t\t\thandle_file_upload(\n\t\t\t\t\tdetail.data,\n\t\t\t\t\t(head) => {\n\t\t\t\t\t\t_headers = make_headers(\n\t\t\t\t\t\t\thead.map((h) => h ?? \"\"),\n\t\t\t\t\t\t\tcol_count,\n\t\t\t\t\t\t\tels\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturn _headers;\n\t\t\t\t\t},\n\t\t\t\t\t(vals) => {\n\t\t\t\t\t\tvalues = vals;\n\t\t\t\t\t}\n\t\t\t\t)}\n\t\t\tbind:dragging\n\t\t\taria_label={i18n(\"dataframe.drop_to_upload\")}\n\t\t>\n\t\t\t<div class=\"table-wrap\">\n\t\t\t\t<VirtualTable\n\t\t\t\t\tbind:items={data}\n\t\t\t\t\t{max_height}\n\t\t\t\t\tbind:actual_height={table_height}\n\t\t\t\t\tbind:table_scrollbar_width={scrollbar_width}\n\t\t\t\t\tselected={selected_index}\n\t\t\t\t\tdisable_scroll={active_cell_menu !== null ||\n\t\t\t\t\t\tactive_header_menu !== null}\n\t\t\t\t>\n\t\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<tr slot=\"thead\">\n\t\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t\t<th\n\t\t\t\t\t\t\t\tclass=\"row-number-header frozen-column always-frozen\"\n\t\t\t\t\t\t\t\tstyle=\"left: 0;\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t\t<div class=\"header-content\">\n\t\t\t\t\t\t\t\t\t\t<div class=\"header-text\"></div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</th>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t\t<th\n\t\t\t\t\t\t\t\tclass:frozen-column={i < actual_pinned_columns}\n\t\t\t\t\t\t\t\tclass:last-frozen={i === actual_pinned_columns - 1}\n\t\t\t\t\t\t\t\tclass:focus={header_edit === i || selected_header === i}\n\t\t\t\t\t\t\t\taria-sort={get_sort_status(value, sort_by, sort_direction)}\n\t\t\t\t\t\t\t\tstyle=\"width: {get_cell_width(i)}; left: {i <\n\t\t\t\t\t\t\t\tactual_pinned_columns\n\t\t\t\t\t\t\t\t\t? i === 0\n\t\t\t\t\t\t\t\t\t\t? show_row_numbers\n\t\t\t\t\t\t\t\t\t\t\t? 'var(--cell-width-row-number)'\n\t\t\t\t\t\t\t\t\t\t\t: '0'\n\t\t\t\t\t\t\t\t\t\t: `calc(${show_row_numbers ? 'var(--cell-width-row-number) + ' : ''}${Array(\n\t\t\t\t\t\t\t\t\t\t\t\ti\n\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t.fill(0)\n\t\t\t\t\t\t\t\t\t\t\t\t.map((_, idx) => `var(--cell-width-${idx})`)\n\t\t\t\t\t\t\t\t\t\t\t\t.join(' + ')})`\n\t\t\t\t\t\t\t\t\t: 'auto'};\"\n\t\t\t\t\t\t\t\ton:click={(event) => handle_header_click(event, i)}\n\t\t\t\t\t\t\t\ton:mousedown={(event) => {\n\t\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t\t<div class=\"header-content\">\n\t\t\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\t\t\t\t\t\t\theader\n\t\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t{#if header_edit !== i}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"sort-buttons\">\n\t\t\t\t\t\t\t\t\t\t\t\t<SortIcon\n\t\t\t\t\t\t\t\t\t\t\t\t\tdirection={sort_by === i ? sort_direction : null}\n\t\t\t\t\t\t\t\t\t\t\t\t\ton:sort={({ detail }) => handle_sort(i, detail)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{#if editable}\n\t\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\t\tclass=\"cell-menu-button\"\n\t\t\t\t\t\t\t\t\t\t\ton:click={(event) => toggle_header_menu(event, i)}\n\t\t\t\t\t\t\t\t\t\t\ton:touchstart={(event) => {\n\t\t\t\t\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\t\t\t\t\tconst touch = event.touches[0];\n\t\t\t\t\t\t\t\t\t\t\t\tconst mouseEvent = new MouseEvent(\"click\", {\n\t\t\t\t\t\t\t\t\t\t\t\t\tclientX: touch.clientX,\n\t\t\t\t\t\t\t\t\t\t\t\t\tclientY: touch.clientY,\n\t\t\t\t\t\t\t\t\t\t\t\t\tbubbles: true,\n\t\t\t\t\t\t\t\t\t\t\t\t\tcancelable: true,\n\t\t\t\t\t\t\t\t\t\t\t\t\tview: window\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\ttoggle_header_menu(mouseEvent, i);\n\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t&#8942;\n\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</th>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr slot=\"tbody\" let:item let:index class:row_odd={index % 2 === 0}>\n\t\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t\t<td\n\t\t\t\t\t\t\t\tclass=\"row-number frozen-column always-frozen\"\n\t\t\t\t\t\t\t\tstyle=\"left: 0;\"\n\t\t\t\t\t\t\t\ttabindex=\"-1\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{index + 1}\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#each item as { value, id }, j (id)}\n\t\t\t\t\t\t\t<td\n\t\t\t\t\t\t\t\tclass:frozen-column={j < actual_pinned_columns}\n\t\t\t\t\t\t\t\tclass:last-frozen={j === actual_pinned_columns - 1}\n\t\t\t\t\t\t\t\ttabindex={show_row_numbers && j === 0 ? -1 : 0}\n\t\t\t\t\t\t\t\tbind:this={els[id].cell}\n\t\t\t\t\t\t\t\ton:touchstart={(event) => {\n\t\t\t\t\t\t\t\t\tconst touch = event.touches[0];\n\t\t\t\t\t\t\t\t\tconst mouseEvent = new MouseEvent(\"click\", {\n\t\t\t\t\t\t\t\t\t\tclientX: touch.clientX,\n\t\t\t\t\t\t\t\t\t\tclientY: touch.clientY,\n\t\t\t\t\t\t\t\t\t\tbubbles: true,\n\t\t\t\t\t\t\t\t\t\tcancelable: true,\n\t\t\t\t\t\t\t\t\t\tview: window\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\thandle_cell_click(mouseEvent, index, j);\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:mousedown={(event) => {\n\t\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:click={(event) => handle_cell_click(event, index, j)}\n\t\t\t\t\t\t\t\tstyle=\"width: {get_cell_width(j)}; left: {j <\n\t\t\t\t\t\t\t\tactual_pinned_columns\n\t\t\t\t\t\t\t\t\t? j === 0\n\t\t\t\t\t\t\t\t\t\t? show_row_numbers\n\t\t\t\t\t\t\t\t\t\t\t? 'var(--cell-width-row-number)'\n\t\t\t\t\t\t\t\t\t\t\t: '0'\n\t\t\t\t\t\t\t\t\t\t: `calc(${show_row_numbers ? 'var(--cell-width-row-number) + ' : ''}${Array(\n\t\t\t\t\t\t\t\t\t\t\t\tj\n\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t.fill(0)\n\t\t\t\t\t\t\t\t\t\t\t\t.map((_, idx) => `var(--cell-width-${idx})`)\n\t\t\t\t\t\t\t\t\t\t\t\t.join(' + ')})`\n\t\t\t\t\t\t\t\t\t: 'auto'}; {styling?.[index]?.[j] || ''}\"\n\t\t\t\t\t\t\t\tclass:flash={copy_flash &&\n\t\t\t\t\t\t\t\t\tis_cell_selected([index, j], selected_cells)}\n\t\t\t\t\t\t\t\tclass={is_cell_selected([index, j], selected_cells)}\n\t\t\t\t\t\t\t\tclass:menu-active={active_cell_menu &&\n\t\t\t\t\t\t\t\t\tactive_cell_menu.row === index &&\n\t\t\t\t\t\t\t\t\tactive_cell_menu.col === j}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t\tbind:value={data[index][j].value}\n\t\t\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t\t\tdisplay_value={display_value?.[index]?.[j]}\n\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\t\tedit={dequal(editing, [index, j])}\n\t\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\t\ton:blur={() => {\n\t\t\t\t\t\t\t\t\t\t\tclear_on_focus = false;\n\t\t\t\t\t\t\t\t\t\t\tparent.focus();\n\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\ton:focus={() => {\n\t\t\t\t\t\t\t\t\t\t\tconst row = index;\n\t\t\t\t\t\t\t\t\t\t\tconst col = j;\n\t\t\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\t\t\t!selected_cells.some(([r, c]) => r === row && c === col)\n\t\t\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\t\t\tselected_cells = [[row, col]];\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t{clear_on_focus}\n\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{#if editable && should_show_cell_menu([index, j], selected_cells, editable)}\n\t\t\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\t\t\tclass=\"cell-menu-button\"\n\t\t\t\t\t\t\t\t\t\t\ton:click={(event) => toggle_cell_menu(event, index, j)}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t&#8942;\n\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</VirtualTable>\n\t\t\t</div>\n\t\t</Upload>\n\t</div>\n</div>\n{#if data.length === 0 && editable && row_count[1] === \"dynamic\"}\n\t<div class=\"add-row-container\">\n\t\t<button class=\"add-row-button\" on:click={() => add_row()}>\n\t\t\t<span>+</span>\n\t\t</button>\n\t</div>\n{/if}\n\n{#if active_cell_menu}\n\t<CellMenu\n\t\tx={active_cell_menu.x}\n\t\ty={active_cell_menu.y}\n\t\trow={active_cell_menu.row}\n\t\t{col_count}\n\t\t{row_count}\n\t\ton_add_row_above={() => add_row_at(active_cell_menu?.row || 0, \"above\")}\n\t\ton_add_row_below={() => add_row_at(active_cell_menu?.row || 0, \"below\")}\n\t\ton_add_column_left={() => add_col_at(active_cell_menu?.col || 0, \"left\")}\n\t\ton_add_column_right={() => add_col_at(active_cell_menu?.col || 0, \"right\")}\n\t\ton_delete_row={() => delete_row_at(active_cell_menu?.row || 0)}\n\t\ton_delete_col={() => delete_col_at(active_cell_menu?.col || 0)}\n\t\tcan_delete_rows={data.length > 1}\n\t\tcan_delete_cols={data[0].length > 1}\n\t\t{i18n}\n\t/>\n{/if}\n\n{#if active_header_menu !== null}\n\t<CellMenu\n\t\t{i18n}\n\t\tx={active_header_menu.x}\n\t\ty={active_header_menu.y}\n\t\trow={-1}\n\t\t{col_count}\n\t\t{row_count}\n\t\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \"above\")}\n\t\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \"below\")}\n\t\ton_add_column_left={() => add_col_at(active_header_menu?.col ?? -1, \"left\")}\n\t\ton_add_column_right={() =>\n\t\t\tadd_col_at(active_header_menu?.col ?? -1, \"right\")}\n\t\ton_delete_row={() => delete_row_at(active_cell_menu?.row ?? -1)}\n\t\ton_delete_col={() => delete_col_at(active_header_menu?.col ?? -1)}\n\t\tcan_delete_rows={false}\n\t\tcan_delete_cols={_headers.length > 1}\n\t/>\n{/if}\n\n<style>\n\t.label p {\n\t\tposition: relative;\n\t\tz-index: var(--layer-4);\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t}\n\n\t.table-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-2);\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t}\n\n\t.table-wrap.menu-open {\n\t\toverflow: hidden;\n\t}\n\n\t.table-wrap:focus-within {\n\t\toutline: none;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\ttable {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\tborder-collapse: separate;\n\t}\n\n\t.table-wrap > :global(button) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow: hidden;\n\t}\n\n\tdiv:not(.no-wrap) td {\n\t\toverflow-wrap: anywhere;\n\t}\n\n\tdiv.no-wrap td {\n\t\toverflow-x: hidden;\n\t}\n\n\ttable.fixed-layout {\n\t\ttable-layout: fixed;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: var(--layer-2);\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n\n\ttr > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth,\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\tth.focus,\n\ttd.focus {\n\t\t--ring-color: var(--color-accent);\n\t\tbox-shadow: inset 0 0 0 2px var(--ring-color);\n\t\tz-index: var(--layer-1);\n\t}\n\n\tth.focus {\n\t\tz-index: var(--layer-2);\n\t}\n\n\ttr:last-child td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\ttr:last-child td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\ttr th {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\t.sort-buttons {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-shrink: 0;\n\t\torder: -1;\n\t}\n\n\t.editing {\n\t\tbackground: var(--table-editing);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-start;\n\t\toutline: none;\n\t\tmin-height: var(--size-9);\n\t\tposition: relative;\n\t\theight: 100%;\n\t\tpadding: var(--size-2);\n\t\tbox-sizing: border-box;\n\t\tmargin: 0;\n\t\tgap: var(--size-1);\n\t\toverflow: visible;\n\t\tmin-width: 0;\n\t\tborder-radius: var(--table-radius);\n\t}\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toverflow: hidden;\n\t\tflex-grow: 1;\n\t\tmin-width: 0;\n\t\twhite-space: normal;\n\t\toverflow-wrap: break-word;\n\t\tword-break: normal;\n\t\theight: 100%;\n\t\tgap: var(--size-1);\n\t}\n\n\t.row_odd {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.row_odd.focus {\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.cell-menu-button {\n\t\tflex-shrink: 0;\n\t\tdisplay: none;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-radius);\n\t\twidth: var(--size-5);\n\t\theight: var(--size-5);\n\t\tmin-width: var(--size-5);\n\t\tpadding: 0;\n\t\tmargin-right: var(--spacing-sm);\n\t\tz-index: var(--layer-1);\n\t\tposition: absolute;\n\t\tright: var(--size-1);\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t}\n\n\t.cell-selected .cell-menu-button,\n\tth:hover .cell-menu-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.header-row {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tmin-height: var(--size-6);\n\t\tflex-wrap: nowrap;\n\t\twidth: 100%;\n\t}\n\n\t.label {\n\t\tflex: 1 1 auto;\n\t\tmargin-right: auto;\n\t}\n\n\t.label p {\n\t\tmargin: 0;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\n\t.toolbar {\n\t\tflex: 0 0 auto;\n\t}\n\n\t.row-number,\n\t.row-number-header {\n\t\ttext-align: center;\n\t\tbackground: var(--table-even-background-fill);\n\t\tfont-size: var(--input-text-size);\n\t\tcolor: var(--body-text-color);\n\t\tpadding: var(--size-1);\n\t\tmin-width: var(--size-12);\n\t\twidth: var(--size-12);\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.row-number-header .header-content {\n\t\tjustify-content: space-between;\n\t\tpadding: var(--size-1);\n\t\theight: var(--size-9);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.row-number-header :global(.sort-icons) {\n\t\tmargin-right: 0;\n\t}\n\n\t:global(tbody > tr:nth-child(odd)) .row-number {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.cell-selected {\n\t\t--ring-color: var(--color-accent);\n\t\tbox-shadow: inset 0 0 0 2px var(--ring-color);\n\t\tz-index: var(--layer-1);\n\t\tposition: relative;\n\t}\n\n\t.cell-selected.no-top {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-right {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-left {\n\t\tbox-shadow:\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-right {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-left {\n\t\tbox-shadow:\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-right {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-bottom {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left.no-right {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-left.no-right {\n\t\tbox-shadow: inset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-left.no-right {\n\t\tbox-shadow: inset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left.no-top.no-bottom {\n\t\tbox-shadow: inset -2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-right.no-top.no-bottom {\n\t\tbox-shadow: inset 2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-bottom.no-left.no-right {\n\t\tbox-shadow: none;\n\t}\n\n\t.selection-button {\n\t\tposition: absolute;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder-radius: var(--radius-sm);\n\t\tz-index: var(--layer-4);\n\t}\n\n\t.selection-button-column {\n\t\twidth: var(--size-3);\n\t\theight: var(--size-5);\n\t\ttop: -10px;\n\t\tleft: var(--selected-col-pos);\n\t\ttransform: rotate(90deg);\n\t}\n\n\t.selection-button-row {\n\t\twidth: var(--size-3);\n\t\theight: var(--size-5);\n\t\tleft: -7px;\n\t\ttop: calc(var(--selected-row-pos) - var(--size-5) / 2);\n\t}\n\n\t.table-wrap:not(:focus-within) .selection-button {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.flash.cell-selected {\n\t\tanimation: flash-color 700ms ease-out;\n\t}\n\n\t@keyframes flash-color {\n\t\t0%,\n\t\t30% {\n\t\t\tbackground: var(--color-accent-copied);\n\t\t}\n\n\t\t100% {\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n\n\t.frozen-column {\n\t\tposition: sticky;\n\t\tz-index: var(--layer-2);\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\ttr:nth-child(odd) .frozen-column {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\ttr:nth-child(even) .frozen-column {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\t.always-frozen {\n\t\tz-index: var(--layer-3);\n\t}\n\n\t.add-row-container {\n\t\tmargin-top: var(--size-2);\n\t}\n\n\t.add-row-button {\n\t\twidth: 100%;\n\t\tpadding: var(--size-1);\n\t\tbackground: transparent;\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\ttransition: all 150ms;\n\t}\n\n\t.add-row-button:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t\tborder-style: solid;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDataFrame } from \"./shared/Table.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"./shared/Table.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Headers, Datatype, DataframeValue } from \"./shared/utils\";\n\texport let headers: Headers = [];\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: DataframeValue = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"],\n\t\tmetadata: null\n\t};\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root: string;\n\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let max_height: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let interactive: boolean;\n\texport let show_fullscreen_button = false;\n\texport let max_chars: number | undefined = undefined;\n\texport let show_copy_button = false;\n\texport let show_row_numbers = false;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\n\tlet search_query: string | null = null;\n\t$: filtered_cell_values = search_query\n\t\t? value.data?.filter((row) =>\n\t\t\t\trow.some(\n\t\t\t\t\t(cell) =>\n\t\t\t\t\t\tsearch_query &&\n\t\t\t\t\t\tString(cell).toLowerCase().includes(search_query.toLowerCase())\n\t\t\t\t)\n\t\t\t)\n\t\t: null;\n\texport let pinned_columns = 0;\n\n\t$: _headers = [...(value.headers || headers)];\n\t$: display_value = value?.metadata?.display_value\n\t\t? [...value?.metadata?.display_value]\n\t\t: null;\n\t$: styling =\n\t\t!interactive && value?.metadata?.styling\n\t\t\t? [...value?.metadata?.styling]\n\t\t\t: null;\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\toverflow_behavior=\"visible\"\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Table\n\t\t{root}\n\t\t{label}\n\t\t{show_label}\n\t\t{row_count}\n\t\t{col_count}\n\t\tvalues={filtered_cell_values || value.data}\n\t\t{display_value}\n\t\t{styling}\n\t\theaders={_headers}\n\t\ton:change={(e) => {\n\t\t\tvalue.data = e.detail.data;\n\t\t\tvalue.headers = e.detail.headers;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}}\n\t\ton:input={(e) => gradio.dispatch(\"input\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:search={(e) => (search_query = e.detail)}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={interactive}\n\t\t{max_height}\n\t\ti18n={gradio.i18n}\n\t\t{line_breaks}\n\t\t{column_widths}\n\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\tbind:value_is_output\n\t\t{show_fullscreen_button}\n\t\t{max_chars}\n\t\t{show_copy_button}\n\t\t{show_row_numbers}\n\t\t{show_search}\n\t\t{pinned_columns}\n\t/>\n</Block>\n"], "names": ["insert", "target", "input", "anchor", "ctx", "set_data", "t_value", "dirty", "markdowncode_changes", "create_if_block_2", "span", "truncate_text", "text", "max_length", "str", "edit", "$$props", "value", "display_value", "styling", "header", "datatype", "latex_delimiters", "clear_on_focus", "line_breaks", "editable", "root", "max_chars", "dispatch", "createEventDispatcher", "is_expanded", "el", "use_focus", "node", "_value", "handle_blur", "currentTarget", "$$invalidate", "handle_keydown", "event", "handle_click", "$$value", "display_text", "get_key", "i", "create_if_block", "height", "svelte_virtual_table_viewport", "append", "div", "table", "thead", "tbody", "tfoot", "get_computed_px_amount", "elem", "property", "compStyle", "items", "max_height", "actual_height", "table_scrollbar_width", "start", "end", "selected", "disable_scroll", "average_height", "bottom", "contents", "head_height", "foot_height", "height_map", "mounted", "rows", "top", "viewport", "viewport_height", "visible", "viewport_box", "is_browser", "raf", "cb", "content_height", "refresh_height_map", "_items", "tick", "scrollTop", "row", "_h", "row_height", "remaining", "scrollbar_height", "filtered_height_map", "v", "a", "b", "scroll_and_render", "n", "direction", "is_in_view", "scroll_to_index", "current", "viewport_top", "handle_scroll", "e", "scroll_top", "is_start_overflow", "sortedItems", "row_top_border", "actual_border_collapsed_width", "new_start", "y", "row_heights", "remaining_height", "index", "opts", "align_end", "_itemHeight", "distance", "_opts", "onMount", "ResizeObserverSingleton", "data", "svg", "rect", "path", "create_if_block_1", "create_if_block_3", "create_if_block_4", "create_if_block_5", "icon", "t1_value", "t4_value", "button0", "button1", "t1", "t4", "button", "is_function", "if_block0", "x", "on_add_row_above", "on_add_row_below", "on_add_column_left", "on_add_column_right", "col_count", "row_count", "on_delete_row", "on_delete_col", "can_delete_rows", "can_delete_cols", "i18n", "menu_element", "position_menu", "viewport_width", "menu_rect", "new_x", "new_y", "is_header", "can_add_rows", "can_add_columns", "div1", "div0", "show_fullscreen_button", "show_copy_button", "show_search", "is_fullscreen", "on_copy", "on_commit_filter", "copied", "timer", "current_search_query", "copy_feedback", "handle_copy", "onDestroy", "attr", "button0_aria_label_value", "button0_aria_pressed_value", "toggle_class", "button1_aria_label_value", "button1_aria_pressed_value", "div_aria_label_value", "svg0", "path0", "svg1", "path1", "is_cell_selected", "cell", "selected_cells", "col", "r", "up", "down", "left", "right", "get_range_selection", "start_row", "start_col", "end_row", "end_col", "min_row", "max_row", "min_col", "max_col", "cells", "j", "handle_selection", "is_cell_match", "c", "_", "handle_delete_key", "new_data", "should_show_cell_menu", "get_next_cell_coordinates", "shift_key", "next_row", "prev_row", "move_cursor", "key", "current_coords", "dir", "get_current_indices", "id", "acc", "arr", "_acc", "_data", "k", "handle_click_outside", "parent", "trigger", "select_column", "select_row", "calculate_selection_positions", "els", "offset", "cell_id", "cell_el", "cell_rect", "table_rect", "col_pos", "row_pos", "sort_data", "sort_by", "sort_direction", "row_indices", "row_a_idx", "row_b_idx", "row_a", "row_b", "val_a", "val_b", "comparison", "get_max", "max", "sort_table_data", "indices", "new_display", "new_styling", "copy_table_data", "csv", "cols", "err", "guess_delimiter", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "handle_file_upload", "update_headers", "update_values", "blob", "reader", "head", "rest", "dsvFormat", "if_block", "create_if_block_15", "p", "caption", "th", "sorticon_changes", "create_if_block_10", "create_if_block_9", "th_aria_sort_value", "set_style", "func", "editablecell_changes", "td", "create_if_block_8", "create_if_block_6", "func_2", "create_if_block_7", "tr", "t", "dequal", "editablecell_props", "td_style_value", "func_1", "td_class_value", "null_to_empty", "virtualtable_changes", "cellmenu_changes", "create_if_block_14", "if_block1", "create_if_block_13", "if_block2", "create_if_block_12", "create_if_block_11", "if_block6", "table_1", "tr0", "tr1", "upload_1_changes", "make_id", "make_headers", "_head", "fill", "_id", "h", "idx", "label", "show_label", "headers", "values", "wrap", "column_widths", "show_row_numbers", "upload", "stream_handler", "value_is_output", "pinned_columns", "actual_pinned_columns", "t_rect", "editing", "header_edit", "selected_header", "active_cell_menu", "active_header_menu", "dragging", "copy_flash", "color_accent_copied", "get_data_at", "process_data", "_values", "data_row_length", "_headers", "old_headers", "old_val", "previous_headers", "previous_data", "trigger_change", "current_headers", "current_data", "get_sort_status", "name", "_sort", "input_el", "next_coords", "add_row", "next_cell", "handle_sort", "edit_header", "_select", "end_header_edit", "new_row", "add_col", "insert_index", "new_w", "handle_click_outside_util", "set_cell_widths", "widths", "data_cells", "width", "scrollbar_width", "get_cell_width", "table_height", "_display_value", "_styling", "is_visible", "observer", "entries", "entry", "handle_resize", "handle_fullscreen_change", "handle_cell_click", "toggle_cell_button", "d", "toggle_cell_menu", "add_row_at", "position", "row_index", "add_col_at", "col_index", "active_button", "toggle_fullscreen", "toggle_header_menu", "afterUpdate", "delete_row", "delete_col", "delete_row_at", "delete_col_at", "handle_select_column", "handle_select_row", "coords", "handle_search", "search_query", "commit_filter", "handle_header_click", "search_handler", "sort_handler", "detail", "click_handler_2", "touch", "mouseEvent", "click_handler_3", "$$self", "click_handler_4", "click_handler_5", "sort_handler_1", "click_handler_6", "click_handler_7", "load_handler", "vals", "func_3", "func_4", "func_5", "func_6", "func_7", "func_8", "func_9", "func_10", "func_11", "func_12", "func_13", "func_14", "selected_index", "sort_index", "comp", "positions", "table_changes", "elem_id", "elem_classes", "scale", "min_width", "gradio", "loading_status", "interactive", "clear_status_handler", "args", "input_handler", "filtered_cell_values"], "mappings": "4/BAuFCA,EAYCC,EAAAC,EAAAC,CAAA,gBATYC,EAAM,EAAA,CAAA,uCAGTA,EAAW,EAAA,CAAA,kHAKRA,EAAc,EAAA,CAAA,mCARdA,EAAM,EAAA,QAANA,EAAM,EAAA,CAAA,wFAoCjBA,EAAQ,CAAA,EAAGA,EAAY,EAAA,EAAGA,MAAiBA,EAAY,EAAA,GAAA,iEAAvDA,EAAQ,CAAA,EAAGA,EAAY,EAAA,EAAGA,MAAiBA,EAAY,EAAA,GAAA,KAAAC,GAAA,EAAAC,CAAA,2EAP9C,QAAAF,MAAa,eAAc,iDAG3B,iFAHAG,EAAA,OAAAC,EAAA,QAAAJ,MAAa,sQAHhBA,EAAY,EAAA,EAAAH,EAAAE,CAAA,+BAAZC,EAAY,EAAA,CAAA,uEA9BhBA,EAAI,CAAA,GAAAK,GAAAL,CAAA,0CA6BH,OAAAA,OAAa,OAAM,EAEdA,OAAa,WAAU,4HAN1BA,EAAO,CAAA,CAAA,sBACCA,EAAQ,CAAA,CAAA,uFAJPA,EAAW,EAAA,CAAA,kBACVA,EAAM,CAAA,CAAA,+BAPxBJ,EA0BMC,EAAAS,EAAAP,CAAA,yCAzBKC,EAAY,EAAA,CAAA,gBACVA,EAAc,EAAA,CAAA,0CAlBtBA,EAAI,CAAA,gOAyBDA,EAAO,CAAA,CAAA,mCACCA,EAAQ,CAAA,CAAA,2DAJPA,EAAW,EAAA,CAAA,8BACVA,EAAM,CAAA,CAAA,4FA5Ed,SAAAO,GACRC,EACAC,EAA4B,KAAA,CAEtB,MAAAC,EAAM,OAAOF,CAAI,SAClBC,GAAcC,EAAI,QAAUD,EAAmBC,EAC7CA,EAAI,MAAM,EAAGD,CAAU,EAAI,iCAnCxB,CAAA,KAAAE,CAAA,EAAAC,GACA,MAAAC,EAAyB,EAAA,EAAAD,GACzB,cAAAE,EAA+B,IAAA,EAAAF,GAC/B,QAAAG,EAAU,EAAA,EAAAH,GACV,OAAAI,EAAS,EAAA,EAAAJ,GACT,SAAAK,EAMC,KAAA,EAAAL,EACD,CAAA,iBAAAM,CAAA,EAAAN,GAKA,eAAAO,EAAiB,EAAA,EAAAP,GACjB,YAAAQ,EAAc,EAAA,EAAAR,GACd,SAAAS,EAAW,EAAA,EAAAT,EACX,CAAA,KAAAU,CAAA,EAAAV,GACA,UAAAW,EAA2B,IAAA,EAAAX,QAEhCY,EAAWC,SACbC,EAAc,GAEP,CAAA,GAAAC,CAAA,EAAAf,WAgBFgB,EAAUC,EAAA,CACd,OAAAV,QACHW,EAAS,EAAA,EAGV,sBAAA,IAAA,CACCD,EAAK,MAAA,gBAMEE,EACR,CAAA,cAAAC,GAAA,CAIAC,EAAA,GAAApB,EAAQmB,EAAc,KAAA,EACtBR,EAAS,MAAM,WAGPU,EAAeC,EAAA,CACnBA,EAAM,MAAQ,UACbxB,QACHE,EAAQiB,CAAA,EACRN,EAAS,MAAM,GACJR,QACXU,EAAe,CAAAA,CAAA,GAGjBF,EAAS,UAAWW,CAAK,EAGjB,SAAAC,GAAA,EACHzB,GAAS,CAAAK,QACbU,EAAe,CAAAA,CAAA,8KAQLC,EAAEU,wBACDP,EAAM,KAAA,ihBA3DnBG,EAAA,GAAGH,EAASjB,CAAA,qBAWZoB,EAAA,GAAGK,EAAeZ,EACfb,EACAN,GAAcO,GAAiBD,EAAOU,CAAS,CAAA,o9CC2OpB,KAAAvB,MAAK,KAAa,MAAAA,MAAK,yEAD1CA,EAAO,EAAA,CAAA,EAAU,MAAAuC,EAAAvC,GAAAA,EAAK,EAAA,EAAA,KAAK,CAAC,EAAE,mBAAnC,OAAIwC,GAAA,EAAA,qMAACxC,EAAO,EAAA,CAAA,sFAAZ,OAAIwC,GAAA,0JACiD;AAAA,OAEtD,+bAJGxC,EAAO,EAAA,EAAC,QAAUA,MAAQ,CAAC,EAAE,KAAK,QAAMyC,GAAAzC,CAAA,qYAN9B0C,EAAM,wBAAoB1C,EAAG,CAAA,EAAA,IAAA,2BAAyBA,EAAM,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,iCAA+BA,EAAc,CAAA,EAAA,IAAA,sBAAoBA,EAAU,CAAA,EAAA,IAAA,uBAJ/MA,EAAc,CAAA,CAAA,UAJvCJ,EA2B+BC,EAAA8C,EAAA5C,CAAA,EA1B9B6C,EAyBKD,EAAAE,CAAA,EAxBJD,EAuBOC,EAAAC,CAAA,EAfNF,EAEOE,EAAAC,CAAA,8CACPH,EAQOE,EAAAE,CAAA,iCACPJ,EAEOE,EAAAG,CAAA,sGAjBIjD,EAAa,EAAA,CAAA,4FAOlBA,EAAO,EAAA,EAAC,QAAUA,MAAQ,CAAC,EAAE,KAAK,gOANEA,EAAG,CAAA,EAAA,IAAA,0CAAyBA,EAAM,CAAA,EAAA,IAAA,6CAA4BA,EAAW,CAAA,EAAA,IAAA,8CAA4BA,EAAW,CAAA,EAAA,IAAA,+CAA+BA,EAAc,CAAA,EAAA,IAAA,oCAAoBA,EAAU,CAAA,EAAA,IAAA,qCAJ/MA,EAAc,CAAA,CAAA,iKA9PlC0C,GAAS,OA+HJ,SAAAQ,GAAuBC,EAAmBC,EAAA,CAC7C,GAAA,CAAAD,EACG,MAAA,GAEF,MAAAE,EAAY,iBAAiBF,CAAI,EAGhC,OADC,SAASE,EAAU,iBAAiBD,CAAQ,CAAA,sDA9I1C,CAAA,MAAAE,EAAA,EAAA,EAAA1C,EAEA,CAAA,WAAA2C,CAAA,EAAA3C,EACA,CAAA,cAAA4C,CAAA,EAAA5C,EACA,CAAA,sBAAA6C,CAAA,EAAA7C,GACA,MAAA8C,EAAQ,CAAA,EAAA9C,GACR,IAAA+C,EAAM,EAAA,EAAA/C,EACN,CAAA,SAAAgD,CAAA,EAAAhD,GACA,eAAAiD,EAAiB,EAAA,EAAAjD,EAGxBkD,EAAiB,GACjBC,EAAS,EACTC,EACAC,EAAc,EACdC,EAAc,EACdC,EAAA,CAAA,EACAC,EACAC,EACAC,EAAM,EACNC,EACAC,GAAkB,IAClBC,GAAA,CAAA,EACAC,EAIE,MAAAC,EAAA,OAAoB,OAAW,IAC/BC,EAAMD,EACT,OAAO,sBACNE,GAAiCA,QAIjCC,EAAiB,iBACNC,EAAmBC,EAAA,IAC7BR,KAAoB,SAKxBvC,EAAA,EAAAgC,EACCM,EAAS,cAAc,QAAQ,GAAG,wBAAwB,QAAU,CAAA,EAC/D,MAAAU,GAAA,QAEE,UAAAC,CAAc,EAAAX,OACtBd,EAAwBc,EAAS,YAAcA,EAAS,WAAA,EAExDO,EAAiBR,GAAOY,EAAYjB,OAChCzB,EAAIkB,OAEDoB,EAAiBvB,GAAcf,EAAIwC,EAAO,QAAA,KAC5CG,GAAMd,EAAK7B,EAAIkB,CAAK,EACnByB,KACJlD,EAAA,GAAA0B,EAAMnB,EAAI,CAAA,EACJ,MAAAyC,GAAA,EACNE,GAAMd,EAAK7B,EAAIkB,CAAK,GAEjB,IAAA0B,GAAKD,IAAK,sBAAwB,EAAA,OACjCC,KACJA,GAAKtB,SAEAuB,GAAclB,EAAW3B,CAAC,EAAI4C,GACpCN,GAAkBO,GAClB7C,GAAK,OAGNmB,EAAMnB,CAAA,EACA,MAAA8C,EAAYN,EAAO,OAASrB,EAE5B4B,EAAmBhB,EAAS,aAAeA,EAAS,aACtDgB,EAAmB,IACtBT,GAAkBS,GAGf,IAAAC,GAAsBrB,EAAW,OAAQsB,IAAa,OAAAA,IAAM,QAAQ,MACxE3B,EACC0B,GAAoB,QAAQE,GAAGC,KAAMD,GAAIC,GAAG,CAAC,EAC7CH,GAAoB,MAAA,EAErBvD,EAAA,EAAA8B,EAASuB,EAAYxB,CAAA,EACrBK,EAAW,OAASa,EAAO,OACrB,MAAAC,GAAA,EACD1B,EAEMuB,EAAiBvB,EAC3BtB,EAAA,GAAAuB,EAAgBsB,EAAiB,CAAA,OAEjCtB,EAAgBD,CAAA,EAJhBtB,EAAA,GAAAuB,EAAgBsB,EAAiB,CAAA,EAO5B,MAAAG,GAAA,iBAKQW,EAAkBC,EAAA,CAChCjB,EAAA,SAAA,WACYiB,GAAM,SAAA,OACX,MAAAC,EAAA,OAAmBD,GAAM,SAAW,GAAQE,GAAWF,CAAC,EAC1DC,IAAc,KAGdA,IAAc,QACX,MAAAE,GAAgBH,GAAK,SAAU,SAAA,CAAA,EAGlCC,IAAc,kBACXE,GAAgBH,EAAA,CAAK,SAAU,SAAA,EAAa,EAAI,cAKhDE,GAAWF,EAAA,OACbI,EAAU5B,GAAQA,EAAKwB,EAAInC,CAAK,EACjC,GAAA,CAAAuC,GAAWJ,EAAInC,EACZ,MAAA,OAEH,GAAA,CAAAuC,GAAWJ,GAAKlC,EAAM,EACnB,MAAA,WAGA,KAAA,CAAA,IAAKuC,GAAiB3B,EAAS,sBAAA,GAC/B,IAAAD,EAAK,OAAAP,GAAWkC,EAAQ,wBAE5B3B,OAAAA,EAAM4B,EAAe,GACjB,OAGJnC,EAASmC,EAAe1B,GACpB,WAGD,kBAaO2B,GAAcC,EAAA,CACtB,MAAAC,EAAa9B,EAAS,UAE5BF,EAAOL,EAAS,SACV,MAAAsC,EAAoBC,EAAY,OAAS7C,EAEzC8C,EAAiBtD,GAAuBmB,EAAK,CAAC,EAAG,kBAAkB,EAEnEoC,EAAgC,EAElCH,SACGN,GAAgBO,EAAY,OAAS,EAAA,CAAK,SAAU,MAAA,CAAA,MAGvDG,GAAY,EAEP,QAAAjB,EAAI,EAAGA,EAAIpB,EAAK,OAAQoB,GAAK,EACrCtB,EAAWT,EAAQ+B,CAAC,EAAIpB,EAAKoB,CAAC,EAAE,sBAAwB,EAAA,WAErDjD,GAAI,EAEJmE,GAAI1C,EAAcuC,EAAiB,EACnCI,GAAA,CAAA,EAEG,KAAApE,GAAI+D,EAAY,QAAA,OAChBlB,EAAalB,EAAW3B,EAAC,GAAKsB,EAGhC,GAFJ8C,GAAYpE,EAAC,EAAI6C,EAEbsB,GAAItB,EAAaoB,EAAgCJ,EAAA,CAEpDK,GAAYlE,OACZ8B,EAAMqC,IAAK1C,EAAcuC,EAAiB,EAAA,QAG3CG,IAAKtB,EACL7C,IAAK,EAIC,IADPkE,GAAY,KAAK,IAAI,EAAGA,EAAS,EAC1BlE,GAAI+D,EAAY,QAAA,OAChBlB,EAAalB,EAAW3B,EAAC,GAAKsB,EAGhC,GAFJ6C,IAAKtB,EACL7C,IAAK,EACDmE,GAAIN,EAAa7B,cAItBd,EAAQgD,EAAA,OACR/C,EAAMnB,EAAA,EACA,MAAA8C,GAAYiB,EAAY,OAAS5C,EACnCA,IAAQ,QACXA,EAAM,EAAA,EAEP1B,EAAA,EAAA6B,GAAkB6C,GAAI1C,GAAeN,CAAA,EACjC,IAAAkD,GAAmBvB,GAAYxB,EAE5B,KAAAtB,GAAI+D,EAAY,QACtB/D,IAAK,EACL2B,EAAW3B,EAAC,EAAIsB,MAEjBC,EAAS8C,EAAA,EACJ,SAAS9C,CAAM,OACnBA,EAAS,GAAA,iBAIWiC,GACrBc,EACAC,EACAC,EAAY,GAAA,CAEN,MAAA/B,GAAA,QAEAgC,EAAcnD,EAEhB,IAAAoD,EAAWJ,EAAQG,EACnBD,IACHE,EAAWA,EAAW1C,GAAkByC,EAAchD,SAGjDsB,GAAmBhB,EAAS,aAAeA,EAAS,aACtDgB,GAAmB,IACtB2B,GAAY3B,IAGP,MAAA4B,GAAA,CACL,IAAKD,EACL,SAAU,SACP,GAAAH,GAGJxC,EAAS,SAAS4C,EAAK,EAexBC,GAAA,IAAA,CACC/C,EAAOL,EAAS,cAChBI,EAAU,EAAA,EACVW,EAAmBzB,CAAK,iBAciBW,EAAW,KAAA,+DAGjCD,EAAQ3B,wBASc6B,EAAW,KAAA,+DAjBxCK,EAAQlC,wBACDqC,EAAY2C,GAAA,QAAA,IAAA,IAAA,GAAA,+ZAhP7B7C,GAAkBE,GAAc,QAAU,0BAuN7CzC,EAAA,GAAGsE,EAAcjD,CAAA,yBAhNdc,GAAWQ,EAAU,IAAAG,EAAmBwB,CAAW,CAAA,wBA8DnDX,EAAkBhC,CAAQ,yBAoJ7B3B,EAAA,GAAGwC,GAAUE,EACV4B,EAAY,MAAM7C,EAAOC,CAAG,EAAE,KAAK2D,EAAM9E,KAChC,CAAA,MAAOA,EAAIkB,EAAO,KAAA4D,CAAA,IAE3Bf,EACC,MAAM,EAAIhD,EAAagD,EAAY,OAAUzC,EAAiB,CAAC,EAC/D,IAAA,CAAKwD,EAAM9E,KACF,CAAA,MAAOA,EAAIkB,EAAO,KAAA4D,CAAA,63CC7J/B1H,EAeKC,EAAA0H,EAAAxH,CAAA,EAdJ6C,EAOC2E,EAAAC,CAAA,EACD5E,EAKC2E,EAAAE,CAAA,2YA/BF7H,EAeKC,EAAA0H,EAAAxH,CAAA,EAdJ6C,EAOC2E,EAAAC,CAAA,EACD5E,EAKC2E,EAAAE,CAAA,maAhCF7H,EAgBKC,EAAA0H,EAAAxH,CAAA,EAfJ6C,EAOC2E,EAAAC,CAAA,EACD5E,EAMC2E,EAAAE,CAAA,gaAjCF7H,EAgBKC,EAAA0H,EAAAxH,CAAA,EAfJ6C,EAOC2E,EAAAC,CAAA,EACD5E,EAMC2E,EAAAE,CAAA,mbAlCF7H,EAiBKC,EAAA0H,EAAAxH,CAAA,EAhBJ6C,EAQC2E,EAAAC,CAAA,EACD5E,EAMC2E,EAAAE,CAAA,sbAnCF7H,EAiBKC,EAAA0H,EAAAxH,CAAA,EAhBJ6C,EAQC2E,EAAAC,CAAA,EACD5E,EAMC2E,EAAAE,CAAA,uDAjBE,GAAAzH,MAAQ,mBAAkB,OAAAyC,GAmBrB,GAAAzC,MAAQ,kBAAiB,OAAA0H,GAmBzB,GAAA1H,MAAQ,gBAAe,OAAAK,GAkBvB,GAAAL,MAAQ,gBAAe,OAAA2H,GAkBvB,GAAA3H,MAAQ,aAAY,OAAA4H,GAiBpB,GAAA5H,MAAQ,gBAAe,OAAA6H,oNA9FrB,GAAA,CAAA,KAAAC,CAAA,EAAAlH,4NCwDRmH,EAAA/H,KAAK,yBAAyB,EAAA,aAI9BgI,EAAAhI,KAAK,yBAAyB,EAAA,uGAE3BA,EAAe,CAAA,GAAA2H,GAAA3H,CAAA,yMARpBJ,EAGQC,EAAAoI,EAAAlI,CAAA,sCACRH,EAGQC,EAAAqI,EAAAnI,CAAA,6HALN,CAAAkG,GAAA9F,EAAA,MAAA4H,KAAAA,EAAA/H,KAAK,yBAAyB,EAAA,KAAAC,GAAAkI,EAAAJ,CAAA,GAI9B,CAAA9B,GAAA9F,EAAA,MAAA6H,KAAAA,EAAAhI,KAAK,yBAAyB,EAAA,KAAAC,GAAAmI,EAAAJ,CAAA,EAE3BhI,EAAe,CAAA,6TAGjB+H,EAAA/H,KAAK,sBAAsB,EAAA,wJAF7BJ,EAGQC,EAAAwI,EAAAtI,CAAA,+DAHUuI,GAAAtI,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,uBAE7B,CAAAiG,GAAA9F,EAAA,MAAA4H,KAAAA,EAAA/H,KAAK,sBAAsB,EAAA,KAAAC,GAAAkI,EAAAJ,CAAA,2HAO5BA,EAAA/H,KAAK,2BAA2B,EAAA,aAIhCgI,EAAAhI,KAAK,4BAA4B,EAAA,4GAE9BA,EAAe,CAAA,GAAA0H,GAAA1H,CAAA,yMARpBJ,EAGQC,EAAAoI,EAAAlI,CAAA,sCACRH,EAGQC,EAAAqI,EAAAnI,CAAA,6HALN,CAAAkG,GAAA9F,EAAA,MAAA4H,KAAAA,EAAA/H,KAAK,2BAA2B,EAAA,KAAAC,GAAAkI,EAAAJ,CAAA,GAIhC,CAAA9B,GAAA9F,EAAA,MAAA6H,KAAAA,EAAAhI,KAAK,4BAA4B,EAAA,KAAAC,GAAAmI,EAAAJ,CAAA,EAE9BhI,EAAe,CAAA,8TAGjB+H,EAAA/H,KAAK,yBAAyB,EAAA,2JAFhCJ,EAGQC,EAAAwI,EAAAtI,CAAA,+DAHUuI,GAAAtI,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,uBAE7B,CAAAiG,GAAA9F,EAAA,MAAA4H,KAAAA,EAAA/H,KAAK,yBAAyB,EAAA,KAAAC,GAAAkI,EAAAJ,CAAA,2HA5B5BQ,EAAA,CAAAvI,OAAaA,EAAY,EAAA,GAAAK,GAAAL,CAAA,IAgB1BA,EAAe,EAAA,GAAAyC,GAAAzC,CAAA,gGAjBrBJ,EAiCKC,EAAAgD,EAAA9C,CAAA,+DAhCE,CAAAC,OAAaA,EAAY,EAAA,mGAgB1BA,EAAe,EAAA,gOAjET,CAAA,EAAAwI,CAAA,EAAA5H,EACA,CAAA,EAAA+F,CAAA,EAAA/F,EACA,CAAA,iBAAA6H,CAAA,EAAA7H,EACA,CAAA,iBAAA8H,CAAA,EAAA9H,EACA,CAAA,mBAAA+H,CAAA,EAAA/H,EACA,CAAA,oBAAAgI,CAAA,EAAAhI,EACA,CAAA,IAAAuE,CAAA,EAAAvE,EACA,CAAA,UAAAiI,CAAA,EAAAjI,EACA,CAAA,UAAAkI,CAAA,EAAAlI,EACA,CAAA,cAAAmI,CAAA,EAAAnI,EACA,CAAA,cAAAoI,CAAA,EAAApI,EACA,CAAA,gBAAAqI,CAAA,EAAArI,EACA,CAAA,gBAAAsI,CAAA,EAAAtI,EAEA,CAAA,KAAAuI,CAAA,EAAAvI,EACPwI,EAMJhC,GAAA,IAAA,CACCiC,MAGQ,SAAAA,GAAA,CACH,GAAA,CAAAD,EAAA,OAEC,MAAAE,EAAiB,OAAO,WACxB9E,EAAkB,OAAO,YACzB+E,EAAYH,EAAa,wBAE3B,IAAAI,EAAQhB,EAAI,GACZiB,EAAQ9C,EAAI,GAEZ6C,EAAQD,EAAU,MAAQD,IAC7BE,EAAQhB,EAAIe,EAAU,MAAQ,IAG3BE,EAAQF,EAAU,OAAS/E,IAC9BiF,EAAQ9C,EAAI4C,EAAU,OAAS,QAGhCH,EAAa,MAAM,QAAUI,CAAK,KAAAJ,CAAA,MAClCA,EAAa,MAAM,OAASK,CAAK,KAAAL,CAAA,cAMTX,UAIAC,WAYAC,WAIAC,8CAtBVQ,EAAY/G,mnBA/B3BJ,EAAA,GAAGyH,EAAYvE,IAAQ,EAAA,qBACpBlD,EAAA,GAAA0H,EAAeb,EAAU,CAAC,IAAM,SAAA,oBAChC7G,EAAA,GAAA2H,EAAkBf,EAAU,CAAC,IAAM,SAAA,y4DCrBvCjJ,EAQKC,EAAA0H,EAAAxH,CAAA,EAPJ6C,EAMC2E,EAAAE,CAAA,6JCyCOzH,EAAoB,CAAA,GAAIA,EAAW,CAAA,IAAK,UAAQ6H,GAAA7H,CAAA,sMAPtDJ,EAiBKC,EAAAgD,EAAA9C,CAAA,EAhBJ6C,EAKCC,EAAA/C,CAAA,OAHYE,EAAoB,CAAA,CAAA,mFAApBA,EAAoB,CAAA,QAApBA,EAAoB,CAAA,CAAA,EAI5BA,EAAoB,CAAA,GAAIA,EAAW,CAAA,IAAK,6bAC5CJ,EAOQC,EAAAwI,EAAAtI,CAAA,iDALGuI,GAAAtI,OAAAA,EAAgB,CAAA,EAAA,MAAA,KAAA,SAAA,qMAgBvBA,EAAM,CAAA,EAAA,yHAHCA,EAAM,CAAA,EAAG,sBAAwB,iBAAiB,gBACvDA,EAAM,CAAA,EAAG,sBAAwB,iBAAiB,UAJ1DJ,EAWQC,EAAAwI,EAAAtI,CAAA,wCATGC,EAAW,CAAA,CAAA,oJACTA,EAAM,CAAA,EAAG,sBAAwB,2DACtCA,EAAM,CAAA,EAAG,sBAAwB,8gBAgBnCA,EAAa,CAAA,EAAA,yHAHNA,EAAa,CAAA,EAAG,kBAAoB,kBAAkB,gBAC3DA,EAAa,CAAA,EAAG,kBAAoB,kBAAkB,UAJ9DJ,EAWQC,EAAAwI,EAAAtI,CAAA,gMARKC,EAAa,CAAA,EAAG,kBAAoB,4DACzCA,EAAa,CAAA,EAAG,kBAAoB,6dAvCxCuI,EAAAvI,OAAgB,QAAM4H,GAAA5H,CAAA,IAoBtBA,EAAgB,CAAA,GAAAK,GAAAL,CAAA,IAchBA,EAAsB,CAAA,GAAAyC,GAAAzC,CAAA,4NApC7BJ,EAmDKC,EAAAgK,EAAA9J,CAAA,EAlDJ6C,EAiDKiH,EAAAC,CAAA,4EAhDC9J,OAAgB,qGAoBhBA,EAAgB,CAAA,gGAchBA,EAAsB,CAAA,8NAvEjB,uBAAA+J,EAAyB,EAAA,EAAAnJ,GACzB,iBAAAoJ,EAAmB,EAAA,EAAApJ,GACnB,YAAAqJ,EAA4C,MAAA,EAAArJ,GAC5C,cAAAsJ,EAAgB,EAAA,EAAAtJ,EAChB,CAAA,QAAAuJ,CAAA,EAAAvJ,EACA,CAAA,iBAAAwJ,CAAA,EAAAxJ,QAELY,EAAWC,SAIb4I,EAAS,GACTC,GACO,qBAAAC,EAAsC,IAAA,EAAA3J,EAIxC,SAAA4J,GAAA,KACRH,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGW,eAAAI,GAAA,CACR,MAAAN,EAAA,EACNK,IAGDE,GAAA,IAAA,CACKJ,GAAO,aAAaA,CAAK,iDAUdC,EAAoB,KAAA,gZA1BjC/I,EAAS,SAAU+I,CAAoB,s6CCL7BI,EAAA1C,EAAA,aAAA2C,EAAA5K,KAAK,0BAA0B,CAAA,EAC7B2K,EAAA1C,EAAA,eAAA4C,EAAA7K,OAAc,KAAK,EAHnB8K,EAAA7C,EAAA,SAAAjI,OAAc,KAAK,mWAyBrB2K,EAAAzC,EAAA,aAAA6C,EAAA/K,KAAK,2BAA2B,CAAA,EAC9B2K,EAAAzC,EAAA,eAAA8C,EAAAhL,OAAc,KAAK,EAHnB8K,EAAA5C,EAAA,SAAAlI,OAAc,KAAK,8DA1Bc2K,EAAA9H,EAAA,aAAAoI,EAAAjL,KAAK,uBAAuB,CAAA,UAA7EJ,EA+CKC,EAAAgD,EAAA9C,CAAA,EA9CJ6C,EAsBQC,EAAAoF,CAAA,EAfPrF,EAcKqF,EAAAiD,CAAA,EAPJtI,EAMCsI,EAAAC,CAAA,SAGHvI,EAsBQC,EAAAqF,CAAA,EAfPtF,EAcKsF,EAAAkD,CAAA,EAPJxI,EAMCwI,EAAAC,CAAA,8DAvCUlL,EAAA,GAAAyK,KAAAA,EAAA5K,KAAK,0BAA0B,wBAC7BG,EAAA,GAAA0K,KAAAA,EAAA7K,OAAc,mCAHd8K,EAAA7C,EAAA,SAAAjI,OAAc,KAAK,EAyBrBG,EAAA,GAAA4K,KAAAA,EAAA/K,KAAK,2BAA2B,wBAC9BG,EAAA,GAAA6K,KAAAA,EAAAhL,OAAc,mCAHd8K,EAAA5C,EAAA,SAAAlI,OAAc,KAAK,EA1BcG,EAAA,GAAA8K,KAAAA,EAAAjL,KAAK,uBAAuB,oFANjE,UAAA8F,EAAkC,IAAA,EAAAlF,EAClC,CAAA,KAAAuI,CAAA,EAAAvI,QAELY,EAAWC,WAOAD,EAAS,OAAQ,KAAK,QAuBtBA,EAAS,OAAQ,KAAK,gVClCxB,SAAA8J,GACfC,EACAC,EACS,CACH,KAAA,CAACrG,EAAKsG,CAAG,EAAIF,EACf,GAAA,CAACC,EAAe,KAAK,CAAC,CAACE,EAAG,CAAC,IAAMA,IAAMvG,GAAO,IAAMsG,CAAG,EAAU,MAAA,GAErE,MAAME,EAAKH,EAAe,KAAK,CAAC,CAACE,EAAG,CAAC,IAAMA,IAAMvG,EAAM,GAAK,IAAMsG,CAAG,EAC/DG,EAAOJ,EAAe,KAAK,CAAC,CAACE,EAAG,CAAC,IAAMA,IAAMvG,EAAM,GAAK,IAAMsG,CAAG,EACjEI,EAAOL,EAAe,KAAK,CAAC,CAACE,EAAG,CAAC,IAAMA,IAAMvG,GAAO,IAAMsG,EAAM,CAAC,EACjEK,EAAQN,EAAe,KAAK,CAAC,CAACE,EAAG,CAAC,IAAMA,IAAMvG,GAAO,IAAMsG,EAAM,CAAC,EAExE,MAAO,gBAAgBE,EAAK,UAAY,EAAE,GAAGC,EAAO,aAAe,EAAE,GAAGC,EAAO,WAAa,EAAE,GAAGC,EAAQ,YAAc,EAAE,EAC1H,CAEgB,SAAAC,GACfrI,EACAC,EACmB,CACb,KAAA,CAACqI,EAAWC,CAAS,EAAIvI,EACzB,CAACwI,EAASC,CAAO,EAAIxI,EACrByI,EAAU,KAAK,IAAIJ,EAAWE,CAAO,EACrCG,EAAU,KAAK,IAAIL,EAAWE,CAAO,EACrCI,EAAU,KAAK,IAAIL,EAAWE,CAAO,EACrCI,EAAU,KAAK,IAAIN,EAAWE,CAAO,EAErCK,EAA0B,CAAA,EAChC,QAAShK,EAAI4J,EAAS5J,GAAK6J,EAAS7J,IACnC,QAASiK,EAAIH,EAASG,GAAKF,EAASE,IACnCD,EAAM,KAAK,CAAChK,EAAGiK,CAAC,CAAC,EAGZ,OAAAD,CACR,CAEgB,SAAAE,GACfzG,EACAuF,EACArJ,EACmB,CACnB,GAAIA,EAAM,UAAYqJ,EAAe,OAAS,EACtC,OAAAO,GACNP,EAAeA,EAAe,OAAS,CAAC,EACxCvF,CAAA,EAIE,GAAA9D,EAAM,SAAWA,EAAM,QAAS,CACnC,MAAMwK,EAAgB,CAAC,CAACjB,EAAGkB,CAAC,IAC3BlB,IAAMzF,EAAQ,CAAC,GAAK2G,IAAM3G,EAAQ,CAAC,EAC9Ba,EAAQ0E,EAAe,UAAUmB,CAAa,EACpD,OAAO7F,IAAU,GACd,CAAC,GAAG0E,EAAgBvF,CAAO,EAC3BuF,EAAe,OAAO,CAACqB,EAAG,IAAM,IAAM/F,CAAK,CAC/C,CAEA,MAAO,CAACb,CAAO,CAChB,CAEgB,SAAA6G,GACfxF,EACAkE,EACe,CACT,MAAAuB,EAAWzF,EAAK,IAAKnC,GAAQ,CAAC,GAAGA,CAAG,CAAC,EAC3C,OAAAqG,EAAe,QAAQ,CAAC,CAACrG,EAAKsG,CAAG,IAAM,CAClCsB,EAAS5H,CAAG,GAAK4H,EAAS5H,CAAG,EAAEsG,CAAG,IACrCsB,EAAS5H,CAAG,EAAEsG,CAAG,EAAI,CAAE,GAAGsB,EAAS5H,CAAG,EAAEsG,CAAG,EAAG,MAAO,EAAG,EACzD,CACA,EACMsB,CACR,CAwBgB,SAAAC,GACfzB,EACAC,EACAnK,EACU,CACJ,KAAA,CAAC8D,EAAKsG,CAAG,EAAIF,EACnB,OACClK,GACAmK,EAAe,SAAW,GAC1BA,EAAe,CAAC,EAAE,CAAC,IAAMrG,GACzBqG,EAAe,CAAC,EAAE,CAAC,IAAMC,CAE3B,CAEgB,SAAAwB,GACfhH,EACAqB,EACA4F,EACyB,CACnB,KAAA,CAAC/H,EAAKsG,CAAG,EAAIxF,EACbH,EAAYoH,EAAY,GAAK,EAEnC,GAAI5F,EAAKnC,CAAG,IAAIsG,EAAM3F,CAAS,EACvB,MAAA,CAACX,EAAKsG,EAAM3F,CAAS,EAG7B,MAAMqH,EAAWhI,GAAOW,EAAY,EAAI,EAAI,GACtCsH,EAAWjI,GAAOW,EAAY,EAAI,GAAK,GAE7C,OAAIA,EAAY,GAAKwB,EAAK6F,CAAQ,IAAI,CAAC,EAC/B,CAACA,EAAU,CAAC,EAGhBrH,EAAY,GAAKwB,EAAK8F,CAAQ,IAAI9F,EAAK,CAAC,EAAE,OAAS,CAAC,EAChD,CAAC8F,EAAU9F,EAAK,CAAC,EAAE,OAAS,CAAC,EAG9B,EACR,CAEgB,SAAA+F,GACfC,EACAC,EACAjG,EACyB,CACzB,MAAMkG,EAAM,CACX,WAAY,CAAC,EAAG,CAAC,EACjB,UAAW,CAAC,EAAG,EAAE,EACjB,UAAW,CAAC,EAAG,CAAC,EAChB,QAAS,CAAC,GAAI,CAAC,GACdF,CAAG,EAEC9K,EAAI+K,EAAe,CAAC,EAAIC,EAAI,CAAC,EAC7Bf,EAAIc,EAAe,CAAC,EAAIC,EAAI,CAAC,EAE/B,OAAAhL,EAAI,GAAKiK,GAAK,EACV,GAGQnF,EAAK9E,CAAC,IAAIiK,CAAC,EAEnB,CAACjK,EAAGiK,CAAC,EAEN,EACR,CAEgB,SAAAgB,GACfC,EACApG,EACmB,CACnB,OAAOA,EAAK,OACX,CAACqG,EAAKC,EAAKpL,IAAM,CAChB,MAAMiK,EAAImB,EAAI,OACb,CAACC,EAAMC,EAAOC,IAAOL,IAAOI,EAAM,GAAKC,EAAIF,EAC3C,EAAA,EAED,OAAOpB,IAAM,GAAKkB,EAAM,CAACnL,EAAGiK,CAAC,CAC9B,EACA,CAAC,GAAI,EAAE,CAAA,CAET,CAEgB,SAAAuB,GACf7L,EACA8L,EACU,CACV,KAAM,CAACC,CAAO,EAAI/L,EAAM,aAAa,EAC9B,MAAA,CAAC8L,EAAO,SAASC,CAAO,CAChC,CAEgB,SAAAC,GAAc7G,EAAemE,EAA+B,CAC3E,OAAO,MAAM,KAAK,CAAE,OAAQnE,EAAK,MAAA,EAAU,CAACuF,EAAGrK,IAAM,CAACA,EAAGiJ,CAAG,CAAC,CAC9D,CAEgB,SAAA2C,GAAW9G,EAAenC,EAA+B,CACxE,OAAO,MAAM,KAAK,CAAE,OAAQmC,EAAK,CAAC,EAAE,MAAO,EAAG,CAACuF,EAAGrK,IAAM,CAAC2C,EAAK3C,CAAC,CAAC,CACjE,CAEO,SAAS6L,GACfzK,EACA0D,EACAgH,EACAL,EACAnL,EACmD,CAC7C,KAAA,CAACqC,EAAKsG,CAAG,EAAI7H,EACnB,GAAI,CAAC0D,EAAKnC,CAAG,IAAIsG,CAAG,EACnB,MAAO,CAAE,QAAS,MAAO,QAAS,MAAU,EAG7C,IAAI8C,EAAS,EACb,QAAS/L,EAAI,EAAGA,EAAIiJ,EAAKjJ,IACd+L,GAAA,WACT,iBAAiBN,CAAM,EAAE,iBAAiB,gBAAgBzL,CAAC,EAAE,CAAA,EAI/D,MAAMgM,EAAUlH,EAAKnC,CAAG,EAAEsG,CAAG,EAAE,GACzBgD,EAAUH,EAAIE,CAAO,GAAG,KAE9B,GAAI,CAACC,EAEJ,MAAO,CAAE,QAAS,MAAO,QAAS,MAAU,EAGvC,MAAAC,EAAYD,EAAQ,wBACpBE,EAAa7L,EAAM,wBACnB8L,EAAU,GAAGF,EAAU,KAAOC,EAAW,KAAOD,EAAU,MAAQ,CAAC,KAEnEG,EAAU,GADKH,EAAU,IAAMC,EAAW,IACdD,EAAU,OAAS,CAAC,KAC/C,MAAA,CAAE,QAAAE,EAAS,QAAAC,EACnB,CClNgB,SAAAC,GACfxH,EACAyH,EACAC,EACW,CACP,GAAA,CAAC1H,GAAQ,CAACA,EAAK,QAAU,CAACA,EAAK,CAAC,EACnC,MAAO,GAIP,GAAA,OAAOyH,GAAY,UACnBC,GACAD,GAAW,GACXA,EAAUzH,EAAK,CAAC,EAAE,OACjB,CACD,MAAM2H,EAAc,CAAC,GAAG,MAAM3H,EAAK,MAAM,CAAC,EAAE,IAAI,CAACuF,EAAGrK,IAAMA,CAAC,EAC/C,OAAAyM,EAAA,KAAK,CAACC,EAAWC,IAAc,CACpC,MAAAC,EAAQ9H,EAAK4H,CAAS,EACtBG,EAAQ/H,EAAK6H,CAAS,EAE3B,GAAA,CAACC,GACD,CAACC,GACDN,GAAWK,EAAM,QACjBL,GAAWM,EAAM,OAEV,MAAA,GAEF,MAAAC,EAAQF,EAAML,CAAO,EAAE,MACvBQ,EAAQF,EAAMN,CAAO,EAAE,MACvBS,EAAaF,EAAQC,EAAQ,GAAKD,EAAQC,EAAQ,EAAI,EACrD,OAAAP,IAAmB,MAAQQ,EAAa,CAACA,CAAA,CAChD,EACMP,CACR,CACO,MAAA,CAAC,GAAG,MAAM3H,EAAK,MAAM,CAAC,EAAE,IAAI,CAACuF,EAAGrK,IAAMA,CAAC,CAC/C,CCsCO,SAASiN,GAAQnI,EAA8B,CACjD,GAAA,CAACA,GAAQ,CAACA,EAAK,OAAQ,MAAO,GAClC,IAAIoI,EAAMpI,EAAK,CAAC,EAAE,MAAM,EACxB,QAAS9E,EAAI,EAAGA,EAAI8E,EAAK,OAAQ9E,IAChC,QAASiK,EAAI,EAAGA,EAAInF,EAAK9E,CAAC,EAAE,OAAQiK,IAC/B,GAAGiD,EAAIjD,CAAC,EAAE,KAAK,GAAG,OAAS,GAAGnF,EAAK9E,CAAC,EAAEiK,CAAC,EAAE,KAAK,GAAG,SACpDiD,EAAIjD,CAAC,EAAInF,EAAK9E,CAAC,EAAEiK,CAAC,GAId,OAAAiD,CACR,CAEO,SAASC,GACfrI,EACAxG,EACAC,EACA0K,EACA+B,EACO,CACP,MAAMoC,EAAUd,GAAUxH,EAAMmE,EAAK+B,CAAG,EAElCT,EAAW6C,EAAQ,IAAKpN,GAAc8E,EAAK9E,CAAC,CAAC,EAGnD,GAFA8E,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGyF,CAAQ,EAEnCjM,EAAe,CAClB,MAAM+O,EAAcD,EAAQ,IAAKpN,GAAc1B,EAAc0B,CAAC,CAAC,EAC/D1B,EAAc,OAAO,EAAGA,EAAc,OAAQ,GAAG+O,CAAW,CAC7D,CAEA,GAAI9O,EAAS,CACZ,MAAM+O,EAAcF,EAAQ,IAAKpN,GAAczB,EAAQyB,CAAC,CAAC,EACzDzB,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAG+O,CAAW,CACjD,CACD,CAEsB,eAAAC,GACrBzI,EACAkE,EACgB,CAChB,MAAMwE,EAAMxE,EAAe,OAC1B,CAACmC,EAAmD,CAACxI,EAAKsG,CAAG,IAAM,CAClEkC,EAAIxI,CAAG,EAAIwI,EAAIxI,CAAG,GAAK,CAAA,EACvB,MAAMtE,EAAQ,OAAOyG,EAAKnC,CAAG,EAAEsG,CAAG,EAAE,KAAK,EACrC,OAAAkC,EAAAxI,CAAG,EAAEsG,CAAG,EACX5K,EAAM,SAAS,GAAG,GAAKA,EAAM,SAAS,GAAG,GAAKA,EAAM,SAAS;AAAA,CAAI,EAC9D,IAAIA,EAAM,QAAQ,KAAM,IAAI,CAAC,IAC7BA,EACG8M,CACR,EACA,CAAC,CAAA,EAGItJ,EAAO,OAAO,KAAK2L,CAAG,EAAE,KAAK,CAACtK,EAAGC,IAAM,CAACD,EAAI,CAACC,CAAC,EAC9CsK,EAAO,OAAO,KAAKD,EAAI3L,EAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAACqB,EAAGC,IAAM,CAACD,EAAI,CAACC,CAAC,EACvDnF,EAAO6D,EACX,IAAKqH,GAAMuE,EAAK,IAAKrD,GAAMoD,EAAItE,CAAC,EAAEkB,CAAC,GAAK,EAAE,EAAE,KAAK,GAAG,CAAC,EACrD,KAAK;AAAA,CAAI,EAEP,GAAA,CACG,MAAA,UAAU,UAAU,UAAUpM,CAAI,QAChC0P,EAAK,CACL,cAAA,MAAM,eAAgBA,CAAG,EAC3B,IAAI,MAAM,gCAAmCA,EAAc,OAAO,CACzE,CACD,CAGgB,SAAAC,GACf3P,EACA4P,EACW,CACJ,OAAAA,EAAmB,OAAOC,CAAO,EAExC,SAASA,EAAQC,EAA4B,CAC5C,IAAIC,EAAQ,GACZ,OAAO/P,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMgQ,CAAW,EAEzC,SAASA,EAAYC,EAAuB,CAC3C,GAAI,CAACA,EAAa,MAAA,GAClB,IAAIC,EAASD,EAAK,MAAMH,CAAS,EAAE,OACnC,OAAIC,EAAQ,IAAWA,EAAAG,GAChBH,IAAUG,GAAUA,EAAS,CACrC,CACD,CACD,CAEO,SAASC,GAAiBC,EAAwB,CACxD,MAAMC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAC,EACtCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAC5DG,EAAK,IAAI,YAAYF,EAAS,MAAM,EACpCG,EAAK,IAAI,WAAWD,CAAE,EAC5B,QAASvO,EAAI,EAAGA,EAAIqO,EAAS,OAAQrO,IACpCwO,EAAGxO,CAAC,EAAIqO,EAAS,WAAWrO,CAAC,EAEvB,OAAA,IAAI,KAAK,CAACuO,CAAE,EAAG,CAAE,KAAMD,EAAU,CACzC,CAEgB,SAAAG,GACfL,EACAM,EACAC,EACO,CACD,MAAAC,EAAOT,GAAiBC,CAAQ,EAChCS,EAAS,IAAI,WACZA,EAAA,iBAAiB,UAAYjL,GAAM,CACzC,GAAI,CAACA,GAAG,QAAQ,QAAU,OAAOA,EAAE,OAAO,QAAW,SAAU,OACzD,KAAA,CAACkK,CAAS,EAAIH,GAAgB/J,EAAE,OAAO,OAAQ,CAAC,IAAK,GAAI,CAAC,EAC1D,CAACkL,EAAM,GAAGC,CAAI,EAAIC,GAAUlB,CAAS,EAAE,UAAUlK,EAAE,OAAO,MAAM,EACtE8K,EAAeI,CAAI,EACnBH,EAAcI,CAAI,CAAA,CAClB,EACDF,EAAO,WAAWD,CAAI,CACvB,obCmtBQK,EAAAzR,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,GAAA0R,GAAA1R,CAAA,mFASpCA,EAAW,EAAA,4DAIFA,EAAa,EAAA,8CALrBA,EAAiB,EAAA,CAAA,yHAT7BJ,EAiBKC,EAAAgD,EAAA9C,CAAA,kDAhBCC,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,mbAExCA,EAAK,CAAA,CAAA,0EADVJ,EAEKC,EAAAgD,EAAA9C,CAAA,EADJ6C,EAAaC,EAAA8O,CAAA,8BAAT3R,EAAK,CAAA,CAAA,kVA4BXJ,EAMQC,EAAAoI,EAAAlI,CAAA,WACRH,EAMQC,EAAAqI,EAAAnI,CAAA,gKAQmBC,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA+R,EAAA7R,CAAA,8BAAfC,EAAK,CAAA,CAAA,ySAK7BJ,EASIC,EAAAgS,EAAA9R,CAAA,8GA4Ca,UAAAC,QAAYA,EAAC,GAAA,EAAGA,EAAc,EAAA,EAAG,uHAF9CJ,EAMKC,EAAAgD,EAAA9C,CAAA,2CAJQI,EAAA,CAAA,EAAA,YAAA2R,EAAA,UAAA9R,QAAYA,EAAC,GAAA,EAAGA,EAAc,EAAA,EAAG,iVAQ/CJ,EAiBQC,EAAAwI,EAAAtI,CAAA,oQAlCD,KAAAC,QAAgBA,EAAC,GAAA,qCAJXA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OACfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,kGAILA,EAAe,EAAA,CAAA,EAKvB,IAAAuI,EAAAvI,QAAgBA,EAAC,GAAA,GAAA+R,GAAA/R,CAAA,IAUlBA,EAAQ,CAAA,GAAAgS,GAAAhS,CAAA,kPA5CH2K,EAAAkH,EAAA,YAAAI,EAAAjS,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,EAC1CkS,GAAAL,EAAA,QAAA7R,MAAeA,EAAC,GAAA,CAAA,CAAA,EAAWkS,GAAAL,EAAA,OAAA7R,EAC1C,GAAA,EAAAA,EAAA,EAAA,EACGA,EAAM,GAAA,IAAA,EACLA,EAAA,EAAA,EACC,+BACA,YACOA,EAAgB,EAAA,EAAG,kCAAoC,EAAE,GAAG,MACpEA,EAAA,GAAA,CAAA,EAEC,KAAK,CAAC,EACN,IAAGmS,EAAA,EACH,KAAK,KAAK,CAAA,IACZ,MAAM,+BAhBYrH,EAAA+G,EAAA,gBAAA7R,OAAIA,EAAqB,EAAA,CAAA,oBAC3BA,EAAC,GAAA,IAAKA,EAAqB,EAAA,EAAG,CAAC,EACrC8K,EAAA+G,EAAA,QAAA7R,EAAgB,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAoBA,EAAC,GAAA,CAAA,uBAHxDJ,EAqEIC,EAAAgS,EAAA9R,CAAA,EA7CH6C,EA4CKiP,EAAAhI,CAAA,EA3CJjH,EAsBKiH,EAAAC,CAAA,oPAfG3J,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,KAAAiS,EAAA,KAAApS,QAAgBA,EAAC,GAAA,wFAJXA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,mDACfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,8BASbA,QAAgBA,EAAC,GAAA,qHAUlBA,EAAQ,CAAA,6DA5CH,CAAAiG,GAAA9F,EAAA,CAAA,EAAA,WAAA8R,KAAAA,EAAAjS,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,6CAC1CkS,GAAAL,EAAA,QAAA7R,MAAeA,EAAC,GAAA,CAAA,CAAA,8BAAWkS,GAAAL,EAAA,OAAA7R,EAC1C,GAAA,EAAAA,EAAA,EAAA,EACGA,EAAM,GAAA,IAAA,EACLA,EAAA,EAAA,EACC,+BACA,YACOA,EAAgB,EAAA,EAAG,kCAAoC,EAAE,GAAG,MACpEA,EAAA,GAAA,CAAA,EAEC,KAAK,CAAC,EACN,IAAGmS,EAAA,EACH,KAAK,KAAK,CAAA,IACZ,MAAM,8BAhBYrH,EAAA+G,EAAA,gBAAA7R,OAAIA,EAAqB,EAAA,CAAA,gDAC3BA,EAAC,GAAA,IAAKA,EAAqB,EAAA,EAAG,CAAC,gCACrC8K,EAAA+G,EAAA,QAAA7R,EAAgB,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAoBA,EAAC,GAAA,CAAA,uPA+E3C,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,OACpD,MACF,kQARPJ,EAaIC,EAAAwS,EAAAtS,CAAA,EAZH6C,EAWKyP,EAAAxP,CAAA,wLANO,MAAM,QAAQ7C,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,iPAiDnCA,EAAK,CAAA,CAAA,iCAA/BJ,EAAyCC,EAAA+R,EAAA7R,CAAA,8BAAfC,EAAK,CAAA,CAAA,uCAD3ByR,EAAAzR,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACsS,GAAAtS,CAAA,kEAA3BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,qXAK7BJ,EASIC,EAAAgS,EAAA9R,CAAA,8GA2Ca,UAAAC,QAAYA,EAAC,GAAA,EAAGA,EAAc,EAAA,EAAG,uHAF9CJ,EAMKC,EAAAgD,EAAA9C,CAAA,2CAJQI,EAAA,CAAA,EAAA,YAAA2R,EAAA,UAAA9R,QAAYA,EAAC,GAAA,EAAGA,EAAc,EAAA,EAAG,mVAQ/CJ,EAiBQC,EAAAwI,EAAAtI,CAAA,oQAjCD,KAAAC,QAAgBA,EAAC,GAAA,qCAJXA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OACfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,kFAQb,IAAAuI,EAAAvI,QAAgBA,EAAC,GAAA,GAAAuS,GAAAvS,CAAA,IAUlBA,EAAQ,CAAA,GAAA6H,GAAA7H,CAAA,mPA3CH2K,EAAAkH,EAAA,YAAAI,EAAAjS,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,CAAA,EAC1CkS,GAAAL,EAAA,QAAA7R,MAAeA,EAAC,GAAA,CAAA,CAAA,EAAWkS,GAAAL,EAAA,OAAA7R,EAC1C,GAAA,EAAAA,EAAA,EAAA,EACGA,EAAM,GAAA,IAAA,EACLA,EAAA,EAAA,EACC,+BACA,YACOA,EAAgB,EAAA,EAAG,kCAAoC,EAAE,GAAG,MACpEA,EAAA,GAAA,CAAA,EAEC,KAAK,CAAC,EACN,IAAGwS,EAAA,EACH,KAAK,KAAK,CAAA,IACZ,MAAM,+BAhBY1H,EAAA+G,EAAA,gBAAA7R,OAAIA,EAAqB,EAAA,CAAA,oBAC3BA,EAAC,GAAA,IAAKA,EAAqB,EAAA,EAAG,CAAC,EACrC8K,EAAA+G,EAAA,QAAA7R,EAAgB,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAoBA,EAAC,GAAA,CAAA,uBAHxDJ,EAoEIC,EAAAgS,EAAA9R,CAAA,EA5CH6C,EA2CKiP,EAAAhI,CAAA,EA1CJjH,EAqBKiH,EAAAC,CAAA,oPAdG3J,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,KAAAiS,EAAA,KAAApS,QAAgBA,EAAC,GAAA,wFAJXA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,mDACfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,8BAQbA,QAAgBA,EAAC,GAAA,qHAUlBA,EAAQ,CAAA,6DA3CH,CAAAiG,GAAA9F,EAAA,CAAA,EAAA,WAAA8R,KAAAA,EAAAjS,EAAgB,EAAA,EAAAA,EAAO,GAAA,EAAAA,MAASA,EAAc,EAAA,CAAA,6CAC1CkS,GAAAL,EAAA,QAAA7R,MAAeA,EAAC,GAAA,CAAA,CAAA,8BAAWkS,GAAAL,EAAA,OAAA7R,EAC1C,GAAA,EAAAA,EAAA,EAAA,EACGA,EAAM,GAAA,IAAA,EACLA,EAAA,EAAA,EACC,+BACA,YACOA,EAAgB,EAAA,EAAG,kCAAoC,EAAE,GAAG,MACpEA,EAAA,GAAA,CAAA,EAEC,KAAK,CAAC,EACN,IAAGwS,EAAA,EACH,KAAK,KAAK,CAAA,IACZ,MAAM,8BAhBY1H,EAAA+G,EAAA,gBAAA7R,OAAIA,EAAqB,EAAA,CAAA,gDAC3BA,EAAC,GAAA,IAAKA,EAAqB,EAAA,EAAG,CAAC,gCACrC8K,EAAA+G,EAAA,QAAA7R,EAAgB,EAAA,IAAAA,EAAK,GAAA,GAAAA,QAAoBA,EAAC,GAAA,CAAA,0KAhBpDA,EAAgB,EAAA,GAAAyS,GAAA,OAYdzS,EAAQ,EAAA,CAAA,aAAsBA,EAAE,GAAA,kBAArC,OAAIwC,GAAA,EAAA,gLAbP5C,EAoFIC,EAAA6S,EAAA3S,CAAA,sFAnFEC,EAAgB,EAAA,6FAYdA,EAAQ,EAAA,CAAA,8EAAb,OAAIwC,GAAA,kJA+EHtC,EAAAF,OAAQ,EAAC,oJALXJ,EAMIC,EAAAwS,EAAAtS,CAAA,iBADFI,EAAA,CAAA,EAAA,SAAAD,KAAAA,EAAAF,OAAQ,EAAC,KAAAC,GAAA0S,EAAAzS,CAAA,8LAyERN,EAKQC,EAAAwI,EAAAtI,CAAA,qGANJC,EAAQ,CAAA,GAAIgN,GAAuB,CAAAhN,OAAOA,EAAC,GAAA,CAAA,EAAGA,EAAc,EAAA,EAAEA,EAAQ,CAAA,CAAA,0JAvB3DA,EAAa,EAAA,IAAGA,EAAK,GAAA,CAAA,IAAIA,EAAC,GAAA,CAAA,wDAInC,KAAA4S,GAAO5S,EAAU,EAAA,EAAA,CAAAA,OAAOA,EAAC,GAAA,CAAA,CAAA,WACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,kDAP9CA,EAAK,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,QAAK,SAApB6S,EAAA,MAAA7S,EAAK,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,OAClBA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,uaAzCTA,EAAgB,EAAA,GAAIA,EAAC,GAAA,IAAK,KAAS,CAAC,EAkB/B2K,EAAA0H,EAAA,QAAAS,EAAA,UAAA9S,EAAe,EAAA,EAAAA,EAAY,GAAA,CAAA,EAAA,YAAAA,EAC1C,GAAA,EAAAA,EAAA,EAAA,EACGA,EAAM,GAAA,IAAA,EACLA,EAAA,EAAA,EACC,+BACA,YACOA,EAAgB,EAAA,EAAG,kCAAoC,EAAE,GAAG,MACpEA,EAAA,GAAA,CAAA,EAEC,KAAK,CAAC,EACN,IAAG+S,EAAA,EACH,KAAK,KAAK,CAAA,IACZ,cAAU/S,EAAO,EAAA,IAAGA,EAAS,GAAA,CAAA,IAAAA,SAAM,GAAE,EAGjC2K,EAAA0H,EAAA,QAAAW,EAAAC,GAAA3H,GAAkB,CAAAtL,EAAO,GAAA,EAAAA,QAAIA,EAAc,EAAA,CAAA,CAAA,EAAA,gBAAA,EAnC7B8K,EAAAuH,EAAA,gBAAArS,OAAIA,EAAqB,EAAA,CAAA,oBAC3BA,EAAC,GAAA,IAAKA,EAAqB,EAAA,EAAG,CAAC,EAgCrC8K,EAAAuH,EAAA,QAAArS,OACZsL,GAAgB,CAAEtL,EAAO,GAAA,EAAAA,QAAIA,EAAc,EAAA,CAAA,CAAA,oBAEzBA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,GAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,GAAA,CAAA,uBAvC5BJ,EA6EIC,EAAAwS,EAAAtS,CAAA,EApCH6C,EAmCKyP,EAAAxP,CAAA,+LA/BY7C,EAAa,EAAA,IAAGA,EAAK,GAAA,CAAA,IAAIA,EAAC,GAAA,CAAA,mGAInCG,EAAA,CAAA,EAAA,GAAAA,EAAA,CAAA,EAAA,UAAAiS,EAAA,KAAAQ,GAAO5S,EAAU,EAAA,EAAA,CAAAA,OAAOA,EAAC,GAAA,CAAA,CAAA,oCACrB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,qIAP9CoS,EAAA,MAAApS,EAAK,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,+DAClBA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,4DAwBbA,EAAQ,CAAA,GAAIgN,GAAuB,CAAAhN,OAAOA,EAAC,GAAA,CAAA,EAAGA,EAAc,EAAA,EAAEA,EAAQ,CAAA,CAAA,oGAjElEA,EAAgB,EAAA,GAAIA,EAAC,GAAA,IAAK,KAAS,wBAkB9B,CAAAiG,GAAA9F,EAAA,CAAA,EAAA,QAAAA,EAAA,CAAA,EAAA,EAAAA,EAAA,CAAA,EAAA,SAAA2S,KAAAA,EAAA,UAAA9S,EAAe,EAAA,EAAAA,EAAY,GAAA,CAAA,EAAA,YAAAA,EAC1C,GAAA,EAAAA,EAAA,EAAA,EACGA,EAAM,GAAA,IAAA,EACLA,EAAA,EAAA,EACC,+BACA,YACOA,EAAgB,EAAA,EAAG,kCAAoC,EAAE,GAAG,MACpEA,EAAA,GAAA,CAAA,EAEC,KAAK,CAAC,EACN,IAAG+S,EAAA,EACH,KAAK,KAAK,CAAA,IACZ,cAAU/S,EAAO,EAAA,IAAGA,EAAS,GAAA,CAAA,IAAAA,SAAM,uBAG/B,CAAAiG,GAAA9F,EAAA,CAAA,EAAA,QAAAA,EAAA,CAAA,EAAA,SAAA6S,KAAAA,EAAAC,GAAA3H,GAAkB,CAAAtL,EAAO,GAAA,EAAAA,QAAIA,EAAc,EAAA,CAAA,CAAA,EAAA,0GAnC7B8K,EAAAuH,EAAA,gBAAArS,OAAIA,EAAqB,EAAA,CAAA,4DAC3BA,EAAC,GAAA,IAAKA,EAAqB,EAAA,EAAG,CAAC,6CAgCrC8K,EAAAuH,EAAA,QAAArS,OACZsL,GAAgB,CAAEtL,EAAO,GAAA,EAAAA,QAAIA,EAAc,EAAA,CAAA,CAAA,8DAEzBA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,GAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,GAAA,CAAA,2JAjDxBA,EAAgB,EAAA,GAAA4H,GAAA5H,CAAA,OASdA,EAAI,GAAA,CAAA,aAAsBA,EAAE,GAAA,kBAAjC,OAAIwC,GAAA,EAAA,sLAV4CxC,EAAK,GAAA,EAAG,IAAM,CAAC,UAAlEJ,EA0FIC,EAAA6S,EAAA3S,CAAA,sFAzFEC,EAAgB,EAAA,2GASdA,EAAI,GAAA,CAAA,mFAVuCA,EAAK,GAAA,EAAG,IAAM,CAAC,+BAU/D,OAAIwC,GAAA,qQAtGGxC,EAAc,EAAA,EACR,eAAAA,EAAqB,EAAA,IAAA,MACpCA,QAAuB,wKANZA,EAAI,EAAA,IAAA,iBAAJA,EAAI,EAAA,GAEIA,EAAY,EAAA,IAAA,yBAAZA,EAAY,EAAA,GACJA,EAAe,EAAA,IAAA,iCAAfA,EAAe,EAAA,wNAL7CJ,EA8LKC,EAAAgD,EAAA9C,CAAA,gGAxLOC,EAAc,EAAA,GACRG,EAAA,CAAA,EAAA,MAAA+S,EAAA,eAAAlT,EAAqB,EAAA,IAAA,MACpCA,QAAuB,+GANZA,EAAI,EAAA,qDAEIA,EAAY,EAAA,8DACJA,EAAe,EAAA,gTA8L/CJ,EAIKC,EAAAgD,EAAA9C,CAAA,EAHJ6C,EAEQC,EAAAwF,CAAA,+GAML,EAAArI,MAAiB,EACjB,EAAAA,MAAiB,EACf,IAAAA,MAAiB,iMASLA,EAAI,EAAA,EAAC,OAAS,EACd,gBAAAA,EAAK,EAAA,EAAA,CAAC,EAAE,OAAS,iFAZ/BG,EAAA,CAAA,EAAA,MAAAgT,EAAA,EAAAnT,MAAiB,GACjBG,EAAA,CAAA,EAAA,MAAAgT,EAAA,EAAAnT,MAAiB,GACfG,EAAA,CAAA,EAAA,MAAAgT,EAAA,IAAAnT,MAAiB,mUASLA,EAAI,EAAA,EAAC,OAAS,GACdG,EAAA,CAAA,EAAA,YAAAgT,EAAA,gBAAAnT,EAAK,EAAA,EAAA,CAAC,EAAE,OAAS,kLAQ/B,EAAAA,MAAmB,EACnB,EAAAA,MAAmB,sMAWL,mBACAA,EAAQ,EAAA,EAAC,OAAS,gGAbhCG,EAAA,CAAA,EAAA,MAAAgT,EAAA,EAAAnT,MAAmB,GACnBG,EAAA,CAAA,EAAA,MAAAgT,EAAA,EAAAnT,MAAmB,gUAYLA,EAAQ,EAAA,EAAC,OAAS,+LA3a9BuI,GAAAvI,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAKA,EAAU,CAAA,GAAKA,EAA0B,EAAA,GAAAA,EAAoB,EAAA,GAAAA,QAAgB,SAAMoT,GAAApT,CAAA,EA+BlHqT,EAAArT,QAAa,IAASA,EAAe,EAAA,EAAA,SAAW,GAACsT,GAAAtT,CAAA,EAqBhDuT,EAAAvT,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACwT,GAAAxT,CAAA,IAKzBA,EAAgB,EAAA,GAAAyT,GAAA,QAYdzT,EAAQ,EAAA,CAAA,cAAsBA,EAAE,GAAA,mBAArC,OAAIwC,GAAA,EAAA,0DA4ECxC,EAAG,EAAA,CAAA,aAAsBA,EAAE,GAAA,mBAAhC,OAAIwC,GAAA,EAAA,sHAsBF,UACE,iBACO,iBACA,aAkBH,WAAAxC,MAAK,0BAA0B,8JAoMzCA,EAAI,EAAA,EAAC,SAAW,GAAKA,EAAQ,CAAA,GAAIA,EAAS,CAAA,EAAC,CAAC,IAAM,WAASK,GAAAL,CAAA,IAQ3DA,EAAgB,EAAA,GAAA0H,GAAA1H,CAAA,EAmBhB0T,EAAA1T,QAAuB,MAAIyC,GAAAzC,CAAA,gcAzWTA,EAAa,EAAA,EAAC,QAAU,CAAC,wDAzB/BA,EAAY,EAAA,EAAA,IAAA,8EADXA,EAAI,CAAA,CAAA,EAEH8K,EAAAhB,EAAA,YAAA9J,OAAoBA,EAAkB,EAAA,CAAA,uDA3BzDJ,EAgYKC,EAAAgK,EAAA9J,CAAA,wBA3WJ6C,EA0WKiH,EAAAC,CAAA,wBA/UJlH,EAoHOkH,EAAA6J,CAAA,wBA5GN/Q,EAuFO+Q,EAAA5Q,CAAA,EAtFNH,EAqFIG,EAAA6Q,CAAA,gFAELhR,EAmBO+Q,EAAA3Q,CAAA,EAlBNJ,EAiBII,EAAA6Q,CAAA,0QAjKD7T,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAKA,EAAU,CAAA,GAAKA,EAA0B,EAAA,GAAAA,EAAoB,EAAA,GAAAA,QAAgB,8GA+B5GA,QAAa,IAASA,EAAe,EAAA,EAAA,SAAW,yDAqB/CA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,yDAKxBA,EAAgB,EAAA,8FAYdA,EAAQ,EAAA,CAAA,uFA4ERA,EAAG,EAAA,CAAA,uFA/FQA,EAAa,EAAA,EAAC,QAAU,CAAC,uGA0IjCG,EAAA,CAAA,EAAA,OAAA2T,EAAA,WAAA9T,MAAK,0BAA0B,0KAnK7BA,EAAY,EAAA,EAAA,IAAA,0EADXA,EAAI,CAAA,CAAA,mBAEH8K,EAAAhB,EAAA,YAAA9J,OAAoBA,EAAkB,EAAA,CAAA,EAsWpDA,EAAI,EAAA,EAAC,SAAW,GAAKA,EAAQ,CAAA,GAAIA,EAAS,CAAA,EAAC,CAAC,IAAM,oFAQlDA,EAAgB,EAAA,gHAmBhBA,QAAuB,yJAtVrB,OAAIwC,GAAA,2BA4EJ,OAAIA,GAAA,uYAv6BD,SAAAuR,IAAA,CACD,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,EAGzC,SAAAC,GACRC,EACApL,EACAyF,EAAAA,KAKIlJ,EAAK6O,GAAA,GACLpL,GAAAA,EAAU,CAAC,IAAM,SAAWzD,EAAG,OAASyD,EAAU,CAAC,EAAA,CAChD,MAAAqL,EAAO,MAAMrL,EAAU,CAAC,EAAIzD,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAK,CAAAyH,EAAG,IAAS,GAAA,EAAIzH,EAAG,MAAM,EAAA,EAChCA,EAAKA,EAAG,OAAO8O,CAAI,EAGf,MAAA,CAAA9O,GAAMA,EAAG,SAAW,EACjB,MAAMyD,EAAU,CAAC,CAAA,EACtB,KAAK,CAAC,EACN,KAAKgE,EAAGrK,IAAA,OACF2R,EAAMJ,KACZzF,OAAAA,EAAI6F,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,MACvB,CAAA,GAAIA,EAAK,MAAO,KAAK,UAAU3R,EAAI,CAAC,KAIzC4C,EAAG,KAAKgP,EAAG5R,IAAA,OACX2R,EAAMJ,KACZzF,OAAAA,EAAI6F,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,OACvB,GAAIA,EAAK,MAAOC,GAAK,EAAA,cA00BhBvH,EAAGwH,IAAG,oBAAyBA,CAAG,OAI7BlS,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,OA+OX0K,EAAGwH,IAAG,oBAAyBA,CAAG,OAf7BlS,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,OArFZ0K,EAAGwH,IAAG,oBAAyBA,CAAG,OAI7BlS,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,8BAnmClB,CAAA,SAAAlB,CAAA,EAAAL,GACA,MAAA0T,EAAuB,IAAA,EAAA1T,GACvB,WAAA2T,EAAa,EAAA,EAAA3T,EACb,CAAA,QAAA4T,EAAA,EAAA,EAAA5T,EACA,CAAA,OAAA6T,EAAA,EAAA,EAAA7T,EACA,CAAA,UAAAiI,CAAA,EAAAjI,EACA,CAAA,UAAAkI,CAAA,EAAAlI,EACA,CAAA,iBAAAM,CAAA,EAAAN,GAMA,SAAAS,EAAW,EAAA,EAAAT,GACX,KAAA8T,EAAO,EAAA,EAAA9T,EACP,CAAA,KAAAU,CAAA,EAAAV,EACA,CAAA,KAAAuI,CAAA,EAAAvI,GAEA,WAAA2C,EAAa,GAAA,EAAA3C,GACb,YAAAQ,EAAc,EAAA,EAAAR,EACd,CAAA,cAAA+T,EAAA,EAAA,EAAA/T,GACA,iBAAAgU,EAAmB,EAAA,EAAAhU,EACnB,CAAA,OAAAiU,CAAA,EAAAjU,EACA,CAAA,eAAAkU,CAAA,EAAAlU,GACA,uBAAAmJ,EAAyB,EAAA,EAAAnJ,GACzB,iBAAAoJ,GAAmB,EAAA,EAAApJ,GACnB,gBAAAmU,GAAkB,EAAA,EAAAnU,GAClB,UAAAW,EAAgC,MAAA,EAAAX,GAChC,YAAAqJ,EAA4C,MAAA,EAAArJ,GAC5C,eAAAoU,EAAiB,CAAA,EAAApU,EAExBqU,EAAwB,EAMxBzJ,EAAA,CAAA,EAEA5H,EAAmC,IAM5B,cAAA9C,GAAmC,IAAA,EAAAF,GACnC,QAAAG,GAA6B,IAAA,EAAAH,EACpCsU,GACA5G,EAAA,CAAA,QAME9M,GAAWC,SAOb0T,EAAwB,GACxBhU,GAAiB,GACjBiU,EAA8B,GAC9BC,EAAkC,GAClCC,EAKO,KACPC,EAIO,KACPrL,EAAgB,GAChBsL,EAAW,GACXC,GAAa,GAEbC,GACJtO,GAAA,IAAA,CAICsO,GAHc,iBAAiB,SAAS,eAAe,EACrD,iBAAiB,gBAAgB,EACjC,OAC4B,KAC9B,SAAS,gBAAgB,MAAM,YAC9B,wBACAA,EAAA,IAII,MAAAC,GAAA,CAAexQ,EAAasG,IACjCnE,IAAOnC,CAAG,IAAIsG,CAAG,GAAG,eAuCZmK,GAAaC,EAAA,CAIf,MAAAC,EAAkBD,EAAQ,cAC5BC,IAAoB,EAAA,GACjB,MAAMhN,EAAU,CAAC,IAAM,QAAUA,EAAU,CAAC,EAAIgN,CAAe,EACpE,KAAK,CAAC,EACN,KAAKjJ,EAAGrK,IACD,MACNqG,EAAU,CAAC,IAAM,QACdA,EAAU,CAAC,EACXgN,EAAQ,CAAC,EAAE,QAAUrB,EAAQ,QAE/B,KAAK,CAAC,EACN,IAAA,CAAK3H,GAAGJ,KAAA,OACFiB,GAAKqG,KACX,OAAA9R,EAAA,GAAAqM,EAAIZ,EAAE,EAAIY,EAAIZ,EAAE,GAAO,CAAA,MAAO,KAAM,KAAM,IAAA,EAAAY,CAAA,EACpC,CAAQ,MAAOuH,IAAUrT,CAAC,IAAIiK,EAAC,GAAK,GAAI,GAAAiB,WAO/CqI,GAAW/B,GAAaQ,EAAS3L,EAAWyF,CAAG,EAC/C0H,GAAwBxB,EASxBlN,EAAA,CAAA,CAAA,CAAA,EACA2O,GAOAC,GAAmBH,GAAS,IAAK3B,GAAMA,EAAE,KAAK,EAC9C+B,GAAgB7O,EAAK,IAAKnC,GAAQA,EAAI,IAAKoG,GAAS,OAAOA,EAAK,KAAK,CAAA,CAAA,EAE1D,eAAA6K,IAAA,CAEV,GAAA7L,GAAA,OACE,MAAA8L,EAAkBN,GAAS,IAAK3B,GAAMA,EAAE,KAAK,EAC7CkC,EAAehP,EAAK,IAAKnC,GAC9BA,EAAI,IAAKoG,GAAS,OAAOA,EAAK,KAAK,CAAA,CAAA,GAIlC,CAAAqH,GAAO0D,EAAcH,EAAa,GAClC,CAAAvD,GAAOyD,EAAiBH,EAAgB,KAIzC1U,GAAS,SAAA,CACR,KAAM8F,EAAK,IAAKnC,GAAQA,EAAI,IAAKoG,GAASA,EAAK,KAAK,CAAA,EACpD,QAASwK,GAAS,IAAK3B,GAAMA,EAAE,KAAK,EACpC,SAAU,OAENW,IACJvT,GAAS,OAAO,EAEjB2U,GAAgBG,EAChBJ,GAAmBG,GAIZ,SAAAE,GACRC,EACAC,EACA3Q,EAAA,KAEK2Q,EAAc,MAAA,OACf,GAAAjC,EAAQiC,CAAK,IAAMD,EAAA,CAClB,GAAA1Q,IAAc,MAAc,MAAA,YAC5B,GAAAA,IAAc,MAAc,MAAA,aAE1B,MAAA,sBAIO5D,GAAeC,EAAA,CACzB,GAAAkT,IAAoB,IAASD,IAAgB,UACxCjT,EAAM,IAAA,CACR,IAAA,YACJF,EAAA,GAAA2B,EAAA,CAAY,EAAGyR,CAAe,CAAA,EAC9BpT,EAAA,GAAAuJ,EAAA,CAAA,CAAmB,EAAG6J,CAAe,CAAA,CAAA,OACrCA,EAAkB,EAAA,SAEd,IAAA,YACJpT,EAAA,GAAAoT,EACCA,EAAkB,EAAIA,EAAkB,EAAIA,CAAA,SAEzC,IAAA,kBACJA,EACCA,EAAkBU,GAAS,OAAS,EACjCV,EAAkB,EAClBA,CAAA,SAEA,IAAA,SACJlT,EAAM,eAAA,OACNkT,EAAkB,EAAA,QAEd,IAAA,QACJlT,EAAM,eAAA,EACFd,QACH+T,EAAcC,CAAA,WAMdlT,EAAM,MAAQ,UAAYA,EAAM,MAAQ,YAAA,CACtC,GAAA,CAAAd,EAAA,OAED,GAAA8T,EAAA,CACI,KAAA,CAAAhQ,EAAKsG,EAAG,EAAI0J,EACbuB,GAAWpI,EAAIhH,EAAKnC,CAAG,EAAEsG,EAAG,EAAE,EAAE,EAAE,SACpCiL,IAAYA,GAAS,iBAAmBA,GAAS,cAIpDvU,EAAM,MAAQ,UACduU,IAAU,iBAAmBA,IAAU,MAAM,QAI1CvU,EAAM,MAAQ,aAAeuU,IAAU,iBAAmB,SAK/DvU,EAAM,eAAA,EACFqJ,EAAe,OAAS,SAC3BlE,EAAOwF,GAAkBxF,EAAMkE,CAAc,CAAA,EAC7ChK,GAAS,SAAA,CACR,KAAM8F,EAAK,IAAKnC,GAAQA,EAAI,IAAKoG,IAASA,GAAK,KAAK,CAAA,EACpD,QAASwK,GAAS,IAAK3B,GAAMA,EAAE,KAAK,EACpC,SAAU,OAENW,IACJvT,GAAS,OAAO,UAMf,GAAAW,EAAM,MAAQ,MAAQA,EAAM,SAAWA,EAAM,SAAA,CAChDA,EAAM,eAAA,EACFqJ,EAAe,OAAS,GACrB,MAAAf,GAAA,SAKH,GAAA,CAAA7G,SAIE,KAAA,CAAApB,EAAGiK,CAAC,EAAI7I,SAEPzB,EAAM,IAAA,CACR,IAAA,aACA,IAAA,YACA,IAAA,YACA,IAAA,UACA,GAAAgT,EAAA,MACJhT,EAAM,eAAA,EACA,MAAAwU,EAActJ,GAAYlL,EAAM,IAAA,CAAMK,EAAGiK,CAAC,EAAGnF,CAAI,EACnDqP,GACCxU,EAAM,eACTqJ,EAAiBO,GAChBP,EAAe,OAAS,EAAIA,EAAe,CAAC,EAAA,CAAKhJ,EAAGiK,CAAC,EACrDkK,CAAA,CAAA,OAEDxB,EAAU,EAAA,IAEVlT,EAAA,GAAAuJ,EAAA,CAAkBmL,CAAW,CAAA,OAC7BxB,EAAU,EAAA,QAEXvR,EAAW+S,CAAA,GAEXA,IAAgB,IAChBxU,EAAM,MAAQ,WACdK,IAAM,SAEN6S,EAAkB5I,CAAA,OAClB7I,EAAW,EAAA,EACX3B,EAAA,GAAAuJ,EAAA,CAAA,CAAA,OACA2J,EAAU,EAAA,SAIP,IAAA,SACC,GAAA,CAAA9T,EAAA,MACLc,EAAM,eAAA,OACNgT,EAAU,EAAA,QAEN,IAAA,QAEA,GADJhT,EAAM,eAAA,EACFd,KACCc,EAAM,SACTyU,GAAQpU,CAAC,EACH,MAAAyC,GAAA,OACNrB,EAAY,CAAApB,EAAI,EAAGiK,CAAC,CAAA,UAEhBmG,GAAOuC,EAAA,CAAU3S,EAAGiK,CAAC,CAAA,EAAA,CAClB,MAAA+B,GAAUlH,EAAK9E,CAAC,EAAEiK,CAAC,EAAE,GACrBiK,GAAWpI,EAAIE,EAAO,EAAE,MAC1BkI,IACHzU,EAAA,GAAAqF,EAAK9E,CAAC,EAAEiK,CAAC,EAAE,MAAQiK,GAAS,MAAApP,CAAA,OAE7B6N,EAAU,EAAA,EACJ,MAAAlQ,GAAA,EACNhD,EAAA,GAAA2B,EAAA,CAAYpB,EAAGiK,CAAC,CAAA,OAEhBxK,EAAA,GAAAkT,EAAA,CAAW3S,EAAGiK,CAAC,CAAA,OACftL,GAAiB,EAAA,QAKhB,IAAA,MACJgB,EAAM,eAAA,OACNgT,EAAU,EAAA,EACJ,MAAA0B,GAAY5J,GAChB,CAAAzK,EAAGiK,CAAC,EACLnF,EACAnF,EAAM,QAAA,EAEH0U,KACH5U,EAAA,GAAAuJ,EAAA,CAAkBqL,EAAS,CAAA,OAC3BjT,EAAWiT,EAAA,EACPxV,SACH8T,EAAU0B,EAAA,OACV1V,GAAiB,EAAA,kBAKd,GAAA,CAAAE,EAAA,QAEF8T,GAAYA,GAAWvC,GAAOuC,EAAU,CAAA3S,EAAGiK,CAAC,CAC9C,IAAAtK,EAAM,IAAI,SAAW,SAErBhB,GAAiB,EAAA,EACjBc,EAAA,GAAAkT,EAAA,CAAW3S,EAAGiK,CAAC,CAAA,IAMf,IAAAuC,GACAD,GAEK,SAAA+H,GAAYrL,EAAa3F,EAAA,CACtB,OAAAiJ,IAAY,UAAYA,KAAYtD,QAC9CuD,GAAiBlJ,CAAA,OACjBiJ,GAAUtD,CAAA,GACAsD,KAAYtD,IAClBuD,KAAmBlJ,QACtBkJ,GAAiB,MAAA,OACjBD,GAAU,MAAA,QAEVC,GAAiBlJ,CAAA,GAKL,eAAAiR,GAAYvU,EAAWwU,EAAU,GAAA,CAC1C,CAAA3V,GAAY+T,IAAgB5S,SACjCoB,EAAW,EAAA,EACX3B,EAAA,GAAAuJ,EAAA,CAAA,CAAA,OACA6J,EAAkB7S,CAAA,OAClB4S,EAAc5S,CAAA,YAGNyU,GAAgB9U,EAAA,CACnB,GAAAd,EAEG,OAAAc,EAAM,OAAO,IAAA,CACf,IAAA,SACA,IAAA,QACA,IAAA,MACJA,EAAM,eAAA,OACNyB,EAAW,EAAA,OACXyR,EAAkBD,CAAA,OAClBA,EAAc,EAAA,EACdnH,GAAO,MAAA,wBAKK2I,GAAQ9P,EAAA,CAGlB,GAFJmH,GAAO,MAAA,EAEHnF,EAAU,CAAC,IAAM,UAAA,OAEf,MAAAoO,EAAU,MAAM5P,EAAK,CAAC,GAAG,QAAUkN,EAAQ,MAAM,EACrD,KAAK,CAAC,EACN,IAAA,CAAK3H,EAAGrK,IAAA,OACF2R,GAAMJ,KACZ,OAAA9R,EAAA,GAAAqM,EAAI6F,EAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAA7F,CAAA,EACvB,CAAA,GAAI6F,GAAK,MAAO,EAAA,IAGvB7M,EAAK,SAAW,EACnBrF,EAAA,GAAAqF,EAAA,CAAQ4P,CAAO,CAAA,EACLpQ,IAAA,QAAuBA,GAAS,GAAKA,GAASQ,EAAK,OAC7DA,EAAK,OAAOR,EAAO,EAAGoQ,CAAO,EAE7B5P,EAAK,KAAK4P,CAAO,0BAIlBjV,EAAA,GAAA2B,EAAA,CAAYkD,WAAsBA,EAAQQ,EAAK,OAAS,EAAG,CAAC,CAAA,iBAK9C6P,GAAQrQ,EAAA,CAElB,GADJmH,GAAO,MAAA,EACHpF,EAAU,CAAC,IAAM,UAAA,OAEf,MAAAuO,EAAetQ,IAAU,OAAYA,EAAQQ,EAAK,CAAC,EAAE,eAElD9E,EAAI,EAAGA,EAAI8E,EAAK,OAAQ9E,IAAA,OAC1B2R,EAAMJ,KACZ9R,EAAA,GAAAqM,EAAI6F,CAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAA7F,CAAA,EAChChH,EAAK9E,CAAC,EAAE,OAAO4U,EAAc,EAAK,CAAA,GAAIjD,EAAK,MAAO,EAAA,CAAA,EAGnDK,EAAQ,OAAO4C,EAAc,EAAa,UAAA5C,EAAQ,OAAS,CAAC,EAAA,kCAKtD,MAAAvP,GAAA,EAEN,sBAAA,IAAA,CACC8R,GAAYK,EAAc,EAAI,EACxB,MAAAC,EAAQpJ,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,YAClDA,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,UAAW,KAAMoJ,CAAA,CAAA,aAI9CrJ,GAAqB7L,EAAA,CACzBmV,GAA0BnV,EAAO8L,EAAM,SAC1CkH,EAAU,EAAA,EACVlT,EAAA,GAAAuJ,EAAA,CAAA,CAAA,OACA4J,EAAc,EAAA,OACdC,EAAkB,EAAA,OAClBC,EAAmB,IAAA,OACnBC,EAAqB,IAAA,GAOnB,IAAA/I,GAAA,CAAA,EACAyB,GACAnL,GAEK,SAAAyU,IAAA,CACF,MAAAC,EAAShL,GAAM,IAAK7K,GAAOA,GAAI,aAAe,CAAC,EACjD,GAAA6V,EAAO,SAAW,EAAA,OAElB5C,GACH3G,GAAO,MAAM,YAA0C,0BAAA,GAAAuJ,EAAO,CAAC,CAAA,IAAA,EAE1D,MAAAC,EAAa7C,EAAmB4C,EAAO,MAAM,CAAC,EAAIA,EACxDC,EAAW,SAASC,EAAOlV,IAAA,CACrBmS,EAAcnS,CAAC,GACnByL,GAAO,MAAM,YACI,gBAAAzL,CAAC,MACdkV,EAAQC,GAAkBF,EAAW,MAAM,IAAA,aAMzCG,GAAe9Q,EAAA,QAChB6N,EAAc7N,CAAK,uBAAyBA,CAAK,QAGrD+Q,GACHpD,EAAO,MAAM,EAAIlR,EAAakR,EAAO,OAAU,EAAE,EAAE,OAAS,GAAK,GAC9DkD,GAAkB,WAEb7I,GACRhB,EACAgK,EACAC,EACAtM,EACA+B,GAAA,KAEIE,GAAK,KAIE,GAHP9J,GAAYA,EAAS,CAAC,IAAKkK,GAASlK,EAAS,CAAC,IAAKkK,EAAMlK,EAAS,CAAC,CAAA,IACtE8J,GAAKI,EAAMlK,EAAS,CAAC,CAAG,EAAAA,EAAS,CAAC,CAAG,EAAA,IAE3B,SAAA6H,GAAQ,UAAa,CAAA+B,MAIhCmC,GAAgB7B,EAAOgK,EAAgBC,EAAUtM,EAAK+B,EAAG,0BAGrDE,IAAA,CACI,KAAA,CAAAlL,GAAGiK,EAAC,EAAIgB,GAAoBC,GAAIpG,CAAI,EAC3CrF,EAAA,GAAA2B,EAAA,CAAYpB,GAAGiK,EAAC,CAAA,OAQduL,GAAa,GAEjB5Q,GAAA,IAAA,CACO,MAAA6Q,EAAA,IAAe,qBAAsBC,GAAA,CAC1CA,EAAQ,QAASC,GAAA,CACZA,EAAM,gBAAmB,CAAAH,KAC5BT,8BAGDS,GAAaG,EAAM,mBAIrB,OAAAF,EAAS,QAAQhK,EAAM,EACvB,SAAS,iBAAiB,QAASD,EAAoB,EACvD,OAAO,iBAAiB,SAAUoK,EAAa,EAC/C,SAAS,iBAAiB,mBAAoBC,EAAwB,OAGrEJ,EAAS,WAAA,EACT,SAAS,oBAAoB,QAASjK,EAAoB,EAC1D,OAAO,oBAAoB,SAAUoK,EAAa,EAClD,SAAS,oBACR,mBACAC,EAAA,KAKM,SAAAC,GACRnW,EACAgD,EACAsG,EAAA,CAEItJ,EAAM,kBAAkB,oBAI5BA,EAAM,eAAA,EACNA,EAAM,gBAAA,EAEF,EAAAyS,GAAoBnJ,IAAQ,WAEhCtK,GAAiB,EAAA,OACjBmU,EAAmB,IAAA,OACnBC,EAAqB,IAAA,OACrBF,EAAkB,EAAA,OAClBD,EAAc,EAAA,EAEdnT,EAAA,GAAAuJ,EAAiBkB,GAAkB,CAAAvH,EAAKsG,CAAG,EAAGD,EAAgBrJ,CAAK,CAAA,EACnE8L,GAAO,MAAA,EAEH5M,IACCmK,EAAe,SAAW,GAC7BvJ,EAAA,GAAAkT,EAAA,CAAWhQ,EAAKsG,CAAG,CAAA,EACnBxG,GAAO,EAAA,KAAA,IAAA,CACA,MAAAyR,EAAWpI,EAAIhH,EAAKnC,CAAG,EAAEsG,CAAG,EAAE,EAAE,EAAE,MACpCiL,IACHA,EAAS,MAAA,EACTA,EAAS,eAAiBA,EAAS,aAClCA,EAAS,MAAM,gBAIlBvB,EAAU,EAAA,GAIZoD,GAAmBpT,EAAKsG,CAAG,EAE3BjK,GAAS,SAAA,CACR,MAAA,CAAQ2D,EAAKsG,CAAG,EAChB,MAAOkK,GAAYxQ,EAAKsG,CAAG,EAC3B,UAAWnE,EAAKnC,CAAG,EAAE,IAAKqT,GAAMA,EAAE,KAAK,MAIhC,SAAAC,GAAiBtW,EAAmBgD,EAAasG,EAAA,CAGxD,GAFDtJ,EAAM,gBAAA,EAELmT,GACAA,EAAiB,MAAQnQ,GACzBmQ,EAAiB,MAAQ7J,OAEzB6J,EAAmB,IAAA,aAEb/J,EAAQpJ,EAAM,OAAuB,QAAQ,IAAI,EACnD,GAAAoJ,EAAA,CACG,MAAA/D,GAAO+D,EAAK,wBAClBtJ,EAAA,GAAAqT,EAAA,CAAqB,IAAAnQ,EAAK,IAAAsG,EAAK,EAAGjE,GAAK,MAAO,EAAGA,GAAK,MAAA,CAAA,IAKhD,SAAAkR,GAAW5R,EAAe6R,EAAA,OAC5BC,EAAYD,IAAa,QAAU7R,EAAQA,EAAQ,EACzD8P,GAAQgC,CAAS,OACjBtD,EAAmB,IAAA,OACnBC,EAAqB,IAAA,EAGb,SAAAsD,GAAW/R,EAAe6R,EAAA,OAC5BG,EAAYH,IAAa,OAAS7R,EAAQA,EAAQ,EACxDqQ,GAAQ2B,CAAS,OACjBxD,EAAmB,IAAA,OACnBC,EAAqB,IAAA,EAGb,SAAA6C,IAAA,MACR9C,EAAmB,IAAA,OACnBC,EAAqB,IAAA,EACrBtT,EAAA,GAAAuJ,EAAA,CAAA,CAAA,OACA5H,EAAW,EAAA,OACXuR,EAAU,EAAA,EACVoC,SAGGwB,GAIO,KASF,SAAAR,GAAmBpT,EAAasG,EAAA,CACxCsN,GACCA,IAAe,OAAS,QACxBA,GAAc,MAAQ5T,GACtB4T,GAAc,MAAQtN,EACnB,KACE,CAAA,KAAM,OAAQ,IAAAtG,EAAK,IAAAsG,CAAA,EAGjB,SAAAuN,IAAA,CACH,SAAS,mBAIb,SAAS,eAAA,OACT9O,EAAgB,EAAA,IAJhB+D,GAAO,kBAAA,OACP/D,EAAgB,EAAA,GAOT,SAAAmO,IAAA,CACRpW,EAAA,GAAAiI,EAAA,CAAA,CAAkB,SAAS,iBAAA,EAGb,eAAAO,IAAA,CACR,MAAAsF,GAAgBzI,EAAMkE,CAAc,OAC1CiK,GAAa,EAAA,EACb,qBACCA,GAAa,EAAA,GACX,KAGK,SAAAwD,GAAmB9W,EAAmBsJ,EAAA,CAE1C,GADJtJ,EAAM,gBAAA,EACFoT,GAAsBA,EAAmB,MAAQ9J,OACpD8J,EAAqB,IAAA,aAEfvU,EAAUmB,EAAM,OAAuB,QAAQ,IAAI,EACrD,GAAAnB,EAAA,CACG,MAAAwG,EAAOxG,EAAO,wBACpBiB,EAAA,GAAAsT,EAAA,CAAuB,IAAA9J,EAAK,EAAGjE,EAAK,MAAO,EAAGA,EAAK,MAAA,CAAA,IAKtD0R,GAAA,IAAA,MACCnE,GAAkB,EAAA,mBAGJoE,GAAWrS,EAAA,CACzBmH,GAAO,MAAA,EACHnF,EAAU,CAAC,IAAM,YACjBxB,EAAK,QAAU,IACnBA,EAAK,OAAOR,EAAO,CAAC,+BAEpBlD,EAAW,EAAA,mBAGGwV,GAAWtS,EAAA,CACzBmH,GAAO,MAAA,EACHpF,EAAU,CAAC,IAAM,YACjBkN,GAAS,QAAU,IAEvBA,GAAS,OAAOjP,EAAO,CAAC,2CAGpBQ,EAAK,OAAS,IACjBA,EAAK,QAASnC,GAAA,CACbA,EAAI,OAAO2B,EAAO,CAAC,kCAIrBlD,EAAW,EAAA,aAGHyV,GAAcvS,EAAA,CACtBqS,GAAWrS,CAAK,OAChBwO,EAAmB,IAAA,OACnBC,EAAqB,IAAA,WAGb+D,GAAcxS,EAAA,CACtBsS,GAAWtS,CAAK,OAChBwO,EAAmB,IAAA,OACnBC,EAAqB,IAAA,WAmCbgE,GAAqB9N,EAAA,MAC7BD,EAAiB2C,GAAc7G,EAAMmE,CAAG,CAAA,EACxCxJ,EAAA,GAAA2B,EAAW4H,EAAe,CAAC,CAAA,OAC3B2J,EAAU,EAAA,WAGFqE,GAAkBrU,EAAA,MAC1BqG,EAAiB4C,GAAW9G,EAAMnC,CAAG,CAAA,EACrClD,EAAA,GAAA2B,EAAW4H,EAAe,CAAC,CAAA,OAC3B2J,EAAU,EAAA,EAGP,IAAAsE,GAuBAlP,GAAsC,cAEjCmP,GAAcC,EAAA,MACtBpP,GAAuBoP,CAAA,EACvBnY,GAAS,SAAUmY,CAAY,EAGvB,SAAAC,IAAA,CACJrP,IAAwBN,IAAgB,WAC3CzI,GAAS,SAAA,CACR,KAAM8F,EAAK,IAAKnC,GAAQA,EAAI,IAAKoG,GAASA,EAAK,KAAK,CAAA,EACpD,QAASwK,GAAS,IAAK3B,GAAMA,EAAE,KAAK,EACpC,SAAU,OAENW,IACJvT,GAAS,OAAO,OAEjB+I,GAAuB,IAAA,GAIhB,SAAAsP,GAAoB1X,EAAmBsJ,EAAA,CAC3CtJ,EAAM,kBAAkB,oBAI5BA,EAAM,eAAA,EACNA,EAAM,gBAAA,EAEDd,SAELF,GAAiB,EAAA,OACjBmU,EAAmB,IAAA,OACnBC,EAAqB,IAAA,OACrB3R,EAAW,EAAA,EACX3B,EAAA,GAAAuJ,EAAA,CAAA,CAAA,OACA6J,EAAkB5J,CAAA,OAClB2J,EAAc3J,CAAA,EAEdwC,GAAO,MAAA,iBAIuBsJ,KAiBhBuC,GAAA1T,GAAMsT,GAActT,EAAE,MAAM,SAoBRmT,GAAqBE,GAAO,CAAC,CAAA,SAO7BD,GAAkBC,GAAO,CAAC,CAAA,kCAyDxC1D,GAASvT,CAAC,EAAE,MAAK3B,CAAA,IAAjBkV,GAASvT,CAAC,EAAE,MAAK3B,4EACpByN,EAAIZ,CAAE,EAAE,MAAK7M,CAAA,IAAbyN,EAAIZ,CAAE,EAAE,MAAK7M,WAaR,MAAAkZ,GAAA,CAAAvX,EAAA,CAAA,OAAAwX,CAAa,IAAAlD,GAAYtU,EAAGwX,CAAM,EASrCC,GAAA,CAAAzX,EAAAL,IAAU8W,GAAmB9W,EAAOK,CAAC,QAChCL,IAAK,CACpBA,EAAM,eAAc,EACd,MAAA+X,EAAQ/X,EAAM,QAAQ,CAAC,EACvBgY,EAAU,IAAO,WAAW,SACjC,QAASD,EAAM,QACf,QAASA,EAAM,QACf,QAAS,GACT,WAAY,GACZ,KAAM,SAEPjB,GAAmBkB,EAAY3X,CAAC,GA5CzB4X,GAAA,CAAA5X,EAAAL,IAAU0X,GAAoB1X,EAAOK,CAAC,+CA0DrBgK,GAAMC,CAAC,EAAApK,2BAlGrB6S,GAAM7N,GAAA,QAAA,IAAA,IAAA,GAAA,gEACbvE,GAAKT,gCAqSGgY,EAAA,GAAA,UAAA/S,EAAKR,CAAK,EAAE2F,CAAC,EAAE,MAAK5L,CAAA,IAApByG,EAAKR,CAAK,EAAE2F,CAAC,EAAE,MAAK5L,2DACvByN,EAAIZ,CAAE,EAAE,MAAK7M,CAAA,IAAbyN,EAAIZ,CAAE,EAAE,MAAK7M,yBAQrBoB,EAAA,GAAAd,GAAiB,EAAK,EACtB8M,GAAO,MAAK,cAGN,MAAA9I,EAAM2B,EACN2E,EAAMgB,EAEVjB,EAAe,KAAO,CAAA,CAAAE,GAAGkB,EAAC,IAAMlB,KAAMvG,GAAOyH,KAAMnB,CAAG,QAEvDD,EAAc,CAAA,CAAKrG,EAAKsG,CAAG,CAAA,CAAA,GAUjB6O,GAAA,CAAAxT,EAAA2F,EAAAtK,IAAUsW,GAAiBtW,EAAO2E,EAAO2F,CAAC,+CAnE7C6B,EAAIZ,CAAE,EAAE,KAAIrL,0BACPF,IAAK,CACd,MAAA+X,EAAQ/X,EAAM,QAAQ,CAAC,EACvBgY,GAAU,IAAO,WAAW,SACjC,QAASD,EAAM,QACf,QAASA,EAAM,QACf,QAAS,GACT,WAAY,GACZ,KAAM,SAEP5B,GAAkB6B,GAAYrT,EAAO2F,CAAC,GAM5B8N,GAAA,CAAAzT,EAAA2F,EAAAtK,IAAUmW,GAAkBnW,EAAO2E,EAAO2F,CAAC,kCA1EvCsJ,GAASvT,CAAC,EAAE,MAAK3B,CAAA,IAAjBkV,GAASvT,CAAC,EAAE,MAAK3B,4EACpByN,EAAIZ,CAAE,EAAE,MAAK7M,CAAA,IAAbyN,EAAIZ,CAAE,EAAE,MAAK7M,WAYR,MAAA2Z,GAAA,CAAAhY,EAAA,CAAA,OAAAwX,CAAa,IAAAlD,GAAYtU,EAAGwX,CAAM,EASrCS,GAAA,CAAAjY,EAAAL,IAAU8W,GAAmB9W,EAAOK,CAAC,QAChCL,IAAK,CACpBA,EAAM,eAAc,EACd,MAAA+X,EAAQ/X,EAAM,QAAQ,CAAC,EACvBgY,EAAU,IAAO,WAAW,SACjC,QAASD,EAAM,QACf,QAASA,EAAM,QACf,QAAS,GACT,WAAY,GACZ,KAAM,SAEPjB,GAAmBkB,EAAY3X,CAAC,GA3CzBkY,GAAA,CAAAlY,EAAAL,IAAU0X,GAAoB1X,EAAOK,CAAC,iBA3CxC8E,EAAIzG,yCAEIgX,GAAYhX,0BACJ8W,GAAe9W,sCAvBjC,MAAA8Z,GAAA,CAAA,CAAA,OAAAX,CAAM,IACjB/I,GACC+I,EAAO,KACN1I,IACArP,EAAA,GAAA8T,GAAW/B,GACV1C,EAAK,IAAK8C,GAAMA,GAAK,EAAE,EACvBvL,EACAyF,CAAA,CAAA,EAEMyH,IAEP6E,GAAI,CACJ3Y,EAAA,EAAAwS,EAASmG,CAAI,+CAnKN3M,GAAM5L,sBAMJ+D,GAAMlE,GAAekE,CAAC,SAuWYwQ,KAavBiE,GAAA,IAAAnC,GAAWpD,GAAkB,KAAO,EAAG,OAAO,EAC9CwF,GAAA,IAAApC,GAAWpD,GAAkB,KAAO,EAAG,OAAO,EAC5CyF,GAAA,IAAAlC,GAAWvD,GAAkB,KAAO,EAAG,MAAM,EAC5C0F,GAAA,IAAAnC,GAAWvD,GAAkB,KAAO,EAAG,OAAO,EACpD2F,GAAA,IAAA5B,GAAc/D,GAAkB,KAAO,CAAC,EACxC4F,GAAA,IAAA5B,GAAchE,GAAkB,KAAO,CAAC,EAerC6F,GAAA,IAAAzC,GAAWpD,GAAkB,KAAQ,GAAG,OAAO,EAC/C8F,GAAA,IAAA1C,GAAWpD,GAAkB,KAAQ,GAAG,OAAO,EAC7C+F,GAAA,IAAAxC,GAAWtD,GAAoB,KAAQ,GAAG,MAAM,EAEzE+F,GAAA,IAAAzC,GAAWtD,GAAoB,KAAQ,GAAG,OAAO,EAC7BgG,GAAA,IAAAlC,GAAc/D,GAAkB,OAAS,EACzCkG,GAAA,IAAAlC,GAAc/D,GAAoB,OAAS,6kCApnCzD3C,GAAO6B,EAAQwB,EAAO,IAC7BhU,EAAA,GAAAqF,EAAOsO,GAAanB,CAA+B,CAAA,EACnDxS,EAAA,GAAAgU,GAAU,KAAK,MAAM,KAAK,UAAUxB,CAAM,CAAA,CAAA,gDA5I3CxS,EAAA,GAAGgT,EACFD,GAAkB1N,IAAO,CAAC,GAAG,OAC1B,KAAK,IAAI0N,EAAgB1N,EAAK,CAAC,EAAE,MAAM,EACvC,CAAA,yBAGJrF,EAAA,GAAGuJ,MAAqBA,CAAc,CAAA,yBAEnCvJ,EAAA,GAAA2B,EACF4H,EAAe,OAAS,EACrBA,EAAeA,EAAe,OAAS,CAAC,EACxC,EAAA,8CAsHEoH,GAAO4B,EAASwB,EAAW,SAC/BD,GAAW/B,GAAaQ,EAAS3L,EAAWyF,CAAG,CAAA,EAC/CrM,EAAA,GAAA+T,GAAc,KAAK,MAAM,KAAK,UAAUxB,CAAO,CAAA,CAAA,8BAqS7ClN,GAAQyO,KAAaK,8BAuCtBnU,EAAA,GAAAyN,EAAMD,GAAQnI,CAAI,CAAA,4BAElBkF,GAAM,CAAC,GAAK+K,8BAuDZzI,GAAUxH,EAAMxG,GAAeC,GAASgO,GAASC,EAAc,0BAE/D/M,EAAA,GAAAwZ,EAAA,CAAA,CAAmB7X,GAAYA,EAAS,CAAC,CAAA,0BA8NnC,GAAA,OAAAmL,IAAY,UACnBC,IACAD,IAAW,GACXA,GAAUzH,EAAK,CAAC,EAAE,OAAA,CAEZ,MAAAsI,EAAA,CAAA,GAAc,MAAMtI,EAAK,MAAM,GAAG,IAAK,CAAAuF,EAAGrK,IAAMA,CAAC,EACjDkZ,EAAa3M,GACnBa,EAAQ,MAAMlK,EAAGC,IAAA,CACV,MAAAyJ,GAAQ9H,EAAK5B,CAAC,EACd2J,GAAQ/H,EAAK3B,CAAC,EAElB,GAAA,CAAAyJ,IAAA,CACAC,IACDqM,GAActM,GAAM,QACpBsM,GAAcrM,GAAM,OAEb,MAAA,SACFC,GAAQF,GAAMsM,CAAU,EAAE,MAC1BnM,GAAQF,GAAMqM,CAAU,EAAE,MAC1BC,GAAOrM,GAAQC,GAAA,GAAaD,GAAQC,GAAQ,EAAI,EAC/C,OAAAP,KAAmB,MAAQ2M,GAAQ,CAAAA,SAI3C,CAAA,GAAgB,MAAMrU,EAAK,MAAM,GAAG,IAAK,CAAAuF,EAAGrK,IAAMA,CAAC,6BAiB9CoB,IAAa,SAAO6V,GAAS7V,CAAA,2CAE7BA,IAAa,GAAA,CACb,MAAAgY,EAAYvN,GACjBzK,EACA0D,EACAgH,EACAL,GACAnL,EAAA,EAED,SAAS,gBAAgB,MAAM,YAC9B,qBACA8Y,EAAU,OAAA,EAEPA,EAAU,SACb,SAAS,gBAAgB,MAAM,YAC9B,qBACAA,EAAU,OAAA,4lGClxBA,WAAA5b,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,8MASVA,EAAoB,EAAA,GAAIA,EAAK,CAAA,EAAC,+CAG7BA,EAAQ,EAAA,2DAYPA,EAAW,EAAA,mBAEf,KAAAA,MAAO,yhBA5BD,WAAAA,MAAO,YACbG,EAAA,CAAA,EAAA,OAAA,CAAA,KAAAH,MAAO,IAAI,kBACbA,EAAc,EAAA,CAAA,8LASVA,EAAoB,EAAA,GAAIA,EAAK,CAAA,EAAC,4GAG7BA,EAAQ,EAAA,2HAYPA,EAAW,EAAA,qCAEfG,EAAA,CAAA,EAAA,QAAA0b,EAAA,KAAA7b,MAAO,2oBArCL,4CAGE,keArEA,CAAA,QAAAwU,EAAA,EAAA,EAAA5T,GACA,QAAAkb,EAAU,EAAA,EAAAlb,EACV,CAAA,aAAAmb,EAAA,EAAA,EAAAnb,GACA,QAAA6D,EAAU,EAAA,EAAA7D,EACV,CAAA,MAAAC,EAAA,CACV,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,EACvB,SAAU,UAEA,gBAAAkU,EAAkB,EAAA,EAAAnU,EAClB,CAAA,UAAAiI,CAAA,EAAAjI,EACA,CAAA,UAAAkI,CAAA,EAAAlI,GACA,MAAA0T,EAAuB,IAAA,EAAA1T,GACvB,WAAA2T,EAAa,EAAA,EAAA3T,EACb,CAAA,KAAA8T,CAAA,EAAA9T,EACA,CAAA,SAAAK,CAAA,EAAAL,GACA,MAAAob,EAAuB,IAAA,EAAApb,GACvB,UAAAqb,EAAgC,MAAA,EAAArb,EAChC,CAAA,KAAAU,CAAA,EAAAV,GAEA,YAAAQ,EAAc,EAAA,EAAAR,EACd,CAAA,cAAA+T,EAAA,EAAA,EAAA/T,EACA,CAAA,OAAAsb,EAAA,EAAAtb,EAMA,CAAA,iBAAAM,EAAA,EAAAN,GAKA,WAAA2C,EAAiC,MAAA,EAAA3C,EACjC,CAAA,eAAAub,CAAA,EAAAvb,EACA,CAAA,YAAAwb,CAAA,EAAAxb,GACA,uBAAAmJ,EAAyB,EAAA,EAAAnJ,GACzB,UAAAW,EAAgC,MAAA,EAAAX,GAChC,iBAAAoJ,EAAmB,EAAA,EAAApJ,GACnB,iBAAAgU,GAAmB,EAAA,EAAAhU,GACnB,YAAAqJ,GAA4C,MAAA,EAAArJ,EAEnD+Y,GAA8B,MAUvB,eAAA3E,EAAiB,CAAA,EAAApU,EA0BJ,MAAAyb,GAAA,IAAAH,GAAO,SAAS,eAAgBC,CAAc,EA4BzDhK,EAAA,IAAAmK,IAASJ,GAAO,OAAO,UAAUI,CAAI,EAC7BvJ,GAAA,IAAAuJ,IAASJ,GAAO,OAAO,UAAUI,CAAI,mCAjB7ClW,GAAC,CACZnE,EAAA,EAAApB,EAAM,KAAOuF,EAAE,OAAO,KAAIvF,CAAA,EAC1BoB,EAAA,EAAApB,EAAM,QAAUuF,EAAE,OAAO,QAAOvF,CAAA,EAChCqb,GAAO,SAAS,QAAQ,GAEdK,EAAAnW,GAAM8V,GAAO,SAAS,OAAO,IAC5B9V,GAAM8V,GAAO,SAAS,SAAU9V,EAAE,MAAM,EACxC0T,EAAA1T,GAAOnE,EAAA,GAAA0X,GAAevT,EAAE,MAAM,woCAtD3CnE,EAAA,GAAGua,EAAuB7C,GACvB9Y,EAAM,MAAM,OAAQsE,GACpBA,EAAI,KACFoG,IACAoO,IACA,OAAOpO,EAAI,EAAE,cAAc,SAASoO,GAAa,YAAA,CAAA,CAAA,CAAA,EAGnD,IAAA,mCAGA1X,EAAA,GAAA8T,EAAA,CAAA,GAAgBlV,EAAM,SAAW2T,CAAQ,CAAA,mBACzCvS,EAAA,GAAAnB,EAAgBD,GAAO,UAAU,cAC7B,CAAA,GAAAA,GAAO,UAAU,aAAa,EAClC,IAAA,yBACAoB,EAAA,GAAAlB,EAAA,CACDqb,GAAevb,GAAO,UAAU,QAC1B,CAAA,GAAAA,GAAO,UAAU,OAAO,EAC5B,IAAA"}