export declare const ordered_colors: readonly ["red", "green", "blue", "yellow", "purple", "teal", "orange", "cyan", "lime", "pink"];
interface ColorPair {
    primary: string;
    secondary: string;
}
interface Colors {
    red: ColorPair;
    green: ColorPair;
    blue: ColorPair;
    yellow: ColorPair;
    purple: ColorPair;
    teal: ColorPair;
    orange: ColorPair;
    cyan: ColorPair;
    lime: ColorPair;
    pink: ColorPair;
}
export declare const color_values: readonly [{
    readonly color: "red";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "green";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "blue";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "yellow";
    readonly primary: 500;
    readonly secondary: 100;
}, {
    readonly color: "purple";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "teal";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "orange";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "cyan";
    readonly primary: 600;
    readonly secondary: 100;
}, {
    readonly color: "lime";
    readonly primary: 500;
    readonly secondary: 100;
}, {
    readonly color: "pink";
    readonly primary: 600;
    readonly secondary: 100;
}];
export declare const colors: Colors;
export {};
