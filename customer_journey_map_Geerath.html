<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Geerath
        </h5>
        <p>
         Student
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigate to Amazon.com, search for 'wireless headphones under $100', filter by customer ratings (4+ stars), open a product details, read reviews
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Locate and evaluate wireless headphones under $100 with a customer rating of 4+ stars."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Successfully navigate through Amazon.com to select high-rated wireless headphones and review customer feedback."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Navigating to Website
     </div>
     <div class="stage" style="background-color: #F55050">
      Search
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Filter Application
     </div>
     <div class="stage" style="background-color: #F7C934">
      Product Detail View
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Customer Review Exploration
     </div>
     <div class="stage" style="background-color: #5E2590">
      Task Completion
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Navigate to Amazon.com by inputting the URL.
     </div>
     <div class="stage" style="background-color: #F55050">
      Search for 'wireless headphones under $100' using the site's search functionality.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Apply filter for 4+ star customer ratings.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Open the product details for the first item displayed.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Read the customer reviews for the selected product.
     </div>
     <div class="stage" style="background-color: #5E2590">
      End the task after evaluating reviews and achieving the goal.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Anticipation
     </div>
     <div class="stage" style="background-color: #F55050">
      Focused and hopeful
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Confident and narrowing choices
     </div>
     <div class="stage" style="background-color: #F7C934">
      Curiosity
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Engaged and evaluative
     </div>
     <div class="stage" style="background-color: #5E2590">
      Satisfied and accomplished
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Potential delays in loading the website or incorrect URL input.
     </div>
     <div class="stage" style="background-color: #F55050">
      Unclear or irrelevant search results displayed depending on search algorithm accuracy.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Difficulty locating or interacting with filter options.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Slow load times or incomplete product information provided.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Potentially overwhelming amount of reviews, filtering relevant insights from customer feedback.
     </div>
     <div class="stage" style="background-color: #5E2590">
      No pain points noted at this stage.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
