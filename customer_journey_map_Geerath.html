<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Geerath
        </h5>
        <p>
         Student
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigate to Amazon.com, search for 'wireless headphones under $100', filter by customer ratings (4+ stars), open a product detail, read reviews.
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Extract customer reviews and key insights for wireless headphones under $100 filtered by 4+ stars."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Complete the navigation and filtering process without obstacles and gain access to sufficient customer review insights for decision-making."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Awareness
     </div>
     <div class="stage" style="background-color: #F55050">
      Consideration
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Evaluation
     </div>
     <div class="stage" style="background-color: #F7C934">
      Decision-making
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Detailed Review Analysis
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Initiated navigation to Amazon.com to search for wireless headphones under $100.
     </div>
     <div class="stage" style="background-color: #F55050">
      Successfully reached Amazon.com homepage and entered the search query.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Filtered search results by customer ratings of 4+ stars.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Opened a product detail page to evaluate features and reviews.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Scrolled to, accessed, and extracted insights from the customer review section.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral
     </div>
     <div class="stage" style="background-color: #F55050">
      Neutral to mild curiosity.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Confident and focused.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Slight anticipation and interest.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Satisfaction upon successful completion.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F55050">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Locating the customer review section required multiple scroll attempts.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Visibility of customer reviews required excessive scrolling and effort.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
