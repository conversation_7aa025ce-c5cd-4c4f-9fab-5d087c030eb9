import{a as h,i as u,s as d,E as a,z as t,d as f,C as o,D as i,l as m}from"../lite.js";function g(l){let e,c,s,r;return{c(){e=a("svg"),c=a("path"),s=a("circle"),r=a("circle"),t(c,"d","M9 18V5l12-2v13"),t(s,"cx","6"),t(s,"cy","18"),t(s,"r","3"),t(r,"cx","18"),t(r,"cy","16"),t(r,"r","3"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-music")},m(n,p){f(n,e,p),o(e,c),o(e,s),o(e,r)},p:i,i,o:i,d(n){n&&m(e)}}}class w extends h{constructor(e){super(),u(this,e,null,g,d,{})}}export{w as M};
//# sourceMappingURL=Music-BCIGqdvV.js.map
