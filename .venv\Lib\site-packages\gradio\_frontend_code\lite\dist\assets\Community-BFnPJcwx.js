import{a as r,i as p,s as c,E as o,z as a,d as m,C as d,D as n,l as h}from"../lite.js";function u(i){let t,e;return{c(){t=o("svg"),e=o("path"),a(e,"d","M23,20a5,5,0,0,0-3.89,1.89L11.8,17.32a4.46,4.46,0,0,0,0-2.64l7.31-4.57A5,5,0,1,0,18,7a4.79,4.79,0,0,0,.2,1.32l-7.31,4.57a5,5,0,1,0,0,6.22l7.31,4.57A4.79,4.79,0,0,0,18,25a5,5,0,1,0,5-5ZM23,4a3,3,0,1,1-3,3A3,3,0,0,1,23,4ZM7,19a3,3,0,1,1,3-3A3,3,0,0,1,7,19Zm16,9a3,3,0,1,1,3-3A3,3,0,0,1,23,28Z"),a(e,"fill","currentColor"),a(t,"id","icon"),a(t,"xmlns","http://www.w3.org/2000/svg"),a(t,"viewBox","0 0 32 32"),a(t,"width","100%"),a(t,"height","100%")},m(s,l){m(s,t,l),d(t,e)},p:n,i:n,o:n,d(s){s&&h(t)}}}class f extends r{constructor(t){super(),p(this,t,null,u,c,{})}}export{f as C};
//# sourceMappingURL=Community-BFnPJcwx.js.map
