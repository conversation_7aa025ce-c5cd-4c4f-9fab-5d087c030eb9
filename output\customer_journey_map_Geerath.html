<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="../images/boy.png"/>
        <h5 class="mt-3">
         Geerath
        </h5>
        <p>
         Student
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Opening Google in a browser
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Open Google successfully"/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Navigate to the Google homepage by accessing its URL without encountering errors"/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Awareness
     </div>
     <div class="stage" style="background-color: #F55050">
      Consideration
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Decision
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Starting from a blank page and realizing the need to open Google
     </div>
     <div class="stage" style="background-color: #F55050">
      Deciding on the next action, which is navigating to Google's URL
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Successfully navigating to Google's URL to access the homepage
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Confused (current page is blank and intent unclear)
     </div>
     <div class="stage" style="background-color: #F55050">
      Resolved (clear next goal to open Google)
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Satisfied (successfully achieved the goal)
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Lack of clarity due to starting at a blank page
     </div>
     <div class="stage" style="background-color: #F55050">
      None reported in this step
     </div>
     <div class="stage" style="background-color: #F78D1E">
      None reported in this step
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
