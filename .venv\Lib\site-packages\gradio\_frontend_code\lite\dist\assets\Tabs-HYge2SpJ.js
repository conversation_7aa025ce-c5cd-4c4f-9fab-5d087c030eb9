import{a as de,i as be,s as he,E as K,z as c,d as N,C as p,D as W,l as O,f as R,q as Ie,y as q,b as A,A as I,$ as te,M as F,k as V,h as Te,t as L,j as je,u as qe,r as Ee,v as Ne,V as Oe,b2 as le,ao as ie,o as Ae,a5 as De,b3 as Me,b4 as S,aq as D,c as Re,m as Se,ay as Ve,as as X,n as Fe,e as me,w as Z,x as y,O as Y,N as Ge}from"../lite.js";function He(t){let e,l,i,o;return{c(){e=K("svg"),l=K("circle"),i=K("circle"),o=K("circle"),c(l,"cx","2.5"),c(l,"cy","8"),c(l,"r","1.5"),c(l,"fill","currentColor"),c(i,"cx","8"),c(i,"cy","8"),c(i,"r","1.5"),c(i,"fill","currentColor"),c(o,"cx","13.5"),c(o,"cy","8"),c(o,"r","1.5"),c(o,"fill","currentColor"),c(e,"width","16"),c(e,"height","16"),c(e,"viewBox","0 0 16 16"),c(e,"fill","none"),c(e,"xmlns","http://www.w3.org/2000/svg")},m(u,_){N(u,e,_),p(e,l),p(e,i),p(e,o)},p:W,i:W,o:W,d(u){u&&O(e)}}}class Je extends de{constructor(e){super(),be(this,e,null,He,he,{})}}function se(t,e,l){const i=t.slice();return i[33]=e[l],i}function ne(t,e,l){const i=t.slice();return i[33]=e[l],i[37]=l,i}function ae(t,e,l){const i=t.slice();return i[33]=e[l],i[38]=e,i[37]=l,i}function oe(t){let e,l,i,o,u,_,f,b,d,r,g,z,E,h=D(t[3]),m=[];for(let a=0;a<h.length;a+=1)m[a]=_e(ae(t,h,a));let T=D(t[7]),v=[];for(let a=0;a<T.length;a+=1)v[a]=re(ne(t,T,a));b=new Je({});let B=D(t[8]),w=[];for(let a=0;a<B.length;a+=1)w[a]=ue(se(t,B,a));return{c(){e=q("div"),l=q("div");for(let a=0;a<m.length;a+=1)m[a].c();i=A(),o=q("div");for(let a=0;a<v.length;a+=1)v[a].c();u=A(),_=q("span"),f=q("button"),Re(b.$$.fragment),d=A(),r=q("div");for(let a=0;a<w.length;a+=1)w[a].c();c(l,"class","tab-container visually-hidden svelte-1tcem6n"),c(l,"aria-hidden","true"),c(o,"class","tab-container svelte-1tcem6n"),c(o,"role","tablist"),c(f,"class","svelte-1tcem6n"),I(f,"overflow-item-selected",t[12]),c(r,"class","overflow-dropdown svelte-1tcem6n"),I(r,"hide",!t[9]),c(_,"class","overflow-menu svelte-1tcem6n"),I(_,"hide",!t[11]),c(e,"class","tab-wrapper svelte-1tcem6n")},m(a,C){N(a,e,C),p(e,l);for(let s=0;s<m.length;s+=1)m[s]&&m[s].m(l,null);p(e,i),p(e,o);for(let s=0;s<v.length;s+=1)v[s]&&v[s].m(o,null);t[28](o),p(e,u),p(e,_),p(_,f),Se(b,f,null),p(_,d),p(_,r);for(let s=0;s<w.length;s+=1)w[s]&&w[s].m(r,null);t[31](_),g=!0,z||(E=F(f,"click",Ve(t[29])),z=!0)},p(a,C){if(C[0]&40){h=D(a[3]);let s;for(s=0;s<h.length;s+=1){const j=ae(a,h,s);m[s]?m[s].p(j,C):(m[s]=_e(j),m[s].c(),m[s].m(l,null))}for(;s<m.length;s+=1)m[s].d(1);m.length=h.length}if(C[0]&393408){T=D(a[7]);let s;for(s=0;s<T.length;s+=1){const j=ne(a,T,s);v[s]?v[s].p(j,C):(v[s]=re(j),v[s].c(),v[s].m(o,null))}for(;s<v.length;s+=1)v[s].d(1);v.length=T.length}if((!g||C[0]&4096)&&I(f,"overflow-item-selected",a[12]),C[0]&262464){B=D(a[8]);let s;for(s=0;s<B.length;s+=1){const j=se(a,B,s);w[s]?w[s].p(j,C):(w[s]=ue(j),w[s].c(),w[s].m(r,null))}for(;s<w.length;s+=1)w[s].d(1);w.length=B.length}(!g||C[0]&512)&&I(r,"hide",!a[9]),(!g||C[0]&2048)&&I(_,"hide",!a[11])},i(a){g||(V(b.$$.fragment,a),g=!0)},o(a){L(b.$$.fragment,a),g=!1},d(a){a&&O(e),X(m,a),X(v,a),t[28](null),Fe(b),X(w,a),t[31](null),z=!1,E()}}}function ce(t){let e,l=t[33]?.label+"",i,o,u=t[33];const _=()=>t[26](e,u),f=()=>t[26](null,u);return{c(){e=q("button"),i=Z(l),o=A(),c(e,"class","svelte-1tcem6n")},m(b,d){N(b,e,d),p(e,i),p(e,o),_()},p(b,d){t=b,d[0]&8&&l!==(l=t[33]?.label+"")&&y(i,l),u!==t[33]&&(f(),u=t[33],_())},d(b){b&&O(e),f()}}}function _e(t){let e,l=t[33]?.visible&&ce(t);return{c(){l&&l.c(),e=me()},m(i,o){l&&l.m(i,o),N(i,e,o)},p(i,o){i[33]?.visible?l?l.p(i,o):(l=ce(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&O(e),l&&l.d(i)}}}function fe(t){let e,l=t[33].label+"",i,o,u,_,f,b,d,r,g,z;function E(){return t[27](t[33],t[37])}return{c(){e=q("button"),i=Z(l),o=A(),c(e,"role","tab"),c(e,"aria-selected",u=t[33].id===t[6]),c(e,"aria-controls",_=t[33].elem_id),e.disabled=f=!t[33].interactive,c(e,"aria-disabled",b=!t[33].interactive),c(e,"id",d=t[33].elem_id?t[33].elem_id+"-button":null),c(e,"data-tab-id",r=t[33].id),c(e,"class","svelte-1tcem6n"),I(e,"selected",t[33].id===t[6])},m(h,m){N(h,e,m),p(e,i),p(e,o),g||(z=F(e,"click",E),g=!0)},p(h,m){t=h,m[0]&128&&l!==(l=t[33].label+"")&&y(i,l),m[0]&192&&u!==(u=t[33].id===t[6])&&c(e,"aria-selected",u),m[0]&128&&_!==(_=t[33].elem_id)&&c(e,"aria-controls",_),m[0]&128&&f!==(f=!t[33].interactive)&&(e.disabled=f),m[0]&128&&b!==(b=!t[33].interactive)&&c(e,"aria-disabled",b),m[0]&128&&d!==(d=t[33].elem_id?t[33].elem_id+"-button":null)&&c(e,"id",d),m[0]&128&&r!==(r=t[33].id)&&c(e,"data-tab-id",r),m[0]&192&&I(e,"selected",t[33].id===t[6])},d(h){h&&O(e),g=!1,z()}}}function re(t){let e,l=t[33]?.visible&&fe(t);return{c(){l&&l.c(),e=me()},m(i,o){l&&l.m(i,o),N(i,e,o)},p(i,o){i[33]?.visible?l?l.p(i,o):(l=fe(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&O(e),l&&l.d(i)}}}function ue(t){let e,l=t[33]?.label+"",i,o,u,_;function f(){return t[30](t[33])}return{c(){e=q("button"),i=Z(l),o=A(),c(e,"class","svelte-1tcem6n"),I(e,"selected",t[33]?.id===t[6])},m(b,d){N(b,e,d),p(e,i),p(e,o),u||(_=F(e,"click",f),u=!0)},p(b,d){t=b,d[0]&256&&l!==(l=t[33]?.label+"")&&y(i,l),d[0]&320&&I(e,"selected",t[33]?.id===t[6])},d(b){b&&O(e),u=!1,_()}}}function Ke(t){let e,l,i,o,u,_,f=t[14]&&oe(t);const b=t[25].default,d=Ie(b,t,t[24],null);return{c(){e=q("div"),f&&f.c(),l=A(),d&&d.c(),c(e,"class",i="tabs "+t[2].join(" ")+" svelte-1tcem6n"),c(e,"id",t[1]),I(e,"hide",!t[0]),te(e,"flex-grow",t[13])},m(r,g){N(r,e,g),f&&f.m(e,null),p(e,l),d&&d.m(e,null),o=!0,u||(_=[F(window,"resize",t[20]),F(window,"click",t[19])],u=!0)},p(r,g){r[14]?f?(f.p(r,g),g[0]&16384&&V(f,1)):(f=oe(r),f.c(),V(f,1),f.m(e,l)):f&&(Te(),L(f,1,1,()=>{f=null}),je()),d&&d.p&&(!o||g[0]&16777216)&&qe(d,b,r,r[24],o?Ne(b,r[24],g,null):Ee(r[24]),null),(!o||g[0]&4&&i!==(i="tabs "+r[2].join(" ")+" svelte-1tcem6n"))&&c(e,"class",i),(!o||g[0]&2)&&c(e,"id",r[1]),(!o||g[0]&5)&&I(e,"hide",!r[0]),g[0]&8192&&te(e,"flex-grow",r[13])},i(r){o||(V(f),V(d,r),o=!0)},o(r){L(f),L(d,r),o=!1},d(r){r&&O(e),f&&f.d(),d&&d.d(r),u=!1,Oe(_)}}}const Le={};function Pe(t,e){const l={};return t.forEach(i=>{i&&(l[i.id]=e[i.id]?.getBoundingClientRect())}),l}function Qe(t,e,l){let i,o,u,_,{$$slots:f={},$$scope:b}=e,{visible:d=!0}=e,{elem_id:r=""}=e,{elem_classes:g=[]}=e,{selected:z}=e,{initial_tabs:E}=e,h=[...E],m=[...E],T=[],v=!1,B,w;const a=le(z||h[0]?.id||!1);ie(t,a,n=>l(6,_=n));const C=le(h.findIndex(n=>n?.id===z)||0);ie(t,C,n=>l(23,u=n));const s=Ae();let j=!1,P=!1,G={};De(()=>{new IntersectionObserver(k=>{Q()}).observe(w)}),Me(Le,{register_tab:(n,k)=>(l(3,h[k]=n,h),_===!1&&n.visible&&n.interactive&&(S(a,_=n.id,_),S(C,u=k,u)),k),unregister_tab:(n,k)=>{_===n.id&&S(a,_=h[0]?.id||!1,_),l(3,h[k]=null,h)},selected_tab:a,selected_tab_index:C});function H(n){const k=h.find(M=>M?.id===n);n!==void 0&&k&&k.interactive&&k.visible&&_!==k.id&&(l(21,z=n),S(a,_=n,_),S(C,u=h.findIndex(M=>M?.id===n),u),s("change"),l(9,v=!1))}function ge(n){v&&B&&!B.contains(n.target)&&l(9,v=!1)}async function Q(){if(!w)return;await Ge();const n=w.getBoundingClientRect();let k=n.width;const M=Pe(h,G);let U=0;const Be=n.left;for(let J=h.length-1;J>=0;J--){const x=h[J];if(!x)continue;const ee=M[x.id];if(ee&&ee.right-Be<k){U=J;break}}l(8,T=h.slice(U+1)),l(7,m=h.slice(0,U+1)),l(12,P=$(_)),l(11,j=T.length>0)}function $(n){return n===!1?!1:T.some(k=>k?.id===n)}function ve(n,k){Y[n?"unshift":"push"](()=>{G[k.id]=n,l(5,G)})}const we=(n,k)=>{n.id!==_&&(H(n.id),s("select",{value:n.label,index:k}))};function ke(n){Y[n?"unshift":"push"](()=>{w=n,l(4,w)})}const pe=()=>l(9,v=!v),Ce=n=>H(n?.id);function ze(n){Y[n?"unshift":"push"](()=>{B=n,l(10,B)})}return t.$$set=n=>{"visible"in n&&l(0,d=n.visible),"elem_id"in n&&l(1,r=n.elem_id),"elem_classes"in n&&l(2,g=n.elem_classes),"selected"in n&&l(21,z=n.selected),"initial_tabs"in n&&l(22,E=n.initial_tabs),"$$scope"in n&&l(24,b=n.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&8&&l(14,i=h.length>0),t.$$.dirty[0]&2097160&&z!==null&&H(z),t.$$.dirty[0]&56&&Q(),t.$$.dirty[0]&64&&l(12,P=$(_)),t.$$.dirty[0]&8388616&&l(13,o=h[u>=0?u:0]?.scale)},[d,r,g,h,w,G,_,m,T,v,B,j,P,o,i,a,C,s,H,ge,Q,z,E,u,b,f,ve,we,ke,pe,Ce,ze]}class Ue extends de{constructor(e){super(),be(this,e,Qe,Ke,he,{visible:0,elem_id:1,elem_classes:2,selected:21,initial_tabs:22},null,[-1,-1])}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),R()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),R()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),R()}get selected(){return this.$$.ctx[21]}set selected(e){this.$$set({selected:e}),R()}get initial_tabs(){return this.$$.ctx[22]}set initial_tabs(e){this.$$set({initial_tabs:e}),R()}}const Xe=Ue;export{Xe as T,Le as a};
//# sourceMappingURL=Tabs-HYge2SpJ.js.map
