<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Shubhrit
        </h5>
        <p>
         Frontend Engineer
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigate to Amazon.com, search for wireless headphones under $100, filter by customer ratings (4+ stars), open a product details page, and read reviews.
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Explore and evaluate wireless headphones under $100 with high customer ratings (4+ stars)."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Quick navigation, accurate results, seamless filtering and review access on Amazon."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Navigation to Homepage
     </div>
     <div class="stage" style="background-color: #F55050">
      Searching for Product
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Autocomplete Selection
     </div>
     <div class="stage" style="background-color: #F7C934">
      Filter Application
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Product Details Exploration
     </div>
     <div class="stage" style="background-color: #5E2590">
      Review Reading
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Navigated to Amazon.com homepage.
     </div>
     <div class="stage" style="background-color: #F55050">
      Entered 'wireless headphones under $100' into the search field.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Selected the autocomplete option 'wireless headphones under $100'.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Applied 4 Stars &amp; Up filter to refine the search results.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Opened the product page for BERIBES Bluetooth Headphones.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Opened review section and read customer reviews for the product.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral to mild curiosity.
     </div>
     <div class="stage" style="background-color: #F55050">
      Focused and purposeful.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Engaged and optimistic.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Pleased with the ability to filter results effectively.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Curious and interested in product features.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Satisfaction with the availability and visibility of detailed reviews.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Initial confusion before loading the page (empty state observed).
     </div>
     <div class="stage" style="background-color: #F55050">
      No significant pain points during search initiation.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      None; autocomplete options helped speed up the search.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Potential to be overwhelmed by the number of filters available (not explicitly stated but inferred).
     </div>
     <div class="stage" style="background-color: #8BC34A">
      None; seamless transition to the product page.
     </div>
     <div class="stage" style="background-color: #5E2590">
      None; the task was completed successfully.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
