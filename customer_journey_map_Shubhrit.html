<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Shubhrit
        </h5>
        <p>
         Frontend Engineer
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigate to Amazon.com, search for 'wireless headphones under $100', filter by customer ratings (4+ stars), open a product details page, and read reviews.
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Find and extract product reviews for 'wireless headphones under $100' with customer ratings of 4+ stars."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Navigate seamlessly, apply filters efficiently, access product details, and view reviews quickly without hurdles."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Starting Journey
     </div>
     <div class="stage" style="background-color: #F55050">
      Initial Navigation
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Search for Products
     </div>
     <div class="stage" style="background-color: #F7C934">
      Apply Filter
     </div>
     <div class="stage" style="background-color: #8BC34A">
      View Product Details
     </div>
     <div class="stage" style="background-color: #5E2590">
      Retry and Success
     </div>
     <div class="stage" style="background-color: #F55050">
      Read Product Reviews
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Attempted to navigate to Amazon.com.
     </div>
     <div class="stage" style="background-color: #F55050">
      Successfully navigated to Amazon.com.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Entered the search query 'wireless headphones under $100'.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Applied the filter for customer ratings (4+ stars).
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Attempted to open product details but encountered an error in navigation.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Successfully navigated to product details upon retry.
     </div>
     <div class="stage" style="background-color: #F55050">
      Extracted and read product reviews.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral, possibly curious.
     </div>
     <div class="stage" style="background-color: #F55050">
      Neutral, confidence begins to build.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Focused, intent-driven.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Satisfaction with progress, optimistic.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Frustration due to failure.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Relief and satisfaction after overcoming the issue.
     </div>
     <div class="stage" style="background-color: #F55050">
      Accomplished, confidence in decision-making.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F55050">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F7C934">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Navigation error when attempting to open product details; had to retry.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Initial failure resulted in wasted time and effort.
     </div>
     <div class="stage" style="background-color: #F55050">
      None at this stage; task completed successfully.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
