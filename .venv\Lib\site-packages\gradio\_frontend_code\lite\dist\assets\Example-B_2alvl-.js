import{a as T,i as V,s as j,f as m,e as B,d as u,D as v,l as o,y as d,z as g,A as r,M as y,V as F,aq as b,b as w,C as k,as as z,w as D,x as H}from"../lite.js";function p(c,e,t){const l=c.slice();return l[9]=e[t],l[11]=t,l}function A(c,e,t){const l=c.slice();return l[12]=e[t],l[14]=t,l}function G(c){let e,t,l;function s(a,i){return typeof a[0]=="string"?K:a[6]?J:I}let f=s(c),n=f(c);return{c(){e=d("div"),n.c(),g(e,"class","svelte-hn96gn"),r(e,"table",c[1]==="table"),r(e,"gallery",c[1]==="gallery"),r(e,"selected",c[2])},m(a,i){u(a,e,i),n.m(e,null),t||(l=[y(e,"mouseenter",c[7]),y(e,"mouseleave",c[8])],t=!0)},p(a,i){f===(f=s(a))&&n?n.p(a,i):(n.d(1),n=f(a),n&&(n.c(),n.m(e,null))),i&2&&r(e,"table",a[1]==="table"),i&2&&r(e,"gallery",a[1]==="gallery"),i&4&&r(e,"selected",a[2])},d(a){a&&o(e),n.d(),t=!1,F(l)}}}function I(c){let e,t,l=b(c[0].slice(0,3)),s=[];for(let n=0;n<l.length;n+=1)s[n]=E(p(c,l,n));let f=c[0].length>3&&M(c);return{c(){e=d("table");for(let n=0;n<s.length;n+=1)s[n].c();t=w(),f&&f.c(),g(e,"class"," svelte-hn96gn")},m(n,a){u(n,e,a);for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(e,null);k(e,t),f&&f.m(e,null)},p(n,a){if(a&1){l=b(n[0].slice(0,3));let i;for(i=0;i<l.length;i+=1){const h=p(n,l,i);s[i]?s[i].p(h,a):(s[i]=E(h),s[i].c(),s[i].m(e,t))}for(;i<s.length;i+=1)s[i].d(1);s.length=l.length}n[0].length>3?f?f.p(n,a):(f=M(n),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&o(e),z(s,n),f&&f.d()}}}function J(c){let e;return{c(){e=d("table"),e.innerHTML='<tr><td class="svelte-hn96gn">Empty</td></tr>',g(e,"class"," svelte-hn96gn")},m(t,l){u(t,e,l)},p:v,d(t){t&&o(e)}}}function K(c){let e;return{c(){e=D(c[0])},m(t,l){u(t,e,l)},p(t,l){l&1&&H(e,t[0])},d(t){t&&o(e)}}}function C(c){let e,t=c[12]+"",l;return{c(){e=d("td"),l=D(t),g(e,"class","svelte-hn96gn")},m(s,f){u(s,e,f),k(e,l)},p(s,f){f&1&&t!==(t=s[12]+"")&&H(l,t)},d(s){s&&o(e)}}}function q(c){let e;return{c(){e=d("td"),e.textContent="…",g(e,"class","svelte-hn96gn")},m(t,l){u(t,e,l)},d(t){t&&o(e)}}}function E(c){let e,t,l=b(c[9].slice(0,3)),s=[];for(let n=0;n<l.length;n+=1)s[n]=C(A(c,l,n));let f=c[9].length>3&&q();return{c(){e=d("tr");for(let n=0;n<s.length;n+=1)s[n].c();t=w(),f&&f.c()},m(n,a){u(n,e,a);for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(e,null);k(e,t),f&&f.m(e,null)},p(n,a){if(a&1){l=b(n[9].slice(0,3));let i;for(i=0;i<l.length;i+=1){const h=A(n,l,i);s[i]?s[i].p(h,a):(s[i]=C(h),s[i].c(),s[i].m(e,t))}for(;i<s.length;i+=1)s[i].d(1);s.length=l.length}n[9].length>3?f||(f=q(),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&o(e),z(s,n),f&&f.d()}}}function M(c){let e;return{c(){e=d("div"),g(e,"class","overlay svelte-hn96gn"),r(e,"odd",c[3]%2!=0),r(e,"even",c[3]%2==0),r(e,"button",c[1]==="gallery")},m(t,l){u(t,e,l)},p(t,l){l&8&&r(e,"odd",t[3]%2!=0),l&8&&r(e,"even",t[3]%2==0),l&2&&r(e,"button",t[1]==="gallery")},d(t){t&&o(e)}}}function N(c){let e,t=c[5]&&G(c);return{c(){t&&t.c(),e=B()},m(l,s){t&&t.m(l,s),u(l,e,s)},p(l,[s]){l[5]&&t.p(l,s)},i:v,o:v,d(l){l&&o(e),t&&t.d(l)}}}function O(c,e,t){let{value:l}=e,{type:s}=e,{selected:f=!1}=e,{index:n}=e,a=!1,i=Array.isArray(l),h=i&&(l.length===0||l[0].length===0);const L=()=>t(4,a=!0),S=()=>t(4,a=!1);return c.$$set=_=>{"value"in _&&t(0,l=_.value),"type"in _&&t(1,s=_.type),"selected"in _&&t(2,f=_.selected),"index"in _&&t(3,n=_.index)},[l,s,f,n,a,i,h,L,S]}class Q extends T{constructor(e){super(),V(this,e,O,N,j,{value:0,type:1,selected:2,index:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),m()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),m()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),m()}}export{Q as default};
//# sourceMappingURL=Example-B_2alvl-.js.map
