import{a as Q,i as R,s as T,f as b,e as U,d as w,h as v,t as g,j as z,k as h,l as q,p as V,q as N,y as S,b as D,z as d,A as k,$ as c,C as E,M as W,u as F,r as G,v as H,c as J,m as K,n as L}from"../lite.js";import{I as O}from"./Image-BPQ6A_U-.js";/* empty css                                                   */function X(i){let e,n,t,s,f,_,u=i[7]&&A(i);const l=i[12].default,a=N(l,i,i[11],null);return{c(){e=S("button"),u&&u.c(),n=D(),a&&a.c(),d(e,"class",t=i[4]+" "+i[3]+" "+i[1].join(" ")+" svelte-1ixn6qd"),d(e,"id",i[0]),e.disabled=i[8],k(e,"hidden",!i[2]),c(e,"flex-grow",i[9]),c(e,"width",i[9]===0?"fit-content":null),c(e,"min-width",typeof i[10]=="number"?`calc(min(${i[10]}px, 100%))`:null)},m(o,m){w(o,e,m),u&&u.m(e,null),E(e,n),a&&a.m(e,null),s=!0,f||(_=W(e,"click",i[13]),f=!0)},p(o,m){o[7]?u?(u.p(o,m),m&128&&h(u,1)):(u=A(o),u.c(),h(u,1),u.m(e,n)):u&&(v(),g(u,1,1,()=>{u=null}),z()),a&&a.p&&(!s||m&2048)&&F(a,l,o,o[11],s?H(l,o[11],m,null):G(o[11]),null),(!s||m&26&&t!==(t=o[4]+" "+o[3]+" "+o[1].join(" ")+" svelte-1ixn6qd"))&&d(e,"class",t),(!s||m&1)&&d(e,"id",o[0]),(!s||m&256)&&(e.disabled=o[8]),(!s||m&30)&&k(e,"hidden",!o[2]),m&512&&c(e,"flex-grow",o[9]),m&512&&c(e,"width",o[9]===0?"fit-content":null),m&1024&&c(e,"min-width",typeof o[10]=="number"?`calc(min(${o[10]}px, 100%))`:null)},i(o){s||(h(u),h(a,o),s=!0)},o(o){g(u),g(a,o),s=!1},d(o){o&&q(e),u&&u.d(),a&&a.d(o),f=!1,_()}}}function Y(i){let e,n,t,s,f=i[7]&&M(i);const _=i[12].default,u=N(_,i,i[11],null);return{c(){e=S("a"),f&&f.c(),n=D(),u&&u.c(),d(e,"href",i[6]),d(e,"rel","noopener noreferrer"),d(e,"aria-disabled",i[8]),d(e,"class",t=i[4]+" "+i[3]+" "+i[1].join(" ")+" svelte-1ixn6qd"),d(e,"id",i[0]),k(e,"hidden",!i[2]),k(e,"disabled",i[8]),c(e,"flex-grow",i[9]),c(e,"pointer-events",i[8]?"none":null),c(e,"width",i[9]===0?"fit-content":null),c(e,"min-width",typeof i[10]=="number"?`calc(min(${i[10]}px, 100%))`:null)},m(l,a){w(l,e,a),f&&f.m(e,null),E(e,n),u&&u.m(e,null),s=!0},p(l,a){l[7]?f?(f.p(l,a),a&128&&h(f,1)):(f=M(l),f.c(),h(f,1),f.m(e,n)):f&&(v(),g(f,1,1,()=>{f=null}),z()),u&&u.p&&(!s||a&2048)&&F(u,_,l,l[11],s?H(_,l[11],a,null):G(l[11]),null),(!s||a&64)&&d(e,"href",l[6]),(!s||a&256)&&d(e,"aria-disabled",l[8]),(!s||a&26&&t!==(t=l[4]+" "+l[3]+" "+l[1].join(" ")+" svelte-1ixn6qd"))&&d(e,"class",t),(!s||a&1)&&d(e,"id",l[0]),(!s||a&30)&&k(e,"hidden",!l[2]),(!s||a&282)&&k(e,"disabled",l[8]),a&512&&c(e,"flex-grow",l[9]),a&256&&c(e,"pointer-events",l[8]?"none":null),a&512&&c(e,"width",l[9]===0?"fit-content":null),a&1024&&c(e,"min-width",typeof l[10]=="number"?`calc(min(${l[10]}px, 100%))`:null)},i(l){s||(h(f),h(u,l),s=!0)},o(l){g(f),g(u,l),s=!1},d(l){l&&q(e),f&&f.d(),u&&u.d(l)}}}function A(i){let e,n;return e=new O({props:{class:`button-icon ${i[5]?"right-padded":""}`,src:i[7].url,alt:`${i[5]} icon`}}),{c(){J(e.$$.fragment)},m(t,s){K(e,t,s),n=!0},p(t,s){const f={};s&32&&(f.class=`button-icon ${t[5]?"right-padded":""}`),s&128&&(f.src=t[7].url),s&32&&(f.alt=`${t[5]} icon`),e.$set(f)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function M(i){let e,n;return e=new O({props:{class:"button-icon",src:i[7].url,alt:`${i[5]} icon`}}),{c(){J(e.$$.fragment)},m(t,s){K(e,t,s),n=!0},p(t,s){const f={};s&128&&(f.src=t[7].url),s&32&&(f.alt=`${t[5]} icon`),e.$set(f)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function Z(i){let e,n,t,s;const f=[Y,X],_=[];function u(l,a){return l[6]&&l[6].length>0?0:1}return e=u(i),n=_[e]=f[e](i),{c(){n.c(),t=U()},m(l,a){_[e].m(l,a),w(l,t,a),s=!0},p(l,[a]){let o=e;e=u(l),e===o?_[e].p(l,a):(v(),g(_[o],1,1,()=>{_[o]=null}),z(),n=_[e],n?n.p(l,a):(n=_[e]=f[e](l),n.c()),h(n,1),n.m(t.parentNode,t))},i(l){s||(h(n),s=!0)},o(l){g(n),s=!1},d(l){l&&q(t),_[e].d(l)}}}function p(i,e,n){let{$$slots:t={},$$scope:s}=e,{elem_id:f=""}=e,{elem_classes:_=[]}=e,{visible:u=!0}=e,{variant:l="secondary"}=e,{size:a="lg"}=e,{value:o=null}=e,{link:m=null}=e,{icon:j=null}=e,{disabled:B=!1}=e,{scale:C=null}=e,{min_width:I=void 0}=e;function P(r){V.call(this,i,r)}return i.$$set=r=>{"elem_id"in r&&n(0,f=r.elem_id),"elem_classes"in r&&n(1,_=r.elem_classes),"visible"in r&&n(2,u=r.visible),"variant"in r&&n(3,l=r.variant),"size"in r&&n(4,a=r.size),"value"in r&&n(5,o=r.value),"link"in r&&n(6,m=r.link),"icon"in r&&n(7,j=r.icon),"disabled"in r&&n(8,B=r.disabled),"scale"in r&&n(9,C=r.scale),"min_width"in r&&n(10,I=r.min_width),"$$scope"in r&&n(11,s=r.$$scope)},[f,_,u,l,a,o,m,j,B,C,I,s,t,P]}class ee extends Q{constructor(e){super(),R(this,e,p,Z,T,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,link:6,icon:7,disabled:8,scale:9,min_width:10})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),b()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),b()}get variant(){return this.$$.ctx[3]}set variant(e){this.$$set({variant:e}),b()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),b()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),b()}get link(){return this.$$.ctx[6]}set link(e){this.$$set({link:e}),b()}get icon(){return this.$$.ctx[7]}set icon(e){this.$$set({icon:e}),b()}get disabled(){return this.$$.ctx[8]}set disabled(e){this.$$set({disabled:e}),b()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),b()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),b()}}export{ee as B};
//# sourceMappingURL=Button-BiPFvbFD.js.map
