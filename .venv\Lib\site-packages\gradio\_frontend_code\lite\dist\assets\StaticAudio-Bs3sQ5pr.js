import{a as P,i as T,s as W,f as b,c as p,b as D,e as E,m as h,d as v,h as I,t as m,j as B,k as _,l as y,n as w,o as G,p as $,I as J}from"../lite.js";import{u as K}from"./utils-BsGrhMNe.js";import{B as O}from"./BlockLabel-DWW9BWN3.js";import{E as Q}from"./Empty-Bzq0Ew6m.js";import{S as R}from"./ShareButton-Be-vgu5O.js";import{D as U}from"./Download-RUpc9r8A.js";import{M as N}from"./Music-BCIGqdvV.js";import{I as V}from"./IconButtonWrapper-BqpIgNIH.js";import{A as X}from"./AudioPlayer-Dn45keYP.js";import{D as Y}from"./DownloadLink-dHe4pFcz.js";import"./Community-BFnPJcwx.js";import"./Trim-CDsEvQ4G.js";import"./Play-BIkNyEKH.js";import"./Undo-50qkik3g.js";import"./file-url-CoOyVRgq.js";import"./hls-CnVhpNcu.js";function Z(l){let e,r;return e=new Q({props:{size:"small",$$slots:{default:[ee]},$$scope:{ctx:l}}}),{c(){p(e.$$.fragment)},m(t,n){h(e,t,n),r=!0},p(t,n){const o={};n&524288&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){r||(_(e.$$.fragment,t),r=!0)},o(t){m(e.$$.fragment,t),r=!1},d(t){w(e,t)}}}function x(l){let e,r,t,n;return e=new V({props:{display_top_corner:l[10],$$slots:{default:[oe]},$$scope:{ctx:l}}}),t=new X({props:{value:l[0],label:l[1],i18n:l[5],waveform_settings:l[6],waveform_options:l[7],editable:l[8],loop:l[9]}}),t.$on("pause",l[14]),t.$on("play",l[15]),t.$on("stop",l[16]),t.$on("load",l[17]),{c(){p(e.$$.fragment),r=D(),p(t.$$.fragment)},m(o,a){h(e,o,a),v(o,r,a),h(t,o,a),n=!0},p(o,a){const f={};a&1024&&(f.display_top_corner=o[10]),a&524345&&(f.$$scope={dirty:a,ctx:o}),e.$set(f);const u={};a&1&&(u.value=o[0]),a&2&&(u.label=o[1]),a&32&&(u.i18n=o[5]),a&64&&(u.waveform_settings=o[6]),a&128&&(u.waveform_options=o[7]),a&256&&(u.editable=o[8]),a&512&&(u.loop=o[9]),t.$set(u)},i(o){n||(_(e.$$.fragment,o),_(t.$$.fragment,o),n=!0)},o(o){m(e.$$.fragment,o),m(t.$$.fragment,o),n=!1},d(o){o&&y(r),w(e,o),w(t,o)}}}function ee(l){let e,r;return e=new N({}),{c(){p(e.$$.fragment)},m(t,n){h(e,t,n),r=!0},i(t){r||(_(e.$$.fragment,t),r=!0)},o(t){m(e.$$.fragment,t),r=!1},d(t){w(e,t)}}}function S(l){let e,r;return e=new Y({props:{href:l[0].is_stream?l[0].url?.replace("playlist.m3u8","playlist-file"):l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[te]},$$scope:{ctx:l}}}),{c(){p(e.$$.fragment)},m(t,n){h(e,t,n),r=!0},p(t,n){const o={};n&1&&(o.href=t[0].is_stream?t[0].url?.replace("playlist.m3u8","playlist-file"):t[0].url),n&1&&(o.download=t[0].orig_name||t[0].path),n&524320&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){r||(_(e.$$.fragment,t),r=!0)},o(t){m(e.$$.fragment,t),r=!1},d(t){w(e,t)}}}function te(l){let e,r;return e=new J({props:{Icon:U,label:l[5]("common.download")}}),{c(){p(e.$$.fragment)},m(t,n){h(e,t,n),r=!0},p(t,n){const o={};n&32&&(o.label=t[5]("common.download")),e.$set(o)},i(t){r||(_(e.$$.fragment,t),r=!0)},o(t){m(e.$$.fragment,t),r=!1},d(t){w(e,t)}}}function A(l){let e,r;return e=new R({props:{i18n:l[5],formatter:l[11],value:l[0]}}),e.$on("error",l[12]),e.$on("share",l[13]),{c(){p(e.$$.fragment)},m(t,n){h(e,t,n),r=!0},p(t,n){const o={};n&32&&(o.i18n=t[5]),n&1&&(o.value=t[0]),e.$set(o)},i(t){r||(_(e.$$.fragment,t),r=!0)},o(t){m(e.$$.fragment,t),r=!1},d(t){w(e,t)}}}function oe(l){let e,r,t,n=l[3]&&S(l),o=l[4]&&A(l);return{c(){n&&n.c(),e=D(),o&&o.c(),r=E()},m(a,f){n&&n.m(a,f),v(a,e,f),o&&o.m(a,f),v(a,r,f),t=!0},p(a,f){a[3]?n?(n.p(a,f),f&8&&_(n,1)):(n=S(a),n.c(),_(n,1),n.m(e.parentNode,e)):n&&(I(),m(n,1,1,()=>{n=null}),B()),a[4]?o?(o.p(a,f),f&16&&_(o,1)):(o=A(a),o.c(),_(o,1),o.m(r.parentNode,r)):o&&(I(),m(o,1,1,()=>{o=null}),B())},i(a){t||(_(n),_(o),t=!0)},o(a){m(n),m(o),t=!1},d(a){a&&(y(e),y(r)),n&&n.d(a),o&&o.d(a)}}}function ne(l){let e,r,t,n,o,a;e=new O({props:{show_label:l[2],Icon:N,float:!1,label:l[1]||l[5]("audio.audio")}});const f=[x,Z],u=[];function k(i,c){return i[0]!==null?0:1}return t=k(l),n=u[t]=f[t](l),{c(){p(e.$$.fragment),r=D(),n.c(),o=E()},m(i,c){h(e,i,c),v(i,r,c),u[t].m(i,c),v(i,o,c),a=!0},p(i,[c]){const d={};c&4&&(d.show_label=i[2]),c&34&&(d.label=i[1]||i[5]("audio.audio")),e.$set(d);let g=t;t=k(i),t===g?u[t].p(i,c):(I(),m(u[g],1,1,()=>{u[g]=null}),B(),n=u[t],n?n.p(i,c):(n=u[t]=f[t](i),n.c()),_(n,1),n.m(o.parentNode,o))},i(i){a||(_(e.$$.fragment,i),_(n),a=!0)},o(i){m(e.$$.fragment,i),m(n),a=!1},d(i){i&&(y(r),y(o)),w(e,i),u[t].d(i)}}}function le(l,e,r){let{value:t=null}=e,{label:n}=e,{show_label:o=!0}=e,{show_download_button:a=!0}=e,{show_share_button:f=!1}=e,{i18n:u}=e,{waveform_settings:k={}}=e,{waveform_options:i={show_recording_waveform:!0}}=e,{editable:c=!0}=e,{loop:d}=e,{display_icon_button_wrapper_top_corner:g=!1}=e;const L=G(),M=async s=>s?`<audio controls src="${await K(s.url)}"></audio>`:"";function j(s){$.call(this,l,s)}function q(s){$.call(this,l,s)}function z(s){$.call(this,l,s)}function C(s){$.call(this,l,s)}function F(s){$.call(this,l,s)}function H(s){$.call(this,l,s)}return l.$$set=s=>{"value"in s&&r(0,t=s.value),"label"in s&&r(1,n=s.label),"show_label"in s&&r(2,o=s.show_label),"show_download_button"in s&&r(3,a=s.show_download_button),"show_share_button"in s&&r(4,f=s.show_share_button),"i18n"in s&&r(5,u=s.i18n),"waveform_settings"in s&&r(6,k=s.waveform_settings),"waveform_options"in s&&r(7,i=s.waveform_options),"editable"in s&&r(8,c=s.editable),"loop"in s&&r(9,d=s.loop),"display_icon_button_wrapper_top_corner"in s&&r(10,g=s.display_icon_button_wrapper_top_corner)},l.$$.update=()=>{l.$$.dirty&1&&t&&L("change",t)},[t,n,o,a,f,u,k,i,c,d,g,M,j,q,z,C,F,H]}class ve extends P{constructor(e){super(),T(this,e,le,ne,W,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8,loop:9,display_icon_button_wrapper_top_corner:10})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),b()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),b()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),b()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),b()}get show_share_button(){return this.$$.ctx[4]}set show_share_button(e){this.$$set({show_share_button:e}),b()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),b()}get waveform_settings(){return this.$$.ctx[6]}set waveform_settings(e){this.$$set({waveform_settings:e}),b()}get waveform_options(){return this.$$.ctx[7]}set waveform_options(e){this.$$set({waveform_options:e}),b()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),b()}get loop(){return this.$$.ctx[9]}set loop(e){this.$$set({loop:e}),b()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[10]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),b()}}export{ve as default};
//# sourceMappingURL=StaticAudio-Bs3sQ5pr.js.map
