{"version": 3, "file": "Index-BHNCUXBa.js", "sources": ["../../../column/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Gradio } from \"@gradio/utils\";\n\n\texport let scale: number | null = null;\n\texport let gap = true;\n\texport let min_width = 0;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"default\" | \"panel\" | \"compact\" = \"default\";\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let gradio: Gradio | undefined = undefined;\n\texport let show_progress = false;\n</script>\n\n<div\n\tid={elem_id}\n\tclass=\"column {elem_classes.join(' ')}\"\n\tclass:gap\n\tclass:compact={variant === \"compact\"}\n\tclass:panel={variant === \"panel\"}\n\tclass:hide={!visible}\n\tstyle:flex-grow={scale}\n\tstyle:min-width=\"calc(min({min_width}px, 100%))\"\n>\n\t{#if loading_status && show_progress && gradio}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tstatus={loading_status\n\t\t\t\t? loading_status.status == \"pending\"\n\t\t\t\t\t? \"generating\"\n\t\t\t\t\t: loading_status.status\n\t\t\t\t: null}\n\t\t/>\n\t{/if}\n\t<slot />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t}\n\n\tdiv > :global(*),\n\tdiv > :global(.form > *) {\n\t\twidth: var(--size-full);\n\t}\n\n\t.gap {\n\t\tgap: var(--layout-gap);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.compact > :global(*),\n\t.compact :global(.box) {\n\t\tborder-radius: 0;\n\t}\n\n\t.compact,\n\t.panel {\n\t\tborder: solid var(--panel-border-width) var(--panel-border-color);\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--panel-background-fill);\n\t\tpadding: var(--spacing-lg);\n\t}\n</style>\n"], "names": ["ctx", "dirty", "create_if_block", "toggle_class", "div", "insert", "target", "anchor", "scale", "$$props", "gap", "min_width", "elem_id", "elem_classes", "visible", "variant", "loading_status", "gradio", "show_progress"], "mappings": "iPA6Be,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,GACV,OAAAA,EAAA,CAAA,EACLA,EAAc,CAAA,EAAC,QAAU,UACxB,aACAA,EAAe,CAAA,EAAA,OAChB,yJAPSC,EAAA,KAAA,CAAA,WAAAD,KAAO,UAAU,EACvBC,EAAA,KAAA,CAAA,KAAAD,KAAO,IAAI,WACbA,EAAc,CAAA,CAAA,UACV,OAAAA,EAAA,CAAA,EACLA,EAAc,CAAA,EAAC,QAAU,UACxB,aACAA,EAAe,CAAA,EAAA,OAChB,2IAXsBA,EAAS,CAAA,CAAA,iBAE/BA,EAAc,CAAA,GAAIA,EAAa,CAAA,GAAIA,EAAM,CAAA,GAAAE,EAAAF,CAAA,mGAT1CA,EAAO,CAAA,CAAA,0BACIA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,kBAErBG,EAAAC,EAAA,UAAAJ,OAAY,SAAS,EACvBG,EAAAC,EAAA,QAAAJ,OAAY,OAAO,cACnBA,EAAO,CAAA,CAAA,kBACHA,EAAK,CAAA,CAAA,6BAPvBK,EAuBKC,EAAAF,EAAAG,CAAA,sDAbCP,EAAc,CAAA,GAAIA,EAAa,CAAA,GAAIA,EAAM,CAAA,0LAT1CA,EAAO,CAAA,CAAA,8BACIA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,2EAErBG,EAAAC,EAAA,UAAAJ,OAAY,SAAS,cACvBG,EAAAC,EAAA,QAAAJ,OAAY,OAAO,0BACnBA,EAAO,CAAA,CAAA,uBACHA,EAAK,CAAA,CAAA,0BACKA,EAAS,CAAA,CAAA,yKApBzB,MAAAQ,EAAuB,IAAA,EAAAC,GACvB,IAAAC,EAAM,EAAA,EAAAD,GACN,UAAAE,EAAY,CAAA,EAAAF,GACZ,QAAAG,EAAU,EAAA,EAAAH,EACV,CAAA,aAAAI,EAAA,EAAA,EAAAJ,GACA,QAAAK,EAAU,EAAA,EAAAL,GACV,QAAAM,EAA2C,SAAA,EAAAN,GAC3C,eAAAO,EAA4C,MAAA,EAAAP,GAC5C,OAAAQ,EAA6B,MAAA,EAAAR,GAC7B,cAAAS,EAAgB,EAAA,EAAAT"}