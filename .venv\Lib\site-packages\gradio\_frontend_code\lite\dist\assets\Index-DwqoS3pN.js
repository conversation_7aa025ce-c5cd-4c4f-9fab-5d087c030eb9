import{a as Ie,i as Re,s as Se,f as v,B as Ve,c as I,m as R,k as b,t as k,n as S,b as Y,e as F,d as q,h as E,j as P,l as z,y as B,E as ie,w as U,z as d,C as N,x as le,aq as M,as as W,M as D,V as ze,A as oe,D as We,au as L,$ as fe,W as re,a0 as O,a6 as Q,Y as T}from"../lite.js";/* empty css                                              */import Fe from"./Example-z38AELH2.js";/* empty css                                              */function ae(s,e,l){const t=s.slice();return t[39]=e[l],t}function _e(s,e,l){const t=s.slice();return t[42]=e[l],t[44]=l,t}function ce(s,e,l){const t=s.slice();t[0]=e[l].value,t[46]=e[l].component,t[49]=l;const n=t[1][t[49]];return t[47]=n,t}function ue(s,e,l){const t=s.slice();return t[50]=e[l],t}function me(s,e,l){const t=s.slice();return t[42]=e[l],t[44]=l,t}function he(s){let e,l,t,n,o;return{c(){e=B("div"),l=ie("svg"),t=ie("path"),n=Y(),o=U(s[4]),d(t,"fill","currentColor"),d(t,"d","M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z"),d(l,"xmlns","http://www.w3.org/2000/svg"),d(l,"xmlns:xlink","http://www.w3.org/1999/xlink"),d(l,"aria-hidden","true"),d(l,"role","img"),d(l,"width","1em"),d(l,"height","1em"),d(l,"preserveAspectRatio","xMidYMid meet"),d(l,"viewBox","0 0 32 32"),d(l,"class","svelte-p5q82i"),d(e,"class","label svelte-p5q82i")},m(r,i){q(r,e,i),N(e,l),N(l,t),N(e,n),N(e,o)},p(r,i){i[0]&16&&le(o,r[4])},d(r){r&&z(e)}}}function Ge(s){let e,l,t,n,o,r,i,f=M(s[6]),a=[];for(let _=0;_<f.length;_+=1)a[_]=ge(ue(s,f,_));let m=M(s[21]),c=[];for(let _=0;_<m.length;_+=1)c[_]=be(_e(s,m,_));const h=_=>k(c[_],1,1,()=>{c[_]=null});return{c(){e=B("div"),l=B("table"),t=B("thead"),n=B("tr");for(let _=0;_<a.length;_+=1)a[_].c();o=Y(),r=B("tbody");for(let _=0;_<c.length;_+=1)c[_].c();d(n,"class","tr-head svelte-p5q82i"),d(l,"tabindex","0"),d(l,"role","grid"),d(l,"class","svelte-p5q82i"),d(e,"class","table-wrap svelte-p5q82i")},m(_,p){q(_,e,p),N(e,l),N(l,t),N(t,n);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(n,null);N(l,o),N(l,r);for(let g=0;g<c.length;g+=1)c[g]&&c[g].m(r,null);i=!0},p(_,p){if(p[0]&64){f=M(_[6]);let g;for(g=0;g<f.length;g+=1){const w=ue(_,f,g);a[g]?a[g].p(w,p):(a[g]=ge(w),a[g].c(),a[g].m(n,null))}for(;g<a.length;g+=1)a[g].d(1);a.length=f.length}if(p[0]&61970447){m=M(_[21]);let g;for(g=0;g<m.length;g+=1){const w=_e(_,m,g);c[g]?(c[g].p(w,p),b(c[g],1)):(c[g]=be(w),c[g].c(),b(c[g],1),c[g].m(r,null))}for(E(),g=m.length;g<c.length;g+=1)h(g);P()}},i(_){if(!i){for(let p=0;p<m.length;p+=1)b(c[p]);i=!0}},o(_){c=c.filter(Boolean);for(let p=0;p<c.length;p+=1)k(c[p]);i=!1},d(_){_&&z(e),W(a,_),W(c,_)}}}function Je(s){let e,l,t=M(s[18]),n=[];for(let r=0;r<t.length;r+=1)n[r]=ve(me(s,t,r));const o=r=>k(n[r],1,1,()=>{n[r]=null});return{c(){e=B("div");for(let r=0;r<n.length;r+=1)n[r].c();d(e,"class","gallery svelte-p5q82i")},m(r,i){q(r,e,i);for(let f=0;f<n.length;f+=1)n[f]&&n[f].m(e,null);l=!0},p(r,i){if(i[0]&62232719){t=M(r[18]);let f;for(f=0;f<t.length;f+=1){const a=me(r,t,f);n[f]?(n[f].p(a,i),b(n[f],1)):(n[f]=ve(a),n[f].c(),b(n[f],1),n[f].m(e,null))}for(E(),f=t.length;f<n.length;f+=1)o(f);P()}},i(r){if(!l){for(let i=0;i<t.length;i+=1)b(n[i]);l=!0}},o(r){n=n.filter(Boolean);for(let i=0;i<n.length;i+=1)k(n[i]);l=!1},d(r){r&&z(e),W(n,r)}}}function ge(s){let e,l=s[50]+"",t,n;return{c(){e=B("th"),t=U(l),n=Y(),d(e,"class","svelte-p5q82i")},m(o,r){q(o,e,r),N(e,t),N(e,n)},p(o,r){r[0]&64&&l!==(l=o[50]+"")&&le(t,l)},d(o){o&&z(e)}}}function pe(s){let e,l,t,n;const o=[s[2][s[49]],{value:s[0]},{samples_dir:s[23]},{type:"table"},{selected:s[20]===s[44]},{index:s[44]},{root:s[11]}];var r=s[46];function i(f,a){let m={};for(let c=0;c<o.length;c+=1)m=T(m,o[c]);return a!==void 0&&a[0]&11536388&&(m=T(m,O(o,[a[0]&4&&Q(f[2][f[49]]),a[0]&2097152&&{value:f[0]},a[0]&8388608&&{samples_dir:f[23]},o[3],a[0]&1048576&&{selected:f[20]===f[44]},o[5],a[0]&2048&&{root:f[11]}]))),{props:m}}return r&&(l=L(r,i(s))),{c(){e=B("td"),l&&I(l.$$.fragment),fe(e,"max-width",s[47]==="textbox"?"35ch":"auto"),d(e,"class",t=re(s[47])+" svelte-p5q82i")},m(f,a){q(f,e,a),l&&R(l,e,null),n=!0},p(f,a){if(a[0]&2097152&&r!==(r=f[46])){if(l){E();const m=l;k(m.$$.fragment,1,0,()=>{S(m,1)}),P()}r?(l=L(r,i(f,a)),I(l.$$.fragment),b(l.$$.fragment,1),R(l,e,null)):l=null}else if(r){const m=a[0]&11536388?O(o,[a[0]&4&&Q(f[2][f[49]]),a[0]&2097152&&{value:f[0]},a[0]&8388608&&{samples_dir:f[23]},o[3],a[0]&1048576&&{selected:f[20]===f[44]},o[5],a[0]&2048&&{root:f[11]}]):{};l.$set(m)}(!n||a[0]&2)&&fe(e,"max-width",f[47]==="textbox"?"35ch":"auto"),(!n||a[0]&2&&t!==(t=re(f[47])+" svelte-p5q82i"))&&d(e,"class",t)},i(f){n||(l&&b(l.$$.fragment,f),n=!0)},o(f){l&&k(l.$$.fragment,f),n=!1},d(f){f&&z(e),l&&S(l)}}}function de(s){let e=s[47]!==void 0&&s[3].get(s[47])!==void 0,l,t,n=e&&pe(s);return{c(){n&&n.c(),l=F()},m(o,r){n&&n.m(o,r),q(o,l,r),t=!0},p(o,r){r[0]&10&&(e=o[47]!==void 0&&o[3].get(o[47])!==void 0),e?n?(n.p(o,r),r[0]&10&&b(n,1)):(n=pe(o),n.c(),b(n,1),n.m(l.parentNode,l)):n&&(E(),k(n,1,1,()=>{n=null}),P())},i(o){t||(b(n),t=!0)},o(o){k(n),t=!1},d(o){o&&z(l),n&&n.d(o)}}}function be(s){let e,l,t,n,o,r=M(s[42]),i=[];for(let c=0;c<r.length;c+=1)i[c]=de(ce(s,r,c));const f=c=>k(i[c],1,1,()=>{i[c]=null});function a(){return s[34](s[44])}function m(){return s[35](s[44])}return{c(){e=B("tr");for(let c=0;c<i.length;c+=1)i[c].c();l=Y(),d(e,"class","tr-body svelte-p5q82i")},m(c,h){q(c,e,h);for(let _=0;_<i.length;_+=1)i[_]&&i[_].m(e,null);N(e,l),t=!0,n||(o=[D(e,"click",a),D(e,"mouseenter",m),D(e,"mouseleave",s[36])],n=!0)},p(c,h){if(s=c,h[0]&11536398){r=M(s[42]);let _;for(_=0;_<r.length;_+=1){const p=ce(s,r,_);i[_]?(i[_].p(p,h),b(i[_],1)):(i[_]=de(p),i[_].c(),b(i[_],1),i[_].m(e,l))}for(E(),_=r.length;_<i.length;_+=1)f(_);P()}},i(c){if(!t){for(let h=0;h<r.length;h+=1)b(i[h]);t=!0}},o(c){i=i.filter(Boolean);for(let h=0;h<i.length;h+=1)k(i[h]);t=!1},d(c){c&&z(e),W(i,c),n=!1,ze(o)}}}function ke(s){let e,l,t,n,o,r,i,f;const a=[Le,Ke],m=[];function c(p,g){return g[0]&2097162&&(l=null),p[7]?0:(l==null&&(l=!!(p[21].length&&p[3].get(p[1][0]))),l?1:-1)}~(t=c(s,[-1,-1]))&&(n=m[t]=a[t](s));function h(){return s[31](s[44],s[42])}function _(){return s[32](s[44])}return{c(){e=B("button"),n&&n.c(),o=Y(),d(e,"class","gallery-item svelte-p5q82i")},m(p,g){q(p,e,g),~t&&m[t].m(e,null),N(e,o),r=!0,i||(f=[D(e,"click",h),D(e,"mouseenter",_),D(e,"mouseleave",s[33])],i=!0)},p(p,g){s=p;let w=t;t=c(s,g),t===w?~t&&m[t].p(s,g):(n&&(E(),k(m[w],1,1,()=>{m[w]=null}),P()),~t?(n=m[t],n?n.p(s,g):(n=m[t]=a[t](s),n.c()),b(n,1),n.m(e,o)):n=null)},i(p){r||(b(n),r=!0)},o(p){k(n),r=!1},d(p){p&&z(e),~t&&m[t].d(),i=!1,ze(f)}}}function Ke(s){let e,l,t;const n=[s[2][0],{value:s[42][0]},{samples_dir:s[23]},{type:"gallery"},{selected:s[20]===s[44]},{index:s[44]},{root:s[11]}];var o=s[21][0][0].component;function r(i,f){let a={};for(let m=0;m<n.length;m+=1)a=T(a,n[m]);return f!==void 0&&f[0]&9701380&&(a=T(a,O(n,[f[0]&4&&Q(i[2][0]),f[0]&262144&&{value:i[42][0]},f[0]&8388608&&{samples_dir:i[23]},n[3],f[0]&1048576&&{selected:i[20]===i[44]},n[5],f[0]&2048&&{root:i[11]}]))),{props:a}}return o&&(e=L(o,r(s))),{c(){e&&I(e.$$.fragment),l=F()},m(i,f){e&&R(e,i,f),q(i,l,f),t=!0},p(i,f){if(f[0]&2097152&&o!==(o=i[21][0][0].component)){if(e){E();const a=e;k(a.$$.fragment,1,0,()=>{S(a,1)}),P()}o?(e=L(o,r(i,f)),I(e.$$.fragment),b(e.$$.fragment,1),R(e,l.parentNode,l)):e=null}else if(o){const a=f[0]&9701380?O(n,[f[0]&4&&Q(i[2][0]),f[0]&262144&&{value:i[42][0]},f[0]&8388608&&{samples_dir:i[23]},n[3],f[0]&1048576&&{selected:i[20]===i[44]},n[5],f[0]&2048&&{root:i[11]}]):{};e.$set(a)}},i(i){t||(e&&b(e.$$.fragment,i),t=!0)},o(i){e&&k(e.$$.fragment,i),t=!1},d(i){i&&z(l),e&&S(e,i)}}}function Le(s){let e,l;return e=new Fe({props:{value:s[42][0],selected:s[20]===s[44],type:"gallery"}}),{c(){I(e.$$.fragment)},m(t,n){R(e,t,n),l=!0},p(t,n){const o={};n[0]&262144&&(o.value=t[42][0]),n[0]&1048576&&(o.selected=t[20]===t[44]),e.$set(o)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){k(e.$$.fragment,t),l=!1},d(t){S(e,t)}}}function ve(s){let e,l,t=s[42][0]!=null&&ke(s);return{c(){t&&t.c(),e=F()},m(n,o){t&&t.m(n,o),q(n,e,o),l=!0},p(n,o){n[42][0]!=null?t?(t.p(n,o),o[0]&262144&&b(t,1)):(t=ke(n),t.c(),b(t,1),t.m(e.parentNode,e)):t&&(E(),k(t,1,1,()=>{t=null}),P())},i(n){l||(b(t),l=!0)},o(n){k(t),l=!1},d(n){n&&z(e),t&&t.d(n)}}}function we(s){let e,l,t=M(s[19]),n=[];for(let o=0;o<t.length;o+=1)n[o]=qe(ae(s,t,o));return{c(){e=B("div"),l=U(`Pages:
			`);for(let o=0;o<n.length;o+=1)n[o].c();d(e,"class","paginate svelte-p5q82i")},m(o,r){q(o,e,r),N(e,l);for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(e,null)},p(o,r){if(r[0]&589824){t=M(o[19]);let i;for(i=0;i<t.length;i+=1){const f=ae(o,t,i);n[i]?n[i].p(f,r):(n[i]=qe(f),n[i].c(),n[i].m(e,null))}for(;i<n.length;i+=1)n[i].d(1);n.length=t.length}},d(o){o&&z(e),W(n,o)}}}function Oe(s){let e,l=s[39]+1+"",t,n,o,r;function i(){return s[37](s[39])}return{c(){e=B("button"),t=U(l),n=Y(),d(e,"class","svelte-p5q82i"),oe(e,"current-page",s[16]===s[39])},m(f,a){q(f,e,a),N(e,t),N(e,n),o||(r=D(e,"click",i),o=!0)},p(f,a){s=f,a[0]&524288&&l!==(l=s[39]+1+"")&&le(t,l),a[0]&589824&&oe(e,"current-page",s[16]===s[39])},d(f){f&&z(e),o=!1,r()}}}function Qe(s){let e;return{c(){e=B("div"),e.textContent="..."},m(l,t){q(l,e,t)},p:We,d(l){l&&z(e)}}}function qe(s){let e;function l(o,r){return o[39]===-1?Qe:Oe}let t=l(s),n=t(s);return{c(){n.c(),e=F()},m(o,r){n.m(o,r),q(o,e,r)},p(o,r){t===(t=l(o))&&n?n.p(o,r):(n.d(1),n=t(o),n&&(n.c(),n.m(e.parentNode,e)))},d(o){o&&z(e),n.d(o)}}}function Te(s){let e,l,t,n,o,r,i=s[5]&&he(s);const f=[Je,Ge],a=[];function m(h,_){return h[22]?0:h[18].length>0?1:-1}~(l=m(s))&&(t=a[l]=f[l](s));let c=s[17]&&we(s);return{c(){i&&i.c(),e=Y(),t&&t.c(),n=Y(),c&&c.c(),o=F()},m(h,_){i&&i.m(h,_),q(h,e,_),~l&&a[l].m(h,_),q(h,n,_),c&&c.m(h,_),q(h,o,_),r=!0},p(h,_){h[5]?i?i.p(h,_):(i=he(h),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null);let p=l;l=m(h),l===p?~l&&a[l].p(h,_):(t&&(E(),k(a[p],1,1,()=>{a[p]=null}),P()),~l?(t=a[l],t?t.p(h,_):(t=a[l]=f[l](h),t.c()),b(t,1),t.m(n.parentNode,n)):t=null),h[17]?c?c.p(h,_):(c=we(h),c.c(),c.m(o.parentNode,o)):c&&(c.d(1),c=null)},i(h){r||(b(t),r=!0)},o(h){k(t),r=!1},d(h){h&&(z(e),z(n),z(o)),i&&i.d(h),~l&&a[l].d(h),c&&c.d(h)}}}function Ue(s){let e,l;return e=new Ve({props:{visible:s[10],padding:!1,elem_id:s[8],elem_classes:s[9],scale:s[13],min_width:s[14],allow_overflow:!1,container:!1,$$slots:{default:[Te]},$$scope:{ctx:s}}}),{c(){I(e.$$.fragment)},m(t,n){R(e,t,n),l=!0},p(t,n){const o={};n[0]&1024&&(o.visible=t[10]),n[0]&256&&(o.elem_id=t[8]),n[0]&512&&(o.elem_classes=t[9]),n[0]&8192&&(o.scale=t[13]),n[0]&16384&&(o.min_width=t[14]),n[0]&8362239|n[1]&4194304&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){k(e.$$.fragment,t),l=!1},d(t){S(e,t)}}}function Xe(s,e,l){let t,{components:n}=e,{component_props:o}=e,{component_map:r}=e,{label:i="Examples"}=e,{show_label:f=!0}=e,{headers:a}=e,{samples:m=null}=e,c=null,{sample_labels:h=null}=e,{elem_id:_=""}=e,{elem_classes:p=[]}=e,{visible:g=!0}=e,{value:w=null}=e,{root:X}=e,{proxy_url:G}=e,{samples_per_page:C=10}=e,{scale:te=null}=e,{min_width:ne=void 0}=e,{gradio:V}=e,{layout:Z=null}=e,Ne=G?`/proxy=${G}file=`:`${X}/file=`,j=0,y=m?m.length>C:!1,J,K,A=[],$=-1;function x(u){l(20,$=u)}function ee(){l(20,$=-1)}let se=[];async function Be(u){l(21,se=await Promise.all(u&&u.map(async H=>await Promise.all(H.map(async(Ye,De)=>({value:Ye,component:(await r.get(n[De]))?.default}))))))}const He=(u,H)=>{l(0,w=u+j*C),V.dispatch("click",w),V.dispatch("select",{index:w,value:H})},Me=u=>x(u),Ce=()=>ee(),Ee=u=>{l(0,w=u+j*C),V.dispatch("click",w)},Pe=u=>x(u),je=()=>ee(),Ae=u=>l(16,j=u);return s.$$set=u=>{"components"in u&&l(1,n=u.components),"component_props"in u&&l(2,o=u.component_props),"component_map"in u&&l(3,r=u.component_map),"label"in u&&l(4,i=u.label),"show_label"in u&&l(5,f=u.show_label),"headers"in u&&l(6,a=u.headers),"samples"in u&&l(26,m=u.samples),"sample_labels"in u&&l(7,h=u.sample_labels),"elem_id"in u&&l(8,_=u.elem_id),"elem_classes"in u&&l(9,p=u.elem_classes),"visible"in u&&l(10,g=u.visible),"value"in u&&l(0,w=u.value),"root"in u&&l(11,X=u.root),"proxy_url"in u&&l(27,G=u.proxy_url),"samples_per_page"in u&&l(12,C=u.samples_per_page),"scale"in u&&l(13,te=u.scale),"min_width"in u&&l(14,ne=u.min_width),"gradio"in u&&l(15,V=u.gradio),"layout"in u&&l(28,Z=u.layout)},s.$$.update=()=>{s.$$.dirty[0]&268435586&&l(22,t=(n.length<2||h!==null)&&Z!=="table"),s.$$.dirty[0]&1678446720&&(h?l(26,m=h.map(u=>[u])):m||l(26,m=[]),m!==c&&(l(16,j=0),l(29,c=m)),l(17,y=m.length>C),y?(l(19,A=[]),l(18,J=m.slice(j*C,(j+1)*C)),l(30,K=Math.ceil(m.length/C)),[0,j,K-1].forEach(u=>{for(let H=u-2;H<=u+2;H++)H>=0&&H<K&&!A.includes(H)&&(A.length>0&&H-A[A.length-1]>1&&A.push(-1),A.push(H))})):l(18,J=m.slice())),s.$$.dirty[0]&262152&&Be(J)},[w,n,o,r,i,f,a,h,_,p,g,X,C,te,ne,V,j,y,J,A,$,se,t,Ne,x,ee,m,G,Z,c,K,He,Me,Ce,Ee,Pe,je,Ae]}class el extends Ie{constructor(e){super(),Re(this,e,Xe,Ue,Se,{components:1,component_props:2,component_map:3,label:4,show_label:5,headers:6,samples:26,sample_labels:7,elem_id:8,elem_classes:9,visible:10,value:0,root:11,proxy_url:27,samples_per_page:12,scale:13,min_width:14,gradio:15,layout:28},null,[-1,-1])}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),v()}get component_props(){return this.$$.ctx[2]}set component_props(e){this.$$set({component_props:e}),v()}get component_map(){return this.$$.ctx[3]}set component_map(e){this.$$set({component_map:e}),v()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),v()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),v()}get headers(){return this.$$.ctx[6]}set headers(e){this.$$set({headers:e}),v()}get samples(){return this.$$.ctx[26]}set samples(e){this.$$set({samples:e}),v()}get sample_labels(){return this.$$.ctx[7]}set sample_labels(e){this.$$set({sample_labels:e}),v()}get elem_id(){return this.$$.ctx[8]}set elem_id(e){this.$$set({elem_id:e}),v()}get elem_classes(){return this.$$.ctx[9]}set elem_classes(e){this.$$set({elem_classes:e}),v()}get visible(){return this.$$.ctx[10]}set visible(e){this.$$set({visible:e}),v()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),v()}get proxy_url(){return this.$$.ctx[27]}set proxy_url(e){this.$$set({proxy_url:e}),v()}get samples_per_page(){return this.$$.ctx[12]}set samples_per_page(e){this.$$set({samples_per_page:e}),v()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),v()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),v()}get gradio(){return this.$$.ctx[15]}set gradio(e){this.$$set({gradio:e}),v()}get layout(){return this.$$.ctx[28]}set layout(e){this.$$set({layout:e}),v()}}export{el as default};
//# sourceMappingURL=Index-DwqoS3pN.js.map
