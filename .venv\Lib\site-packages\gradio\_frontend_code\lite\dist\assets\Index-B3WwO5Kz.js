import{a as p,i as x,s as ee,f as h,y as B,b as C,z as _,d as N,C as m,D as Q,l as S,o as ae,w as F,A as G,$ as J,x as K,aq as R,e as te,as as ne,M as ie,B as ce,c as E,m as q,k,t as L,n as D,Y as oe,S as ue,a0 as re,a6 as fe,h as T,j as U}from"../lite.js";import{L as le}from"./LineChart-D-_g4n6L.js";import{B as _e}from"./BlockLabel-DWW9BWN3.js";import{E as de}from"./Empty-Bzq0Ew6m.js";function V(s,e,a){const t=s.slice();return t[6]=e[a],t[8]=a,t}function W(s){let e,a=s[0].label+"",t;return{c(){e=B("h2"),t=F(a),_(e,"class","output-class svelte-1mutzus"),_(e,"data-testid","label-output-value"),G(e,"no-confidence",!("confidences"in s[0])),J(e,"background-color",s[1]||"transparent")},m(l,n){N(l,e,n),m(e,t)},p(l,n){n&1&&a!==(a=l[0].label+"")&&K(t,a),n&1&&G(e,"no-confidence",!("confidences"in l[0])),n&2&&J(e,"background-color",l[1]||"transparent")},d(l){l&&S(e)}}}function X(s){let e,a=R(s[0].confidences),t=[];for(let l=0;l<a.length;l+=1)t[l]=Z(V(s,a,l));return{c(){for(let l=0;l<t.length;l+=1)t[l].c();e=te()},m(l,n){for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(l,n);N(l,e,n)},p(l,n){if(n&21){a=R(l[0].confidences);let c;for(c=0;c<a.length;c+=1){const d=V(l,a,c);t[c]?t[c].p(d,n):(t[c]=Z(d),t[c].c(),t[c].m(e.parentNode,e))}for(;c<t.length;c+=1)t[c].d(1);t.length=a.length}},d(l){l&&S(e),ne(t,l)}}}function Z(s){let e,a,t,l,n,c,d,v,r,u,z=s[6].label+"",b,M,i,f,$,w=Math.round(s[6].confidence*100)+"",j,O,A,o,H,P;function se(){return s[5](s[8],s[6])}return{c(){e=B("button"),a=B("div"),t=B("meter"),v=C(),r=B("dl"),u=B("dt"),b=F(z),M=C(),f=B("div"),$=B("dd"),j=F(w),O=F("%"),A=C(),_(t,"aria-labelledby",l=Y(`meter-text-${s[6].label}`)),_(t,"aria-label",n=s[6].label),_(t,"aria-valuenow",c=Math.round(s[6].confidence*100)),_(t,"aria-valuemin","0"),_(t,"aria-valuemax","100"),_(t,"class","bar svelte-1mutzus"),_(t,"min","0"),_(t,"max","1"),t.value=d=s[6].confidence,J(t,"width",s[6].confidence*100+"%"),J(t,"background","var(--stat-background-fill)"),_(u,"id",i=Y(`meter-text-${s[6].label}`)),_(u,"class","text svelte-1mutzus"),_(f,"class","line svelte-1mutzus"),_($,"class","confidence svelte-1mutzus"),_(r,"class","label svelte-1mutzus"),_(a,"class","inner-wrap svelte-1mutzus"),_(e,"class","confidence-set group svelte-1mutzus"),_(e,"data-testid",o=`${s[6].label}-confidence-set`),G(e,"selectable",s[2])},m(I,g){N(I,e,g),m(e,a),m(a,t),m(a,v),m(a,r),m(r,u),m(u,b),m(u,M),m(r,f),m(r,$),m($,j),m($,O),m(e,A),H||(P=ie(e,"click",se),H=!0)},p(I,g){s=I,g&1&&l!==(l=Y(`meter-text-${s[6].label}`))&&_(t,"aria-labelledby",l),g&1&&n!==(n=s[6].label)&&_(t,"aria-label",n),g&1&&c!==(c=Math.round(s[6].confidence*100))&&_(t,"aria-valuenow",c),g&1&&d!==(d=s[6].confidence)&&(t.value=d),g&1&&J(t,"width",s[6].confidence*100+"%"),g&1&&z!==(z=s[6].label+"")&&K(b,z),g&1&&i!==(i=Y(`meter-text-${s[6].label}`))&&_(u,"id",i),g&1&&w!==(w=Math.round(s[6].confidence*100)+"")&&K(j,w),g&1&&o!==(o=`${s[6].label}-confidence-set`)&&_(e,"data-testid",o),g&4&&G(e,"selectable",s[2])},d(I){I&&S(e),H=!1,P()}}}function he(s){let e,a,t=(s[3]||!s[0].confidences)&&W(s),l=typeof s[0]=="object"&&s[0].confidences&&X(s);return{c(){e=B("div"),t&&t.c(),a=C(),l&&l.c(),_(e,"class","container svelte-1mutzus")},m(n,c){N(n,e,c),t&&t.m(e,null),m(e,a),l&&l.m(e,null)},p(n,[c]){n[3]||!n[0].confidences?t?t.p(n,c):(t=W(n),t.c(),t.m(e,a)):t&&(t.d(1),t=null),typeof n[0]=="object"&&n[0].confidences?l?l.p(n,c):(l=X(n),l.c(),l.m(e,null)):l&&(l.d(1),l=null)},i:Q,o:Q,d(n){n&&S(e),t&&t.d(),l&&l.d()}}}function Y(s){return s.replace(/\s/g,"-")}function be(s,e,a){let{value:t}=e;const l=ae();let{color:n=void 0}=e,{selectable:c=!1}=e,{show_heading:d=!0}=e;const v=(r,u)=>{l("select",{index:r,value:u.label})};return s.$$set=r=>{"value"in r&&a(0,t=r.value),"color"in r&&a(1,n=r.color),"selectable"in r&&a(2,c=r.selectable),"show_heading"in r&&a(3,d=r.show_heading)},[t,n,c,d,l,v]}class me extends p{constructor(e){super(),x(this,e,be,he,ee,{value:0,color:1,selectable:2,show_heading:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get color(){return this.$$.ctx[1]}set color(e){this.$$set({color:e}),h()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),h()}get show_heading(){return this.$$.ctx[3]}set show_heading(e){this.$$set({show_heading:e}),h()}}const ge=me;function y(s){let e,a;return e=new _e({props:{Icon:le,label:s[6],disable:s[7]===!1,float:s[13]===!0}}),{c(){E(e.$$.fragment)},m(t,l){q(e,t,l),a=!0},p(t,l){const n={};l&64&&(n.label=t[6]),l&128&&(n.disable=t[7]===!1),l&8192&&(n.float=t[13]===!0),e.$set(n)},i(t){a||(k(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){D(e,t)}}}function ve(s){let e,a;return e=new de({props:{unpadded_box:!0,$$slots:{default:[ke]},$$scope:{ctx:s}}}),{c(){E(e.$$.fragment)},m(t,l){q(e,t,l),a=!0},p(t,l){const n={};l&262144&&(n.$$scope={dirty:l,ctx:t}),e.$set(n)},i(t){a||(k(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){D(e,t)}}}function we(s){let e,a;return e=new ge({props:{selectable:s[12],value:s[5],color:s[4],show_heading:s[13]}}),e.$on("select",s[17]),{c(){E(e.$$.fragment)},m(t,l){q(e,t,l),a=!0},p(t,l){const n={};l&4096&&(n.selectable=t[12]),l&32&&(n.value=t[5]),l&16&&(n.color=t[4]),l&8192&&(n.show_heading=t[13]),e.$set(n)},i(t){a||(k(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){D(e,t)}}}function ke(s){let e,a;return e=new le({}),{c(){E(e.$$.fragment)},m(t,l){q(e,t,l),a=!0},i(t){a||(k(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){D(e,t)}}}function ze(s){let e,a,t,l,n,c,d;const v=[{autoscroll:s[0].autoscroll},{i18n:s[0].i18n},s[10]];let r={};for(let i=0;i<v.length;i+=1)r=oe(r,v[i]);e=new ue({props:r}),e.$on("clear_status",s[16]);let u=s[11]&&y(s);const z=[we,ve],b=[];function M(i,f){return i[14]!==void 0&&i[14]!==null?0:1}return l=M(s),n=b[l]=z[l](s),{c(){E(e.$$.fragment),a=C(),u&&u.c(),t=C(),n.c(),c=te()},m(i,f){q(e,i,f),N(i,a,f),u&&u.m(i,f),N(i,t,f),b[l].m(i,f),N(i,c,f),d=!0},p(i,f){const $=f&1025?re(v,[f&1&&{autoscroll:i[0].autoscroll},f&1&&{i18n:i[0].i18n},f&1024&&fe(i[10])]):{};e.$set($),i[11]?u?(u.p(i,f),f&2048&&k(u,1)):(u=y(i),u.c(),k(u,1),u.m(t.parentNode,t)):u&&(T(),L(u,1,1,()=>{u=null}),U());let w=l;l=M(i),l===w?b[l].p(i,f):(T(),L(b[w],1,1,()=>{b[w]=null}),U(),n=b[l],n?n.p(i,f):(n=b[l]=z[l](i),n.c()),k(n,1),n.m(c.parentNode,c))},i(i){d||(k(e.$$.fragment,i),k(u),k(n),d=!0)},o(i){L(e.$$.fragment,i),L(u),L(n),d=!1},d(i){i&&(S(a),S(t),S(c)),D(e,i),u&&u.d(i),b[l].d(i)}}}function $e(s){let e,a;return e=new ce({props:{test_id:"label",visible:s[3],elem_id:s[1],elem_classes:s[2],container:s[7],scale:s[8],min_width:s[9],padding:!1,$$slots:{default:[ze]},$$scope:{ctx:s}}}),{c(){E(e.$$.fragment)},m(t,l){q(e,t,l),a=!0},p(t,[l]){const n={};l&8&&(n.visible=t[3]),l&2&&(n.elem_id=t[1]),l&4&&(n.elem_classes=t[2]),l&128&&(n.container=t[7]),l&256&&(n.scale=t[8]),l&512&&(n.min_width=t[9]),l&294129&&(n.$$scope={dirty:l,ctx:t}),e.$set(n)},i(t){a||(k(e.$$.fragment,t),a=!0)},o(t){L(e.$$.fragment,t),a=!1},d(t){D(e,t)}}}function Le(s,e,a){let t,{gradio:l}=e,{elem_id:n=""}=e,{elem_classes:c=[]}=e,{visible:d=!0}=e,{color:v=void 0}=e,{value:r={}}=e,u=null,{label:z=l.i18n("label.label")}=e,{container:b=!0}=e,{scale:M=null}=e,{min_width:i=void 0}=e,{loading_status:f}=e,{show_label:$=!0}=e,{_selectable:w=!1}=e,{show_heading:j=!0}=e;const O=()=>l.dispatch("clear_status",f),A=({detail:o})=>l.dispatch("select",o);return s.$$set=o=>{"gradio"in o&&a(0,l=o.gradio),"elem_id"in o&&a(1,n=o.elem_id),"elem_classes"in o&&a(2,c=o.elem_classes),"visible"in o&&a(3,d=o.visible),"color"in o&&a(4,v=o.color),"value"in o&&a(5,r=o.value),"label"in o&&a(6,z=o.label),"container"in o&&a(7,b=o.container),"scale"in o&&a(8,M=o.scale),"min_width"in o&&a(9,i=o.min_width),"loading_status"in o&&a(10,f=o.loading_status),"show_label"in o&&a(11,$=o.show_label),"_selectable"in o&&a(12,w=o._selectable),"show_heading"in o&&a(13,j=o.show_heading)},s.$$.update=()=>{s.$$.dirty&32801&&JSON.stringify(r)!==JSON.stringify(u)&&(a(15,u=r),l.dispatch("change")),s.$$.dirty&32&&a(14,t=r.label)},[l,n,c,d,v,r,z,b,M,i,f,$,w,j,t,u,O,A]}class je extends p{constructor(e){super(),x(this,e,Le,$e,ee,{gradio:0,elem_id:1,elem_classes:2,visible:3,color:4,value:5,label:6,container:7,scale:8,min_width:9,loading_status:10,show_label:11,_selectable:12,show_heading:13})}get gradio(){return this.$$.ctx[0]}set gradio(e){this.$$set({gradio:e}),h()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),h()}get color(){return this.$$.ctx[4]}set color(e){this.$$set({color:e}),h()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),h()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),h()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),h()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),h()}get show_heading(){return this.$$.ctx[13]}set show_heading(e){this.$$set({show_heading:e}),h()}}export{ge as BaseLabel,je as default};
//# sourceMappingURL=Index-B3WwO5Kz.js.map
