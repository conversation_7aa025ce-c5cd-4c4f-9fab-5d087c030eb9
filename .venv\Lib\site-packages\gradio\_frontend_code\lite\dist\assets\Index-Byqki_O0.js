import{a as m,i as g,s as b,f as d,q as h,y as f,z as o,$ as u,A as c,d as v,C as p,u as k,r as w,v as j,k as q,t as x,l as C}from"../lite.js";function y(n){let e,t,r,a;const _=n[4].default,i=h(_,n,n[3],null);return{c(){e=f("div"),t=f("div"),i&&i.c(),o(t,"class","styler svelte-1nguped"),u(t,"--block-radius","0px"),u(t,"--block-border-width","0px"),u(t,"--layout-gap","1px"),u(t,"--form-gap-width","1px"),u(t,"--button-border-width","0px"),u(t,"--button-large-radius","0px"),u(t,"--button-small-radius","0px"),o(e,"id",n[0]),o(e,"class",r="gr-group "+n[1].join(" ")+" svelte-1nguped"),c(e,"hide",!n[2])},m(s,l){v(s,e,l),p(e,t),i&&i.m(t,null),a=!0},p(s,[l]){i&&i.p&&(!a||l&8)&&k(i,_,s,s[3],a?j(_,s[3],l,null):w(s[3]),null),(!a||l&1)&&o(e,"id",s[0]),(!a||l&2&&r!==(r="gr-group "+s[1].join(" ")+" svelte-1nguped"))&&o(e,"class",r),(!a||l&6)&&c(e,"hide",!s[2])},i(s){a||(q(i,s),a=!0)},o(s){x(i,s),a=!1},d(s){s&&C(e),i&&i.d(s)}}}function z(n,e,t){let{$$slots:r={},$$scope:a}=e,{elem_id:_=""}=e,{elem_classes:i=[]}=e,{visible:s=!0}=e;return n.$$set=l=>{"elem_id"in l&&t(0,_=l.elem_id),"elem_classes"in l&&t(1,i=l.elem_classes),"visible"in l&&t(2,s=l.visible),"$$scope"in l&&t(3,a=l.$$scope)},[_,i,s,a,r]}class I extends m{constructor(e){super(),g(this,e,z,y,b,{elem_id:0,elem_classes:1,visible:2})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),d()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),d()}}export{I as default};
//# sourceMappingURL=Index-Byqki_O0.js.map
