import{a as h,i as g,s as y,f as d,y as b,w as m,z as v,P as p,A as r,d as z,C as k,a4 as q,x as C,D as c,l as P,a5 as S,O as w}from"../lite.js";/* empty css                                              */function A(t){let e,a=(t[0]?t[0]:"")+"",n,u;return{c(){e=b("div"),n=m(a),v(e,"class","svelte-1oitfqa"),p(()=>t[5].call(e)),r(e,"table",t[1]==="table"),r(e,"gallery",t[1]==="gallery"),r(e,"selected",t[2])},m(s,l){z(s,e,l),k(e,n),u=q(e,t[5].bind(e)),t[6](e)},p(s,[l]){l&1&&a!==(a=(s[0]?s[0]:"")+"")&&C(n,a),l&2&&r(e,"table",s[1]==="table"),l&2&&r(e,"gallery",s[1]==="gallery"),l&4&&r(e,"selected",s[2])},i:c,o:c,d(s){s&&P(e),u(),t[6](null)}}}function D(t,e){t.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),t.style.whiteSpace="unset"}function E(t,e,a){let{value:n}=e,{type:u}=e,{selected:s=!1}=e,l,f;S(()=>{D(f,l)});function o(){l=this.clientWidth,a(3,l)}function _(i){w[i?"unshift":"push"](()=>{f=i,a(4,f)})}return t.$$set=i=>{"value"in i&&a(0,n=i.value),"type"in i&&a(1,u=i.type),"selected"in i&&a(2,s=i.selected)},[n,u,s,l,f,o,_]}class W extends h{constructor(e){super(),g(this,e,E,A,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),d()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),d()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),d()}}export{W as default};
//# sourceMappingURL=Example-z38AELH2.js.map
