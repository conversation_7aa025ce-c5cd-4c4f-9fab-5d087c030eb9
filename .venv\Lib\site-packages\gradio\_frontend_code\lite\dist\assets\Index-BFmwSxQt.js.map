{"version": 3, "file": "Index-BFmwSxQt.js", "sources": ["../../../uploadbutton/shared/UploadButton.svelte", "../../../uploadbutton/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { tick, createEventDispatcher } from \"svelte\";\n\timport { BaseButton } from \"@gradio/button\";\n\timport { prepare_files, type FileData, type Client } from \"@gradio/client\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label: string | null;\n\texport let value: null | FileData | FileData[];\n\texport let file_count: string;\n\texport let file_types: string[] = [];\n\texport let root: string;\n\texport let size: \"sm\" | \"md\" | \"lg\" = \"lg\";\n\texport let icon: FileData | null = null;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let disabled = false;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\n\tconst dispatch = createEventDispatcher();\n\n\tlet hidden_upload: HTMLInputElement;\n\tlet accept_file_types: string | null;\n\n\tif (file_types == null) {\n\t\taccept_file_types = null;\n\t} else {\n\t\tfile_types = file_types.map((x) => {\n\t\t\tif (x.startsWith(\".\")) {\n\t\t\t\treturn x;\n\t\t\t}\n\t\t\treturn x + \"/*\";\n\t\t});\n\t\taccept_file_types = file_types.join(\", \");\n\t}\n\n\tfunction open_file_upload(): void {\n\t\tdispatch(\"click\");\n\t\thidden_upload.click();\n\t}\n\n\tasync function load_files(files: FileList): Promise<void> {\n\t\tlet _files: File[] = Array.from(files);\n\n\t\tif (!files.length) {\n\t\t\treturn;\n\t\t}\n\t\tif (file_count === \"single\") {\n\t\t\t_files = [files[0]];\n\t\t}\n\t\tlet all_file_data = await prepare_files(_files);\n\t\tawait tick();\n\n\t\ttry {\n\t\t\tall_file_data = (\n\t\t\t\tawait upload(all_file_data, root, undefined, max_file_size ?? Infinity)\n\t\t\t)?.filter((x) => x !== null) as FileData[];\n\t\t} catch (e) {\n\t\t\tdispatch(\"error\", (e as Error).message);\n\t\t\treturn;\n\t\t}\n\t\tvalue = file_count === \"single\" ? all_file_data?.[0] : all_file_data;\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"upload\", value);\n\t}\n\n\tasync function load_files_from_upload(e: Event): Promise<void> {\n\t\tconst target = e.target as HTMLInputElement;\n\n\t\tif (!target.files) return;\n\t\tawait load_files(target.files);\n\t}\n\n\tfunction clear_input_value(e: Event): void {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tif (target.value) target.value = \"\";\n\t}\n</script>\n\n<input\n\tclass=\"hide\"\n\taccept={accept_file_types}\n\ttype=\"file\"\n\tbind:this={hidden_upload}\n\ton:change={load_files_from_upload}\n\ton:click={clear_input_value}\n\tmultiple={file_count === \"multiple\" || undefined}\n\twebkitdirectory={file_count === \"directory\" || undefined}\n\tmozdirectory={file_count === \"directory\" || undefined}\n\tdata-testid=\"{label}-upload-button\"\n/>\n\n<BaseButton\n\t{size}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\ton:click={open_file_upload}\n\t{scale}\n\t{min_width}\n\t{disabled}\n>\n\t{#if icon}\n\t\t<img class=\"button-icon\" src={icon.url} alt={`${value} icon`} />\n\t{/if}\n\t<slot />\n</BaseButton>\n\n<style>\n\t.hide {\n\t\tdisplay: none;\n\t}\n\t.button-icon {\n\t\twidth: var(--text-xl);\n\t\theight: var(--text-xl);\n\t\tmargin-right: var(--spacing-xl);\n\t}\n</style>\n", "<script lang=\"ts\" context=\"module\">\n\texport { default as BaseUploadButton } from \"./shared/UploadButton.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/client\";\n\timport UploadButton from \"./shared/UploadButton.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label: string | null;\n\texport let value: null | FileData | FileData[];\n\texport let file_count: string;\n\texport let file_types: string[] = [];\n\texport let root: string;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: FileData | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tupload: never;\n\t\tclick: never;\n\t\terror: string;\n\t}>;\n\texport let interactive: boolean;\n\n\t$: disabled = !interactive;\n\n\tasync function handle_event(\n\t\tdetail: null | FileData | FileData[],\n\t\tevent: \"change\" | \"upload\" | \"click\"\n\t): Promise<void> {\n\t\tvalue = detail;\n\t\tgradio.dispatch(event);\n\t}\n</script>\n\n<UploadButton\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{file_count}\n\t{file_types}\n\t{size}\n\t{scale}\n\t{icon}\n\t{min_width}\n\t{root}\n\t{value}\n\t{disabled}\n\t{variant}\n\t{label}\n\tmax_file_size={gradio.max_file_size}\n\ton:click={() => gradio.dispatch(\"click\")}\n\ton:change={({ detail }) => handle_event(detail, \"change\")}\n\ton:upload={({ detail }) => handle_event(detail, \"upload\")}\n\ton:error={({ detail }) => {\n\t\tgradio.dispatch(\"error\", detail);\n\t}}\n\tupload={(...args) => gradio.client.upload(...args)}\n>\n\t{label ? gradio.i18n(label) : \"\"}\n</UploadButton>\n"], "names": ["src_url_equal", "img", "img_src_value", "ctx", "attr", "insert", "target", "anchor", "dirty", "create_if_block", "input", "clear_input_value", "e", "elem_id", "$$props", "elem_classes", "visible", "label", "value", "file_count", "file_types", "root", "size", "icon", "scale", "min_width", "variant", "disabled", "max_file_size", "upload", "dispatch", "createEventDispatcher", "hidden_upload", "accept_file_types", "x", "open_file_upload", "load_files", "files", "_files", "all_file_data", "prepare_files", "tick", "$$invalidate", "load_files_from_upload", "$$value", "set_data", "t_value", "uploadbutton_changes", "gradio", "interactive", "handle_event", "detail", "event", "func", "args", "change_handler", "upload_handler"], "mappings": "2dA2GgCA,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,GAAAC,EAAAH,EAAA,MAAAC,CAAA,iBAAUC,EAAK,CAAA,CAAA,OAAA,UAArDE,EAA+DC,EAAAL,EAAAM,CAAA,UAAjCC,EAAA,KAAA,CAAAR,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,+BAAUA,EAAK,CAAA,CAAA,gEADjDA,EAAI,CAAA,GAAAM,EAAAN,CAAA,qIAAJA,EAAI,CAAA,+bALCA,EAAgB,EAAA,CAAA,2FAjBlBA,EAAiB,EAAA,CAAA,kCAKfA,EAAU,CAAA,IAAK,YAAc,+BACtBA,EAAU,CAAA,IAAK,aAAe,MAAS,uBAC1CA,EAAU,CAAA,IAAK,aAAe,MAAS,sBACvCA,EAAK,CAAA,EAAA,gBAAA,UAVpBE,EAWCC,EAAAI,EAAAH,CAAA,sDANWJ,EAAsB,EAAA,CAAA,cACvBQ,EAAiB,8CAJnBR,EAAiB,EAAA,CAAA,oBAKfA,EAAU,CAAA,IAAK,YAAc,2CACtBA,EAAU,CAAA,IAAK,aAAe,qDACjCA,EAAU,CAAA,IAAK,aAAe,kDAC9BA,EAAK,CAAA,EAAA,maAhBVQ,GAAkBC,EAAA,CACpB,MAAAN,EAASM,EAAE,OACbN,EAAO,QAAOA,EAAO,MAAQ,sDAzEvB,QAAAO,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,MAAAG,CAAA,EAAAH,EACA,CAAA,MAAAI,CAAA,EAAAJ,EACA,CAAA,WAAAK,CAAA,EAAAL,EACA,CAAA,WAAAM,EAAA,EAAA,EAAAN,EACA,CAAA,KAAAO,CAAA,EAAAP,GACA,KAAAQ,EAA2B,IAAA,EAAAR,GAC3B,KAAAS,EAAwB,IAAA,EAAAT,GACxB,MAAAU,EAAuB,IAAA,EAAAV,GACvB,UAAAW,EAAgC,MAAA,EAAAX,GAChC,QAAAY,EAA4C,WAAA,EAAAZ,GAC5C,SAAAa,EAAW,EAAA,EAAAb,GACX,cAAAc,EAA+B,IAAA,EAAAd,EAC/B,CAAA,OAAAe,CAAA,EAAAf,QAELgB,EAAWC,IAEb,IAAAC,EACAC,EAEAb,GAAc,KACjBa,EAAoB,MAEpBb,EAAaA,EAAW,IAAKc,GACxBA,EAAE,WAAW,GAAG,EACZA,EAEDA,EAAI,MAEZD,EAAoBb,EAAW,KAAK,IAAI,GAGhC,SAAAe,GAAA,CACRL,EAAS,OAAO,EAChBE,EAAc,MAAA,iBAGAI,EAAWC,EAAA,KACrBC,EAAiB,MAAM,KAAKD,CAAK,MAEhCA,EAAM,cAGPlB,IAAe,WAClBmB,EAAA,CAAUD,EAAM,CAAC,CAAA,GAEd,IAAAE,EAAA,MAAsBC,GAAcF,CAAM,EACxC,MAAAG,GAAA,MAGLF,GACO,MAAAV,EAAOU,EAAelB,EAAA,OAAiBO,GAAiB,GAAQ,IACpE,OAAQM,GAAMA,IAAM,IAAI,CACnB,OAAAtB,EAAA,CACRkB,EAAS,QAAUlB,EAAY,OAAO,SAGvC8B,EAAA,EAAAxB,EAAQC,IAAe,SAAWoB,IAAgB,CAAC,EAAIA,CAAA,EACvDT,EAAS,SAAUZ,CAAK,EACxBY,EAAS,SAAUZ,CAAK,iBAGVyB,EAAuB/B,EAAA,CAC/B,MAAAN,EAASM,EAAE,OAEZN,EAAO,OACN,MAAA8B,EAAW9B,EAAO,KAAK,4CAanB0B,EAAaY,4oECrBvBzC,EAAK,CAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,CAAA,CAAA,EAAI,IAAE,iEAA/BA,EAAK,CAAA,EAAGA,EAAM,EAAA,EAAC,KAAKA,EAAK,CAAA,CAAA,EAAI,IAAE,KAAA0C,GAAA,EAAAC,CAAA,0PATjB,cAAA3C,MAAO,yiBAAPK,EAAA,OAAAuC,EAAA,cAAA5C,MAAO,mMA/CX,QAAAU,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,MAAAG,CAAA,EAAAH,EACA,CAAA,MAAAI,CAAA,EAAAJ,EACA,CAAA,WAAAK,CAAA,EAAAL,EACA,CAAA,WAAAM,EAAA,EAAA,EAAAN,EACA,CAAA,KAAAO,CAAA,EAAAP,GACA,KAAAQ,EAAoB,IAAA,EAAAR,GACpB,MAAAU,EAAuB,IAAA,EAAAV,GACvB,KAAAS,EAAwB,IAAA,EAAAT,GACxB,UAAAW,EAAgC,MAAA,EAAAX,GAChC,QAAAY,EAA4C,WAAA,EAAAZ,EAC5C,CAAA,OAAAkC,CAAA,EAAAlC,EAMA,CAAA,YAAAmC,CAAA,EAAAnC,EAII,eAAAoC,EACdC,EACAC,EAAA,KAEAlC,EAAQiC,CAAA,EACRH,EAAO,SAASI,CAAK,EA0BV,MAAAC,EAAA,IAAAC,IAASN,EAAO,OAAO,UAAUM,CAAI,QANjCN,EAAO,SAAS,OAAO,EACzBO,EAAA,CAAA,CAAA,OAAAJ,CAAM,IAAOD,EAAaC,EAAQ,QAAQ,EAC1CK,EAAA,CAAA,CAAA,OAAAL,CAAM,IAAOD,EAAaC,EAAQ,QAAQ,MAC3C,OAAAA,KAAM,CAClBH,EAAO,SAAS,QAASG,CAAM,iiBA/BhCT,EAAA,GAAGf,EAAY,CAAAsB,CAAA"}