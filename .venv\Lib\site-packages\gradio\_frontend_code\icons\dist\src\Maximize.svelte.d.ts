/** @typedef {typeof __propDef.props}  MaximizeProps */
/** @typedef {typeof __propDef.events}  MaximizeEvents */
/** @typedef {typeof __propDef.slots}  MaximizeSlots */
export default class Maximize extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type MaximizeProps = typeof __propDef.props;
export type MaximizeEvents = typeof __propDef.events;
export type MaximizeSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
