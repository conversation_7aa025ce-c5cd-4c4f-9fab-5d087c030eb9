{"version": 3, "file": "FullscreenButton-DsVuMC2h.js", "sources": ["../../../icons/src/Image.svelte", "../../../atoms/src/FullscreenButton.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-image\"\n>\n\t<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n\t<circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\" />\n\t<polyline points=\"21 15 16 10 5 21\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Maximize, Minimize } from \"@gradio/icons\";\n\n\texport let container: HTMLElement | undefined = undefined;\n\tconst dispatch = createEventDispatcher<{\n\t\tfullscreenchange: boolean;\n\t}>();\n\n\tlet is_full_screen = false;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t\tdispatch(\"fullscreenchange\", is_full_screen);\n\t\t});\n\t});\n\n\tconst toggle_full_screen = async (): Promise<void> => {\n\t\tif (!container) return;\n\n\t\tif (!is_full_screen) {\n\t\t\tawait container.requestFullscreen();\n\t\t} else {\n\t\t\tawait document.exitFullscreen();\n\t\t\tis_full_screen = !is_full_screen;\n\t\t}\n\t};\n</script>\n\n{#if !is_full_screen}\n\t<IconButton\n\t\tIcon={Maximize}\n\t\tlabel=\"View in full screen\"\n\t\ton:click={toggle_full_screen}\n\t/>\n{/if}\n\n{#if is_full_screen}\n\t<IconButton\n\t\tIcon={Minimize}\n\t\tlabel=\"Exit full screen\"\n\t\ton:click={toggle_full_screen}\n\t/>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "rect", "circle", "polyline", "Maximize", "ctx", "Minimize", "create_if_block_1", "create_if_block", "container", "$$props", "dispatch", "createEventDispatcher", "is_full_screen", "onMount", "$$invalidate", "toggle_full_screen"], "mappings": "uwBAAAA,EAeKC,EAAAC,EAAAC,CAAA,EAHJC,EAAwDF,EAAAG,CAAA,EACxDD,EAAmCF,EAAAI,CAAA,EACnCF,EAAqCF,EAAAK,CAAA,gJCmB9BC,+CAEIC,EAAkB,CAAA,CAAA,oLAMtBC,4CAEID,EAAkB,CAAA,CAAA,6JAZxBA,EAAc,CAAA,GAAAE,EAAAF,CAAA,IAQfA,EAAc,CAAA,GAAAG,EAAAH,CAAA,4GARbA,EAAc,CAAA,wGAQfA,EAAc,CAAA,wNAlCP,UAAAI,EAAqC,MAAA,EAAAC,QAC1CC,EAAWC,QAIbC,EAAiB,GAErBC,EAAA,IAAA,CACC,SAAS,iBAAiB,mBAAA,IAAA,CACzBC,EAAA,EAAAF,EAAA,CAAA,CAAmB,SAAS,iBAAA,EAC5BF,EAAS,mBAAoBE,CAAc,MAIvC,MAAAG,EAAA,SAAA,CACAP,IAEAI,SAGE,SAAS,qBACfA,EAAkB,CAAAA,CAAA,SAHZJ,EAAU"}