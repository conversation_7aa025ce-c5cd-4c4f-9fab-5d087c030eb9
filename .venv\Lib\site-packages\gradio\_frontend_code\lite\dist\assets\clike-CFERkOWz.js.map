{"version": 3, "file": "clike-CFERkOWz.js", "sources": ["../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.3.1/node_modules/@codemirror/legacy-modes/mode/clike.js"], "sourcesContent": ["function Context(indented, column, type, info, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.info = info;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type, info) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\" && type != \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, info, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\nfunction typeBefore(stream, state, pos) {\n  if (state.prevToken == \"variable\" || state.prevToken == \"type\") return true;\n  if (/\\S(?:[^- ]>|[*\\]])\\s*$|\\*$/.test(stream.string.slice(0, pos))) return true;\n  if (state.typeAtEndOfLine && stream.column() == stream.indentation()) return true;\n}\n\nfunction isTopScope(context) {\n  for (;;) {\n    if (!context || context.type == \"top\") return true;\n    if (context.type == \"}\" && context.prev.info != \"namespace\") return false;\n    context = context.prev;\n  }\n}\n\nexport function clike(parserConfig) {\n  var statementIndentUnit = parserConfig.statementIndentUnit,\n      dontAlignCalls = parserConfig.dontAlignCalls,\n      keywords = parserConfig.keywords || {},\n      types = parserConfig.types || {},\n      builtin = parserConfig.builtin || {},\n      blockKeywords = parserConfig.blockKeywords || {},\n      defKeywords = parserConfig.defKeywords || {},\n      atoms = parserConfig.atoms || {},\n      hooks = parserConfig.hooks || {},\n      multiLineStrings = parserConfig.multiLineStrings,\n      indentStatements = parserConfig.indentStatements !== false,\n      indentSwitch = parserConfig.indentSwitch !== false,\n      namespaceSeparator = parserConfig.namespaceSeparator,\n      isPunctuationChar = parserConfig.isPunctuationChar || /[\\[\\]{}\\(\\),;\\:\\.]/,\n      numberStart = parserConfig.numberStart || /[\\d\\.]/,\n      number = parserConfig.number || /^(?:0x[a-f\\d]+|0b[01]+|(?:\\d+\\.?\\d*|\\.\\d+)(?:e[-+]?\\d+)?)(u|ll?|l|f)?/i,\n      isOperatorChar = parserConfig.isOperatorChar || /[+\\-*&%=<>!?|\\/]/,\n      isIdentifierChar = parserConfig.isIdentifierChar || /[\\w\\$_\\xa1-\\uffff]/,\n      // An optional function that takes a {string} token and returns true if it\n      // should be treated as a builtin.\n      isReservedIdentifier = parserConfig.isReservedIdentifier || false;\n\n  var curPunc, isDefKeyword;\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    if (numberStart.test(ch)) {\n      stream.backUp(1)\n      if (stream.match(number)) return \"number\"\n      stream.next()\n    }\n    if (isPunctuationChar.test(ch)) {\n      curPunc = ch;\n      return null;\n    }\n    if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      }\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (isOperatorChar.test(ch)) {\n      while (!stream.match(/^\\/[\\/*]/, false) && stream.eat(isOperatorChar)) {}\n      return \"operator\";\n    }\n    stream.eatWhile(isIdentifierChar);\n    if (namespaceSeparator) while (stream.match(namespaceSeparator))\n      stream.eatWhile(isIdentifierChar);\n\n    var cur = stream.current();\n    if (contains(keywords, cur)) {\n      if (contains(blockKeywords, cur)) curPunc = \"newstatement\";\n      if (contains(defKeywords, cur)) isDefKeyword = true;\n      return \"keyword\";\n    }\n    if (contains(types, cur)) return \"type\";\n    if (contains(builtin, cur)\n        || (isReservedIdentifier && isReservedIdentifier(cur))) {\n      if (contains(blockKeywords, cur)) curPunc = \"newstatement\";\n      return \"builtin\";\n    }\n    if (contains(atoms, cur)) return \"atom\";\n    return \"variable\";\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) {end = true; break;}\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = null;\n      return \"string\";\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = null;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return \"comment\";\n  }\n\n  function maybeEOL(stream, state) {\n    if (parserConfig.typeFirstDefinitions && stream.eol() && isTopScope(state.context))\n      state.typeAtEndOfLine = typeBefore(stream, state, stream.pos)\n  }\n\n  // Interface\n\n  return {\n    name: parserConfig.name,\n    startState: function(indentUnit) {\n      return {\n        tokenize: null,\n        context: new Context(-indentUnit, 0, \"top\", null, false),\n        indented: 0,\n        startOfLine: true,\n        prevToken: null\n      };\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (stream.eatSpace()) { maybeEOL(stream, state); return null; }\n      curPunc = isDefKeyword = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\" || style == \"meta\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if (curPunc == \";\" || curPunc == \":\" || (curPunc == \",\" && stream.match(/^\\s*(?:\\/\\/.*)?$/, false)))\n        while (state.context.type == \"statement\") popContext(state);\n      else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n      else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n      else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n      else if (curPunc == \"}\") {\n        while (ctx.type == \"statement\") ctx = popContext(state);\n        if (ctx.type == \"}\") ctx = popContext(state);\n        while (ctx.type == \"statement\") ctx = popContext(state);\n      }\n      else if (curPunc == ctx.type) popContext(state);\n      else if (indentStatements &&\n               (((ctx.type == \"}\" || ctx.type == \"top\") && curPunc != \";\") ||\n                (ctx.type == \"statement\" && curPunc == \"newstatement\"))) {\n        pushContext(state, stream.column(), \"statement\", stream.current());\n      }\n\n      if (style == \"variable\" &&\n          ((state.prevToken == \"def\" ||\n            (parserConfig.typeFirstDefinitions && typeBefore(stream, state, stream.start) &&\n             isTopScope(state.context) && stream.match(/^\\s*\\(/, false)))))\n        style = \"def\";\n\n      if (hooks.token) {\n        var result = hooks.token(stream, state, style);\n        if (result !== undefined) style = result;\n      }\n\n      if (style == \"def\" && parserConfig.styleDefs === false) style = \"variable\";\n\n      state.startOfLine = false;\n      state.prevToken = isDefKeyword ? \"def\" : style || curPunc;\n      maybeEOL(stream, state);\n      return style;\n    },\n\n    indent: function(state, textAfter, context) {\n      if (state.tokenize != tokenBase && state.tokenize != null || state.typeAtEndOfLine) return null;\n      var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n      var closing = firstChar == ctx.type;\n      if (ctx.type == \"statement\" && firstChar == \"}\") ctx = ctx.prev;\n      if (parserConfig.dontIndentStatements)\n        while (ctx.type == \"statement\" && parserConfig.dontIndentStatements.test(ctx.info))\n          ctx = ctx.prev\n      if (hooks.indent) {\n        var hook = hooks.indent(state, ctx, textAfter, context.unit);\n        if (typeof hook == \"number\") return hook\n      }\n      var switchBlock = ctx.prev && ctx.prev.info == \"switch\";\n      if (parserConfig.allmanIndentation && /[{(]/.test(firstChar)) {\n        while (ctx.type != \"top\" && ctx.type != \"}\") ctx = ctx.prev\n        return ctx.indented\n      }\n      if (ctx.type == \"statement\")\n        return ctx.indented + (firstChar == \"{\" ? 0 : statementIndentUnit || context.unit);\n      if (ctx.align && (!dontAlignCalls || ctx.type != \")\"))\n        return ctx.column + (closing ? 0 : 1);\n      if (ctx.type == \")\" && !closing)\n        return ctx.indented + (statementIndentUnit || context.unit);\n\n      return ctx.indented + (closing ? 0 : context.unit) +\n        (!closing && switchBlock && !/^(?:case|default)\\b/.test(textAfter) ? context.unit : 0);\n    },\n\n    languageData: {\n      indentOnInput: indentSwitch ? /^\\s*(?:case .*?:|default:|\\{\\}?|\\})$/ : /^\\s*[{}]$/,\n      commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n      autocomplete: Object.keys(keywords).concat(Object.keys(types)).concat(Object.keys(builtin)).concat(Object.keys(atoms)),\n      ...parserConfig.languageData\n    }\n  };\n};\n\nfunction words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nfunction contains(words, word) {\n  if (typeof words === \"function\") {\n    return words(word);\n  } else {\n    return words.propertyIsEnumerable(word);\n  }\n}\nvar cKeywords = \"auto if break case register continue return default do sizeof \" +\n    \"static else struct switch extern typedef union for goto while enum const \" +\n    \"volatile inline restrict asm fortran\";\n\n// Keywords from https://en.cppreference.com/w/cpp/keyword includes C++20.\nvar cppKeywords = \"alignas alignof and and_eq audit axiom bitand bitor catch \" +\n    \"class compl concept constexpr const_cast decltype delete dynamic_cast \" +\n    \"explicit export final friend import module mutable namespace new noexcept \" +\n    \"not not_eq operator or or_eq override private protected public \" +\n    \"reinterpret_cast requires static_assert static_cast template this \" +\n    \"thread_local throw try typeid typename using virtual xor xor_eq\";\n\nvar objCKeywords = \"bycopy byref in inout oneway out self super atomic nonatomic retain copy \" +\n    \"readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd \" +\n    \"@interface @implementation @end @protocol @encode @property @synthesize @dynamic @class \" +\n    \"@public @package @private @protected @required @optional @try @catch @finally @import \" +\n    \"@selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available\";\n\nvar objCBuiltins = \"FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION \" +\n    \" NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER \" +\n    \"NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION \" +\n    \"NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT\"\n\n// Do not use this. Use the cTypes function below. This is global just to avoid\n// excessive calls when cTypes is being called multiple times during a parse.\nvar basicCTypes = words(\"int long char short double float unsigned signed \" +\n                        \"void bool\");\n\n// Do not use this. Use the objCTypes function below. This is global just to avoid\n// excessive calls when objCTypes is being called multiple times during a parse.\nvar basicObjCTypes = words(\"SEL instancetype id Class Protocol BOOL\");\n\n// Returns true if identifier is a \"C\" type.\n// C type is defined as those that are reserved by the compiler (basicTypes),\n// and those that end in _t (Reserved by POSIX for types)\n// http://www.gnu.org/software/libc/manual/html_node/Reserved-Names.html\nfunction cTypes(identifier) {\n  return contains(basicCTypes, identifier) || /.+_t$/.test(identifier);\n}\n\n// Returns true if identifier is a \"Objective C\" type.\nfunction objCTypes(identifier) {\n  return cTypes(identifier) || contains(basicObjCTypes, identifier);\n}\n\nvar cBlockKeywords = \"case do else for if switch while struct enum union\";\nvar cDefKeywords = \"struct enum union\";\n\nfunction cppHook(stream, state) {\n  if (!state.startOfLine) return false\n  for (var ch, next = null; ch = stream.peek();) {\n    if (ch == \"\\\\\" && stream.match(/^.$/)) {\n      next = cppHook\n      break\n    } else if (ch == \"/\" && stream.match(/^\\/[\\/\\*]/, false)) {\n      break\n    }\n    stream.next()\n  }\n  state.tokenize = next\n  return \"meta\"\n}\n\nfunction pointerHook(_stream, state) {\n  if (state.prevToken == \"type\") return \"type\";\n  return false;\n}\n\n// For C and C++ (and ObjC): identifiers starting with __\n// or _ followed by a capital letter are reserved for the compiler.\nfunction cIsReservedIdentifier(token) {\n  if (!token || token.length < 2) return false;\n  if (token[0] != '_') return false;\n  return (token[1] == '_') || (token[1] !== token[1].toLowerCase());\n}\n\nfunction cpp14Literal(stream) {\n  stream.eatWhile(/[\\w\\.']/);\n  return \"number\";\n}\n\nfunction cpp11StringHook(stream, state) {\n  stream.backUp(1);\n  // Raw strings.\n  if (stream.match(/^(?:R|u8R|uR|UR|LR)/)) {\n    var match = stream.match(/^\"([^\\s\\\\()]{0,16})\\(/);\n    if (!match) {\n      return false;\n    }\n    state.cpp11RawStringDelim = match[1];\n    state.tokenize = tokenRawString;\n    return tokenRawString(stream, state);\n  }\n  // Unicode strings/chars.\n  if (stream.match(/^(?:u8|u|U|L)/)) {\n    if (stream.match(/^[\"']/, /* eat */ false)) {\n      return \"string\";\n    }\n    return false;\n  }\n  // Ignore this hook.\n  stream.next();\n  return false;\n}\n\nfunction cppLooksLikeConstructor(word) {\n  var lastTwo = /(\\w+)::~?(\\w+)$/.exec(word);\n  return lastTwo && lastTwo[1] == lastTwo[2];\n}\n\n// C#-style strings where \"\" escapes a quote.\nfunction tokenAtString(stream, state) {\n  var next;\n  while ((next = stream.next()) != null) {\n    if (next == '\"' && !stream.eat('\"')) {\n      state.tokenize = null;\n      break;\n    }\n  }\n  return \"string\";\n}\n\n// C++11 raw string literal is <prefix>\"<delim>( anything )<delim>\", where\n// <delim> can be a string up to 16 characters long.\nfunction tokenRawString(stream, state) {\n  // Escape characters that have special regex meanings.\n  var delim = state.cpp11RawStringDelim.replace(/[^\\w\\s]/g, '\\\\$&');\n  var match = stream.match(new RegExp(\".*?\\\\)\" + delim + '\"'));\n  if (match)\n    state.tokenize = null;\n  else\n    stream.skipToEnd();\n  return \"string\";\n}\n\nexport const c = clike({\n  name: \"c\",\n  keywords: words(cKeywords),\n  types: cTypes,\n  blockKeywords: words(cBlockKeywords),\n  defKeywords: words(cDefKeywords),\n  typeFirstDefinitions: true,\n  atoms: words(\"NULL true false\"),\n  isReservedIdentifier: cIsReservedIdentifier,\n  hooks: {\n    \"#\": cppHook,\n    \"*\": pointerHook,\n  }\n})\n\nexport const cpp = clike({\n  name: \"cpp\",\n  keywords: words(cKeywords + \" \" + cppKeywords),\n  types: cTypes,\n  blockKeywords: words(cBlockKeywords + \" class try catch\"),\n  defKeywords: words(cDefKeywords + \" class namespace\"),\n  typeFirstDefinitions: true,\n  atoms: words(\"true false NULL nullptr\"),\n  dontIndentStatements: /^template$/,\n  isIdentifierChar: /[\\w\\$_~\\xa1-\\uffff]/,\n  isReservedIdentifier: cIsReservedIdentifier,\n  hooks: {\n    \"#\": cppHook,\n    \"*\": pointerHook,\n    \"u\": cpp11StringHook,\n    \"U\": cpp11StringHook,\n    \"L\": cpp11StringHook,\n    \"R\": cpp11StringHook,\n    \"0\": cpp14Literal,\n    \"1\": cpp14Literal,\n    \"2\": cpp14Literal,\n    \"3\": cpp14Literal,\n    \"4\": cpp14Literal,\n    \"5\": cpp14Literal,\n    \"6\": cpp14Literal,\n    \"7\": cpp14Literal,\n    \"8\": cpp14Literal,\n    \"9\": cpp14Literal,\n    token: function(stream, state, style) {\n      if (style == \"variable\" && stream.peek() == \"(\" &&\n          (state.prevToken == \";\" || state.prevToken == null ||\n           state.prevToken == \"}\") &&\n          cppLooksLikeConstructor(stream.current()))\n        return \"def\";\n    }\n  },\n  namespaceSeparator: \"::\"\n});\n\nexport const java = clike({\n  name: \"java\",\n  keywords: words(\"abstract assert break case catch class const continue default \" +\n                  \"do else enum extends final finally for goto if implements import \" +\n                  \"instanceof interface native new package private protected public \" +\n                  \"return static strictfp super switch synchronized this throw throws transient \" +\n                  \"try volatile while @interface\"),\n  types: words(\"var byte short int long float double boolean char void Boolean Byte Character Double Float \" +\n               \"Integer Long Number Object Short String StringBuffer StringBuilder Void\"),\n  blockKeywords: words(\"catch class do else finally for if switch try while\"),\n  defKeywords: words(\"class interface enum @interface\"),\n  typeFirstDefinitions: true,\n  atoms: words(\"true false null\"),\n  number: /^(?:0x[a-f\\d_]+|0b[01_]+|(?:[\\d_]+\\.?\\d*|\\.\\d+)(?:e[-+]?[\\d_]+)?)(u|ll?|l|f)?/i,\n  hooks: {\n    \"@\": function(stream) {\n      // Don't match the @interface keyword.\n      if (stream.match('interface', false)) return false;\n\n      stream.eatWhile(/[\\w\\$_]/);\n      return \"meta\";\n    },\n    '\"': function(stream, state) {\n      if (!stream.match(/\"\"$/)) return false;\n      state.tokenize = tokenTripleString;\n      return state.tokenize(stream, state);\n    }\n  }\n})\n\nexport const csharp = clike({\n  name: \"csharp\",\n  keywords: words(\"abstract as async await base break case catch checked class const continue\" +\n                  \" default delegate do else enum event explicit extern finally fixed for\" +\n                  \" foreach goto if implicit in interface internal is lock namespace new\" +\n                  \" operator out override params private protected public readonly ref return sealed\" +\n                  \" sizeof stackalloc static struct switch this throw try typeof unchecked\" +\n                  \" unsafe using virtual void volatile while add alias ascending descending dynamic from get\" +\n                  \" global group into join let orderby partial remove select set value var yield\"),\n  types: words(\"Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func\" +\n               \" Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32\" +\n               \" UInt64 bool byte char decimal double short int long object\"  +\n               \" sbyte float string ushort uint ulong\"),\n  blockKeywords: words(\"catch class do else finally for foreach if struct switch try while\"),\n  defKeywords: words(\"class interface namespace struct var\"),\n  typeFirstDefinitions: true,\n  atoms: words(\"true false null\"),\n  hooks: {\n    \"@\": function(stream, state) {\n      if (stream.eat('\"')) {\n        state.tokenize = tokenAtString;\n        return tokenAtString(stream, state);\n      }\n      stream.eatWhile(/[\\w\\$_]/);\n      return \"meta\";\n    }\n  }\n});\n\nfunction tokenTripleString(stream, state) {\n  var escaped = false;\n  while (!stream.eol()) {\n    if (!escaped && stream.match('\"\"\"')) {\n      state.tokenize = null;\n      break;\n    }\n    escaped = stream.next() == \"\\\\\" && !escaped;\n  }\n  return \"string\";\n}\n\nfunction tokenNestedComment(depth) {\n  return function (stream, state) {\n    var ch\n    while (ch = stream.next()) {\n      if (ch == \"*\" && stream.eat(\"/\")) {\n        if (depth == 1) {\n          state.tokenize = null\n          break\n        } else {\n          state.tokenize = tokenNestedComment(depth - 1)\n          return state.tokenize(stream, state)\n        }\n      } else if (ch == \"/\" && stream.eat(\"*\")) {\n        state.tokenize = tokenNestedComment(depth + 1)\n        return state.tokenize(stream, state)\n      }\n    }\n    return \"comment\"\n  }\n}\n\nexport const scala = clike({\n  name: \"scala\",\n  keywords: words(\n    /* scala */\n    \"abstract case catch class def do else extends final finally for forSome if \" +\n      \"implicit import lazy match new null object override package private protected return \" +\n      \"sealed super this throw trait try type val var while with yield _ \" +\n\n    /* package scala */\n    \"assert assume require print println printf readLine readBoolean readByte readShort \" +\n      \"readChar readInt readLong readFloat readDouble\"\n  ),\n  types: words(\n    \"AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either \" +\n      \"Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable \" +\n      \"Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering \" +\n      \"Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder \" +\n      \"StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector \" +\n\n    /* package java.lang */\n    \"Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable \" +\n      \"Compiler Double Exception Float Integer Long Math Number Object Package Pair Process \" +\n      \"Runtime Runnable SecurityManager Short StackTraceElement StrictMath String \" +\n      \"StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void\"\n  ),\n  multiLineStrings: true,\n  blockKeywords: words(\"catch class enum do else finally for forSome if match switch try while\"),\n  defKeywords: words(\"class enum def object package trait type val var\"),\n  atoms: words(\"true false null\"),\n  indentStatements: false,\n  indentSwitch: false,\n  isOperatorChar: /[+\\-*&%=<>!?|\\/#:@]/,\n  hooks: {\n    \"@\": function(stream) {\n      stream.eatWhile(/[\\w\\$_]/);\n      return \"meta\";\n    },\n    '\"': function(stream, state) {\n      if (!stream.match('\"\"')) return false;\n      state.tokenize = tokenTripleString;\n      return state.tokenize(stream, state);\n    },\n    \"'\": function(stream) {\n      stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n      return \"atom\";\n    },\n    \"=\": function(stream, state) {\n      var cx = state.context\n      if (cx.type == \"}\" && cx.align && stream.eat(\">\")) {\n        state.context = new Context(cx.indented, cx.column, cx.type, cx.info, null, cx.prev)\n        return \"operator\"\n      } else {\n        return false\n      }\n    },\n\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false\n      state.tokenize = tokenNestedComment(1)\n      return state.tokenize(stream, state)\n    }\n  },\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', '\"\"\"']}\n  }\n});\n\nfunction tokenKotlinString(tripleString){\n  return function (stream, state) {\n    var escaped = false, next, end = false;\n    while (!stream.eol()) {\n      if (!tripleString && !escaped && stream.match('\"') ) {end = true; break;}\n      if (tripleString && stream.match('\"\"\"')) {end = true; break;}\n      next = stream.next();\n      if(!escaped && next == \"$\" && stream.match('{'))\n        stream.skipTo(\"}\");\n      escaped = !escaped && next == \"\\\\\" && !tripleString;\n    }\n    if (end || !tripleString)\n      state.tokenize = null;\n    return \"string\";\n  }\n}\n\nexport const kotlin = clike({\n  name: \"kotlin\",\n  keywords: words(\n    /*keywords*/\n    \"package as typealias class interface this super val operator \" +\n      \"var fun for is in This throw return annotation \" +\n      \"break continue object if else while do try when !in !is as? \" +\n\n    /*soft keywords*/\n    \"file import where by get set abstract enum open inner override private public internal \" +\n      \"protected catch finally out final vararg reified dynamic companion constructor init \" +\n      \"sealed field property receiver param sparam lateinit data inline noinline tailrec \" +\n      \"external annotation crossinline const operator infix suspend actual expect setparam\"\n  ),\n  types: words(\n    /* package java.lang */\n    \"Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable \" +\n      \"Compiler Double Exception Float Integer Long Math Number Object Package Pair Process \" +\n      \"Runtime Runnable SecurityManager Short StackTraceElement StrictMath String \" +\n      \"StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray \" +\n      \"ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy \" +\n      \"LazyThreadSafetyMode LongArray Nothing ShortArray Unit\"\n  ),\n  intendSwitch: false,\n  indentStatements: false,\n  multiLineStrings: true,\n  number: /^(?:0x[a-f\\d_]+|0b[01_]+|(?:[\\d_]+(\\.\\d+)?|\\.\\d+)(?:e[-+]?[\\d_]+)?)(u|ll?|l|f)?/i,\n  blockKeywords: words(\"catch class do else finally for if where try while enum\"),\n  defKeywords: words(\"class val var object interface fun\"),\n  atoms: words(\"true false null this\"),\n  hooks: {\n    \"@\": function(stream) {\n      stream.eatWhile(/[\\w\\$_]/);\n      return \"meta\";\n    },\n    '*': function(_stream, state) {\n      return state.prevToken == '.' ? 'variable' : 'operator';\n    },\n    '\"': function(stream, state) {\n      state.tokenize = tokenKotlinString(stream.match('\"\"'));\n      return state.tokenize(stream, state);\n    },\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenNestedComment(1);\n      return state.tokenize(stream, state)\n    },\n    indent: function(state, ctx, textAfter, indentUnit) {\n      var firstChar = textAfter && textAfter.charAt(0);\n      if ((state.prevToken == \"}\" || state.prevToken == \")\") && textAfter == \"\")\n        return state.indented;\n      if ((state.prevToken == \"operator\" && textAfter != \"}\" && state.context.type != \"}\") ||\n          state.prevToken == \"variable\" && firstChar == \".\" ||\n          (state.prevToken == \"}\" || state.prevToken == \")\") && firstChar == \".\")\n        return indentUnit * 2 + ctx.indented;\n      if (ctx.align && ctx.type == \"}\")\n        return ctx.indented + (state.context.type == (textAfter || \"\").charAt(0) ? 0 : indentUnit);\n    }\n  },\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', '\"\"\"']}\n  }\n});\n\nexport const shader = clike({\n  name: \"shader\",\n  keywords: words(\"sampler1D sampler2D sampler3D samplerCube \" +\n                  \"sampler1DShadow sampler2DShadow \" +\n                  \"const attribute uniform varying \" +\n                  \"break continue discard return \" +\n                  \"for while do if else struct \" +\n                  \"in out inout\"),\n  types: words(\"float int bool void \" +\n               \"vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 \" +\n               \"mat2 mat3 mat4\"),\n  blockKeywords: words(\"for while do if else struct\"),\n  builtin: words(\"radians degrees sin cos tan asin acos atan \" +\n                 \"pow exp log exp2 sqrt inversesqrt \" +\n                 \"abs sign floor ceil fract mod min max clamp mix step smoothstep \" +\n                 \"length distance dot cross normalize ftransform faceforward \" +\n                 \"reflect refract matrixCompMult \" +\n                 \"lessThan lessThanEqual greaterThan greaterThanEqual \" +\n                 \"equal notEqual any all not \" +\n                 \"texture1D texture1DProj texture1DLod texture1DProjLod \" +\n                 \"texture2D texture2DProj texture2DLod texture2DProjLod \" +\n                 \"texture3D texture3DProj texture3DLod texture3DProjLod \" +\n                 \"textureCube textureCubeLod \" +\n                 \"shadow1D shadow2D shadow1DProj shadow2DProj \" +\n                 \"shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod \" +\n                 \"dFdx dFdy fwidth \" +\n                 \"noise1 noise2 noise3 noise4\"),\n  atoms: words(\"true false \" +\n               \"gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex \" +\n               \"gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 \" +\n               \"gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 \" +\n               \"gl_FogCoord gl_PointCoord \" +\n               \"gl_Position gl_PointSize gl_ClipVertex \" +\n               \"gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor \" +\n               \"gl_TexCoord gl_FogFragCoord \" +\n               \"gl_FragCoord gl_FrontFacing \" +\n               \"gl_FragData gl_FragDepth \" +\n               \"gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix \" +\n               \"gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse \" +\n               \"gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse \" +\n               \"gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose \" +\n               \"gl_ProjectionMatrixInverseTranspose \" +\n               \"gl_ModelViewProjectionMatrixInverseTranspose \" +\n               \"gl_TextureMatrixInverseTranspose \" +\n               \"gl_NormalScale gl_DepthRange gl_ClipPlane \" +\n               \"gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel \" +\n               \"gl_FrontLightModelProduct gl_BackLightModelProduct \" +\n               \"gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ \" +\n               \"gl_FogParameters \" +\n               \"gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords \" +\n               \"gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats \" +\n               \"gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits \" +\n               \"gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits \" +\n               \"gl_MaxDrawBuffers\"),\n  indentSwitch: false,\n  hooks: {\"#\": cppHook}\n})\n\nexport const nesC = clike({\n  name: \"nesc\",\n  keywords: words(cKeywords + \" as atomic async call command component components configuration event generic \" +\n                  \"implementation includes interface module new norace nx_struct nx_union post provides \" +\n                  \"signal task uses abstract extends\"),\n  types: cTypes,\n  blockKeywords: words(cBlockKeywords),\n  atoms: words(\"null true false\"),\n  hooks: {\"#\": cppHook}\n})\n\nexport const objectiveC = clike({\n  name: \"objectivec\",\n  keywords: words(cKeywords + \" \" + objCKeywords),\n  types: objCTypes,\n  builtin: words(objCBuiltins),\n  blockKeywords: words(cBlockKeywords + \" @synthesize @try @catch @finally @autoreleasepool @synchronized\"),\n  defKeywords: words(cDefKeywords + \" @interface @implementation @protocol @class\"),\n  dontIndentStatements: /^@.*$/,\n  typeFirstDefinitions: true,\n  atoms: words(\"YES NO NULL Nil nil true false nullptr\"),\n  isReservedIdentifier: cIsReservedIdentifier,\n  hooks: {\n    \"#\": cppHook,\n    \"*\": pointerHook,\n  }\n})\n\nexport const objectiveCpp = clike({\n  name: \"objectivecpp\",\n  keywords: words(cKeywords + \" \" + objCKeywords + \" \" + cppKeywords),\n  types: objCTypes,\n  builtin: words(objCBuiltins),\n  blockKeywords: words(cBlockKeywords + \" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch\"),\n  defKeywords: words(cDefKeywords + \" @interface @implementation @protocol @class class namespace\"),\n  dontIndentStatements: /^@.*$|^template$/,\n  typeFirstDefinitions: true,\n  atoms: words(\"YES NO NULL Nil nil true false nullptr\"),\n  isReservedIdentifier: cIsReservedIdentifier,\n  hooks: {\n    \"#\": cppHook,\n    \"*\": pointerHook,\n    \"u\": cpp11StringHook,\n    \"U\": cpp11StringHook,\n    \"L\": cpp11StringHook,\n    \"R\": cpp11StringHook,\n    \"0\": cpp14Literal,\n    \"1\": cpp14Literal,\n    \"2\": cpp14Literal,\n    \"3\": cpp14Literal,\n    \"4\": cpp14Literal,\n    \"5\": cpp14Literal,\n    \"6\": cpp14Literal,\n    \"7\": cpp14Literal,\n    \"8\": cpp14Literal,\n    \"9\": cpp14Literal,\n    token: function(stream, state, style) {\n      if (style == \"variable\" && stream.peek() == \"(\" &&\n          (state.prevToken == \";\" || state.prevToken == null ||\n           state.prevToken == \"}\") &&\n          cppLooksLikeConstructor(stream.current()))\n        return \"def\";\n    }\n  },\n  namespaceSeparator: \"::\"\n})\n\nexport const squirrel = clike({\n  name: \"squirrel\",\n  keywords: words(\"base break clone continue const default delete enum extends function in class\" +\n                  \" foreach local resume return this throw typeof yield constructor instanceof static\"),\n  types: cTypes,\n  blockKeywords: words(\"case catch class else for foreach if switch try while\"),\n  defKeywords: words(\"function local class\"),\n  typeFirstDefinitions: true,\n  atoms: words(\"true false null\"),\n  hooks: {\"#\": cppHook}\n})\n\n// Ceylon Strings need to deal with interpolation\nvar stringTokenizer = null;\nfunction tokenCeylonString(type) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while (!stream.eol()) {\n      if (!escaped && stream.match('\"') &&\n          (type == \"single\" || stream.match('\"\"'))) {\n        end = true;\n        break;\n      }\n      if (!escaped && stream.match('``')) {\n        stringTokenizer = tokenCeylonString(type);\n        end = true;\n        break;\n      }\n      next = stream.next();\n      escaped = type == \"single\" && !escaped && next == \"\\\\\";\n    }\n    if (end)\n      state.tokenize = null;\n    return \"string\";\n  }\n}\n\nexport const ceylon = clike({\n  name: \"ceylon\",\n  keywords: words(\"abstracts alias assembly assert assign break case catch class continue dynamic else\" +\n                  \" exists extends finally for function given if import in interface is let module new\" +\n                  \" nonempty object of out outer package return satisfies super switch then this throw\" +\n                  \" try value void while\"),\n  types: function(word) {\n    // In Ceylon all identifiers that start with an uppercase are types\n    var first = word.charAt(0);\n    return (first === first.toUpperCase() && first !== first.toLowerCase());\n  },\n  blockKeywords: words(\"case catch class dynamic else finally for function if interface module new object switch try while\"),\n  defKeywords: words(\"class dynamic function interface module object package value\"),\n  builtin: words(\"abstract actual aliased annotation by default deprecated doc final formal late license\" +\n                 \" native optional sealed see serializable shared suppressWarnings tagged throws variable\"),\n  isPunctuationChar: /[\\[\\]{}\\(\\),;\\:\\.`]/,\n  isOperatorChar: /[+\\-*&%=<>!?|^~:\\/]/,\n  numberStart: /[\\d#$]/,\n  number: /^(?:#[\\da-fA-F_]+|\\$[01_]+|[\\d_]+[kMGTPmunpf]?|[\\d_]+\\.[\\d_]+(?:[eE][-+]?\\d+|[kMGTPmunpf]|)|)/i,\n  multiLineStrings: true,\n  typeFirstDefinitions: true,\n  atoms: words(\"true false null larger smaller equal empty finished\"),\n  indentSwitch: false,\n  styleDefs: false,\n  hooks: {\n    \"@\": function(stream) {\n      stream.eatWhile(/[\\w\\$_]/);\n      return \"meta\";\n    },\n    '\"': function(stream, state) {\n      state.tokenize = tokenCeylonString(stream.match('\"\"') ? \"triple\" : \"single\");\n      return state.tokenize(stream, state);\n    },\n    '`': function(stream, state) {\n      if (!stringTokenizer || !stream.match('`')) return false;\n      state.tokenize = stringTokenizer;\n      stringTokenizer = null;\n      return state.tokenize(stream, state);\n    },\n    \"'\": function(stream) {\n      stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n      return \"atom\";\n    },\n    token: function(_stream, state, style) {\n      if ((style == \"variable\" || style == \"type\") &&\n          state.prevToken == \".\") {\n        return \"variableName.special\";\n      }\n    }\n  },\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', '\"\"\"']}\n  }\n})\n\nfunction pushInterpolationStack(state) {\n  (state.interpolationStack || (state.interpolationStack = [])).push(state.tokenize);\n}\n\nfunction popInterpolationStack(state) {\n  return (state.interpolationStack || (state.interpolationStack = [])).pop();\n}\n\nfunction sizeInterpolationStack(state) {\n  return state.interpolationStack ? state.interpolationStack.length : 0;\n}\n\nfunction tokenDartString(quote, stream, state, raw) {\n  var tripleQuoted = false;\n  if (stream.eat(quote)) {\n    if (stream.eat(quote)) tripleQuoted = true;\n    else return \"string\"; //empty string\n  }\n  function tokenStringHelper(stream, state) {\n    var escaped = false;\n    while (!stream.eol()) {\n      if (!raw && !escaped && stream.peek() == \"$\") {\n        pushInterpolationStack(state);\n        state.tokenize = tokenInterpolation;\n        return \"string\";\n      }\n      var next = stream.next();\n      if (next == quote && !escaped && (!tripleQuoted || stream.match(quote + quote))) {\n        state.tokenize = null;\n        break;\n      }\n      escaped = !raw && !escaped && next == \"\\\\\";\n    }\n    return \"string\";\n  }\n  state.tokenize = tokenStringHelper;\n  return tokenStringHelper(stream, state);\n}\n\nfunction tokenInterpolation(stream, state) {\n  stream.eat(\"$\");\n  if (stream.eat(\"{\")) {\n    // let clike handle the content of ${...},\n    // we take over again when \"}\" appears (see hooks).\n    state.tokenize = null;\n  } else {\n    state.tokenize = tokenInterpolationIdentifier;\n  }\n  return null;\n}\n\nfunction tokenInterpolationIdentifier(stream, state) {\n  stream.eatWhile(/[\\w_]/);\n  state.tokenize = popInterpolationStack(state);\n  return \"variable\";\n}\n\nexport const dart = clike({\n  name: \"dart\",\n  keywords: words(\"this super static final const abstract class extends external factory \" +\n                  \"implements mixin get native set typedef with enum throw rethrow \" +\n                  \"assert break case continue default in return new deferred async await covariant \" +\n                  \"try catch finally do else for if switch while import library export \" +\n                  \"part of show hide is as extension on yield late required\"),\n  blockKeywords: words(\"try catch finally do else for if switch while\"),\n  builtin: words(\"void bool num int double dynamic var String Null Never\"),\n  atoms: words(\"true false null\"),\n  hooks: {\n    \"@\": function(stream) {\n      stream.eatWhile(/[\\w\\$_\\.]/);\n      return \"meta\";\n    },\n\n    // custom string handling to deal with triple-quoted strings and string interpolation\n    \"'\": function(stream, state) {\n      return tokenDartString(\"'\", stream, state, false);\n    },\n    \"\\\"\": function(stream, state) {\n      return tokenDartString(\"\\\"\", stream, state, false);\n    },\n    \"r\": function(stream, state) {\n      var peek = stream.peek();\n      if (peek == \"'\" || peek == \"\\\"\") {\n        return tokenDartString(stream.next(), stream, state, true);\n      }\n      return false;\n    },\n\n    \"}\": function(_stream, state) {\n      // \"}\" is end of interpolation, if interpolation stack is non-empty\n      if (sizeInterpolationStack(state) > 0) {\n        state.tokenize = popInterpolationStack(state);\n        return null;\n      }\n      return false;\n    },\n\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false\n      state.tokenize = tokenNestedComment(1)\n      return state.tokenize(stream, state)\n    },\n    token: function(stream, _, style) {\n      if (style == \"variable\") {\n        // Assume uppercase symbols are classes\n        var isUpper = RegExp('^[_$]*[A-Z][a-zA-Z0-9_$]*$','g');\n        if (isUpper.test(stream.current())) {\n          return 'type';\n        }\n      }\n    }\n  }\n})\n"], "names": ["Context", "indented", "column", "type", "info", "align", "prev", "pushContext", "state", "col", "indent", "popContext", "typeBefore", "stream", "pos", "isTopScope", "context", "clike", "parserConfig", "statementIndentUnit", "dontAlignCalls", "keywords", "types", "builtin", "blockKeywords", "defKeywords", "atoms", "hooks", "multiLineStrings", "indentStatements", "indentSwitch", "namespaceSeparator", "isPunctuationChar", "numberStart", "number", "isOperatorChar", "isIdentifierChar", "isReservedIdentifier", "curPunc", "isDefKeyword", "tokenBase", "ch", "result", "tokenString", "tokenComment", "cur", "contains", "quote", "escaped", "next", "end", "maybeEnd", "maybeEOL", "indentUnit", "ctx", "style", "textAfter", "firstChar", "closing", "hook", "switchBlock", "words", "str", "obj", "i", "word", "cKeywords", "cppKeywords", "objCKeywords", "objCBuiltins", "basicCTypes", "basicObjCTypes", "cTypes", "identifier", "objCTypes", "cBlockKeywords", "cDefKeywords", "cppHook", "pointer<PERSON>ook", "_stream", "cIsReservedIdentifier", "token", "cpp14Literal", "cpp11StringHook", "match", "tokenRawString", "cppLooksLikeConstructor", "lastTwo", "tokenAtString", "delim", "c", "cpp", "java", "tokenTripleString", "csharp", "tokenNestedComment", "depth", "scala", "cx", "tokenKotlinString", "tripleString", "kotlin", "shader", "nesC", "objectiveC", "objectiveCpp", "squirrel", "stringTokenizer", "tokenCeylonString", "ceylon", "first", "pushInterpolationStack", "popInterpolationStack", "sizeInterpolationStack", "tokenDartString", "raw", "tripleQuoted", "tokenStringHelper", "tokenInterpolation", "tokenInterpolationIdentifier", "dart", "peek", "_", "isUpper"], "mappings": "AAAA,SAASA,EAAQC,EAAUC,EAAQC,EAAMC,EAAMC,EAAOC,EAAM,CAC1D,KAAK,SAAWL,EAChB,KAAK,OAASC,EACd,KAAK,KAAOC,EACZ,KAAK,KAAOC,EACZ,KAAK,MAAQC,EACb,KAAK,KAAOC,CACd,CACA,SAASC,EAAYC,EAAOC,EAAKN,EAAMC,EAAM,CAC3C,IAAIM,EAASF,EAAM,SACnB,OAAIA,EAAM,SAAWA,EAAM,QAAQ,MAAQ,aAAeL,GAAQ,cAChEO,EAASF,EAAM,QAAQ,UAClBA,EAAM,QAAU,IAAIR,EAAQU,EAAQD,EAAKN,EAAMC,EAAM,KAAMI,EAAM,OAAO,CACjF,CACA,SAASG,EAAWH,EAAO,CACzB,IAAI,EAAIA,EAAM,QAAQ,KACtB,OAAI,GAAK,KAAO,GAAK,KAAO,GAAK,OAC/BA,EAAM,SAAWA,EAAM,QAAQ,UAC1BA,EAAM,QAAUA,EAAM,QAAQ,IACvC,CAEA,SAASI,EAAWC,EAAQL,EAAOM,EAAK,CAGtC,GAFIN,EAAM,WAAa,YAAcA,EAAM,WAAa,QACpD,6BAA6B,KAAKK,EAAO,OAAO,MAAM,EAAGC,CAAG,CAAC,GAC7DN,EAAM,iBAAmBK,EAAO,OAAQ,GAAIA,EAAO,cAAe,MAAO,EAC/E,CAEA,SAASE,EAAWC,EAAS,CAC3B,OAAS,CACP,GAAI,CAACA,GAAWA,EAAQ,MAAQ,MAAO,MAAO,GAC9C,GAAIA,EAAQ,MAAQ,KAAOA,EAAQ,KAAK,MAAQ,YAAa,MAAO,GACpEA,EAAUA,EAAQ,IACnB,CACH,CAEO,SAASC,EAAMC,EAAc,CAClC,IAAIC,EAAsBD,EAAa,oBACnCE,EAAiBF,EAAa,eAC9BG,EAAWH,EAAa,UAAY,CAAE,EACtCI,EAAQJ,EAAa,OAAS,CAAE,EAChCK,EAAUL,EAAa,SAAW,CAAE,EACpCM,EAAgBN,EAAa,eAAiB,CAAE,EAChDO,EAAcP,EAAa,aAAe,CAAE,EAC5CQ,EAAQR,EAAa,OAAS,CAAE,EAChCS,EAAQT,EAAa,OAAS,CAAE,EAChCU,GAAmBV,EAAa,iBAChCW,GAAmBX,EAAa,mBAAqB,GACrDY,GAAeZ,EAAa,eAAiB,GAC7Ca,EAAqBb,EAAa,mBAClCc,GAAoBd,EAAa,mBAAqB,qBACtDe,GAAcf,EAAa,aAAe,SAC1CgB,GAAShB,EAAa,QAAU,yEAChCiB,EAAiBjB,EAAa,gBAAkB,mBAChDkB,EAAmBlB,EAAa,kBAAoB,qBAGpDmB,EAAuBnB,EAAa,sBAAwB,GAE5DoB,EAASC,EAEb,SAASC,EAAU3B,EAAQL,EAAO,CAChC,IAAIiC,EAAK5B,EAAO,OAChB,GAAIc,EAAMc,CAAE,EAAG,CACb,IAAIC,EAASf,EAAMc,CAAE,EAAE5B,EAAQL,CAAK,EACpC,GAAIkC,IAAW,GAAO,OAAOA,CAC9B,CACD,GAAID,GAAM,KAAOA,GAAM,IACrB,OAAAjC,EAAM,SAAWmC,GAAYF,CAAE,EACxBjC,EAAM,SAASK,EAAQL,CAAK,EAErC,GAAIyB,GAAY,KAAKQ,CAAE,EAAG,CAExB,GADA5B,EAAO,OAAO,CAAC,EACXA,EAAO,MAAMqB,EAAM,EAAG,MAAO,SACjCrB,EAAO,KAAM,CACd,CACD,GAAImB,GAAkB,KAAKS,CAAE,EAC3B,OAAAH,EAAUG,EACH,KAET,GAAIA,GAAM,IAAK,CACb,GAAI5B,EAAO,IAAI,GAAG,EAChB,OAAAL,EAAM,SAAWoC,EACVA,EAAa/B,EAAQL,CAAK,EAEnC,GAAIK,EAAO,IAAI,GAAG,EAChB,OAAAA,EAAO,UAAS,EACT,SAEV,CACD,GAAIsB,EAAe,KAAKM,CAAE,EAAG,CAC3B,KAAO,CAAC5B,EAAO,MAAM,WAAY,EAAK,GAAKA,EAAO,IAAIsB,CAAc,GAAG,CACvE,MAAO,UACR,CAED,GADAtB,EAAO,SAASuB,CAAgB,EAC5BL,EAAoB,KAAOlB,EAAO,MAAMkB,CAAkB,GAC5DlB,EAAO,SAASuB,CAAgB,EAElC,IAAIS,EAAMhC,EAAO,UACjB,OAAIiC,EAASzB,EAAUwB,CAAG,GACpBC,EAAStB,EAAeqB,CAAG,IAAGP,EAAU,gBACxCQ,EAASrB,EAAaoB,CAAG,IAAGN,EAAe,IACxC,WAELO,EAASxB,EAAOuB,CAAG,EAAU,OAC7BC,EAASvB,EAASsB,CAAG,GACjBR,GAAwBA,EAAqBQ,CAAG,GAClDC,EAAStB,EAAeqB,CAAG,IAAGP,EAAU,gBACrC,WAELQ,EAASpB,EAAOmB,CAAG,EAAU,OAC1B,UACR,CAED,SAASF,GAAYI,EAAO,CAC1B,OAAO,SAASlC,EAAQL,EAAO,CAE7B,QADIwC,EAAU,GAAOC,EAAMC,EAAM,IACzBD,EAAOpC,EAAO,KAAI,IAAO,MAAM,CACrC,GAAIoC,GAAQF,GAAS,CAACC,EAAS,CAACE,EAAM,GAAM,KAAM,CAClDF,EAAU,CAACA,GAAWC,GAAQ,IAC/B,CACD,OAAIC,GAAO,EAAEF,GAAWpB,OACtBpB,EAAM,SAAW,MACZ,QACb,CACG,CAED,SAASoC,EAAa/B,EAAQL,EAAO,CAEnC,QADI2C,EAAW,GAAOV,EACfA,EAAK5B,EAAO,QAAQ,CACzB,GAAI4B,GAAM,KAAOU,EAAU,CACzB3C,EAAM,SAAW,KACjB,KACD,CACD2C,EAAYV,GAAM,GACnB,CACD,MAAO,SACR,CAED,SAASW,EAASvC,EAAQL,EAAO,CAC3BU,EAAa,sBAAwBL,EAAO,IAAG,GAAME,EAAWP,EAAM,OAAO,IAC/EA,EAAM,gBAAkBI,EAAWC,EAAQL,EAAOK,EAAO,GAAG,EAC/D,CAID,MAAO,CACL,KAAMK,EAAa,KACnB,WAAY,SAASmC,EAAY,CAC/B,MAAO,CACL,SAAU,KACV,QAAS,IAAIrD,EAAQ,CAACqD,EAAY,EAAG,MAAO,KAAM,EAAK,EACvD,SAAU,EACV,YAAa,GACb,UAAW,IACnB,CACK,EAED,MAAO,SAASxC,EAAQL,EAAO,CAC7B,IAAI8C,EAAM9C,EAAM,QAMhB,GALIK,EAAO,QACLyC,EAAI,OAAS,OAAMA,EAAI,MAAQ,IACnC9C,EAAM,SAAWK,EAAO,cACxBL,EAAM,YAAc,IAElBK,EAAO,SAAU,EAAI,OAAAuC,EAASvC,EAAQL,CAAK,EAAU,KACzD8B,EAAUC,EAAe,KACzB,IAAIgB,GAAS/C,EAAM,UAAYgC,GAAW3B,EAAQL,CAAK,EACvD,GAAI+C,GAAS,WAAaA,GAAS,OAAQ,OAAOA,EAGlD,GAFID,EAAI,OAAS,OAAMA,EAAI,MAAQ,IAE/BhB,GAAW,KAAOA,GAAW,KAAQA,GAAW,KAAOzB,EAAO,MAAM,mBAAoB,EAAK,EAC/F,KAAOL,EAAM,QAAQ,MAAQ,aAAaG,EAAWH,CAAK,UACnD8B,GAAW,IAAK/B,EAAYC,EAAOK,EAAO,OAAM,EAAI,GAAG,UACvDyB,GAAW,IAAK/B,EAAYC,EAAOK,EAAO,OAAM,EAAI,GAAG,UACvDyB,GAAW,IAAK/B,EAAYC,EAAOK,EAAO,OAAM,EAAI,GAAG,UACvDyB,GAAW,IAAK,CACvB,KAAOgB,EAAI,MAAQ,aAAaA,EAAM3C,EAAWH,CAAK,EAEtD,IADI8C,EAAI,MAAQ,MAAKA,EAAM3C,EAAWH,CAAK,GACpC8C,EAAI,MAAQ,aAAaA,EAAM3C,EAAWH,CAAK,CACvD,MACQ8B,GAAWgB,EAAI,KAAM3C,EAAWH,CAAK,EACrCqB,MACGyB,EAAI,MAAQ,KAAOA,EAAI,MAAQ,QAAUhB,GAAW,KACrDgB,EAAI,MAAQ,aAAehB,GAAW,iBAC/C/B,EAAYC,EAAOK,EAAO,OAAM,EAAI,YAAaA,EAAO,QAAO,CAAE,EASnE,GANI0C,GAAS,aACP/C,EAAM,WAAa,OAClBU,EAAa,sBAAwBN,EAAWC,EAAQL,EAAOK,EAAO,KAAK,GAC3EE,EAAWP,EAAM,OAAO,GAAKK,EAAO,MAAM,SAAU,EAAK,KAC9D0C,EAAQ,OAEN5B,EAAM,MAAO,CACf,IAAIe,EAASf,EAAM,MAAMd,EAAQL,EAAO+C,CAAK,EACzCb,IAAW,SAAWa,EAAQb,EACnC,CAED,OAAIa,GAAS,OAASrC,EAAa,YAAc,KAAOqC,EAAQ,YAEhE/C,EAAM,YAAc,GACpBA,EAAM,UAAY+B,EAAe,MAAQgB,GAASjB,EAClDc,EAASvC,EAAQL,CAAK,EACf+C,CACR,EAED,OAAQ,SAAS/C,EAAOgD,EAAWxC,EAAS,CAC1C,GAAIR,EAAM,UAAYgC,GAAahC,EAAM,UAAY,MAAQA,EAAM,gBAAiB,OAAO,KAC3F,IAAI8C,EAAM9C,EAAM,QAASiD,EAAYD,GAAaA,EAAU,OAAO,CAAC,EAChEE,EAAUD,GAAaH,EAAI,KAE/B,GADIA,EAAI,MAAQ,aAAeG,GAAa,MAAKH,EAAMA,EAAI,MACvDpC,EAAa,qBACf,KAAOoC,EAAI,MAAQ,aAAepC,EAAa,qBAAqB,KAAKoC,EAAI,IAAI,GAC/EA,EAAMA,EAAI,KACd,GAAI3B,EAAM,OAAQ,CAChB,IAAIgC,EAAOhC,EAAM,OAAOnB,EAAO8C,EAAKE,EAAWxC,EAAQ,IAAI,EAC3D,GAAI,OAAO2C,GAAQ,SAAU,OAAOA,CACrC,CACD,IAAIC,GAAcN,EAAI,MAAQA,EAAI,KAAK,MAAQ,SAC/C,GAAIpC,EAAa,mBAAqB,OAAO,KAAKuC,CAAS,EAAG,CAC5D,KAAOH,EAAI,MAAQ,OAASA,EAAI,MAAQ,KAAKA,EAAMA,EAAI,KACvD,OAAOA,EAAI,QACZ,CACD,OAAIA,EAAI,MAAQ,YACPA,EAAI,UAAYG,GAAa,IAAM,EAAItC,GAAuBH,EAAQ,MAC3EsC,EAAI,QAAU,CAAClC,GAAkBkC,EAAI,MAAQ,KACxCA,EAAI,QAAUI,EAAU,EAAI,GACjCJ,EAAI,MAAQ,KAAO,CAACI,EACfJ,EAAI,UAAYnC,GAAuBH,EAAQ,MAEjDsC,EAAI,UAAYI,EAAU,EAAI1C,EAAQ,OAC1C,CAAC0C,GAAWE,IAAe,CAAC,sBAAsB,KAAKJ,CAAS,EAAIxC,EAAQ,KAAO,EACvF,EAED,aAAc,CACZ,cAAec,GAAe,uCAAyC,YACvE,cAAe,CAAC,KAAM,KAAM,MAAO,CAAC,KAAM,KAAM,MAAO,IAAI,CAAC,EAC5D,aAAc,OAAO,KAAKT,CAAQ,EAAE,OAAO,OAAO,KAAKC,CAAK,CAAC,EAAE,OAAO,OAAO,KAAKC,CAAO,CAAC,EAAE,OAAO,OAAO,KAAKG,CAAK,CAAC,EACrH,GAAGR,EAAa,YACjB,CACL,CACA,CAEA,SAAS2C,EAAMC,EAAK,CAElB,QADIC,EAAM,CAAA,EAAIF,EAAQC,EAAI,MAAM,GAAG,EAC1BE,EAAI,EAAGA,EAAIH,EAAM,OAAQ,EAAEG,EAAGD,EAAIF,EAAMG,CAAC,CAAC,EAAI,GACvD,OAAOD,CACT,CACA,SAASjB,EAASe,EAAOI,EAAM,CAC7B,OAAI,OAAOJ,GAAU,WACZA,EAAMI,CAAI,EAEVJ,EAAM,qBAAqBI,CAAI,CAE1C,CACA,IAAIC,EAAY,8KAKZC,EAAc,6YAOdC,EAAe,maAMfC,EAAe,gTAOfC,GAAcT,EAAM,4DACW,EAI/BU,GAAiBV,EAAM,yCAAyC,EAMpE,SAASW,EAAOC,EAAY,CAC1B,OAAO3B,EAASwB,GAAaG,CAAU,GAAK,QAAQ,KAAKA,CAAU,CACrE,CAGA,SAASC,EAAUD,EAAY,CAC7B,OAAOD,EAAOC,CAAU,GAAK3B,EAASyB,GAAgBE,CAAU,CAClE,CAEA,IAAIE,EAAiB,qDACjBC,EAAe,oBAEnB,SAASC,EAAQhE,EAAQL,EAAO,CAC9B,GAAI,CAACA,EAAM,YAAa,MAAO,GAC/B,QAASiC,EAAIQ,EAAO,KAAMR,EAAK5B,EAAO,QAAS,CAC7C,GAAI4B,GAAM,MAAQ5B,EAAO,MAAM,KAAK,EAAG,CACrCoC,EAAO4B,EACP,KACN,SAAepC,GAAM,KAAO5B,EAAO,MAAM,YAAa,EAAK,EACrD,MAEFA,EAAO,KAAM,CACd,CACD,OAAAL,EAAM,SAAWyC,EACV,MACT,CAEA,SAAS6B,EAAYC,EAASvE,EAAO,CACnC,OAAIA,EAAM,WAAa,OAAe,OAC/B,EACT,CAIA,SAASwE,EAAsBC,EAAO,CAEpC,MADI,CAACA,GAASA,EAAM,OAAS,GACzBA,EAAM,CAAC,GAAK,IAAY,GACpBA,EAAM,CAAC,GAAK,KAASA,EAAM,CAAC,IAAMA,EAAM,CAAC,EAAE,YAAa,CAClE,CAEA,SAASC,EAAarE,EAAQ,CAC5B,OAAAA,EAAO,SAAS,SAAS,EAClB,QACT,CAEA,SAASsE,EAAgBtE,EAAQL,EAAO,CAGtC,GAFAK,EAAO,OAAO,CAAC,EAEXA,EAAO,MAAM,qBAAqB,EAAG,CACvC,IAAIuE,EAAQvE,EAAO,MAAM,uBAAuB,EAChD,OAAKuE,GAGL5E,EAAM,oBAAsB4E,EAAM,CAAC,EACnC5E,EAAM,SAAW6E,EACVA,EAAexE,EAAQL,CAAK,GAJ1B,EAKV,CAED,OAAIK,EAAO,MAAM,eAAe,EAC1BA,EAAO,MAAM,QAAmB,EAAK,EAChC,SAEF,IAGTA,EAAO,KAAI,EACJ,GACT,CAEA,SAASyE,EAAwBrB,EAAM,CACrC,IAAIsB,EAAU,kBAAkB,KAAKtB,CAAI,EACzC,OAAOsB,GAAWA,EAAQ,CAAC,GAAKA,EAAQ,CAAC,CAC3C,CAGA,SAASC,EAAc3E,EAAQL,EAAO,CAEpC,QADIyC,GACIA,EAAOpC,EAAO,KAAI,IAAO,MAC/B,GAAIoC,GAAQ,KAAO,CAACpC,EAAO,IAAI,GAAG,EAAG,CACnCL,EAAM,SAAW,KACjB,KACD,CAEH,MAAO,QACT,CAIA,SAAS6E,EAAexE,EAAQL,EAAO,CAErC,IAAIiF,EAAQjF,EAAM,oBAAoB,QAAQ,WAAY,MAAM,EAC5D4E,EAAQvE,EAAO,MAAM,IAAI,OAAO,SAAW4E,EAAQ,GAAG,CAAC,EAC3D,OAAIL,EACF5E,EAAM,SAAW,KAEjBK,EAAO,UAAS,EACX,QACT,CAEY,MAAC6E,GAAIzE,EAAM,CACrB,KAAM,IACN,SAAU4C,EAAMK,CAAS,EACzB,MAAOM,EACP,cAAeX,EAAMc,CAAc,EACnC,YAAad,EAAMe,CAAY,EAC/B,qBAAsB,GACtB,MAAOf,EAAM,iBAAiB,EAC9B,qBAAsBmB,EACtB,MAAO,CACL,IAAKH,EACL,IAAKC,CACN,CACH,CAAC,EAEYa,GAAM1E,EAAM,CACvB,KAAM,MACN,SAAU4C,EAAMK,EAAY,IAAMC,CAAW,EAC7C,MAAOK,EACP,cAAeX,EAAMc,EAAiB,kBAAkB,EACxD,YAAad,EAAMe,EAAe,kBAAkB,EACpD,qBAAsB,GACtB,MAAOf,EAAM,yBAAyB,EACtC,qBAAsB,aACtB,iBAAkB,sBAClB,qBAAsBmB,EACtB,MAAO,CACL,IAAKH,EACL,IAAKC,EACL,EAAKK,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKD,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,MAAO,SAASrE,EAAQL,EAAO+C,EAAO,CACpC,GAAIA,GAAS,YAAc1C,EAAO,KAAM,GAAI,MACvCL,EAAM,WAAa,KAAOA,EAAM,WAAa,MAC7CA,EAAM,WAAa,MACpB8E,EAAwBzE,EAAO,SAAS,EAC1C,MAAO,KACV,CACF,EACD,mBAAoB,IACtB,CAAC,EAEY+E,GAAO3E,EAAM,CACxB,KAAM,OACN,SAAU4C,EAAM,4SAI+B,EAC/C,MAAOA,EAAM,oKACyE,EACtF,cAAeA,EAAM,qDAAqD,EAC1E,YAAaA,EAAM,iCAAiC,EACpD,qBAAsB,GACtB,MAAOA,EAAM,iBAAiB,EAC9B,OAAQ,iFACR,MAAO,CACL,IAAK,SAAShD,EAAQ,CAEpB,OAAIA,EAAO,MAAM,YAAa,EAAK,EAAU,IAE7CA,EAAO,SAAS,SAAS,EAClB,OACR,EACD,IAAK,SAASA,EAAQL,EAAO,CAC3B,OAAKK,EAAO,MAAM,KAAK,GACvBL,EAAM,SAAWqF,EACVrF,EAAM,SAASK,EAAQL,CAAK,GAFF,EAGlC,CACF,CACH,CAAC,EAEYsF,GAAS7E,EAAM,CAC1B,KAAM,SACN,SAAU4C,EAAM,qhBAM+E,EAC/F,MAAOA,EAAM,oPAGuC,EACpD,cAAeA,EAAM,oEAAoE,EACzF,YAAaA,EAAM,sCAAsC,EACzD,qBAAsB,GACtB,MAAOA,EAAM,iBAAiB,EAC9B,MAAO,CACL,IAAK,SAAShD,EAAQL,EAAO,CAC3B,OAAIK,EAAO,IAAI,GAAG,GAChBL,EAAM,SAAWgF,EACVA,EAAc3E,EAAQL,CAAK,IAEpCK,EAAO,SAAS,SAAS,EAClB,OACR,CACF,CACH,CAAC,EAED,SAASgF,EAAkBhF,EAAQL,EAAO,CAExC,QADIwC,EAAU,GACP,CAACnC,EAAO,OAAO,CACpB,GAAI,CAACmC,GAAWnC,EAAO,MAAM,KAAK,EAAG,CACnCL,EAAM,SAAW,KACjB,KACD,CACDwC,EAAUnC,EAAO,KAAM,GAAI,MAAQ,CAACmC,CACrC,CACD,MAAO,QACT,CAEA,SAAS+C,EAAmBC,EAAO,CACjC,OAAO,SAAUnF,EAAQL,EAAO,CAE9B,QADIiC,EACGA,EAAK5B,EAAO,QACjB,GAAI4B,GAAM,KAAO5B,EAAO,IAAI,GAAG,EAC7B,GAAImF,GAAS,EAAG,CACdxF,EAAM,SAAW,KACjB,KACV,KACU,QAAAA,EAAM,SAAWuF,EAAmBC,EAAQ,CAAC,EACtCxF,EAAM,SAASK,EAAQL,CAAK,UAE5BiC,GAAM,KAAO5B,EAAO,IAAI,GAAG,EACpC,OAAAL,EAAM,SAAWuF,EAAmBC,EAAQ,CAAC,EACtCxF,EAAM,SAASK,EAAQL,CAAK,EAGvC,MAAO,SACR,CACH,CAEY,MAACyF,GAAQhF,EAAM,CACzB,KAAM,QACN,SAAU4C,EAER,qWAOD,EACD,MAAOA,EACL,iuBAWD,EACD,iBAAkB,GAClB,cAAeA,EAAM,wEAAwE,EAC7F,YAAaA,EAAM,kDAAkD,EACrE,MAAOA,EAAM,iBAAiB,EAC9B,iBAAkB,GAClB,aAAc,GACd,eAAgB,sBAChB,MAAO,CACL,IAAK,SAAShD,EAAQ,CACpB,OAAAA,EAAO,SAAS,SAAS,EAClB,MACR,EACD,IAAK,SAASA,EAAQL,EAAO,CAC3B,OAAKK,EAAO,MAAM,IAAI,GACtBL,EAAM,SAAWqF,EACVrF,EAAM,SAASK,EAAQL,CAAK,GAFH,EAGjC,EACD,IAAK,SAASK,EAAQ,CACpB,OAAAA,EAAO,SAAS,oBAAoB,EAC7B,MACR,EACD,IAAK,SAASA,EAAQL,EAAO,CAC3B,IAAI0F,EAAK1F,EAAM,QACf,OAAI0F,EAAG,MAAQ,KAAOA,EAAG,OAASrF,EAAO,IAAI,GAAG,GAC9CL,EAAM,QAAU,IAAIR,EAAQkG,EAAG,SAAUA,EAAG,OAAQA,EAAG,KAAMA,EAAG,KAAM,KAAMA,EAAG,IAAI,EAC5E,YAEA,EAEV,EAED,IAAK,SAASrF,EAAQL,EAAO,CAC3B,OAAKK,EAAO,IAAI,GAAG,GACnBL,EAAM,SAAWuF,EAAmB,CAAC,EAC9BvF,EAAM,SAASK,EAAQL,CAAK,GAFN,EAG9B,CACF,EACD,aAAc,CACZ,cAAe,CAAC,SAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,KAAK,CAAC,CAC3D,CACH,CAAC,EAED,SAAS2F,GAAkBC,EAAa,CACtC,OAAO,SAAUvF,EAAQL,EAAO,CAE9B,QADIwC,EAAU,GAAOC,EAAMC,EAAM,GAC1B,CAACrC,EAAO,OAAO,CACpB,GAAI,CAACuF,GAAgB,CAACpD,GAAWnC,EAAO,MAAM,GAAG,EAAI,CAACqC,EAAM,GAAM,KAAM,CACxE,GAAIkD,GAAgBvF,EAAO,MAAM,KAAK,EAAG,CAACqC,EAAM,GAAM,KAAM,CAC5DD,EAAOpC,EAAO,OACX,CAACmC,GAAWC,GAAQ,KAAOpC,EAAO,MAAM,GAAG,GAC5CA,EAAO,OAAO,GAAG,EACnBmC,EAAU,CAACA,GAAWC,GAAQ,MAAQ,CAACmD,CACxC,CACD,OAAIlD,GAAO,CAACkD,KACV5F,EAAM,SAAW,MACZ,QACR,CACH,CAEY,MAAC6F,GAASpF,EAAM,CAC1B,KAAM,SACN,SAAU4C,EAER,0fASD,EACD,MAAOA,EAEL,yeAMD,EACD,aAAc,GACd,iBAAkB,GAClB,iBAAkB,GAClB,OAAQ,mFACR,cAAeA,EAAM,yDAAyD,EAC9E,YAAaA,EAAM,oCAAoC,EACvD,MAAOA,EAAM,sBAAsB,EACnC,MAAO,CACL,IAAK,SAAShD,EAAQ,CACpB,OAAAA,EAAO,SAAS,SAAS,EAClB,MACR,EACD,IAAK,SAASkE,EAASvE,EAAO,CAC5B,OAAOA,EAAM,WAAa,IAAM,WAAa,UAC9C,EACD,IAAK,SAASK,EAAQL,EAAO,CAC3B,OAAAA,EAAM,SAAW2F,GAAkBtF,EAAO,MAAM,IAAI,CAAC,EAC9CL,EAAM,SAASK,EAAQL,CAAK,CACpC,EACD,IAAK,SAASK,EAAQL,EAAO,CAC3B,OAAKK,EAAO,IAAI,GAAG,GACnBL,EAAM,SAAWuF,EAAmB,CAAC,EAC9BvF,EAAM,SAASK,EAAQL,CAAK,GAFN,EAG9B,EACD,OAAQ,SAASA,EAAO8C,EAAKE,EAAWH,EAAY,CAClD,IAAII,EAAYD,GAAaA,EAAU,OAAO,CAAC,EAC/C,IAAKhD,EAAM,WAAa,KAAOA,EAAM,WAAa,MAAQgD,GAAa,GACrE,OAAOhD,EAAM,SACf,GAAKA,EAAM,WAAa,YAAcgD,GAAa,KAAOhD,EAAM,QAAQ,MAAQ,KAC5EA,EAAM,WAAa,YAAciD,GAAa,MAC7CjD,EAAM,WAAa,KAAOA,EAAM,WAAa,MAAQiD,GAAa,IACrE,OAAOJ,EAAa,EAAIC,EAAI,SAC9B,GAAIA,EAAI,OAASA,EAAI,MAAQ,IAC3B,OAAOA,EAAI,UAAY9C,EAAM,QAAQ,OAASgD,GAAa,IAAI,OAAO,CAAC,EAAI,EAAIH,EAClF,CACF,EACD,aAAc,CACZ,cAAe,CAAC,SAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,KAAK,CAAC,CAC3D,CACH,CAAC,EAEYiD,GAASrF,EAAM,CAC1B,KAAM,SACN,SAAU4C,EAAM,kLAKc,EAC9B,MAAOA,EAAM,uFAEgB,EAC7B,cAAeA,EAAM,6BAA6B,EAClD,QAASA,EAAM,qoBAc6B,EAC5C,MAAOA,EAAM,iyCA0BmB,EAChC,aAAc,GACd,MAAO,CAAC,IAAKgB,CAAO,CACtB,CAAC,EAEY0B,GAAOtF,EAAM,CACxB,KAAM,OACN,SAAU4C,EAAMK,EAAY,uMAEuB,EACnD,MAAOM,EACP,cAAeX,EAAMc,CAAc,EACnC,MAAOd,EAAM,iBAAiB,EAC9B,MAAO,CAAC,IAAKgB,CAAO,CACtB,CAAC,EAEY2B,GAAavF,EAAM,CAC9B,KAAM,aACN,SAAU4C,EAAMK,EAAY,IAAME,CAAY,EAC9C,MAAOM,EACP,QAASb,EAAMQ,CAAY,EAC3B,cAAeR,EAAMc,EAAiB,kEAAkE,EACxG,YAAad,EAAMe,EAAe,8CAA8C,EAChF,qBAAsB,QACtB,qBAAsB,GACtB,MAAOf,EAAM,wCAAwC,EACrD,qBAAsBmB,EACtB,MAAO,CACL,IAAKH,EACL,IAAKC,CACN,CACH,CAAC,EAEY2B,GAAexF,EAAM,CAChC,KAAM,eACN,SAAU4C,EAAMK,EAAY,IAAME,EAAe,IAAMD,CAAW,EAClE,MAAOO,EACP,QAASb,EAAMQ,CAAY,EAC3B,cAAeR,EAAMc,EAAiB,kFAAkF,EACxH,YAAad,EAAMe,EAAe,8DAA8D,EAChG,qBAAsB,mBACtB,qBAAsB,GACtB,MAAOf,EAAM,wCAAwC,EACrD,qBAAsBmB,EACtB,MAAO,CACL,IAAKH,EACL,IAAKC,EACL,EAAKK,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKD,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,EAAKA,EACL,MAAO,SAASrE,EAAQL,EAAO+C,EAAO,CACpC,GAAIA,GAAS,YAAc1C,EAAO,KAAM,GAAI,MACvCL,EAAM,WAAa,KAAOA,EAAM,WAAa,MAC7CA,EAAM,WAAa,MACpB8E,EAAwBzE,EAAO,SAAS,EAC1C,MAAO,KACV,CACF,EACD,mBAAoB,IACtB,CAAC,EAEY6F,GAAWzF,EAAM,CAC5B,KAAM,WACN,SAAU4C,EAAM,iKACoF,EACpG,MAAOW,EACP,cAAeX,EAAM,uDAAuD,EAC5E,YAAaA,EAAM,sBAAsB,EACzC,qBAAsB,GACtB,MAAOA,EAAM,iBAAiB,EAC9B,MAAO,CAAC,IAAKgB,CAAO,CACtB,CAAC,EAGD,IAAI8B,EAAkB,KACtB,SAASC,GAAkBzG,EAAM,CAC/B,OAAO,SAASU,EAAQL,EAAO,CAE7B,QADIwC,EAAU,GAAOC,EAAMC,EAAM,GAC1B,CAACrC,EAAO,OAAO,CACpB,GAAI,CAACmC,GAAWnC,EAAO,MAAM,GAAG,IAC3BV,GAAQ,UAAYU,EAAO,MAAM,IAAI,GAAI,CAC5CqC,EAAM,GACN,KACD,CACD,GAAI,CAACF,GAAWnC,EAAO,MAAM,IAAI,EAAG,CAClC8F,EAAkBC,GAAkBzG,CAAI,EACxC+C,EAAM,GACN,KACD,CACDD,EAAOpC,EAAO,OACdmC,EAAU7C,GAAQ,UAAY,CAAC6C,GAAWC,GAAQ,IACnD,CACD,OAAIC,IACF1C,EAAM,SAAW,MACZ,QACR,CACH,CAEY,MAACqG,GAAS5F,EAAM,CAC1B,KAAM,SACN,SAAU4C,EAAM,gRAGuB,EACvC,MAAO,SAASI,EAAM,CAEpB,IAAI6C,EAAQ7C,EAAK,OAAO,CAAC,EACzB,OAAQ6C,IAAUA,EAAM,YAAW,GAAMA,IAAUA,EAAM,aAC1D,EACD,cAAejD,EAAM,oGAAoG,EACzH,YAAaA,EAAM,8DAA8D,EACjF,QAASA,EAAM,+KACyF,EACxG,kBAAmB,sBACnB,eAAgB,sBAChB,YAAa,SACb,OAAQ,iGACR,iBAAkB,GAClB,qBAAsB,GACtB,MAAOA,EAAM,qDAAqD,EAClE,aAAc,GACd,UAAW,GACX,MAAO,CACL,IAAK,SAAShD,EAAQ,CACpB,OAAAA,EAAO,SAAS,SAAS,EAClB,MACR,EACD,IAAK,SAASA,EAAQL,EAAO,CAC3B,OAAAA,EAAM,SAAWoG,GAAkB/F,EAAO,MAAM,IAAI,EAAI,SAAW,QAAQ,EACpEL,EAAM,SAASK,EAAQL,CAAK,CACpC,EACD,IAAK,SAASK,EAAQL,EAAO,CAC3B,MAAI,CAACmG,GAAmB,CAAC9F,EAAO,MAAM,GAAG,EAAU,IACnDL,EAAM,SAAWmG,EACjBA,EAAkB,KACXnG,EAAM,SAASK,EAAQL,CAAK,EACpC,EACD,IAAK,SAASK,EAAQ,CACpB,OAAAA,EAAO,SAAS,oBAAoB,EAC7B,MACR,EACD,MAAO,SAASkE,EAASvE,EAAO+C,EAAO,CACrC,IAAKA,GAAS,YAAcA,GAAS,SACjC/C,EAAM,WAAa,IACrB,MAAO,sBAEV,CACF,EACD,aAAc,CACZ,cAAe,CAAC,SAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,KAAK,CAAC,CAC3D,CACH,CAAC,EAED,SAASuG,GAAuBvG,EAAO,EACpCA,EAAM,qBAAuBA,EAAM,mBAAqB,CAAE,IAAG,KAAKA,EAAM,QAAQ,CACnF,CAEA,SAASwG,GAAsBxG,EAAO,CACpC,OAAQA,EAAM,qBAAuBA,EAAM,mBAAqB,CAAE,IAAG,KACvE,CAEA,SAASyG,GAAuBzG,EAAO,CACrC,OAAOA,EAAM,mBAAqBA,EAAM,mBAAmB,OAAS,CACtE,CAEA,SAAS0G,EAAgBnE,EAAOlC,EAAQL,EAAO2G,EAAK,CAClD,IAAIC,EAAe,GACnB,GAAIvG,EAAO,IAAIkC,CAAK,EAClB,GAAIlC,EAAO,IAAIkC,CAAK,EAAGqE,EAAe,OACjC,OAAO,SAEd,SAASC,EAAkBxG,EAAQL,EAAO,CAExC,QADIwC,EAAU,GACP,CAACnC,EAAO,OAAO,CACpB,GAAI,CAACsG,GAAO,CAACnE,GAAWnC,EAAO,KAAM,GAAI,IACvC,OAAAkG,GAAuBvG,CAAK,EAC5BA,EAAM,SAAW8G,GACV,SAET,IAAIrE,EAAOpC,EAAO,OAClB,GAAIoC,GAAQF,GAAS,CAACC,IAAY,CAACoE,GAAgBvG,EAAO,MAAMkC,EAAQA,CAAK,GAAI,CAC/EvC,EAAM,SAAW,KACjB,KACD,CACDwC,EAAU,CAACmE,GAAO,CAACnE,GAAWC,GAAQ,IACvC,CACD,MAAO,QACR,CACD,OAAAzC,EAAM,SAAW6G,EACVA,EAAkBxG,EAAQL,CAAK,CACxC,CAEA,SAAS8G,GAAmBzG,EAAQL,EAAO,CACzC,OAAAK,EAAO,IAAI,GAAG,EACVA,EAAO,IAAI,GAAG,EAGhBL,EAAM,SAAW,KAEjBA,EAAM,SAAW+G,GAEZ,IACT,CAEA,SAASA,GAA6B1G,EAAQL,EAAO,CACnD,OAAAK,EAAO,SAAS,OAAO,EACvBL,EAAM,SAAWwG,GAAsBxG,CAAK,EACrC,UACT,CAEY,MAACgH,GAAOvG,EAAM,CACxB,KAAM,OACN,SAAU4C,EAAM,oVAI0D,EAC1E,cAAeA,EAAM,+CAA+C,EACpE,QAASA,EAAM,wDAAwD,EACvE,MAAOA,EAAM,iBAAiB,EAC9B,MAAO,CACL,IAAK,SAAShD,EAAQ,CACpB,OAAAA,EAAO,SAAS,WAAW,EACpB,MACR,EAGD,IAAK,SAASA,EAAQL,EAAO,CAC3B,OAAO0G,EAAgB,IAAKrG,EAAQL,EAAO,EAAK,CACjD,EACD,IAAM,SAASK,EAAQL,EAAO,CAC5B,OAAO0G,EAAgB,IAAMrG,EAAQL,EAAO,EAAK,CAClD,EACD,EAAK,SAASK,EAAQL,EAAO,CAC3B,IAAIiH,EAAO5G,EAAO,OAClB,OAAI4G,GAAQ,KAAOA,GAAQ,IAClBP,EAAgBrG,EAAO,KAAI,EAAIA,EAAQL,EAAO,EAAI,EAEpD,EACR,EAED,IAAK,SAASuE,EAASvE,EAAO,CAE5B,OAAIyG,GAAuBzG,CAAK,EAAI,GAClCA,EAAM,SAAWwG,GAAsBxG,CAAK,EACrC,MAEF,EACR,EAED,IAAK,SAASK,EAAQL,EAAO,CAC3B,OAAKK,EAAO,IAAI,GAAG,GACnBL,EAAM,SAAWuF,EAAmB,CAAC,EAC9BvF,EAAM,SAASK,EAAQL,CAAK,GAFN,EAG9B,EACD,MAAO,SAASK,EAAQ6G,EAAGnE,EAAO,CAChC,GAAIA,GAAS,WAAY,CAEvB,IAAIoE,EAAU,OAAO,6BAA6B,GAAG,EACrD,GAAIA,EAAQ,KAAK9G,EAAO,QAAS,CAAA,EAC/B,MAAO,MAEV,CACF,CACF,CACH,CAAC", "x_google_ignoreList": [0]}