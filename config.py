import os
import json
from dataclasses import dataclass, asdict
from typing import Optional
from pathlib import Path


@dataclass
class AppConfig:
	"""Configuration class for application settings."""
	
	# Browser launch settings
	launch_gradio_in_browser: bool = True
	launch_pdf_in_browser: bool = True
	
	# Browser settings
	headless_default: bool = False
	chrome_instance_path: str = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
	edge_path: str = "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe"
	
	# Azure OpenAI settings
	azure_endpoint: str = "https://edgewater.openai.azure.com/"
	azure_deployment: str = "gpt-4o"
	openai_api_version: str = "2024-05-01-preview"
	
	# UI settings
	default_persona: str = "Engineer"
	explorer_default: bool = True
	
	def save_to_file(self, config_path: str = "config.json") -> None:
		"""Save configuration to JSON file."""
		config_dict = asdict(self)
		with open(config_path, 'w', encoding='utf-8') as f:
			json.dump(config_dict, f, indent=4)
		print(f"Configuration saved to {config_path}")
	
	@classmethod
	def load_from_file(cls, config_path: str = "config.json") -> 'AppConfig':
		"""Load configuration from JSON file."""
		if not os.path.exists(config_path):
			print(f"Config file {config_path} not found. Creating default configuration.")
			config = cls()
			config.save_to_file(config_path)
			return config

		try:
			with open(config_path, 'r', encoding='utf-8') as f:
				config_dict = json.load(f)
			valid_fields = {field.name for field in cls.__dataclass_fields__.values()}
			filtered_config = {k: v for k, v in config_dict.items() if k in valid_fields}
			config = cls(**filtered_config)
			print(f"Configuration loaded from {config_path}")
			return config
		except (json.JSONDecodeError, TypeError) as e:
			print(f"Error loading config file {config_path}: {e}")
			print("Using default configuration.")
			return cls()
	
	@classmethod
	def load_from_env(cls) -> 'AppConfig':
		"""Load configuration from environment variables with fallback to defaults."""
		return cls(
			launch_gradio_in_browser=os.getenv("LAUNCH_GRADIO_IN_BROWSER", "true").lower() == "true",
			launch_pdf_in_browser=os.getenv("LAUNCH_PDF_IN_BROWSER", "true").lower() == "true",
			headless_default=os.getenv("HEADLESS_DEFAULT", "false").lower() == "true",
			chrome_instance_path=os.getenv("CHROME_INSTANCE_PATH", "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"),
			edge_path=os.getenv("EDGE_PATH", "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe"),
			azure_endpoint=os.getenv("AZURE_ENDPOINT", "https://edgewater.openai.azure.com/"),
			azure_deployment=os.getenv("AZURE_DEPLOYMENT", "gpt-4o"),
			openai_api_version=os.getenv("OPENAI_API_VERSION", "2024-05-01-preview"),
			default_persona=os.getenv("DEFAULT_PERSONA", "Engineer"),
			explorer_default=os.getenv("EXPLORER_DEFAULT", "true").lower() == "true"
		)
	
	def update_setting(self, key: str, value) -> None:
		"""Update a specific configuration setting."""
		if hasattr(self, key):
			setattr(self, key, value)
			print(f"Updated {key} to {value}")
		else:
			print(f"Unknown configuration key: {key}")
	
	def get_setting(self, key: str, default=None):
		"""Get a specific configuration setting."""
		return getattr(self, key, default)

_config: Optional[AppConfig] = None


def get_config() -> AppConfig:
	"""Get the global configuration instance."""
	global _config
	if _config is None:
		_config = AppConfig.load_from_file()
	return _config


def reload_config() -> AppConfig:
	"""Reload configuration from file."""
	global _config
	_config = AppConfig.load_from_file()
	return _config


def update_config(**kwargs) -> None:
	"""Update configuration settings."""
	config = get_config()
	for key, value in kwargs.items():
		config.update_setting(key, value)


def save_config() -> None:
	"""Save current configuration to file."""
	config = get_config()
	config.save_to_file()


if __name__ == "__main__":
	config = AppConfig()
	config.save_to_file("config.json")
	loaded_config = AppConfig.load_from_file("config.json")
	print(f"Launch Gradio in browser: {loaded_config.launch_gradio_in_browser}")
	print(f"Launch PDF in browser: {loaded_config.launch_pdf_in_browser}")
	env_config = AppConfig.load_from_env()
	print(f"Environment config - Launch Gradio in browser: {env_config.launch_gradio_in_browser}")
