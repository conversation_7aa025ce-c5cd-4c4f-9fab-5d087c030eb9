{"version": 3, "file": "UploadText-Chjc4Zy7.js", "sources": ["../../../atoms/src/utils/parse_placeholder.ts", "../../../atoms/src/UploadText.svelte"], "sourcesContent": ["const RE_HEADING = /^(#\\s*)(.+)$/m;\n\nexport function inject(text: string): [string | false, string | false] {\n\tconst trimmed_text = text.trim();\n\n\tconst heading_match = trimmed_text.match(RE_HEADING);\n\tif (!heading_match) {\n\t\treturn [false, trimmed_text || false];\n\t}\n\n\tconst [full_match, , heading_content] = heading_match;\n\tconst _heading = heading_content.trim();\n\n\tif (trimmed_text === full_match) {\n\t\treturn [_heading, false];\n\t}\n\n\tconst heading_end_index =\n\t\theading_match.index !== undefined\n\t\t\t? heading_match.index + full_match.length\n\t\t\t: 0;\n\tconst remaining_text = trimmed_text.substring(heading_end_index).trim();\n\n\tconst _paragraph = remaining_text || false;\n\n\treturn [_heading, _paragraph];\n}\n", "<script lang=\"ts\">\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { Upload as UploadIcon, ImagePaste } from \"@gradio/icons\";\n\timport { inject } from \"./utils/parse_placeholder\";\n\n\texport let type:\n\t\t| \"video\"\n\t\t| \"image\"\n\t\t| \"audio\"\n\t\t| \"file\"\n\t\t| \"csv\"\n\t\t| \"clipboard\"\n\t\t| \"gallery\" = \"file\";\n\texport let i18n: I18nFormatter;\n\texport let message: string | undefined = undefined;\n\texport let mode: \"full\" | \"short\" = \"full\";\n\texport let hovered = false;\n\texport let placeholder: string | undefined = undefined;\n\n\tconst defs = {\n\t\timage: \"upload_text.drop_image\",\n\t\tvideo: \"upload_text.drop_video\",\n\t\taudio: \"upload_text.drop_audio\",\n\t\tfile: \"upload_text.drop_file\",\n\t\tcsv: \"upload_text.drop_csv\",\n\t\tgallery: \"upload_text.drop_gallery\",\n\t\tclipboard: \"upload_text.paste_clipboard\"\n\t};\n\n\t$: [heading, paragraph] = placeholder ? inject(placeholder) : [false, false];\n</script>\n\n<div class=\"wrap\">\n\t<span class=\"icon-wrap\" class:hovered>\n\t\t{#if type === \"clipboard\"}\n\t\t\t<ImagePaste />\n\t\t{:else}\n\t\t\t<UploadIcon />\n\t\t{/if}\n\t</span>\n\n\t{#if heading || paragraph}\n\t\t{#if heading}\n\t\t\t<h2>{heading}</h2>\n\t\t{/if}\n\t\t{#if paragraph}\n\t\t\t<p>{paragraph}</p>\n\t\t{/if}\n\t{:else}\n\t\t{i18n(defs[type] || defs.file)}\n\n\t\t{#if mode !== \"short\"}\n\t\t\t<span class=\"or\">- {i18n(\"common.or\")} -</span>\n\t\t\t{message || i18n(\"upload_text.click_to_upload\")}\n\t\t{/if}\n\t{/if}\n</div>\n\n<style>\n\th2 {\n\t\tfont-size: var(--text-xl) !important;\n\t}\n\n\tp,\n\th2 {\n\t\twhite-space: pre-line;\n\t}\n\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmin-height: var(--size-60);\n\t\tcolor: var(--block-label-text-color);\n\t\tline-height: var(--line-md);\n\t\theight: 100%;\n\t\tpadding-top: var(--size-3);\n\t\ttext-align: center;\n\t\tmargin: auto var(--spacing-lg);\n\t}\n\n\t.or {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tdisplay: flex;\n\t}\n\n\t.icon-wrap {\n\t\twidth: 30px;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n\n\t.hovered {\n\t\tcolor: var(--color-accent);\n\t}\n</style>\n"], "names": ["RE_HEADING", "inject", "text", "trimmed_text", "heading_match", "full_match", "heading_content", "_heading", "heading_end_index", "_paragraph", "t0_value", "ctx", "if_block", "create_if_block_3", "dirty", "set_data", "t0", "create_if_block_2", "create_if_block_1", "t1_value", "insert", "target", "span", "anchor", "t1", "t4", "t4_value", "h2", "p", "create_if_block", "div", "append", "type", "$$props", "i18n", "message", "mode", "hovered", "placeholder", "defs", "$$invalidate", "heading", "paragraph"], "mappings": "iNAAA,MAAMA,EAAa,gBAEZ,SAASC,EAAOC,EAAgD,CAChE,MAAAC,EAAeD,EAAK,OAEpBE,EAAgBD,EAAa,MAAMH,CAAU,EACnD,GAAI,CAACI,EACG,MAAA,CAAC,GAAOD,GAAgB,EAAK,EAGrC,KAAM,CAACE,EAAA,CAAcC,CAAe,EAAIF,EAClCG,EAAWD,EAAgB,OAEjC,GAAIH,IAAiBE,EACb,MAAA,CAACE,EAAU,EAAK,EAGxB,MAAMC,EACLJ,EAAc,QAAU,OACrBA,EAAc,MAAQC,EAAW,OACjC,EAGEI,EAFiBN,EAAa,UAAUK,CAAiB,EAAE,KAAK,GAEjC,GAE9B,MAAA,CAACD,EAAUE,CAAU,CAC7B,yVCuBG,IAAAC,EAAAC,KAAKA,EAAI,CAAA,EAACA,EAAS,CAAA,CAAA,GAAAA,KAAK,IAAI,EAAA,SAExBC,EAAAD,OAAS,SAAOE,EAAAF,CAAA,gGAFpBG,EAAA,GAAAJ,KAAAA,EAAAC,KAAKA,EAAI,CAAA,EAACA,EAAS,CAAA,CAAA,GAAAA,KAAK,IAAI,EAAA,KAAAI,EAAAC,EAAAN,CAAA,EAExBC,OAAS,uIATTA,EAAO,CAAA,GAAAM,EAAAN,CAAA,IAGPA,EAAS,CAAA,GAAAO,EAAAP,CAAA,qGAHTA,EAAO,CAAA,mEAGPA,EAAS,CAAA,oIAOOQ,EAAAR,KAAK,WAAW,EAAA,YACnCA,EAAO,CAAA,GAAIA,EAAI,CAAA,EAAC,6BAA6B,GAAA,gCAD7B,IAAE,aAAmB,IAAE,wDAAxCS,EAA8CC,EAAAC,EAAAC,CAAA,iDAA1BT,EAAA,GAAAK,KAAAA,EAAAR,KAAK,WAAW,EAAA,KAAAI,EAAAS,EAAAL,CAAA,eACnCR,EAAO,CAAA,GAAIA,EAAI,CAAA,EAAC,6BAA6B,GAAA,KAAAI,EAAAU,EAAAC,CAAA,6EAVzCf,EAAO,CAAA,CAAA,wCAAZS,EAAiBC,EAAAM,EAAAJ,CAAA,2BAAZZ,EAAO,CAAA,CAAA,gEAGRA,EAAS,CAAA,CAAA,wCAAbS,EAAiBC,EAAAO,EAAAL,CAAA,2BAAbZ,EAAS,CAAA,CAAA,mFAZT,OAAAA,OAAS,YAAW,0CAOrB,OAAAA,MAAWA,EAAS,CAAA,EAAAkB,iLAT1BT,EAwBKC,EAAAS,EAAAP,CAAA,EAvBJQ,EAMMD,EAAAR,CAAA,kWAlCK,KAAAU,EAOI,MAAA,EAAAC,EACJ,CAAA,KAAAC,CAAA,EAAAD,GACA,QAAAE,EAA8B,MAAA,EAAAF,GAC9B,KAAAG,EAAyB,MAAA,EAAAH,GACzB,QAAAI,EAAU,EAAA,EAAAJ,GACV,YAAAK,EAAkC,MAAA,EAAAL,EAEvC,MAAAM,EAAA,CACL,MAAO,yBACP,MAAO,yBACP,MAAO,yBACP,KAAM,wBACN,IAAK,uBACL,QAAS,2BACT,UAAW,0QAGRC,EAAA,EAAA,CAAAC,EAASC,CAAS,EAAIJ,EAAcrC,EAAOqC,CAAW,EAAA,CAAK,GAAO,EAAK,EAAAG,GAAAD,EAAA,EAAAE,CAAA,EAAAF,EAAA,EAAAF,CAAA"}