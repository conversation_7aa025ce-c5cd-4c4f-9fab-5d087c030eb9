import{a as $,i as ee,s as te,E as x,z as d,d as j,C as y,D as V,l as B,w as me,f as T,e as De,h as oe,t as W,j as le,k as A,c as q,m as X,n as G,y as O,M as F,V as Ce,a5 as Ne,O as Q,b as K,$ as we,A as ye,x as je,a7 as pe,a8 as ve,o as Ue,p as Ie}from"../lite.js";import{M as Fe}from"./Music-BCIGqdvV.js";import{f as ke}from"./utils-BsGrhMNe.js";import{P as qe,T as Xe}from"./Trim-CDsEvQ4G.js";import{P as Ge}from"./Play-BIkNyEKH.js";import{U as Ze}from"./Undo-50qkik3g.js";import{E as Ye}from"./Empty-Bzq0Ew6m.js";import{r as Te}from"./file-url-CoOyVRgq.js";import{H as he}from"./hls-CnVhpNcu.js";function Ke(r){let e,t;return{c(){e=x("svg"),t=x("path"),d(t,"stroke","currentColor"),d(t,"stroke-width","1.5"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","24px"),d(e,"height","24px"),d(e,"fill","currentColor"),d(e,"stroke-width","1.5"),d(e,"viewBox","0 0 24 24"),d(e,"color","currentColor")},m(i,n){j(i,e,n),y(e,t)},p:V,i:V,o:V,d(i){i&&B(e)}}}class Je extends ${constructor(e){super(),ee(this,e,null,Ke,te,{})}}function Qe(r){let e,t;return{c(){e=x("svg"),t=x("path"),d(t,"stroke","currentColor"),d(t,"stroke-width","1.5"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","24px"),d(e,"height","24px"),d(e,"fill","currentColor"),d(e,"stroke-width","1.5"),d(e,"viewBox","0 0 24 24"),d(e,"color","currentColor")},m(i,n){j(i,e,n),y(e,t)},p:V,i:V,o:V,d(i){i&&B(e)}}}class $e extends ${constructor(e){super(),ee(this,e,null,Qe,te,{})}}function et(r){let e,t,i,n,s;return{c(){e=x("svg"),t=x("title"),i=me("Low volume"),n=x("path"),s=x("path"),d(n,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),d(n,"stroke-width","1.5"),d(n,"stroke-linecap","round"),d(n,"stroke-linejoin","round"),d(s,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),d(s,"stroke-width","1.5"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"fill","none"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"stroke","currentColor"),d(e,"color","currentColor")},m(o,a){j(o,e,a),y(e,t),y(t,i),y(e,n),y(e,s)},p:V,i:V,o:V,d(o){o&&B(e)}}}class tt extends ${constructor(e){super(),ee(this,e,null,et,te,{})}}function it(r){let e,t,i,n,s,o;return{c(){e=x("svg"),t=x("title"),i=me("High volume"),n=x("path"),s=x("path"),o=x("path"),d(n,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),d(n,"stroke-width","1.5"),d(s,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),d(s,"stroke-width","1.5"),d(s,"stroke-linecap","round"),d(s,"stroke-linejoin","round"),d(o,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),d(o,"stroke-width","1.5"),d(o,"stroke-linecap","round"),d(o,"stroke-linejoin","round"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"fill","none"),d(e,"stroke","currentColor"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"color","currentColor")},m(a,u){j(a,e,u),y(e,t),y(t,i),y(e,n),y(e,s),y(e,o)},p:V,i:V,o:V,d(a){a&&B(e)}}}class nt extends ${constructor(e){super(),ee(this,e,null,it,te,{})}}function st(r){let e,t,i,n,s,o,a,u,h;return{c(){e=x("svg"),t=x("title"),i=me("Muted volume"),n=x("g"),s=x("path"),o=x("path"),a=x("defs"),u=x("clipPath"),h=x("rect"),d(s,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),d(s,"stroke-width","1.5"),d(s,"stroke-linecap","round"),d(s,"stroke-linejoin","round"),d(o,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),d(o,"stroke-width","1.5"),d(n,"clip-path","url(#clip0_3173_16686)"),d(h,"width","24"),d(h,"height","24"),d(h,"fill","white"),d(u,"id","clip0_3173_16686"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"fill","none"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"stroke","currentColor"),d(e,"color","currentColor")},m(l,c){j(l,e,c),y(e,t),y(t,i),y(e,n),y(n,s),y(n,o),y(e,a),y(a,u),y(u,h)},p:V,i:V,o:V,d(l){l&&B(e)}}}class rt extends ${constructor(e){super(),ee(this,e,null,st,te,{})}}var ot=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{h(i.next(l))}catch(c){o(c)}}function u(l){try{h(i.throw(l))}catch(c){o(c)}}function h(l){l.done?s(l.value):n(l.value).then(a,u)}h((i=i.apply(r,e||[])).next())})};function lt(r,e){return ot(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(r).finally(()=>t.close())})}function at(r){const e=r[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let i=0;for(let n=0;n<t;n++){const s=Math.abs(e[n]);s>i&&(i=s)}for(const n of r)for(let s=0;s<t;s++)n[s]/=i}return r}function ut(r,e){return typeof r[0]=="number"&&(r=[r]),at(r),{duration:e,length:r[0].length,sampleRate:r[0].length/e,numberOfChannels:r.length,getChannelData:t=>r?.[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const Re={decode:lt,createBuffer:ut};var Ae=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{h(i.next(l))}catch(c){o(c)}}function u(l){try{h(i.throw(l))}catch(c){o(c)}}function h(l){l.done?s(l.value):n(l.value).then(a,u)}h((i=i.apply(r,e||[])).next())})};function dt(r,e,t){var i,n;return Ae(this,void 0,void 0,function*(){const s=yield fetch(r,t);{const o=(i=s.clone().body)===null||i===void 0?void 0:i.getReader(),a=Number((n=s.headers)===null||n===void 0?void 0:n.get("Content-Length"));let u=0;const h=(l,c)=>Ae(this,void 0,void 0,function*(){if(l)return;u+=c?.length||0;const v=Math.round(u/a*100);return e(v),o?.read().then(({done:f,value:m})=>h(f,m))});o?.read().then(({done:l,value:c})=>h(l,c))}return s.blob()})}const ht={fetchBlob:dt};class Ee{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class ct extends Ee{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,i){return this.media.addEventListener(e,t,i),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const n=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=n,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function ft(r,e,t,i,n=5){let s=()=>{};if(!r)return s;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),r.style.touchAction="none";let u=a.clientX,h=a.clientY,l=!1;const c=m=>{m.preventDefault(),m.stopPropagation();const k=m.clientX,D=m.clientY;if(l||Math.abs(k-u)>=n||Math.abs(D-h)>=n){const{left:R,top:p}=r.getBoundingClientRect();l||(l=!0,t?.(u-R,h-p)),e(k-u,D-h,k-R,D-p),u=k,h=D}},v=m=>{l&&(m.preventDefault(),m.stopPropagation())},f=()=>{r.style.touchAction="",l&&i?.(),s()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",f),document.addEventListener("pointerleave",f),document.addEventListener("click",v,!0),s=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",f),document.removeEventListener("pointerleave",f),setTimeout(()=>{document.removeEventListener("click",v,!0)},10)}};return r.addEventListener("pointerdown",o),()=>{s(),r.removeEventListener("pointerdown",o)}}class Le extends Ee{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const i=this.parentFromOptionsContainer(e.container);this.parent=i;const[n,s]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=s.querySelector(".scroll"),this.wrapper=s.querySelector(".wrapper"),this.canvasWrapper=s.querySelector(".canvases"),this.progressWrapper=s.querySelector(".progress"),this.cursor=s.querySelector(".cursor"),t&&s.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=i=>{const n=this.wrapper.getBoundingClientRect(),s=i.clientX-n.left,o=i.clientX-n.left,a=s/n.width,u=o/n.height;return[a,u]};this.wrapper.addEventListener("click",i=>{const[n,s]=e(i);this.emit("click",n,s)}),this.wrapper.addEventListener("dblclick",i=>{const[n,s]=e(i);this.emit("dblclick",n,s)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:i,scrollWidth:n,clientWidth:s}=this.scrollContainer,o=i/n,a=(i+s)/n;this.emit("scroll",o,a)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){ft(this.wrapper,(e,t,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),i=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(i,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),n=t.getContext("2d").createLinearGradient(0,0,0,t.height),s=1/(e.length-1);return e.forEach((o,a)=>{const u=a*s;n.addColorStop(u,o)}),n}renderBarWaveform(e,t,i,n){const s=e[0],o=e[1]||e[0],a=s.length,{width:u,height:h}=i.canvas,l=h/2,c=window.devicePixelRatio||1,v=t.barWidth?t.barWidth*c:1,f=t.barGap?t.barGap*c:t.barWidth?v/2:0,m=t.barRadius||0,k=u/(v+f)/a,D=m&&"roundRect"in i?"roundRect":"rect";i.beginPath();let R=0,p=0,S=0;for(let C=0;C<=a;C++){const w=Math.round(C*k);if(w>R){const L=Math.round(p*l*n),M=Math.round(S*l*n),Z=L+M||1;let N=l-L;t.barAlign==="top"?N=0:t.barAlign==="bottom"&&(N=h-Z),i[D](R*(v+f),N,v,Z,m),R=w,p=0,S=0}const P=Math.abs(s[C]||0),_=Math.abs(o[C]||0);P>p&&(p=P),_>S&&(S=_)}i.fill(),i.closePath()}renderLineWaveform(e,t,i,n){const s=o=>{const a=e[o]||e[0],u=a.length,{height:h}=i.canvas,l=h/2,c=i.canvas.width/u;i.moveTo(0,l);let v=0,f=0;for(let m=0;m<=u;m++){const k=Math.round(m*c);if(k>v){const R=Math.round(f*l*n)||1,p=l+R*(o===0?-1:1);i.lineTo(v,p),v=k,f=0}const D=Math.abs(a[m]||0);D>f&&(f=D)}i.lineTo(v,l)};i.beginPath(),s(0),s(1),i.fill(),i.closePath()}renderWaveform(e,t,i){if(i.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,i);return}let n=t.barHeight||1;if(t.normalize){const s=Array.from(e[0]).reduce((o,a)=>Math.max(o,Math.abs(a)),0);n=s?1/s:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,i,n);return}this.renderLineWaveform(e,t,i,n)}renderSingleCanvas(e,t,i,n,s,o,a,u){const h=window.devicePixelRatio||1,l=document.createElement("canvas"),c=e[0].length;l.width=Math.round(i*(o-s)/c),l.height=n*h,l.style.width=`${Math.floor(l.width/h)}px`,l.style.height=`${n}px`,l.style.left=`${Math.floor(s*i/h/c)}px`,a.appendChild(l);const v=l.getContext("2d");if(this.renderWaveform(e.map(f=>f.slice(s,o)),t,v),l.width>0&&l.height>0){const f=l.cloneNode(),m=f.getContext("2d");m.drawImage(l,0,0),m.globalCompositeOperation="source-in",m.fillStyle=this.convertColorValues(t.progressColor),m.fillRect(0,0,l.width,l.height),u.appendChild(f)}}renderChannel(e,t,i){const n=document.createElement("div"),s=this.getHeight();n.style.height=`${s}px`,this.canvasWrapper.style.minHeight=`${s}px`,this.canvasWrapper.appendChild(n);const o=n.cloneNode();this.progressWrapper.appendChild(o);const{scrollLeft:a,scrollWidth:u,clientWidth:h}=this.scrollContainer,l=e[0].length,c=l/u;let v=Math.min(Le.MAX_CANVAS_WIDTH,h);if(t.barWidth||t.barGap){const w=t.barWidth||.5,P=t.barGap||w/2,_=w+P;v%_!==0&&(v=Math.floor(v/_)*_)}const f=Math.floor(Math.abs(a)*c),m=Math.floor(f+v*c),k=m-f,D=(w,P)=>{this.renderSingleCanvas(e,t,i,s,Math.max(0,w),Math.min(P,l),n,o)},R=this.createDelay(),p=this.createDelay(),S=(w,P)=>{D(w,P),w>0&&R(()=>{S(w-k,P-k)})},C=(w,P)=>{D(w,P),P<l&&p(()=>{C(w+k,P+k)})};S(f,m),m<l&&C(m,m+k)}render(e){this.timeouts.forEach(a=>a.timeout&&clearTimeout(a.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,i=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=n>i;const s=this.options.fillParent&&!this.isScrolling,o=(s?i:n)*t;if(this.wrapper.style.width=s?"100%":`${n}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let a=0;a<e.numberOfChannels;a++){const u=Object.assign(Object.assign({},this.options),this.options.splitChannels[a]);this.renderChannel([e.getChannelData(a)],u,o)}else{const a=[e.getChannelData(0)];e.numberOfChannels>1&&a.push(e.getChannelData(1)),this.renderChannel(a,this.options,o)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:i,scrollLeft:n,scrollWidth:s}=this.scrollContainer,o=s*e,a=i/2,u=t&&this.options.autoCenter&&!this.isDragging?a:i;if(o>n+u||o<n)if(this.options.autoCenter&&!this.isDragging){const h=a/20;o-(n+a)>=h&&o<n+i?this.scrollContainer.scrollLeft+=h:this.scrollContainer.scrollLeft=o-a}else this.isDragging?this.scrollContainer.scrollLeft=o<n?o-10:o-i+10:this.scrollContainer.scrollLeft=o;{const{scrollLeft:h}=this.scrollContainer,l=h/s,c=(h+i)/s;this.emit("scroll",l,c)}}renderProgress(e,t){if(isNaN(e))return;const i=e*100;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.marginLeft=Math.round(i)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}Le.MAX_CANVAS_WIDTH=4e3;class mt extends Ee{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var Me=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{h(i.next(l))}catch(c){o(c)}}function u(l){try{h(i.throw(l))}catch(c){o(c)}}function h(l){l.done?s(l.value):n(l.value).then(a,u)}h((i=i.apply(r,e||[])).next())})};class gt extends Ee{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return Me(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return Me(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return Me(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var ce=function(r,e,t,i){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(l){try{h(i.next(l))}catch(c){o(c)}}function u(l){try{h(i.throw(l))}catch(c){o(c)}}function h(l){l.done?s(l.value):n(l.value).then(a,u)}h((i=i.apply(r,e||[])).next())})};const pt={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class Se extends ct{static create(e){return new Se(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new gt:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},pt,e),this.timer=new mt;const i=t?void 0:this.getMediaElement();this.renderer=new Le(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc();n?this.load(n,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const i=this.getDuration();this.emit("scroll",e*i,t*i)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return ce(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=Re.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return ce(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,i,n){return ce(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!i){const s=o=>this.emit("loading",o);t=yield ht.fetchBlob(e,s,this.options.fetchParams)}if(this.setSrc(e,t),n=(yield Promise.resolve(n||this.getDuration()))||(yield new Promise(s=>{this.onceMediaEvent("loadedmetadata",()=>s(this.getDuration()))}))||(yield Promise.resolve(0)),i)this.decodedData=Re.createBuffer(i,n);else if(t){const s=yield t.arrayBuffer();this.decodedData=yield Re.decode(s,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,i){return ce(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,i)})}loadBlob(e,t,i){return ce(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,i)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(e,this.decodedData.numberOfChannels),s=[];for(let o=0;o<n;o++){const a=this.decodedData.getChannelData(o),u=[],h=Math.round(a.length/t);for(let l=0;l<t;l++){const c=a.slice(l*h,(l+1)*h),v=Math.max(...c);u.push(Math.round(v*i)/i)}s.push(u)}return s}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return ce(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function vt(r){const e=r.numberOfChannels,t=r.length*e*2+44,i=new ArrayBuffer(t),n=new DataView(i);let s=0;const o=function(a,u,h){for(let l=0;l<h.length;l++)a.setUint8(u+l,h.charCodeAt(l))};o(n,s,"RIFF"),s+=4,n.setUint32(s,t-8,!0),s+=4,o(n,s,"WAVE"),s+=4,o(n,s,"fmt "),s+=4,n.setUint32(s,16,!0),s+=4,n.setUint16(s,1,!0),s+=2,n.setUint16(s,e,!0),s+=2,n.setUint32(s,r.sampleRate,!0),s+=4,n.setUint32(s,r.sampleRate*2*e,!0),s+=4,n.setUint16(s,e*2,!0),s+=2,n.setUint16(s,16,!0),s+=2,o(n,s,"data"),s+=4,n.setUint32(s,r.length*e*2,!0),s+=4;for(let a=0;a<r.length;a++)for(let u=0;u<e;u++){const h=Math.max(-1,Math.min(1,r.getChannelData(u)[a]));n.setInt16(s,h*32767,!0),s+=2}return new Uint8Array(i)}const _t=async(r,e,t,i)=>{const n=new AudioContext({sampleRate:i||r.sampleRate}),s=r.numberOfChannels,o=i||r.sampleRate;let a=r.length,u=0;e&&t&&(u=Math.round(e*o),a=Math.round(t*o)-u);const h=n.createBuffer(s,a,o);for(let l=0;l<s;l++){const c=r.getChannelData(l),v=h.getChannelData(l);for(let f=0;f<a;f++)v[f]=c[u+f]}return vt(h)},We=(r,e)=>{r&&r.skip(e)},fe=(r,e)=>(e||(e=5),r/100*e||5);class Be{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class bt extends Be{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}function be(r,e,t,i,n=5){let s=()=>{};if(!r)return s;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),r.style.touchAction="none";let u=a.clientX,h=a.clientY,l=!1;const c=m=>{m.preventDefault(),m.stopPropagation();const k=m.clientX,D=m.clientY;if(l||Math.abs(k-u)>=n||Math.abs(D-h)>=n){const{left:R,top:p}=r.getBoundingClientRect();l||(l=!0,t?.(u-R,h-p)),e(k-u,D-h,k-R,D-p),u=k,h=D}},v=m=>{l&&(m.preventDefault(),m.stopPropagation())},f=()=>{r.style.touchAction="",l&&i?.(),s()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",f),document.addEventListener("pointerleave",f),document.addEventListener("click",v,!0),s=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",f),document.removeEventListener("pointerleave",f),setTimeout(()=>{document.removeEventListener("click",v,!0)},10)}};return r.addEventListener("pointerdown",o),()=>{s(),r.removeEventListener("pointerdown",o)}}class Oe extends Be{constructor(e,t,i=0){var n,s,o,a,u,h,l;super(),this.totalDuration=t,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:e.start),this.drag=(s=e.drag)===null||s===void 0||s,this.resize=(o=e.resize)===null||o===void 0||o,this.color=(a=e.color)!==null&&a!==void 0?a:"rgba(0, 0, 0, 0.1)",this.minLength=(u=e.minLength)!==null&&u!==void 0?u:this.minLength,this.maxLength=(h=e.maxLength)!==null&&h!==void 0?h:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const i=t.cloneNode();i.setAttribute("data-resize","right"),i.style.left="",i.style.right="0",i.style.borderRight=i.style.borderLeft,i.style.borderLeft="",i.style.borderRadius="0 2px 2px 0",i.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(i),be(t,n=>this.onResize(n,"start"),()=>null,()=>this.onEndResizing(),1),be(i,n=>this.onResize(n,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),i=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),i&&e.removeChild(i)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${i}%;
      height: ${n}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),be(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const i=e/this.element.parentElement.clientWidth*this.totalDuration,n=t&&t!=="start"?this.start:this.start+i,s=t&&t!=="end"?this.end:this.end+i,o=s-n;n>=0&&s<=this.totalDuration&&n<=s&&o>=this.minLength&&o<=this.maxLength&&(this.start=n,this.end=s,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const i=this.start===this.end;this.content.style.padding=`0.2em ${i?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,i;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const n=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((i=e.end)!==null&&i!==void 0?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const n=this.start===this.end;this.resize=e.resize,this.resize&&!n?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}class Pe extends bt{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new Pe(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const i=this.regions.filter(n=>n.start<=t&&n.end>=t);i.forEach(n=>{e.includes(n)||this.emit("region-in",n)}),e.forEach(n=>{i.includes(n)||this.emit("region-out",n)}),e=i}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,i=t.getBoundingClientRect().left,n=e.element.scrollWidth,s=this.regions.filter(o=>{if(o===e||!o.content)return!1;const a=o.content.getBoundingClientRect().left,u=o.element.scrollWidth;return i<a+u&&a<i+n}).map(o=>{var a;return((a=o.content)===null||a===void 0?void 0:a.getBoundingClientRect().height)||0}).reduce((o,a)=>o+a,0);t.style.marginTop=`${s}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var i,n;(i=this.wavesurfer)===null||i===void 0||i.play(),(n=this.wavesurfer)===null||n===void 0||n.setTime(e.start)}),e.on("click",i=>{this.emit("region-clicked",e,i)}),e.on("dblclick",i=>{this.emit("region-double-clicked",e,i)}),e.once("remove",()=>{t.forEach(i=>i()),this.regions=this.regions.filter(i=>i!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,i;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const n=this.wavesurfer.getDuration(),s=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||i===void 0?void 0:i.numberOfChannels,o=new Oe(e,n,s);return n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once("ready",a=>{o._setTotalDuration(a),this.saveRegion(o)})),o}enableDragSelection(e){var t,i;const n=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||i===void 0?void 0:i.querySelector("div");if(!n)return()=>{};let s=null,o=0;return be(n,(a,u,h)=>{s&&s._onUpdate(a,h>o?"end":"start")},a=>{var u,h;if(o=a,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),c=(h=(u=this.wavesurfer)===null||u===void 0?void 0:u.getDecodedData())===null||h===void 0?void 0:h.numberOfChannels,v=this.wavesurfer.getWrapper().clientWidth,f=a/v*l,m=(a+5)/v*l;s=new Oe(Object.assign(Object.assign({},e),{start:f,end:m}),l,c),this.regionsContainer.appendChild(s.element)},()=>{s&&(this.saveRegion(s),s=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}}function wt(r){let e,t;return e=new nt({}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function yt(r){let e,t;return e=new tt({}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function kt(r){let e,t;return e=new rt({}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function Ct(r){let e,t,i,n;const s=[kt,yt,wt],o=[];function a(u,h){return u[0]==0?0:u[0]<.5?1:u[0]>=.5?2:-1}return~(e=a(r))&&(t=o[e]=s[e](r)),{c(){t&&t.c(),i=De()},m(u,h){~e&&o[e].m(u,h),j(u,i,h),n=!0},p(u,[h]){let l=e;e=a(u),e!==l&&(t&&(oe(),W(o[l],1,1,()=>{o[l]=null}),le()),~e?(t=o[e],t||(t=o[e]=s[e](u),t.c()),A(t,1),t.m(i.parentNode,i)):t=null)},i(u){n||(A(t),n=!0)},o(u){W(t),n=!1},d(u){u&&B(i),~e&&o[e].d(u)}}}function Et(r,e,t){let{currentVolume:i}=e;return r.$$set=n=>{"currentVolume"in n&&t(0,i=n.currentVolume)},[i]}class Lt extends ${constructor(e){super(),ee(this,e,Et,Ct,te,{currentVolume:0})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),T()}}function Rt(r){let e,t,i;return{c(){e=O("input"),d(e,"id","volume"),d(e,"class","volume-slider svelte-wuo8j5"),d(e,"type","range"),d(e,"min","0"),d(e,"max","1"),d(e,"step","0.01"),e.value=r[0]},m(n,s){j(n,e,s),r[4](e),t||(i=[F(e,"focusout",r[5]),F(e,"input",r[6])],t=!0)},p(n,[s]){s&1&&(e.value=n[0])},i:V,o:V,d(n){n&&B(e),r[4](null),t=!1,Ce(i)}}}function Mt(r,e,t){let{currentVolume:i=1}=e,{show_volume_slider:n=!1}=e,{waveform:s}=e,o;Ne(()=>{a()});const a=()=>{let c=o;c&&(c.style.background=`linear-gradient(to right, var(--color-accent) ${i*100}%, var(--neutral-400) ${i*100}%)`)};function u(c){Q[c?"unshift":"push"](()=>{o=c,t(3,o)})}const h=()=>t(1,n=!1),l=c=>{c.target instanceof HTMLInputElement&&(t(0,i=parseFloat(c.target.value)),s?.setVolume(i))};return r.$$set=c=>{"currentVolume"in c&&t(0,i=c.currentVolume),"show_volume_slider"in c&&t(1,n=c.show_volume_slider),"waveform"in c&&t(2,s=c.waveform)},r.$$.update=()=>{r.$$.dirty&1&&a()},[i,n,s,o,u,h,l]}class Dt extends ${constructor(e){super(),ee(this,e,Mt,Rt,te,{currentVolume:0,show_volume_slider:1,waveform:2})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),T()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),T()}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),T()}}function ze(r){let e,t,i,n;function s(u){r[27](u)}function o(u){r[28](u)}let a={waveform:r[2]};return r[12]!==void 0&&(a.currentVolume=r[12]),r[1]!==void 0&&(a.show_volume_slider=r[1]),e=new Dt({props:a}),Q.push(()=>pe(e,"currentVolume",s)),Q.push(()=>pe(e,"show_volume_slider",o)),{c(){q(e.$$.fragment)},m(u,h){X(e,u,h),n=!0},p(u,h){const l={};h[0]&4&&(l.waveform=u[2]),!t&&h[0]&4096&&(t=!0,l.currentVolume=u[12],ve(()=>t=!1)),!i&&h[0]&2&&(i=!0,l.show_volume_slider=u[1],ve(()=>i=!1)),e.$set(l)},i(u){n||(A(e.$$.fragment,u),n=!0)},o(u){W(e.$$.fragment,u),n=!1},d(u){G(e,u)}}}function St(r){let e,t;return e=new Ge({}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function Pt(r){let e,t;return e=new qe({}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function He(r){let e,t,i,n,s,o=r[6]&&r[0]===""&&Ve(r);const a=[At,Tt],u=[];function h(l,c){return l[0]===""?0:1}return t=h(r),i=u[t]=a[t](r),{c(){o&&o.c(),e=K(),i.c(),n=De()},m(l,c){o&&o.m(l,c),j(l,e,c),u[t].m(l,c),j(l,n,c),s=!0},p(l,c){l[6]&&l[0]===""?o?(o.p(l,c),c[0]&65&&A(o,1)):(o=Ve(l),o.c(),A(o,1),o.m(e.parentNode,e)):o&&(oe(),W(o,1,1,()=>{o=null}),le());let v=t;t=h(l),t===v?u[t].p(l,c):(oe(),W(u[v],1,1,()=>{u[v]=null}),le(),i=u[t],i?i.p(l,c):(i=u[t]=a[t](l),i.c()),A(i,1),i.m(n.parentNode,n))},i(l){s||(A(o),A(i),s=!0)},o(l){W(o),W(i),s=!1},d(l){l&&(B(e),B(n)),o&&o.d(l),u[t].d(l)}}}function Ve(r){let e,t,i,n,s;return t=new Ze({}),{c(){e=O("button"),q(t.$$.fragment),d(e,"class","action icon svelte-ije4bl"),d(e,"aria-label","Reset audio")},m(o,a){j(o,e,a),X(t,e,null),i=!0,n||(s=F(e,"click",r[33]),n=!0)},p:V,i(o){i||(A(t.$$.fragment,o),i=!0)},o(o){W(t.$$.fragment,o),i=!1},d(o){o&&B(e),G(t),n=!1,s()}}}function Tt(r){let e,t,i,n,s;return{c(){e=O("button"),e.textContent="Trim",t=K(),i=O("button"),i.textContent="Cancel",d(e,"class","text-button svelte-ije4bl"),d(i,"class","text-button svelte-ije4bl")},m(o,a){j(o,e,a),j(o,t,a),j(o,i,a),n||(s=[F(e,"click",r[14]),F(i,"click",r[16])],n=!0)},p:V,i:V,o:V,d(o){o&&(B(e),B(t),B(i)),n=!1,Ce(s)}}}function At(r){let e,t,i,n,s;return t=new Xe({}),{c(){e=O("button"),q(t.$$.fragment),d(e,"class","action icon svelte-ije4bl"),d(e,"aria-label","Trim audio to selection")},m(o,a){j(o,e,a),X(t,e,null),i=!0,n||(s=F(e,"click",r[16]),n=!0)},p:V,i(o){i||(A(t.$$.fragment,o),i=!0)},o(o){W(t.$$.fragment,o),i=!1},d(o){o&&B(e),G(t),n=!1,s()}}}function Wt(r){let e,t,i,n,s,o,a,u,h,l,c,v,f,m,k,D,R,p,S,C,w,P,_,L,M,Z,N,I,ne,se;n=new Lt({props:{currentVolume:r[12]}});let z=r[1]&&ze(r);k=new Je({});const ae=[Pt,St],J=[];function ue(E,U){return E[5]?0:1}S=ue(r),C=J[S]=ae[S](r),L=new $e({});let H=r[10]&&r[7]&&He(r);return{c(){e=O("div"),t=O("div"),i=O("button"),q(n.$$.fragment),s=K(),z&&z.c(),o=K(),a=O("button"),u=O("span"),h=me(r[11]),l=me("x"),v=K(),f=O("div"),m=O("button"),q(k.$$.fragment),R=K(),p=O("button"),C.c(),P=K(),_=O("button"),q(L.$$.fragment),Z=K(),N=O("div"),H&&H.c(),d(i,"class","action icon volume svelte-ije4bl"),d(i,"aria-label","Adjust volume"),we(i,"color",r[1]?"var(--color-accent)":"var(--neutral-400)"),d(a,"class","playback icon svelte-ije4bl"),d(a,"aria-label",c=`Adjust playback speed to ${r[13][(r[13].indexOf(r[11])+1)%r[13].length]}x`),ye(a,"hidden",r[1]),d(t,"class","control-wrapper svelte-ije4bl"),d(m,"class","rewind icon svelte-ije4bl"),d(m,"aria-label",D=`Skip backwards by ${fe(r[3],r[9].skip_length)} seconds`),d(p,"class","play-pause-button icon svelte-ije4bl"),d(p,"aria-label",w=r[5]?r[4]("audio.pause"):r[4]("audio.play")),d(_,"class","skip icon svelte-ije4bl"),d(_,"aria-label",M="Skip forward by "+fe(r[3],r[9].skip_length)+" seconds"),d(f,"class","play-pause-wrapper svelte-ije4bl"),d(N,"class","settings-wrapper svelte-ije4bl"),d(e,"class","controls svelte-ije4bl"),d(e,"data-testid","waveform-controls")},m(E,U){j(E,e,U),y(e,t),y(t,i),X(n,i,null),y(t,s),z&&z.m(t,null),y(t,o),y(t,a),y(a,u),y(u,h),y(u,l),y(e,v),y(e,f),y(f,m),X(k,m,null),y(f,R),y(f,p),J[S].m(p,null),y(f,P),y(f,_),X(L,_,null),y(e,Z),y(e,N),H&&H.m(N,null),I=!0,ne||(se=[F(i,"click",r[26]),F(a,"click",r[29]),F(m,"click",r[30]),F(p,"click",r[31]),F(_,"click",r[32])],ne=!0)},p(E,U){const de={};U[0]&4096&&(de.currentVolume=E[12]),n.$set(de),U[0]&2&&we(i,"color",E[1]?"var(--color-accent)":"var(--neutral-400)"),E[1]?z?(z.p(E,U),U[0]&2&&A(z,1)):(z=ze(E),z.c(),A(z,1),z.m(t,o)):z&&(oe(),W(z,1,1,()=>{z=null}),le()),(!I||U[0]&2048)&&je(h,E[11]),(!I||U[0]&2048&&c!==(c=`Adjust playback speed to ${E[13][(E[13].indexOf(E[11])+1)%E[13].length]}x`))&&d(a,"aria-label",c),(!I||U[0]&2)&&ye(a,"hidden",E[1]),(!I||U[0]&520&&D!==(D=`Skip backwards by ${fe(E[3],E[9].skip_length)} seconds`))&&d(m,"aria-label",D);let b=S;S=ue(E),S!==b&&(oe(),W(J[b],1,1,()=>{J[b]=null}),le(),C=J[S],C||(C=J[S]=ae[S](E),C.c()),A(C,1),C.m(p,null)),(!I||U[0]&48&&w!==(w=E[5]?E[4]("audio.pause"):E[4]("audio.play")))&&d(p,"aria-label",w),(!I||U[0]&520&&M!==(M="Skip forward by "+fe(E[3],E[9].skip_length)+" seconds"))&&d(_,"aria-label",M),E[10]&&E[7]?H?(H.p(E,U),U[0]&1152&&A(H,1)):(H=He(E),H.c(),A(H,1),H.m(N,null)):H&&(oe(),W(H,1,1,()=>{H=null}),le())},i(E){I||(A(n.$$.fragment,E),A(z),A(k.$$.fragment,E),A(C),A(L.$$.fragment,E),A(H),I=!0)},o(E){W(n.$$.fragment,E),W(z),W(k.$$.fragment,E),W(C),W(L.$$.fragment,E),W(H),I=!1},d(E){E&&B(e),G(n),z&&z.d(),G(k),J[S].d(),G(L),H&&H.d(),ne=!1,Ce(se)}}}function Ot(r,e,t){let{waveform:i}=e,{audio_duration:n}=e,{i18n:s}=e,{playing:o}=e,{show_redo:a=!1}=e,{interactive:u=!1}=e,{handle_trim_audio:h}=e,{mode:l=""}=e,{container:c}=e,{handle_reset_value:v}=e,{waveform_options:f={}}=e,{trim_region_settings:m={}}=e,{show_volume_slider:k=!1}=e,{editable:D=!0}=e,{trimDuration:R=0}=e,p=[.5,1,1.5,2],S=p[1],C=null,w=null,P,_,L="",M=1;const Z=()=>{C&&(t(22,w=C?.addRegion({start:n/4,end:n/2,...m})),t(17,R=w.end-w.start))},N=()=>{if(i&&C&&w){const b=w.start,ie=w.end;h(b,ie),t(0,l=""),t(22,w=null)}},I=()=>{C?.getRegions().forEach(b=>{b.remove()}),C?.clearRegions()},ne=()=>{I(),l==="edit"?t(0,l=""):(t(0,l="edit"),Z())},se=(b,ie)=>{let re,g;w&&(b==="left"?ie==="ArrowLeft"?(re=w.start-.05,g=w.end):(re=w.start+.05,g=w.end):ie==="ArrowLeft"?(re=w.start,g=w.end-.05):(re=w.start,g=w.end+.05),w.setOptions({start:re,end:g}),t(17,R=w.end-w.start))},z=()=>t(1,k=!k);function ae(b){M=b,t(12,M)}function J(b){k=b,t(1,k)}const ue=()=>{t(11,S=p[(p.indexOf(S)+1)%p.length]),i?.setPlaybackRate(S)},H=()=>i?.skip(fe(n,f.skip_length)*-1),E=()=>i?.playPause(),U=()=>i?.skip(fe(n,f.skip_length)),de=()=>{v(),I(),t(0,l="")};return r.$$set=b=>{"waveform"in b&&t(2,i=b.waveform),"audio_duration"in b&&t(3,n=b.audio_duration),"i18n"in b&&t(4,s=b.i18n),"playing"in b&&t(5,o=b.playing),"show_redo"in b&&t(6,a=b.show_redo),"interactive"in b&&t(7,u=b.interactive),"handle_trim_audio"in b&&t(18,h=b.handle_trim_audio),"mode"in b&&t(0,l=b.mode),"container"in b&&t(19,c=b.container),"handle_reset_value"in b&&t(8,v=b.handle_reset_value),"waveform_options"in b&&t(9,f=b.waveform_options),"trim_region_settings"in b&&t(20,m=b.trim_region_settings),"show_volume_slider"in b&&t(1,k=b.show_volume_slider),"editable"in b&&t(10,D=b.editable),"trimDuration"in b&&t(17,R=b.trimDuration)},r.$$.update=()=>{if(r.$$.dirty[0]&524292&&t(21,C=c&&i?i.registerPlugin(Pe.create()):null),r.$$.dirty[0]&2097152&&C?.on("region-out",b=>{b.play()}),r.$$.dirty[0]&2097152&&C?.on("region-updated",b=>{t(17,R=b.end-b.start)}),r.$$.dirty[0]&2097152&&C?.on("region-clicked",(b,ie)=>{ie.stopPropagation(),t(22,w=b),b.play()}),r.$$.dirty[0]&31981568&&w){const b=c.children[0].shadowRoot;t(24,_=b.querySelector('[data-resize="right"]')),t(23,P=b.querySelector('[data-resize="left"]')),P&&_&&(P.setAttribute("role","button"),_.setAttribute("role","button"),P?.setAttribute("aria-label","Drag to adjust start time"),_?.setAttribute("aria-label","Drag to adjust end time"),P?.setAttribute("tabindex","0"),_?.setAttribute("tabindex","0"),P.addEventListener("focus",()=>{C&&t(25,L="left")}),_.addEventListener("focus",()=>{C&&t(25,L="right")}))}r.$$.dirty[0]&35651584&&C&&window.addEventListener("keydown",b=>{b.key==="ArrowLeft"?se(L,"ArrowLeft"):b.key==="ArrowRight"&&se(L,"ArrowRight")})},[l,k,i,n,s,o,a,u,v,f,D,S,M,p,N,I,ne,R,h,c,m,C,w,P,_,L,z,ae,J,ue,H,E,U,de]}class zt extends ${constructor(e){super(),ee(this,e,Ot,Wt,te,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),T()}get audio_duration(){return this.$$.ctx[3]}set audio_duration(e){this.$$set({audio_duration:e}),T()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),T()}get playing(){return this.$$.ctx[5]}set playing(e){this.$$set({playing:e}),T()}get show_redo(){return this.$$.ctx[6]}set show_redo(e){this.$$set({show_redo:e}),T()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),T()}get handle_trim_audio(){return this.$$.ctx[18]}set handle_trim_audio(e){this.$$set({handle_trim_audio:e}),T()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),T()}get container(){return this.$$.ctx[19]}set container(e){this.$$set({container:e}),T()}get handle_reset_value(){return this.$$.ctx[8]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),T()}get waveform_options(){return this.$$.ctx[9]}set waveform_options(e){this.$$set({waveform_options:e}),T()}get trim_region_settings(){return this.$$.ctx[20]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),T()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),T()}get editable(){return this.$$.ctx[10]}set editable(e){this.$$set({editable:e}),T()}get trimDuration(){return this.$$.ctx[17]}set trimDuration(e){this.$$set({trimDuration:e}),T()}}function Ht(r){let e,t,i,n,s,o,a,u,h,l,c,v,f,m,k,D,R,p=r[0]==="edit"&&r[17]>0&&xe(r);function S(_){r[32](_)}function C(_){r[33](_)}function w(_){r[34](_)}let P={container:r[10],waveform:r[11],playing:r[15],audio_duration:r[16],i18n:r[3],interactive:r[4],handle_trim_audio:r[21],show_redo:r[4],handle_reset_value:r[9],waveform_options:r[8],trim_region_settings:r[6],editable:r[5]};return r[0]!==void 0&&(P.mode=r[0]),r[17]!==void 0&&(P.trimDuration=r[17]),r[18]!==void 0&&(P.show_volume_slider=r[18]),v=new zt({props:P}),Q.push(()=>pe(v,"mode",S)),Q.push(()=>pe(v,"trimDuration",C)),Q.push(()=>pe(v,"show_volume_slider",w)),{c(){e=O("div"),t=O("div"),i=O("div"),n=K(),s=O("div"),o=O("time"),o.textContent="0:00",a=K(),u=O("div"),p&&p.c(),h=K(),l=O("time"),l.textContent="0:00",c=K(),q(v.$$.fragment),d(i,"id","waveform"),d(i,"class","svelte-19usgod"),we(i,"height",r[10]?null:"58px"),d(t,"class","waveform-container svelte-19usgod"),d(o,"id","time"),d(o,"class","svelte-19usgod"),d(l,"id","duration"),d(l,"class","svelte-19usgod"),d(s,"class","timestamps svelte-19usgod"),d(e,"class","component-wrapper svelte-19usgod"),d(e,"data-testid",D=r[2]?"waveform-"+r[2]:"unlabelled-audio")},m(_,L){j(_,e,L),y(e,t),y(t,i),r[29](i),y(e,n),y(e,s),y(s,o),r[30](o),y(s,a),y(s,u),p&&p.m(u,null),y(u,h),y(u,l),r[31](l),y(e,c),X(v,e,null),R=!0},p(_,L){L[0]&1024&&we(i,"height",_[10]?null:"58px"),_[0]==="edit"&&_[17]>0?p?p.p(_,L):(p=xe(_),p.c(),p.m(u,h)):p&&(p.d(1),p=null);const M={};L[0]&1024&&(M.container=_[10]),L[0]&2048&&(M.waveform=_[11]),L[0]&32768&&(M.playing=_[15]),L[0]&65536&&(M.audio_duration=_[16]),L[0]&8&&(M.i18n=_[3]),L[0]&16&&(M.interactive=_[4]),L[0]&16&&(M.show_redo=_[4]),L[0]&512&&(M.handle_reset_value=_[9]),L[0]&256&&(M.waveform_options=_[8]),L[0]&64&&(M.trim_region_settings=_[6]),L[0]&32&&(M.editable=_[5]),!f&&L[0]&1&&(f=!0,M.mode=_[0],ve(()=>f=!1)),!m&&L[0]&131072&&(m=!0,M.trimDuration=_[17],ve(()=>m=!1)),!k&&L[0]&262144&&(k=!0,M.show_volume_slider=_[18],ve(()=>k=!1)),v.$set(M),(!R||L[0]&4&&D!==(D=_[2]?"waveform-"+_[2]:"unlabelled-audio"))&&d(e,"data-testid",D)},i(_){R||(A(v.$$.fragment,_),R=!0)},o(_){W(v.$$.fragment,_),R=!1},d(_){_&&B(e),r[29](null),r[30](null),p&&p.d(),r[31](null),G(v)}}}function Vt(r){let e,t;return e=new Ye({props:{size:"small",$$slots:{default:[xt]},$$scope:{ctx:r}}}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},p(i,n){const s={};n[1]&256&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function xe(r){let e,t=ke(r[17])+"",i;return{c(){e=O("time"),i=me(t),d(e,"id","trim-duration"),d(e,"class","svelte-19usgod")},m(n,s){j(n,e,s),y(e,i)},p(n,s){s[0]&131072&&t!==(t=ke(n[17])+"")&&je(i,t)},d(n){n&&B(e)}}}function xt(r){let e,t;return e=new Fe({}),{c(){q(e.$$.fragment)},m(i,n){X(e,i,n),t=!0},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){W(e.$$.fragment,i),t=!1},d(i){G(e,i)}}}function Nt(r){let e,t,i,n,s,o,a,u,h;const l=[Vt,Ht],c=[];function v(f,m){return f[1]===null?0:f[14]?1:-1}return~(n=v(r))&&(s=c[n]=l[n](r)),{c(){e=O("audio"),i=K(),s&&s.c(),o=De(),d(e,"class","standard-player svelte-19usgod"),e.controls=!0,e.autoplay=t=r[7].autoplay,ye(e,"hidden",r[14])},m(f,m){j(f,e,m),r[26](e),j(f,i,m),~n&&c[n].m(f,m),j(f,o,m),a=!0,u||(h=[F(e,"load",r[25]),F(e,"ended",r[27]),F(e,"play",r[28])],u=!0)},p(f,m){(!a||m[0]&128&&t!==(t=f[7].autoplay))&&(e.autoplay=t),(!a||m[0]&16384)&&ye(e,"hidden",f[14]);let k=n;n=v(f),n===k?~n&&c[n].p(f,m):(s&&(oe(),W(c[k],1,1,()=>{c[k]=null}),le()),~n?(s=c[n],s?s.p(f,m):(s=c[n]=l[n](f),s.c()),A(s,1),s.m(o.parentNode,o)):s=null)},i(f){a||(A(s),a=!0)},o(f){W(s),a=!1},d(f){f&&(B(e),B(i),B(o)),r[26](null),~n&&c[n].d(f),u=!1,Ce(h)}}}function jt(r,e,t){let i,n,{value:s=null}=e,{label:o}=e,{i18n:a}=e,{dispatch_blob:u=()=>Promise.resolve()}=e,{interactive:h=!1}=e,{editable:l=!0}=e,{trim_region_settings:c={}}=e,{waveform_settings:v}=e,{waveform_options:f}=e,{mode:m=""}=e,{loop:k}=e,{handle_reset_value:D=()=>{}}=e,R,p,S=!1,C,w,P,_=0,L=!1,M,Z=!1;const N=Ue(),I=()=>{t(11,p=Se.create({container:R,...v})),Te(s?.url).then(g=>{if(g&&p)return p.load(g)})},ne=async(g,Y)=>{t(0,m="");const _e=p?.getDecodedData();_e&&await _t(_e,g,Y,v.sampleRate).then(async ge=>{await u([ge],"change"),p?.destroy(),t(10,R.innerHTML="",R)}),N("edit")};async function se(g){Z=!1,await Te(g).then(Y=>{!Y||s?.is_stream||(f.show_recording_waveform?p?.load(Y):M&&t(19,M.src=Y,M))})}function z(g){if(!(!g||!g.is_stream||!g.url)&&M)if(he.isSupported()&&!Z){const Y=new he({maxBufferLength:1,maxMaxBufferLength:1,lowLatencyMode:!0});Y.loadSource(g.url),Y.attachMedia(M),Y.on(he.Events.MANIFEST_PARSED,function(){v.autoplay&&M.play()}),Y.on(he.Events.ERROR,function(_e,ge){if(console.error("HLS error:",_e,ge),ge.fatal)switch(ge.type){case he.ErrorTypes.NETWORK_ERROR:console.error("Fatal network error encountered, trying to recover"),Y.startLoad();break;case he.ErrorTypes.MEDIA_ERROR:console.error("Fatal media error encountered, trying to recover"),Y.recoverMediaError();break;default:console.error("Fatal error, cannot recover"),Y.destroy();break}}),Z=!0}else Z||(t(19,M.src=g.url,M),v.autoplay&&M.play(),Z=!0)}Ne(()=>{window.addEventListener("keydown",g=>{!p||L||(g.key==="ArrowRight"&&m!=="edit"?We(p,.1):g.key==="ArrowLeft"&&m!=="edit"&&We(p,-.1))})});function ae(g){Ie.call(this,r,g)}function J(g){Q[g?"unshift":"push"](()=>{M=g,t(19,M)})}const ue=()=>N("stop"),H=()=>N("play");function E(g){Q[g?"unshift":"push"](()=>{R=g,t(10,R),t(14,n),t(11,p),t(8,f),t(1,s)})}function U(g){Q[g?"unshift":"push"](()=>{C=g,t(12,C),t(11,p)})}function de(g){Q[g?"unshift":"push"](()=>{w=g,t(13,w),t(11,p)})}function b(g){m=g,t(0,m)}function ie(g){_=g,t(17,_)}function re(g){L=g,t(18,L)}return r.$$set=g=>{"value"in g&&t(1,s=g.value),"label"in g&&t(2,o=g.label),"i18n"in g&&t(3,a=g.i18n),"dispatch_blob"in g&&t(22,u=g.dispatch_blob),"interactive"in g&&t(4,h=g.interactive),"editable"in g&&t(5,l=g.editable),"trim_region_settings"in g&&t(6,c=g.trim_region_settings),"waveform_settings"in g&&t(7,v=g.waveform_settings),"waveform_options"in g&&t(8,f=g.waveform_options),"mode"in g&&t(0,m=g.mode),"loop"in g&&t(23,k=g.loop),"handle_reset_value"in g&&t(9,D=g.handle_reset_value)},r.$$.update=()=>{r.$$.dirty[0]&2&&t(24,i=s?.url),r.$$.dirty[0]&258&&t(14,n=f.show_recording_waveform&&!s?.is_stream),r.$$.dirty[0]&19456&&n&&R!==void 0&&R!==null&&(p!==void 0&&p.destroy(),t(10,R.innerHTML="",R),I(),t(15,S=!1)),r.$$.dirty[0]&10240&&p?.on("decode",g=>{t(16,P=g),w&&t(13,w.textContent=ke(g),w)}),r.$$.dirty[0]&6144&&p?.on("timeupdate",g=>C&&t(12,C.textContent=ke(g),C)),r.$$.dirty[0]&2176&&p?.on("ready",()=>{v.autoplay?p?.play():p?.stop()}),r.$$.dirty[0]&8390656&&p?.on("finish",()=>{k?p?.play():(t(15,S=!1),N("stop"))}),r.$$.dirty[0]&2048&&p?.on("pause",()=>{t(15,S=!1),N("pause")}),r.$$.dirty[0]&2048&&p?.on("play",()=>{t(15,S=!0),N("play")}),r.$$.dirty[0]&2048&&p?.on("load",()=>{N("load")}),r.$$.dirty[0]&16777216&&i&&se(i),r.$$.dirty[0]&2&&z(s)},[m,s,o,a,h,l,c,v,f,D,R,p,C,w,n,S,P,_,L,M,N,ne,u,k,i,ae,J,ue,H,E,U,de,b,ie,re]}class Bt extends ${constructor(e){super(),ee(this,e,jt,Nt,te,{value:1,label:2,i18n:3,dispatch_blob:22,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,loop:23,handle_reset_value:9},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),T()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),T()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),T()}get dispatch_blob(){return this.$$.ctx[22]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),T()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),T()}get editable(){return this.$$.ctx[5]}set editable(e){this.$$set({editable:e}),T()}get trim_region_settings(){return this.$$.ctx[6]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),T()}get waveform_settings(){return this.$$.ctx[7]}set waveform_settings(e){this.$$set({waveform_settings:e}),T()}get waveform_options(){return this.$$.ctx[8]}set waveform_options(e){this.$$set({waveform_options:e}),T()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),T()}get loop(){return this.$$.ctx[23]}set loop(e){this.$$set({loop:e}),T()}get handle_reset_value(){return this.$$.ctx[9]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),T()}}const Jt=Bt;export{Jt as A,zt as W,Se as a,_t as p,We as s};
//# sourceMappingURL=AudioPlayer-Dn45keYP.js.map
