<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Geerath
        </h5>
        <p>
         Student
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigate to Amazon.com, search for 'wireless headphones under $100', filter by customer ratings (4+ stars), open a product details, read reviews
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Find a suitable pair of wireless headphones under $100 with 4+ star ratings based on customer reviews."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="The customer expects to find a product that fits their budget, meets quality expectations, and has positive customer reviews."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Start
     </div>
     <div class="stage" style="background-color: #F55050">
      Navigation
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Search Input
     </div>
     <div class="stage" style="background-color: #F7C934">
      Search Results
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Filtering
     </div>
     <div class="stage" style="background-color: #5E2590">
      Product Selection
     </div>
     <div class="stage" style="background-color: #F55050">
      Customer Reviews Access
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Review Analysis
     </div>
     <div class="stage" style="background-color: #F7C934">
      Completion
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Step initiated to begin the process of searching for wireless headphones.
     </div>
     <div class="stage" style="background-color: #F55050">
      Successfully navigates to Amazon.com.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Inputs 'wireless headphones under $100' in the search box and sees suggestions.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Completes search initiation and reaches search results page.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Applies filter for customer ratings (4+ stars).
     </div>
     <div class="stage" style="background-color: #5E2590">
      Chooses and opens the details page for a product with high ratings.
     </div>
     <div class="stage" style="background-color: #F55050">
      Scrolls to the 'Customer Reviews' section to analyze feedback.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Extracts and analyzes customer reviews to finalize decision.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Finishes the process and summarizes findings from customer reviews.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral, task is just starting.
     </div>
     <div class="stage" style="background-color: #F55050">
      Positive, initial progress made.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Neutral to slightly positive, task progressing smoothly.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Positive; task progressing.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Satisfied; narrowing down options.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Curious and engaged; exploring product details.
     </div>
     <div class="stage" style="background-color: #F55050">
      Interested; gathering insights for decision making.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Mixed; positive emotions from good reviews, frustration or concern from negative reviews.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Satisfied; informed decision-making achieved.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      None identified yet.
     </div>
     <div class="stage" style="background-color: #F55050">
      None at this stage.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Partial completion; still needs to initiate search fully.
     </div>
     <div class="stage" style="background-color: #F7C934">
      None identified.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      None identified.
     </div>
     <div class="stage" style="background-color: #5E2590">
      None identified.
     </div>
     <div class="stage" style="background-color: #F55050">
      None identified.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Concerns about sound quality and disconnection issues for some products.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Uncertainty about product reliability for some specific cases due to mixed reviews.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
