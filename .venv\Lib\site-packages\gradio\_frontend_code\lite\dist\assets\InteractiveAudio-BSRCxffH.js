const __vite__fileDeps=["./module-B6bz2o68.js","./module-C-VadMaF.js","../lite.js","../lite.css","./module-BA06XY8_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{a as be,i as we,s as ve,f as b,y as M,z as R,d as C,D as fe,l as T,o as Ee,aq as Pe,e as Me,as as ft,w as Q,R as Re,C as v,x as X,O as U,a7 as Z,b as I,c as H,m as V,M as re,a8 as x,k as A,t as L,n as Y,V as dt,h as me,j as ge,a5 as Ve,$ as Se,W as ze,aa as _t,az as Be,ax as mt,p as ce,q as gt,u as ht,r as bt,v as wt}from"../lite.js";import{U as vt}from"./Upload-Do_omv-N.js";import{M as Ye}from"./ModifyUpload-b77W1M2_.js";import{B as pt}from"./BlockLabel-DWW9BWN3.js";import{M as kt}from"./Music-BCIGqdvV.js";import{a as yt,S as Rt}from"./SelectSource-kJI_8u2f.js";import{S as Dt}from"./StreamingBar-lVbwTGD1.js";import{s as Ie,W as Et,p as Ce,a as De,A as Mt}from"./AudioPlayer-Dn45keYP.js";import{P as At}from"./Trim-CDsEvQ4G.js";import{f as oe}from"./utils-BsGrhMNe.js";function ye(i,e,t,n){return new(t||(t=Promise))(function(r,a){function o(u){try{l(n.next(u))}catch(s){a(s)}}function c(u){try{l(n.throw(u))}catch(s){a(s)}}function l(u){var s;u.done?r(u.value):(s=u.value,s instanceof t?s:new t(function(f){f(s)})).then(o,c)}l((n=n.apply(i,[])).next())})}class Lt{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,n){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),n?.once){const r=()=>{this.removeEventListener(e,r),this.removeEventListener(e,t)};return this.addEventListener(e,r),r}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;(n=this.listeners[e])===null||n===void 0||n.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(n=>n(...t))}}class Pt extends Lt{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const St=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class pe extends Pt{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new pe(e||{})}renderMicStream(e){const t=new AudioContext,n=t.createMediaStreamSource(e),r=t.createAnalyser();n.connect(r);const a=r.frequencyBinCount,o=new Float32Array(a),c=a/t.sampleRate;let l;const u=()=>{r.getFloatTimeDomainData(o),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[o],c)),l=requestAnimationFrame(u)};return u(),()=>{cancelAnimationFrame(l),n?.disconnect(),t?.close()}}startMic(e){return ye(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(r){throw new Error("Error accessing the microphone: "+r.message)}const n=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",n)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return ye(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),n=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||St.find(a=>MediaRecorder.isTypeSupported(a)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=n,this.stopRecording();const r=[];n.ondataavailable=a=>{a.data.size>0&&r.push(a.data)},n.onstop=()=>{var a;const o=new Blob(r,{type:n.mimeType});this.emit("record-end",o),this.options.renderRecordedAudio!==!1&&((a=this.wavesurfer)===null||a===void 0||a.load(URL.createObjectURL(o)))},n.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return ye(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}function Te(i,e,t){const n=i.slice();return n[3]=e[t],n}function zt(i){let e,t=Pe(i[0]),n=[];for(let r=0;r<t.length;r+=1)n[r]=Ue(Te(i,t,r));return{c(){for(let r=0;r<n.length;r+=1)n[r].c();e=Me()},m(r,a){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(r,a);C(r,e,a)},p(r,a){if(a&1){t=Pe(r[0]);let o;for(o=0;o<t.length;o+=1){const c=Te(r,t,o);n[o]?n[o].p(c,a):(n[o]=Ue(c),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(r){r&&T(e),ft(n,r)}}}function Bt(i){let e,t=i[1]("audio.no_microphone")+"",n;return{c(){e=M("option"),n=Q(t),e.__value="",Re(e,e.__value)},m(r,a){C(r,e,a),v(e,n)},p(r,a){a&2&&t!==(t=r[1]("audio.no_microphone")+"")&&X(n,t)},d(r){r&&T(e)}}}function Ue(i){let e,t=i[3].label+"",n,r;return{c(){e=M("option"),n=Q(t),e.__value=r=i[3].deviceId,Re(e,e.__value)},m(a,o){C(a,e,o),v(e,n)},p(a,o){o&1&&t!==(t=a[3].label+"")&&X(n,t),o&1&&r!==(r=a[3].deviceId)&&(e.__value=r,Re(e,e.__value))},d(a){a&&T(e)}}}function It(i){let e,t;function n(o,c){return o[0].length===0?Bt:zt}let r=n(i),a=r(i);return{c(){e=M("select"),a.c(),R(e,"class","mic-select svelte-1ya9x7a"),R(e,"aria-label","Select input device"),e.disabled=t=i[0].length===0},m(o,c){C(o,e,c),a.m(e,null)},p(o,[c]){r===(r=n(o))&&a?a.p(o,c):(a.d(1),a=r(o),a&&(a.c(),a.m(e,null))),c&1&&t!==(t=o[0].length===0)&&(e.disabled=t)},i:fe,o:fe,d(o){o&&T(e),a.d()}}}function Ct(i,e,t){let{i18n:n}=e,{micDevices:r=[]}=e;const a=Ee();return i.$$set=o=>{"i18n"in o&&t(1,n=o.i18n),"micDevices"in o&&t(0,r=o.micDevices)},i.$$.update=()=>{if(i.$$.dirty&2&&typeof window<"u")try{let o=[];pe.getAvailableAudioDevices().then(c=>{t(0,r=c),c.forEach(l=>{l.deviceId&&o.push(l)}),t(0,r=o)})}catch(o){throw o instanceof DOMException&&o.name=="NotAllowedError"&&a("error",n("audio.allow_recording_access")),o}},[r,n]}class Ge extends be{constructor(e){super(),we(this,e,Ct,It,ve,{i18n:1,micDevices:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),b()}get micDevices(){return this.$$.ctx[0]}set micDevices(e){this.$$set({micDevices:e}),b()}}function je(i){let e,t;return{c(){e=M("time"),t=Q(i[2]),R(e,"class","duration-button duration svelte-1oiuk2f")},m(n,r){C(n,e,r),v(e,t)},p(n,r){r&4&&X(t,n[2])},d(n){n&&T(e)}}}function Tt(i){let e,t,n,r=i[1]("audio.record")+"",a,o,c,l=i[1]("audio.stop")+"",u,s,f,_,p=i[1]("audio.stop")+"",D,P,h,E,g,k,W=i[1]("audio.resume")+"",y,N,F,q,K,B,w,se;E=new At({});let j=i[4]&&!i[3]&&je(i);function $(S){i[23](S)}let de={i18n:i[1]};return i[5]!==void 0&&(de.micDevices=i[5]),q=new Ge({props:de}),U.push(()=>Z(q,"micDevices",$)),{c(){e=M("div"),t=M("div"),n=M("button"),a=Q(r),o=I(),c=M("button"),u=Q(l),f=I(),_=M("button"),D=Q(p),P=I(),h=M("button"),H(E.$$.fragment),g=I(),k=M("button"),y=Q(W),N=I(),j&&j.c(),F=I(),H(q.$$.fragment),R(n,"class","record record-button svelte-1oiuk2f"),R(c,"class",s="stop-button "+(i[0].isPaused()?"stop-button-paused":"")+" svelte-1oiuk2f"),R(_,"id","stop-paused"),R(_,"class","stop-button-paused svelte-1oiuk2f"),R(h,"aria-label","pause"),R(h,"class","pause-button svelte-1oiuk2f"),R(k,"class","resume-button svelte-1oiuk2f"),R(t,"class","wrapper svelte-1oiuk2f"),R(e,"class","controls svelte-1oiuk2f")},m(S,z){C(S,e,z),v(e,t),v(t,n),v(n,a),i[13](n),v(t,o),v(t,c),v(c,u),i[15](c),v(t,f),v(t,_),v(_,D),i[17](_),v(t,P),v(t,h),V(E,h,null),i[19](h),v(t,g),v(t,k),v(k,y),i[21](k),v(t,N),j&&j.m(t,null),v(e,F),V(q,e,null),B=!0,w||(se=[re(n,"click",i[14]),re(c,"click",i[16]),re(_,"click",i[18]),re(h,"click",i[20]),re(k,"click",i[22])],w=!0)},p(S,[z]){(!B||z&2)&&r!==(r=S[1]("audio.record")+"")&&X(a,r),(!B||z&2)&&l!==(l=S[1]("audio.stop")+"")&&X(u,l),(!B||z&1&&s!==(s="stop-button "+(S[0].isPaused()?"stop-button-paused":"")+" svelte-1oiuk2f"))&&R(c,"class",s),(!B||z&2)&&p!==(p=S[1]("audio.stop")+"")&&X(D,p),(!B||z&2)&&W!==(W=S[1]("audio.resume")+"")&&X(y,W),S[4]&&!S[3]?j?j.p(S,z):(j=je(S),j.c(),j.m(t,null)):j&&(j.d(1),j=null);const G={};z&2&&(G.i18n=S[1]),!K&&z&32&&(K=!0,G.micDevices=S[5],x(()=>K=!1)),q.$set(G)},i(S){B||(A(E.$$.fragment,S),A(q.$$.fragment,S),B=!0)},o(S){L(E.$$.fragment,S),L(q.$$.fragment,S),B=!1},d(S){S&&T(e),i[13](null),i[15](null),i[17](null),Y(E),i[19](null),i[21](null),j&&j.d(),Y(q),w=!1,dt(se)}}}function Ut(i,e,t){let{record:n}=e,{i18n:r}=e,{recording:a=!1}=e,o=[],c,l,u,s,f,_=!1,{record_time:p}=e,{show_recording_waveform:D}=e,{timing:P=!1}=e;function h(w){U[w?"unshift":"push"](()=>{c=w,t(6,c),t(0,n)})}const E=()=>n.startRecording();function g(w){U[w?"unshift":"push"](()=>{s=w,t(9,s),t(0,n)})}const k=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function W(w){U[w?"unshift":"push"](()=>{f=w,t(10,f),t(0,n)})}const y=()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopRecording()};function N(w){U[w?"unshift":"push"](()=>{l=w,t(7,l),t(0,n)})}const F=()=>n.pauseRecording();function q(w){U[w?"unshift":"push"](()=>{u=w,t(8,u),t(0,n)})}const K=()=>n.resumeRecording();function B(w){o=w,t(5,o)}return i.$$set=w=>{"record"in w&&t(0,n=w.record),"i18n"in w&&t(1,r=w.i18n),"recording"in w&&t(11,a=w.recording),"record_time"in w&&t(2,p=w.record_time),"show_recording_waveform"in w&&t(3,D=w.show_recording_waveform),"timing"in w&&t(4,P=w.timing)},i.$$.update=()=>{i.$$.dirty&1&&n.on("record-start",()=>{n.startMic(),t(6,c.style.display="none",c),t(9,s.style.display="flex",s),t(7,l.style.display="block",l)}),i.$$.dirty&1&&n.on("record-end",()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopMic(),t(6,c.style.display="flex",c),t(9,s.style.display="none",s),t(7,l.style.display="none",l),t(6,c.disabled=!1,c)}),i.$$.dirty&1&&n.on("record-pause",()=>{t(7,l.style.display="none",l),t(8,u.style.display="block",u),t(9,s.style.display="none",s),t(10,f.style.display="flex",f)}),i.$$.dirty&1&&n.on("record-resume",()=>{t(7,l.style.display="block",l),t(8,u.style.display="none",u),t(6,c.style.display="none",c),t(9,s.style.display="flex",s),t(10,f.style.display="none",f)}),i.$$.dirty&6145&&(a&&!_?(n.startRecording(),t(12,_=!0)):(n.stopRecording(),t(12,_=!1)))},[n,r,p,D,P,o,c,l,u,s,f,a,_,h,E,g,k,W,y,N,F,q,K,B]}class jt extends be{constructor(e){super(),we(this,e,Ut,Tt,ve,{record:0,i18n:1,recording:11,record_time:2,show_recording_waveform:3,timing:4})}get record(){return this.$$.ctx[0]}set record(e){this.$$set({record:e}),b()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),b()}get recording(){return this.$$.ctx[11]}set recording(e){this.$$set({recording:e}),b()}get record_time(){return this.$$.ctx[2]}set record_time(e){this.$$set({record_time:e}),b()}get show_recording_waveform(){return this.$$.ctx[3]}set show_recording_waveform(e){this.$$set({show_recording_waveform:e}),b()}get timing(){return this.$$.ctx[4]}set timing(e){this.$$set({timing:e}),b()}}function Oe(i){let e,t,n,r,a,o=i[0]==="edit"&&i[17]>0&&We(i);function c(s,f){return s[16]?Wt:Ot}let l=c(i),u=l(i);return{c(){e=M("div"),t=M("time"),t.textContent="0:00",n=I(),r=M("div"),o&&o.c(),a=I(),u.c(),R(t,"class","time svelte-9n45fh"),R(e,"class","timestamps svelte-9n45fh")},m(s,f){C(s,e,f),v(e,t),i[23](t),v(e,n),v(e,r),o&&o.m(r,null),v(r,a),u.m(r,null)},p(s,f){s[0]==="edit"&&s[17]>0?o?o.p(s,f):(o=We(s),o.c(),o.m(r,a)):o&&(o.d(1),o=null),l===(l=c(s))&&u?u.p(s,f):(u.d(1),u=l(s),u&&(u.c(),u.m(r,null)))},d(s){s&&T(e),i[23](null),o&&o.d(),u.d()}}}function We(i){let e,t=oe(i[17])+"",n;return{c(){e=M("time"),n=Q(t),R(e,"class","trim-duration svelte-9n45fh")},m(r,a){C(r,e,a),v(e,n)},p(r,a){a[0]&131072&&t!==(t=oe(r[17])+"")&&X(n,t)},d(r){r&&T(e)}}}function Ot(i){let e;return{c(){e=M("time"),e.textContent="0:00",R(e,"class","duration svelte-9n45fh")},m(t,n){C(t,e,n),i[24](e)},p:fe,d(t){t&&T(e),i[24](null)}}}function Wt(i){let e,t=oe(i[15])+"",n;return{c(){e=M("time"),n=Q(t),R(e,"class","duration svelte-9n45fh")},m(r,a){C(r,e,a),v(e,n)},p(r,a){a[0]&32768&&t!==(t=oe(r[15])+"")&&X(n,t)},d(r){r&&T(e)}}}function Ne(i){let e,t,n;function r(o){i[25](o)}let a={i18n:i[1],timing:i[16],recording:i[5],show_recording_waveform:i[2].show_recording_waveform,record_time:oe(i[15])};return i[7]!==void 0&&(a.record=i[7]),e=new jt({props:a}),U.push(()=>Z(e,"record",r)),{c(){H(e.$$.fragment)},m(o,c){V(e,o,c),n=!0},p(o,c){const l={};c[0]&2&&(l.i18n=o[1]),c[0]&65536&&(l.timing=o[16]),c[0]&32&&(l.recording=o[5]),c[0]&4&&(l.show_recording_waveform=o[2].show_recording_waveform),c[0]&32768&&(l.record_time=oe(o[15])),!t&&c[0]&128&&(t=!0,l.record=o[7],x(()=>t=!1)),e.$set(l)},i(o){n||(A(e.$$.fragment,o),n=!0)},o(o){L(e.$$.fragment,o),n=!1},d(o){Y(e,o)}}}function qe(i){let e,t,n,r,a;function o(s){i[26](s)}function c(s){i[27](s)}function l(s){i[28](s)}let u={container:i[11],playing:i[10],audio_duration:i[14],i18n:i[1],editable:i[4],interactive:!0,handle_trim_audio:i[18],show_redo:!0,handle_reset_value:i[3],waveform_options:i[2]};return i[6]!==void 0&&(u.waveform=i[6]),i[17]!==void 0&&(u.trimDuration=i[17]),i[0]!==void 0&&(u.mode=i[0]),e=new Et({props:u}),U.push(()=>Z(e,"waveform",o)),U.push(()=>Z(e,"trimDuration",c)),U.push(()=>Z(e,"mode",l)),{c(){H(e.$$.fragment)},m(s,f){V(e,s,f),a=!0},p(s,f){const _={};f[0]&2048&&(_.container=s[11]),f[0]&1024&&(_.playing=s[10]),f[0]&16384&&(_.audio_duration=s[14]),f[0]&2&&(_.i18n=s[1]),f[0]&16&&(_.editable=s[4]),f[0]&8&&(_.handle_reset_value=s[3]),f[0]&4&&(_.waveform_options=s[2]),!t&&f[0]&64&&(t=!0,_.waveform=s[6],x(()=>t=!1)),!n&&f[0]&131072&&(n=!0,_.trimDuration=s[17],x(()=>n=!1)),!r&&f[0]&1&&(r=!0,_.mode=s[0],x(()=>r=!1)),e.$set(_)},i(s){a||(A(e.$$.fragment,s),a=!0)},o(s){L(e.$$.fragment,s),a=!1},d(s){Y(e,s)}}}function Nt(i){let e,t,n,r,a,o,c,l,u=(i[16]||i[13])&&i[2].show_recording_waveform&&Oe(i),s=i[12]&&!i[13]&&Ne(i),f=i[6]&&i[13]&&qe(i);return{c(){e=M("div"),t=M("div"),n=I(),r=M("div"),a=I(),u&&u.c(),o=I(),s&&s.c(),c=I(),f&&f.c(),R(t,"class","microphone svelte-9n45fh"),R(t,"data-testid","microphone-waveform"),R(r,"data-testid","recording-waveform"),R(e,"class","component-wrapper svelte-9n45fh")},m(_,p){C(_,e,p),v(e,t),i[21](t),v(e,n),v(e,r),i[22](r),v(e,a),u&&u.m(e,null),v(e,o),s&&s.m(e,null),v(e,c),f&&f.m(e,null),l=!0},p(_,p){(_[16]||_[13])&&_[2].show_recording_waveform?u?u.p(_,p):(u=Oe(_),u.c(),u.m(e,o)):u&&(u.d(1),u=null),_[12]&&!_[13]?s?(s.p(_,p),p[0]&12288&&A(s,1)):(s=Ne(_),s.c(),A(s,1),s.m(e,c)):s&&(me(),L(s,1,1,()=>{s=null}),ge()),_[6]&&_[13]?f?(f.p(_,p),p[0]&8256&&A(f,1)):(f=qe(_),f.c(),A(f,1),f.m(e,null)):f&&(me(),L(f,1,1,()=>{f=null}),ge())},i(_){l||(A(s),A(f),l=!0)},o(_){L(s),L(f),l=!1},d(_){_&&T(e),i[21](null),i[22](null),u&&u.d(),s&&s.d(),f&&f.d()}}}function qt(i,e,t){let{mode:n}=e,{i18n:r}=e,{dispatch_blob:a}=e,{waveform_settings:o}=e,{waveform_options:c={show_recording_waveform:!0}}=e,{handle_reset_value:l}=e,{editable:u=!0}=e,{recording:s=!1}=e,f,_,p=!1,D,P,h,E=null,g,k,W,y=0,N,F=!1,q=0;const K=()=>{clearInterval(N),N=setInterval(()=>{t(15,y++,y)},1e3)},B=Ee();function w(){if(K(),t(16,F=!0),B("start_recording"),c.show_recording_waveform){let m=P;m&&(m.style.display="block")}}async function se(m){t(15,y=0),t(16,F=!1),clearInterval(N);try{const ee=await m.arrayBuffer(),O=await new AudioContext({sampleRate:o.sampleRate}).decodeAudioData(ee);O&&await Ce(O).then(async ne=>{await a([ne],"change"),await a([ne],"stop_recording")})}catch(ee){console.error(ee)}}const j=()=>{P&&t(12,P.innerHTML="",P),f!==void 0&&f.destroy(),P&&(f=De.create({...o,normalize:!1,container:P}),t(7,h=f.registerPlugin(pe.create())),h?.on("record-end",se),h?.on("record-start",w),h?.on("record-pause",()=>{B("pause_recording"),clearInterval(N)}),h?.on("record-end",m=>{t(13,E=URL.createObjectURL(m));const ee=P,ae=D;ee&&(ee.style.display="none"),ae&&E&&(ae.innerHTML="",$())}))},$=()=>{let m=D;!E||!m||t(6,_=De.create({container:m,url:E,...o}))},de=async(m,ee)=>{t(0,n="edit");const ae=_.getDecodedData();ae&&await Ce(ae,m,ee).then(async O=>{await a([O],"change"),await a([O],"stop_recording"),_.destroy(),$()}),B("edit")};Ve(()=>{j(),window.addEventListener("keydown",m=>{m.key==="ArrowRight"?Ie(_,.1):m.key==="ArrowLeft"&&Ie(_,-.1)})});function S(m){U[m?"unshift":"push"](()=>{P=m,t(12,P)})}function z(m){U[m?"unshift":"push"](()=>{D=m,t(11,D)})}function G(m){U[m?"unshift":"push"](()=>{g=m,t(8,g),t(6,_)})}function te(m){U[m?"unshift":"push"](()=>{k=m,t(9,k),t(6,_)})}function ie(m){h=m,t(7,h)}function _e(m){_=m,t(6,_)}function ke(m){q=m,t(17,q)}function le(m){n=m,t(0,n)}return i.$$set=m=>{"mode"in m&&t(0,n=m.mode),"i18n"in m&&t(1,r=m.i18n),"dispatch_blob"in m&&t(19,a=m.dispatch_blob),"waveform_settings"in m&&t(20,o=m.waveform_settings),"waveform_options"in m&&t(2,c=m.waveform_options),"handle_reset_value"in m&&t(3,l=m.handle_reset_value),"editable"in m&&t(4,u=m.editable),"recording"in m&&t(5,s=m.recording)},i.$$.update=()=>{i.$$.dirty[0]&128&&h?.on("record-resume",()=>{K()}),i.$$.dirty[0]&576&&_?.on("decode",m=>{t(14,W=m),k&&t(9,k.textContent=oe(m),k)}),i.$$.dirty[0]&320&&_?.on("timeupdate",m=>g&&t(8,g.textContent=oe(m),g)),i.$$.dirty[0]&64&&_?.on("pause",()=>{B("pause"),t(10,p=!1)}),i.$$.dirty[0]&64&&_?.on("play",()=>{B("play"),t(10,p=!0)}),i.$$.dirty[0]&64&&_?.on("finish",()=>{B("stop"),t(10,p=!1)})},[n,r,c,l,u,s,_,h,g,k,p,D,P,E,W,y,F,q,de,a,o,S,z,G,te,ie,_e,ke,le]}class Ft extends be{constructor(e){super(),we(this,e,qt,Nt,ve,{mode:0,i18n:1,dispatch_blob:19,waveform_settings:20,waveform_options:2,handle_reset_value:3,editable:4,recording:5},null,[-1,-1])}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),b()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),b()}get dispatch_blob(){return this.$$.ctx[19]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),b()}get waveform_settings(){return this.$$.ctx[20]}set waveform_settings(e){this.$$set({waveform_settings:e}),b()}get waveform_options(){return this.$$.ctx[2]}set waveform_options(e){this.$$set({waveform_options:e}),b()}get handle_reset_value(){return this.$$.ctx[3]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),b()}get editable(){return this.$$.ctx[4]}set editable(e){this.$$set({editable:e}),b()}get recording(){return this.$$.ctx[5]}set recording(e){this.$$set({recording:e}),b()}}function Fe(i){let e;return{c(){e=M("div"),Se(e,"display",i[0]?"block":"none")},m(t,n){C(t,e,n),i[11](e)},p(t,n){n&1&&Se(e,"display",t[0]?"block":"none")},d(t){t&&T(e),i[11](null)}}}function Ht(i){let e,t,n,r=i[4]("audio.record")+"",a,o,c;return{c(){e=M("button"),t=M("span"),t.innerHTML='<span class="dot"></span>',n=I(),a=Q(r),R(t,"class","record-icon"),R(e,"class","record-button svelte-1fz19cj")},m(l,u){C(l,e,u),v(e,t),v(e,n),v(e,a),o||(c=re(e,"click",i[14]),o=!0)},p(l,u){u&16&&r!==(r=l[4]("audio.record")+"")&&X(a,r)},i:fe,o:fe,d(l){l&&T(e),o=!1,c()}}}function Vt(i){let e,t,n,r,a=i[4]("audio.waiting")+"",o,c,l,u;return n=new yt({}),{c(){e=M("button"),t=M("div"),H(n.$$.fragment),r=I(),o=Q(a),R(t,"class","icon svelte-1fz19cj"),R(e,"class","spinner-button svelte-1fz19cj")},m(s,f){C(s,e,f),v(e,t),V(n,t,null),v(e,r),v(e,o),c=!0,l||(u=re(e,"click",i[13]),l=!0)},p(s,f){(!c||f&16)&&a!==(a=s[4]("audio.waiting")+"")&&X(o,a)},i(s){c||(A(n.$$.fragment,s),c=!0)},o(s){L(n.$$.fragment,s),c=!1},d(s){s&&T(e),Y(n),l=!1,u()}}}function Yt(i){let e,t,n,r=(i[1]?i[4]("audio.pause"):i[4]("audio.stop"))+"",a,o,c,l;return{c(){e=M("button"),t=M("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',n=I(),a=Q(r),R(t,"class","record-icon"),R(e,"class",o=ze(i[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")},m(u,s){C(u,e,s),v(e,t),v(e,n),v(e,a),c||(l=re(e,"click",i[12]),c=!0)},p(u,s){s&18&&r!==(r=(u[1]?u[4]("audio.pause"):u[4]("audio.stop"))+"")&&X(a,r),s&2&&o!==(o=ze(u[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")&&R(e,"class",o)},i:fe,o:fe,d(u){u&&T(e),c=!1,l()}}}function Gt(i){let e,t,n,r,a,o,c,l,u,s=i[5].show_recording_waveform&&Fe(i);const f=[Yt,Vt,Ht],_=[];function p(h,E){return h[0]&&!h[6]?0:h[0]&&h[6]?1:2}r=p(i),a=_[r]=f[r](i);function D(h){i[15](h)}let P={i18n:i[4]};return i[9]!==void 0&&(P.micDevices=i[9]),c=new Ge({props:P}),U.push(()=>Z(c,"micDevices",D)),{c(){e=M("div"),s&&s.c(),t=I(),n=M("div"),a.c(),o=I(),H(c.$$.fragment),R(n,"class","controls svelte-1fz19cj"),R(e,"class","mic-wrap svelte-1fz19cj")},m(h,E){C(h,e,E),s&&s.m(e,null),v(e,t),v(e,n),_[r].m(n,null),v(n,o),V(c,n,null),u=!0},p(h,[E]){h[5].show_recording_waveform?s?s.p(h,E):(s=Fe(h),s.c(),s.m(e,t)):s&&(s.d(1),s=null);let g=r;r=p(h),r===g?_[r].p(h,E):(me(),L(_[g],1,1,()=>{_[g]=null}),ge(),a=_[r],a?a.p(h,E):(a=_[r]=f[r](h),a.c()),A(a,1),a.m(n,o));const k={};E&16&&(k.i18n=h[4]),!l&&E&512&&(l=!0,k.micDevices=h[9],x(()=>l=!1)),c.$set(k)},i(h){u||(A(a),A(c.$$.fragment,h),u=!0)},o(h){L(a),L(c.$$.fragment,h),u=!1},d(h){h&&T(e),s&&s.d(),_[r].d(),Y(c)}}}function Jt(i,e,t){let{recording:n=!1}=e,{paused_recording:r=!1}=e,{stop:a}=e,{record:o}=e,{i18n:c}=e,{waveform_settings:l}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{waiting:s=!1}=e,f,_,p,D=[];Ve(()=>{P()});const P=()=>{f!==void 0&&f.destroy(),p&&(f=De.create({...l,height:100,container:p}),t(7,_=f.registerPlugin(pe.create())))};function h(y){U[y?"unshift":"push"](()=>{p=y,t(8,p)})}const E=()=>{_?.stopMic(),a()},g=()=>{a()},k=()=>{_?.startMic(),o()};function W(y){D=y,t(9,D)}return i.$$set=y=>{"recording"in y&&t(0,n=y.recording),"paused_recording"in y&&t(1,r=y.paused_recording),"stop"in y&&t(2,a=y.stop),"record"in y&&t(3,o=y.record),"i18n"in y&&t(4,c=y.i18n),"waveform_settings"in y&&t(10,l=y.waveform_settings),"waveform_options"in y&&t(5,u=y.waveform_options),"waiting"in y&&t(6,s=y.waiting)},[n,r,a,o,c,u,s,_,p,D,l,h,E,g,k,W]}class Kt extends be{constructor(e){super(),we(this,e,Jt,Gt,ve,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:10,waveform_options:5,waiting:6})}get recording(){return this.$$.ctx[0]}set recording(e){this.$$set({recording:e}),b()}get paused_recording(){return this.$$.ctx[1]}set paused_recording(e){this.$$set({paused_recording:e}),b()}get stop(){return this.$$.ctx[2]}set stop(e){this.$$set({stop:e}),b()}get record(){return this.$$.ctx[3]}set record(e){this.$$set({record:e}),b()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),b()}get waveform_settings(){return this.$$.ctx[10]}set waveform_settings(e){this.$$set({waveform_settings:e}),b()}get waveform_options(){return this.$$.ctx[5]}set waveform_options(e){this.$$set({waveform_options:e}),b()}get waiting(){return this.$$.ctx[6]}set waiting(e){this.$$set({waiting:e}),b()}}function Qt(i){let e,t,n,r,a;e=new Ye({props:{i18n:i[12],download:i[9]?i[2].url:null}}),e.$on("clear",i[28]),e.$on("edit",i[47]);function o(l){i[48](l)}let c={value:i[2],label:i[5],i18n:i[12],dispatch_blob:i[26],waveform_settings:i[13],waveform_options:i[15],trim_region_settings:i[14],handle_reset_value:i[16],editable:i[17],loop:i[7],interactive:!0};return i[24]!==void 0&&(c.mode=i[24]),n=new Mt({props:c}),U.push(()=>Z(n,"mode",o)),n.$on("stop",i[49]),n.$on("play",i[50]),n.$on("pause",i[51]),n.$on("edit",i[52]),{c(){H(e.$$.fragment),t=I(),H(n.$$.fragment)},m(l,u){V(e,l,u),C(l,t,u),V(n,l,u),a=!0},p(l,u){const s={};u[0]&4096&&(s.i18n=l[12]),u[0]&516&&(s.download=l[9]?l[2].url:null),e.$set(s);const f={};u[0]&4&&(f.value=l[2]),u[0]&32&&(f.label=l[5]),u[0]&4096&&(f.i18n=l[12]),u[0]&8192&&(f.waveform_settings=l[13]),u[0]&32768&&(f.waveform_options=l[15]),u[0]&16384&&(f.trim_region_settings=l[14]),u[0]&65536&&(f.handle_reset_value=l[16]),u[0]&131072&&(f.editable=l[17]),u[0]&128&&(f.loop=l[7]),!r&&u[0]&16777216&&(r=!0,f.mode=l[24],x(()=>r=!1)),n.$set(f)},i(l){a||(A(e.$$.fragment,l),A(n.$$.fragment,l),a=!0)},o(l){L(e.$$.fragment,l),L(n.$$.fragment,l),a=!1},d(l){l&&T(t),Y(e,l),Y(n,l)}}}function Xt(i){let e,t,n,r;const a=[xt,Zt],o=[];function c(l,u){return l[3]==="microphone"?0:l[3]==="upload"?1:-1}return~(e=c(i))&&(t=o[e]=a[e](i)),{c(){t&&t.c(),n=Me()},m(l,u){~e&&o[e].m(l,u),C(l,n,u),r=!0},p(l,u){let s=e;e=c(l),e===s?~e&&o[e].p(l,u):(t&&(me(),L(o[s],1,1,()=>{o[s]=null}),ge()),~e?(t=o[e],t?t.p(l,u):(t=o[e]=a[e](l),t.c()),A(t,1),t.m(n.parentNode,n)):t=null)},i(l){r||(A(t),r=!0)},o(l){L(t),r=!1},d(l){l&&T(n),~e&&o[e].d(l)}}}function Zt(i){let e,t,n,r;function a(l){i[44](l)}function o(l){i[45](l)}let c={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:i[6],max_file_size:i[18],upload:i[19],stream_handler:i[20],aria_label:i[12]("audio.drop_to_upload"),$$slots:{default:[$t]},$$scope:{ctx:i}};return i[0]!==void 0&&(c.dragging=i[0]),i[4]!==void 0&&(c.uploading=i[4]),e=new vt({props:c}),U.push(()=>Z(e,"dragging",a)),U.push(()=>Z(e,"uploading",o)),e.$on("load",i[29]),e.$on("error",i[46]),{c(){H(e.$$.fragment)},m(l,u){V(e,l,u),r=!0},p(l,u){const s={};u[0]&64&&(s.root=l[6]),u[0]&262144&&(s.max_file_size=l[18]),u[0]&524288&&(s.upload=l[19]),u[0]&1048576&&(s.stream_handler=l[20]),u[0]&4096&&(s.aria_label=l[12]("audio.drop_to_upload")),u[1]&8388608&&(s.$$scope={dirty:u,ctx:l}),!t&&u[0]&1&&(t=!0,s.dragging=l[0],x(()=>t=!1)),!n&&u[0]&16&&(n=!0,s.uploading=l[4],x(()=>n=!1)),e.$set(s)},i(l){r||(A(e.$$.fragment,l),r=!0)},o(l){L(e.$$.fragment,l),r=!1},d(l){Y(e,l)}}}function xt(i){let e,t,n,r,a,o;e=new Ye({props:{i18n:i[12]}}),e.$on("clear",i[28]);const c=[ti,ei],l=[];function u(s,f){return s[11]?0:1}return n=u(i),r=l[n]=c[n](i),{c(){H(e.$$.fragment),t=I(),r.c(),a=Me()},m(s,f){V(e,s,f),C(s,t,f),l[n].m(s,f),C(s,a,f),o=!0},p(s,f){const _={};f[0]&4096&&(_.i18n=s[12]),e.$set(_);let p=n;n=u(s),n===p?l[n].p(s,f):(me(),L(l[p],1,1,()=>{l[p]=null}),ge(),r=l[n],r?r.p(s,f):(r=l[n]=c[n](s),r.c()),A(r,1),r.m(a.parentNode,a))},i(s){o||(A(e.$$.fragment,s),A(r),o=!0)},o(s){L(e.$$.fragment,s),L(r),o=!1},d(s){s&&(T(t),T(a)),Y(e,s),l[n].d(s)}}}function $t(i){let e;const t=i[39].default,n=gt(t,i,i[54],null);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||a[1]&8388608)&&ht(n,t,r,r[54],e?wt(t,r[54],a,null):bt(r[54]),null)},i(r){e||(A(n,r),e=!0)},o(r){L(n,r),e=!1},d(r){n&&n.d(r)}}}function ei(i){let e,t,n;function r(o){i[40](o)}let a={i18n:i[12],editable:i[17],recording:i[1],dispatch_blob:i[26],waveform_settings:i[13],waveform_options:i[15],handle_reset_value:i[16]};return i[24]!==void 0&&(a.mode=i[24]),e=new Ft({props:a}),U.push(()=>Z(e,"mode",r)),e.$on("start_recording",i[41]),e.$on("pause_recording",i[42]),e.$on("stop_recording",i[43]),{c(){H(e.$$.fragment)},m(o,c){V(e,o,c),n=!0},p(o,c){const l={};c[0]&4096&&(l.i18n=o[12]),c[0]&131072&&(l.editable=o[17]),c[0]&2&&(l.recording=o[1]),c[0]&8192&&(l.waveform_settings=o[13]),c[0]&32768&&(l.waveform_options=o[15]),c[0]&65536&&(l.handle_reset_value=o[16]),!t&&c[0]&16777216&&(t=!0,l.mode=o[24],x(()=>t=!1)),e.$set(l)},i(o){n||(A(e.$$.fragment,o),n=!0)},o(o){L(e.$$.fragment,o),n=!1},d(o){Y(e,o)}}}function ti(i){let e,t;return e=new Kt({props:{record:i[27],recording:i[1],stop:i[30],i18n:i[12],waveform_settings:i[13],waveform_options:i[15],waiting:i[23]==="waiting"}}),{c(){H(e.$$.fragment)},m(n,r){V(e,n,r),t=!0},p(n,r){const a={};r[0]&2&&(a.recording=n[1]),r[0]&4096&&(a.i18n=n[12]),r[0]&8192&&(a.waveform_settings=n[13]),r[0]&32768&&(a.waveform_options=n[15]),r[0]&8388608&&(a.waiting=n[23]==="waiting"),e.$set(a)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){L(e.$$.fragment,n),t=!1},d(n){Y(e,n)}}}function ii(i){let e,t,n,r,a,o,c,l,u,s,f,_;e=new pt({props:{show_label:i[8],Icon:kt,float:i[3]==="upload"&&i[2]===null,label:i[5]||i[12]("audio.audio")}}),r=new Dt({props:{time_limit:i[22]}});const p=[Xt,Qt],D=[];function P(g,k){return g[2]===null||g[11]?0:1}o=P(i),c=D[o]=p[o](i);function h(g){i[53](g)}let E={sources:i[10],handle_clear:i[28]};return i[3]!==void 0&&(E.active_source=i[3]),u=new Rt({props:E}),U.push(()=>Z(u,"active_source",h)),{c(){H(e.$$.fragment),t=I(),n=M("div"),H(r.$$.fragment),a=I(),c.c(),l=I(),H(u.$$.fragment),R(n,"class",f="audio-container "+i[21]+" svelte-1ud6e7m")},m(g,k){V(e,g,k),C(g,t,k),C(g,n,k),V(r,n,null),v(n,a),D[o].m(n,null),v(n,l),V(u,n,null),_=!0},p(g,k){const W={};k[0]&256&&(W.show_label=g[8]),k[0]&12&&(W.float=g[3]==="upload"&&g[2]===null),k[0]&4128&&(W.label=g[5]||g[12]("audio.audio")),e.$set(W);const y={};k[0]&4194304&&(y.time_limit=g[22]),r.$set(y);let N=o;o=P(g),o===N?D[o].p(g,k):(me(),L(D[N],1,1,()=>{D[N]=null}),ge(),c=D[o],c?c.p(g,k):(c=D[o]=p[o](g),c.c()),A(c,1),c.m(n,l));const F={};k[0]&1024&&(F.sources=g[10]),!s&&k[0]&8&&(s=!0,F.active_source=g[3],x(()=>s=!1)),u.$set(F),(!_||k[0]&2097152&&f!==(f="audio-container "+g[21]+" svelte-1ud6e7m"))&&R(n,"class",f)},i(g){_||(A(e.$$.fragment,g),A(r.$$.fragment,g),A(c),A(u.$$.fragment,g),_=!0)},o(g){L(e.$$.fragment,g),L(r.$$.fragment,g),L(c),L(u.$$.fragment,g),_=!1},d(g){g&&(T(t),T(n)),Y(e,g),Y(r),D[o].d(),Y(u)}}}const He=44;function ni(i,e,t){let{$$slots:n={},$$scope:r}=e,{value:a=null}=e,{label:o}=e,{root:c}=e,{loop:l}=e,{show_label:u=!0}=e,{show_download_button:s=!1}=e,{sources:f=["microphone","upload"]}=e,{pending:_=!1}=e,{streaming:p=!1}=e,{i18n:D}=e,{waveform_settings:P}=e,{trim_region_settings:h={}}=e,{waveform_options:E={}}=e,{dragging:g}=e,{active_source:k}=e,{handle_reset_value:W=()=>{}}=e,{editable:y=!0}=e,{max_file_size:N=null}=e,{upload:F}=e,{stream_handler:q}=e,{stream_every:K}=e,{uploading:B=!1}=e,{recording:w=!1}=e,{class_name:se=""}=e,j=null,$="closed";const de=d=>{d==="closed"?(t(22,j=null),t(23,$="closed")):d==="waiting"?t(23,$="waiting"):t(23,$="open")},S=d=>{w&&t(22,j=d)};let z,G="",te,ie=[],_e=!1,ke=!1,le=[],m;function ee(){m=[Be(()=>import("./module-B6bz2o68.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),Be(()=>import("./module-BA06XY8_.js"),__vite__mapDeps([4,1]),import.meta.url)]}typeof window<"u"&&p&&ee();const O=Ee(),ne=async(d,J)=>{let ue=new File(d,"audio.wav");const he=await mt([ue],J==="stream");t(2,a=(await F(he,c,void 0,N||void 0))?.filter(Boolean)[0]),O(J,a)};_t(()=>{p&&z&&z.state!=="inactive"&&z.stop()});async function Je(){let d;try{d=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(J){if(!navigator.mediaDevices){O("error",D("audio.no_device_support"));return}if(J instanceof DOMException&&J.name=="NotAllowedError"){O("error",D("audio.allow_recording_access"));return}throw J}if(d!=null){if(p){const[{MediaRecorder:J,register:ue},{connect:he}]=await Promise.all(m);await ue(await he()),t(35,z=new J(d,{mimeType:"audio/wav"})),z.addEventListener("dataavailable",Ke)}else t(35,z=new MediaRecorder(d)),z.addEventListener("dataavailable",J=>{le.push(J.data)});z.addEventListener("stop",async()=>{t(1,w=!1),await ne(le,"change"),await ne(le,"stop_recording"),le=[]}),ke=!0}}async function Ke(d){let J=await d.data.arrayBuffer(),ue=new Uint8Array(J);if(te||(t(36,te=new Uint8Array(J.slice(0,He))),ue=new Uint8Array(J.slice(He))),_)ie.push(ue);else{let he=[te].concat(ie,[ue]);if(!w||$==="waiting")return;ne(he,"stream"),t(37,ie=[])}}async function Ae(){t(1,w=!0),O("start_recording"),ke||await Je(),t(36,te=void 0),p&&z.state!="recording"&&z.start(K*1e3)}function Qe(){O("change",null),O("clear"),t(24,G=""),t(2,a=null)}function Xe({detail:d}){t(2,a=d),O("change",d),O("upload",d)}async function Le(){t(1,w=!1),p&&(O("close_stream"),O("stop_recording"),z.stop(),_&&t(38,_e=!0),ne(le,"stop_recording"),O("clear"),t(24,G=""))}function Ze(d){G=d,t(24,G)}function xe(d){ce.call(this,i,d)}function $e(d){ce.call(this,i,d)}function et(d){ce.call(this,i,d)}function tt(d){g=d,t(0,g)}function it(d){B=d,t(4,B)}const nt=({detail:d})=>O("error",d),rt=()=>t(24,G="edit");function ot(d){G=d,t(24,G)}function st(d){ce.call(this,i,d)}function lt(d){ce.call(this,i,d)}function at(d){ce.call(this,i,d)}function ut(d){ce.call(this,i,d)}function ct(d){k=d,t(3,k)}return i.$$set=d=>{"value"in d&&t(2,a=d.value),"label"in d&&t(5,o=d.label),"root"in d&&t(6,c=d.root),"loop"in d&&t(7,l=d.loop),"show_label"in d&&t(8,u=d.show_label),"show_download_button"in d&&t(9,s=d.show_download_button),"sources"in d&&t(10,f=d.sources),"pending"in d&&t(31,_=d.pending),"streaming"in d&&t(11,p=d.streaming),"i18n"in d&&t(12,D=d.i18n),"waveform_settings"in d&&t(13,P=d.waveform_settings),"trim_region_settings"in d&&t(14,h=d.trim_region_settings),"waveform_options"in d&&t(15,E=d.waveform_options),"dragging"in d&&t(0,g=d.dragging),"active_source"in d&&t(3,k=d.active_source),"handle_reset_value"in d&&t(16,W=d.handle_reset_value),"editable"in d&&t(17,y=d.editable),"max_file_size"in d&&t(18,N=d.max_file_size),"upload"in d&&t(19,F=d.upload),"stream_handler"in d&&t(20,q=d.stream_handler),"stream_every"in d&&t(32,K=d.stream_every),"uploading"in d&&t(4,B=d.uploading),"recording"in d&&t(1,w=d.recording),"class_name"in d&&t(21,se=d.class_name),"$$scope"in d&&t(54,r=d.$$scope)},i.$$.update=()=>{if(i.$$.dirty[0]&1&&O("drag",g),i.$$.dirty[1]&225&&_e&&_===!1&&(t(38,_e=!1),te&&ie)){let d=[te].concat(ie);t(37,ie=[]),ne(d,"stream")}i.$$.dirty[0]&2|i.$$.dirty[1]&16&&!w&&z&&Le(),i.$$.dirty[0]&2|i.$$.dirty[1]&16&&w&&z&&Ae()},[g,w,a,k,B,o,c,l,u,s,f,p,D,P,h,E,W,y,N,F,q,se,j,$,G,O,ne,Ae,Qe,Xe,Le,_,K,de,S,z,te,ie,_e,n,Ze,xe,$e,et,tt,it,nt,rt,ot,st,lt,at,ut,ct,r]}class ri extends be{constructor(e){super(),we(this,e,ni,ii,ve,{value:2,label:5,root:6,loop:7,show_label:8,show_download_button:9,sources:10,pending:31,streaming:11,i18n:12,waveform_settings:13,trim_region_settings:14,waveform_options:15,dragging:0,active_source:3,handle_reset_value:16,editable:17,max_file_size:18,upload:19,stream_handler:20,stream_every:32,uploading:4,recording:1,class_name:21,modify_stream:33,set_time_limit:34},null,[-1,-1])}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),b()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),b()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),b()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),b()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),b()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),b()}get sources(){return this.$$.ctx[10]}set sources(e){this.$$set({sources:e}),b()}get pending(){return this.$$.ctx[31]}set pending(e){this.$$set({pending:e}),b()}get streaming(){return this.$$.ctx[11]}set streaming(e){this.$$set({streaming:e}),b()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),b()}get waveform_settings(){return this.$$.ctx[13]}set waveform_settings(e){this.$$set({waveform_settings:e}),b()}get trim_region_settings(){return this.$$.ctx[14]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),b()}get waveform_options(){return this.$$.ctx[15]}set waveform_options(e){this.$$set({waveform_options:e}),b()}get dragging(){return this.$$.ctx[0]}set dragging(e){this.$$set({dragging:e}),b()}get active_source(){return this.$$.ctx[3]}set active_source(e){this.$$set({active_source:e}),b()}get handle_reset_value(){return this.$$.ctx[16]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),b()}get editable(){return this.$$.ctx[17]}set editable(e){this.$$set({editable:e}),b()}get max_file_size(){return this.$$.ctx[18]}set max_file_size(e){this.$$set({max_file_size:e}),b()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),b()}get stream_handler(){return this.$$.ctx[20]}set stream_handler(e){this.$$set({stream_handler:e}),b()}get stream_every(){return this.$$.ctx[32]}set stream_every(e){this.$$set({stream_every:e}),b()}get uploading(){return this.$$.ctx[4]}set uploading(e){this.$$set({uploading:e}),b()}get recording(){return this.$$.ctx[1]}set recording(e){this.$$set({recording:e}),b()}get class_name(){return this.$$.ctx[21]}set class_name(e){this.$$set({class_name:e}),b()}get modify_stream(){return this.$$.ctx[33]}get set_time_limit(){return this.$$.ctx[34]}}const gi=ri;export{gi as I};
//# sourceMappingURL=InteractiveAudio-BSRCxffH.js.map
