import{a as j,i as x,s as B,f as g,y as C,z as l,d as D,D as u,l as S,o as P,aa as $}from"../lite.js";function q(s){let e;return{c(){e=C("div"),l(e,"data-testid","bokeh"),l(e,"id",s[0]),l(e,"class","gradio-bokeh svelte-1rhu6ax")},m(o,i){D(o,e,i)},p:u,i:u,o:u,d(o){o&&S(e)}}}function I(s,e,o){let i,{value:c}=e,{bokeh_version:n}=e;const r=`bokehDiv-${Math.random().toString(5).substring(2)}`,v=P();async function y(t){if(document&&document.getElementById(r)&&(document.getElementById(r).innerHTML=""),window.Bokeh){k();let h=JSON.parse(t);(await window.Bokeh.embed.embed_item(h,r))._roots.forEach(async a=>{await a.ready,v("load")})}}const m=`https://cdn.bokeh.org/bokeh/release/bokeh-${n}.min.js`,w=[`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-tables-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-gl-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-api-${n}.min.js`];let d=!1;async function E(){await Promise.all(w.map((t,h)=>new Promise(f=>{const a=document.createElement("script");return a.onload=f,a.src=t,document.head.appendChild(a),a}))),o(3,d=!0)}let p=[];function b(){p=E()}function k(){const t=document.createElement("script");return t.onload=b,t.src=m,document.head.querySelector(`script[src="${m}"]`)?b():document.head.appendChild(t),t}const _=n?k():null;return $(()=>{_ in document.children&&(document.removeChild(_),p.forEach(t=>document.removeChild(t)))}),s.$$set=t=>{"value"in t&&o(1,c=t.value),"bokeh_version"in t&&o(2,n=t.bokeh_version)},s.$$.update=()=>{s.$$.dirty&2&&o(4,i=c?.plot),s.$$.dirty&24&&d&&y(i)},[r,c,n,d,i]}class O extends j{constructor(e){super(),x(this,e,I,q,B,{value:1,bokeh_version:2})}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),g()}get bokeh_version(){return this.$$.ctx[2]}set bokeh_version(e){this.$$set({bokeh_version:e}),g()}}export{O as default};
//# sourceMappingURL=BokehPlot-O00Avp7W.js.map
