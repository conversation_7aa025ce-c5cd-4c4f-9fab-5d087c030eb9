<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="../images/boy.png"/>
        <h5 class="mt-3">
         Shubhrit
        </h5>
        <p>
         Frontend Engineer
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Opening a website (Google) in the browser
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Open Google successfully"/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Navigate to Google's homepage without issues"/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Initial Setup
     </div>
     <div class="stage" style="background-color: #F55050">
      Navigation
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Completion
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Begin execution task. Browser currently on about:blank.
     </div>
     <div class="stage" style="background-color: #F55050">
      Navigate to Google by entering the URL or following a link.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Verify Google is successfully opened and confirm task completion.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral/Uncertain
     </div>
     <div class="stage" style="background-color: #F55050">
      Neutral to Satisfied (progress made successfully)
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Satisfied
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      No visible progress initially; on an empty page (about:blank).
     </div>
     <div class="stage" style="background-color: #F55050">
      None observed during navigation.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      None; task completed successfully.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
