import{a as r,i as h,s as m,f as _,q as d,y as g,z as w,A as o,$ as u,d as v,u as b,r as q,v as k,k as z,t as A,l as C}from"../lite.js";function I(n){let e,i;const f=n[4].default,s=d(f,n,n[3],null);return{c(){e=g("div"),s&&s.c(),w(e,"class","form svelte-633qhp"),o(e,"hidden",!n[0]),u(e,"flex-grow",n[1]),u(e,"min-width",`calc(min(${n[2]}px, 100%))`)},m(t,l){v(t,e,l),s&&s.m(e,null),i=!0},p(t,[l]){s&&s.p&&(!i||l&8)&&b(s,f,t,t[3],i?k(f,t[3],l,null):q(t[3]),null),(!i||l&1)&&o(e,"hidden",!t[0]),l&2&&u(e,"flex-grow",t[1]),l&4&&u(e,"min-width",`calc(min(${t[2]}px, 100%))`)},i(t){i||(z(s,t),i=!0)},o(t){A(s,t),i=!1},d(t){t&&C(e),s&&s.d(t)}}}function S(n,e,i){let{$$slots:f={},$$scope:s}=e,{visible:t=!0}=e,{scale:l=null}=e,{min_width:c=0}=e;return n.$$set=a=>{"visible"in a&&i(0,t=a.visible),"scale"in a&&i(1,l=a.scale),"min_width"in a&&i(2,c=a.min_width),"$$scope"in a&&i(3,s=a.$$scope)},[t,l,c,s,f]}class B extends r{constructor(e){super(),h(this,e,S,I,m,{visible:0,scale:1,min_width:2})}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),_()}get scale(){return this.$$.ctx[1]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[2]}set min_width(e){this.$$set({min_width:e}),_()}}export{B as default};
//# sourceMappingURL=Index-BkBXyN3a.js.map
