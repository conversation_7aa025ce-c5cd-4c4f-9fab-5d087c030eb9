import{a as p,i as h,s as v,f as b,Y as _,y,_ as u,A as m,d as j,M as q,a0 as I,ar as x,D as g,l as A,X as f,Z as C,p as D}from"../lite.js";import{r as M}from"./file-url-CoOyVRgq.js";function P(t){let s,r,i,o,a=[{src:r=t[0]},t[1]],l={};for(let e=0;e<a.length;e+=1)l=_(l,a[e]);return{c(){s=y("img"),u(s,l),m(s,"svelte-1pijsyv",!0)},m(e,n){j(e,s,n),i||(o=q(s,"load",t[4]),i=!0)},p(e,[n]){u(s,l=I(a,[n&1&&!x(s.src,r=e[0])&&{src:r},n&2&&e[1]])),m(s,"svelte-1pijsyv",!0)},i:g,o:g,d(e){e&&A(s),i=!1,o()}}}function S(t,s,r){const i=["src"];let o=f(s,i),{src:a=void 0}=s,l,e;function n(c){D.call(this,t,c)}return t.$$set=c=>{s=_(_({},s),C(c)),r(1,o=f(s,i)),"src"in c&&r(2,a=c.src)},t.$$.update=()=>{if(t.$$.dirty&12){r(0,l=a),r(3,e=a);const c=a;M(c).then(d=>{e===c&&r(0,l=d)})}},[l,o,a,e,n]}class Z extends p{constructor(s){super(),h(this,s,S,P,v,{src:2})}get src(){return this.$$.ctx[2]}set src(s){this.$$set({src:s}),b()}}export{Z as I};
//# sourceMappingURL=Image-BPQ6A_U-.js.map
