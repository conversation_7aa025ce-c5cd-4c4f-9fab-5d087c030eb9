import{a as f,i as u,s as d,f as o,E as a,z as e,d as w,C as c,D as h,l as _}from"../lite.js";function k(n){let t,s,l;return{c(){t=a("svg"),s=a("rect"),e(s,"x","3"),e(s,"y","3"),e(s,"width","18"),e(s,"height","18"),e(s,"rx","2"),e(s,"ry","2"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill",n[0]),e(t,"stroke","currentColor"),e(t,"stroke-width",l=`${n[1]}`),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-square")},m(i,r){w(i,t,r),c(t,s)},p(i,[r]){r&1&&e(t,"fill",i[0]),r&2&&l!==(l=`${i[1]}`)&&e(t,"stroke-width",l)},i:h,o:h,d(i){i&&_(t)}}}function g(n,t,s){let{fill:l="currentColor"}=t,{stroke_width:i=1.5}=t;return n.$$set=r=>{"fill"in r&&s(0,l=r.fill),"stroke_width"in r&&s(1,i=r.stroke_width)},[l,i]}class v extends f{constructor(t){super(),u(this,t,g,k,d,{fill:0,stroke_width:1})}get fill(){return this.$$.ctx[0]}set fill(t){this.$$set({fill:t}),o()}get stroke_width(){return this.$$.ctx[1]}set stroke_width(t){this.$$set({stroke_width:t}),o()}}export{v as S};
//# sourceMappingURL=Square-CkbFMpLj.js.map
