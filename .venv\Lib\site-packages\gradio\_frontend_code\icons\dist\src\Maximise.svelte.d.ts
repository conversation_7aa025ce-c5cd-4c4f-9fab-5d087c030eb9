/** @typedef {typeof __propDef.props}  MaximiseProps */
/** @typedef {typeof __propDef.events}  MaximiseEvents */
/** @typedef {typeof __propDef.slots}  MaximiseSlots */
export default class Maximise extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type MaximiseProps = typeof __propDef.props;
export type MaximiseEvents = typeof __propDef.events;
export type MaximiseSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
