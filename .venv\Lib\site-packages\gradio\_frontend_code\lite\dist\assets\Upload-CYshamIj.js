import{a as c,i as p,s as d,E as n,z as e,d as u,C as r,D as o,l as v}from"../lite.js";function m(i){let t,a;return{c(){t=n("svg"),a=n("path"),e(a,"fill","currentColor"),e(a,"d","M13.75 2a2.25 2.25 0 0 1 2.236 2.002V4h1.764A2.25 2.25 0 0 1 20 6.25V11h-1.5V6.25a.75.75 0 0 0-.75-.75h-2.129c-.404.603-1.091 1-1.871 1h-3.5c-.78 0-1.467-.397-1.871-1H6.25a.75.75 0 0 0-.75.75v13.5c0 .414.336.75.75.75h4.78a4 4 0 0 0 .505 1.5H6.25A2.25 2.25 0 0 1 4 19.75V6.25A2.25 2.25 0 0 1 6.25 4h1.764a2.25 2.25 0 0 1 2.236-2zm2.245 2.096L16 4.25q0-.078-.005-.154M13.75 3.5h-3.5a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5M15 12a3 3 0 0 0-3 3v5c0 .556.151 1.077.415 1.524l3.494-3.494a2.25 2.25 0 0 1 3.182 0l3.494 3.494c.264-.447.415-.968.415-1.524v-5a3 3 0 0 0-3-3zm0 11a3 3 0 0 1-1.524-.415l3.494-3.494a.75.75 0 0 1 1.06 0l3.494 3.494A3 3 0 0 1 20 23zm5-7a1 1 0 1 1 0-2 1 1 0 0 1 0 2"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"viewBox","0 0 24 24"),e(t,"width","100%"),e(t,"height","100%")},m(l,s){u(l,t,s),r(t,a)},p:o,i:o,o,d(l){l&&v(t)}}}class x extends c{constructor(t){super(),p(this,t,null,m,d,{})}}function w(i){let t,a,l,s;return{c(){t=n("svg"),a=n("path"),l=n("polyline"),s=n("line"),e(a,"d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"),e(l,"points","17 8 12 3 7 8"),e(s,"x1","12"),e(s,"y1","3"),e(s,"x2","12"),e(s,"y2","15"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","90%"),e(t,"height","90%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","2"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-upload")},m(h,g){u(h,t,g),r(t,a),r(t,l),r(t,s)},p:o,i:o,o,d(h){h&&v(t)}}}class _ extends c{constructor(t){super(),p(this,t,null,w,d,{})}}export{x as I,_ as U};
//# sourceMappingURL=Upload-CYshamIj.js.map
