import{a as N,i as O,s as U,f as h,y as S,b as D,w as H,z as k,A,d as E,C,M as j,x as J,D as K,l as q,V as L,o as P,B as Q,c as T,m as B,k as m,t as v,n as I,K as R,Y as W,S as X,O as Z,a7 as x,a0 as y,a6 as $,h as p,j as ee,a8 as te}from"../lite.js";import{I as se}from"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";function ie(i){let e,t,a,s,u,o,d;return{c(){e=S("label"),t=S("input"),a=D(),s=S("span"),u=H(i[1]),t.disabled=i[2],k(t,"type","checkbox"),k(t,"name","test"),k(t,"data-testid","checkbox"),k(t,"class","svelte-5ncdh7"),k(s,"class","svelte-5ncdh7"),k(e,"class","svelte-5ncdh7"),A(e,"disabled",i[2])},m(_,c){E(_,e,c),C(e,t),t.checked=i[0],C(e,a),C(e,s),C(s,u),o||(d=[j(t,"change",i[6]),j(t,"keydown",i[3]),j(t,"input",i[4])],o=!0)},p(_,[c]){c&4&&(t.disabled=_[2]),c&1&&(t.checked=_[0]),c&2&&J(u,_[1]),c&4&&A(e,"disabled",_[2])},i:K,o:K,d(_){_&&q(e),o=!1,L(d)}}}function ae(i,e,t){let a,{value:s=!1}=e,{label:u="Checkbox"}=e,{interactive:o}=e;const d=P();async function _(r){r.key==="Enter"&&(t(0,s=!s),d("select",{index:0,value:r.currentTarget.checked,selected:r.currentTarget.checked}))}async function c(r){t(0,s=r.currentTarget.checked),d("select",{index:0,value:r.currentTarget.checked,selected:r.currentTarget.checked})}function b(){s=this.checked,t(0,s)}return i.$$set=r=>{"value"in r&&t(0,s=r.value),"label"in r&&t(1,u=r.label),"interactive"in r&&t(5,o=r.interactive)},i.$$.update=()=>{i.$$.dirty&1&&d("change",s),i.$$.dirty&32&&t(2,a=!o)},[s,u,a,_,c,o,b]}class ne extends N{constructor(e){super(),O(this,e,ae,ie,U,{value:0,label:1,interactive:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),h()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),h()}}const le=ne;function M(i){let e,t;return e=new se({props:{root:i[6],info:i[5]}}),{c(){T(e.$$.fragment)},m(a,s){B(e,a,s),t=!0},p(a,s){const u={};s&64&&(u.root=a[6]),s&32&&(u.info=a[5]),e.$set(u)},i(a){t||(m(e.$$.fragment,a),t=!0)},o(a){v(e.$$.fragment,a),t=!1},d(a){I(e,a)}}}function ce(i){let e,t,a,s,u,o;const d=[{autoscroll:i[11].autoscroll},{i18n:i[11].i18n},i[10]];let _={};for(let n=0;n<d.length;n+=1)_=W(_,d[n]);e=new X({props:_}),e.$on("clear_status",i[15]);let c=i[5]&&M(i);function b(n){i[16](n)}let r={label:i[4],interactive:i[12]};return i[0]!==void 0&&(r.value=i[0]),s=new le({props:r}),Z.push(()=>x(s,"value",b)),s.$on("change",i[13]),s.$on("select",i[17]),{c(){T(e.$$.fragment),t=D(),c&&c.c(),a=D(),T(s.$$.fragment)},m(n,f){B(e,n,f),E(n,t,f),c&&c.m(n,f),E(n,a,f),B(s,n,f),o=!0},p(n,f){const w=f&3072?y(d,[f&2048&&{autoscroll:n[11].autoscroll},f&2048&&{i18n:n[11].i18n},f&1024&&$(n[10])]):{};e.$set(w),n[5]?c?(c.p(n,f),f&32&&m(c,1)):(c=M(n),c.c(),m(c,1),c.m(a.parentNode,a)):c&&(p(),v(c,1,1,()=>{c=null}),ee());const g={};f&16&&(g.label=n[4]),f&4096&&(g.interactive=n[12]),!u&&f&1&&(u=!0,g.value=n[0],te(()=>u=!1)),s.$set(g)},i(n){o||(m(e.$$.fragment,n),m(c),m(s.$$.fragment,n),o=!0)},o(n){v(e.$$.fragment,n),v(c),v(s.$$.fragment,n),o=!1},d(n){n&&(q(t),q(a)),I(e,n),c&&c.d(n),I(s,n)}}}function ue(i){let e,t;return e=new Q({props:{visible:i[3],elem_id:i[1],elem_classes:i[2],container:i[7],scale:i[8],min_width:i[9],$$slots:{default:[ce]},$$scope:{ctx:i}}}),{c(){T(e.$$.fragment)},m(a,s){B(e,a,s),t=!0},p(a,[s]){const u={};s&8&&(u.visible=a[3]),s&2&&(u.elem_id=a[1]),s&4&&(u.elem_classes=a[2]),s&128&&(u.container=a[7]),s&256&&(u.scale=a[8]),s&512&&(u.min_width=a[9]),s&269425&&(u.$$scope={dirty:s,ctx:a}),e.$set(u)},i(a){t||(m(e.$$.fragment,a),t=!0)},o(a){v(e.$$.fragment,a),t=!1},d(a){I(e,a)}}}function re(i,e,t){let{elem_id:a=""}=e,{elem_classes:s=[]}=e,{visible:u=!0}=e,{value:o=!1}=e,{value_is_output:d=!1}=e,{label:_="Checkbox"}=e,{info:c=void 0}=e,{root:b}=e,{container:r=!0}=e,{scale:n=null}=e,{min_width:f=void 0}=e,{loading_status:w}=e,{gradio:g}=e,{interactive:z}=e;function V(){g.dispatch("change"),d||g.dispatch("input")}R(()=>{t(14,d=!1)});const Y=()=>g.dispatch("clear_status",w);function F(l){o=l,t(0,o)}const G=l=>g.dispatch("select",l.detail);return i.$$set=l=>{"elem_id"in l&&t(1,a=l.elem_id),"elem_classes"in l&&t(2,s=l.elem_classes),"visible"in l&&t(3,u=l.visible),"value"in l&&t(0,o=l.value),"value_is_output"in l&&t(14,d=l.value_is_output),"label"in l&&t(4,_=l.label),"info"in l&&t(5,c=l.info),"root"in l&&t(6,b=l.root),"container"in l&&t(7,r=l.container),"scale"in l&&t(8,n=l.scale),"min_width"in l&&t(9,f=l.min_width),"loading_status"in l&&t(10,w=l.loading_status),"gradio"in l&&t(11,g=l.gradio),"interactive"in l&&t(12,z=l.interactive)},[o,a,s,u,_,c,b,r,n,f,w,g,z,V,d,Y,F,G]}class he extends N{constructor(e){super(),O(this,e,re,ue,U,{elem_id:1,elem_classes:2,visible:3,value:0,value_is_output:14,label:4,info:5,root:6,container:7,scale:8,min_width:9,loading_status:10,gradio:11,interactive:12})}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get value_is_output(){return this.$$.ctx[14]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[5]}set info(e){this.$$set({info:e}),h()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),h()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),h()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),h()}get gradio(){return this.$$.ctx[11]}set gradio(e){this.$$set({gradio:e}),h()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),h()}}export{le as BaseCheckbox,he as default};
//# sourceMappingURL=Index-Iv9hveqC.js.map
