{"version": 3, "file": "Index-C__eI-v0.js", "sources": ["../../../plot/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BasePlot } from \"./shared/Plot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport Plot from \"./shared/Plot.svelte\";\n\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { Plot as PlotIcon } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype ThemeMode = \"system\" | \"light\" | \"dark\";\n\n\texport let value: null | string = null;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let theme_mode: ThemeMode;\n\texport let caption: string;\n\texport let bokeh_version: string | null;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear_status: LoadingStatus;\n\t\tselect: SelectData;\n\t}>;\n\texport let show_actions_button = false;\n\texport let _selectable = false;\n\texport let x_lim: [number, number] | null = null;\n</script>\n\n<Block\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<BlockLabel\n\t\t{show_label}\n\t\tlabel={label || gradio.i18n(\"plot.plot\")}\n\t\tIcon={PlotIcon}\n\t/>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Plot\n\t\t{value}\n\t\t{theme_mode}\n\t\t{caption}\n\t\t{bokeh_version}\n\t\t{show_actions_button}\n\t\t{gradio}\n\t\t{show_label}\n\t\t{_selectable}\n\t\t{x_lim}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t/>\n</Block>\n"], "names": ["ctx", "PlotIcon", "dirty", "blocklabel_changes", "value", "$$props", "elem_id", "elem_classes", "visible", "loading_status", "label", "show_label", "container", "scale", "min_width", "theme_mode", "caption", "bokeh_version", "gradio", "show_actions_button", "_selectable", "x_lim", "clear_status_handler", "e"], "mappings": "iUAmDS,MAAAA,EAAS,CAAA,GAAAA,EAAO,EAAA,EAAA,KAAK,WAAW,OACjCC,eAGM,WAAAD,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,ueANXE,EAAA,OAAAC,EAAA,MAAAH,EAAS,CAAA,GAAAA,EAAO,EAAA,EAAA,KAAK,WAAW,0CAI3B,WAAAA,MAAO,YACbE,EAAA,MAAA,CAAA,KAAAF,MAAO,IAAI,UACbA,EAAc,CAAA,CAAA,ihBAjBV,uGAOO,qZA/BL,MAAAI,EAAuB,IAAA,EAAAC,GACvB,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,aAAAE,EAAA,EAAA,EAAAF,GACA,QAAAG,EAAU,EAAA,EAAAH,EACV,CAAA,eAAAI,CAAA,EAAAJ,EACA,CAAA,MAAAK,CAAA,EAAAL,EACA,CAAA,WAAAM,CAAA,EAAAN,GACA,UAAAO,EAAY,EAAA,EAAAP,GACZ,MAAAQ,EAAuB,IAAA,EAAAR,GACvB,UAAAS,EAAgC,MAAA,EAAAT,EAChC,CAAA,WAAAU,CAAA,EAAAV,EACA,CAAA,QAAAW,CAAA,EAAAX,EACA,CAAA,cAAAY,CAAA,EAAAZ,EACA,CAAA,OAAAa,CAAA,EAAAb,GAKA,oBAAAc,EAAsB,EAAA,EAAAd,GACtB,YAAAe,EAAc,EAAA,EAAAf,GACd,MAAAgB,EAAiC,IAAA,EAAAhB,EAsBpB,MAAAiB,EAAA,IAAAJ,EAAO,SAAS,eAAgBT,CAAc,QAYpDS,EAAO,SAAS,QAAQ,IAC7BK,GAAML,EAAO,SAAS,SAAUK,EAAE,MAAM"}