import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: `data-${string}`]: any;
        type?: string | undefined | null;
        id?: string | undefined | null;
        download: any;
        href?: string | undefined | null;
        hreflang?: string | undefined | null;
        media?: string | undefined | null;
        ping?: string | undefined | null;
        rel?: string | undefined | null;
        referrerpolicy?: ReferrerPolicy | undefined | null;
        'sapper:noscroll'?: true | undefined | null;
        'sapper:prefetch'?: true | undefined | null;
        accesskey?: string | undefined | null;
        autofocus?: boolean | undefined | null;
        class?: string | undefined | null;
        contenteditable?: import("svelte/elements").Booleanish | "inherit" | "plaintext-only" | undefined | null;
        contextmenu?: string | undefined | null;
        dir?: string | undefined | null;
        draggable?: import("svelte/elements").Booleanish | undefined | null;
        enterkeyhint?: "enter" | "done" | "go" | "next" | "previous" | "search" | "send" | undefined | null;
        hidden?: boolean | undefined | null;
        lang?: string | undefined | null;
        part?: string | undefined | null;
        placeholder?: string | undefined | null;
        slot?: string | undefined | null;
        spellcheck?: import("svelte/elements").Booleanish | undefined | null;
        style?: string | undefined | null;
        tabindex?: number | undefined | null;
        title?: string | undefined | null;
        translate?: "yes" | "no" | "" | undefined | null;
        inert?: boolean | undefined | null;
        popover?: "auto" | "manual" | "" | undefined | null;
        radiogroup?: string | undefined | null;
        role?: import("svelte/elements").AriaRole | undefined | null;
        about?: string | undefined | null;
        datatype?: string | undefined | null;
        inlist?: any;
        prefix?: string | undefined | null;
        property?: string | undefined | null;
        resource?: string | undefined | null;
        typeof?: string | undefined | null;
        vocab?: string | undefined | null;
        autocapitalize?: string | undefined | null;
        autocorrect?: string | undefined | null;
        autosave?: string | undefined | null;
        color?: string | undefined | null;
        itemprop?: string | undefined | null;
        itemscope?: boolean | undefined | null;
        itemtype?: string | undefined | null;
        itemid?: string | undefined | null;
        itemref?: string | undefined | null;
        results?: number | undefined | null;
        security?: string | undefined | null;
        unselectable?: "on" | "off" | undefined | null;
        inputmode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined | null;
        is?: string | undefined | null;
        'bind:innerHTML'?: string | undefined | null;
        'bind:textContent'?: string | undefined | null;
        'bind:innerText'?: string | undefined | null;
        readonly 'bind:contentRect'?: DOMRectReadOnly | undefined | null;
        readonly 'bind:contentBoxSize'?: ResizeObserverSize[] | undefined | null;
        readonly 'bind:borderBoxSize'?: ResizeObserverSize[] | undefined | null;
        readonly 'bind:devicePixelContentBoxSize'?: ResizeObserverSize[] | undefined | null;
        'aria-activedescendant'?: string | undefined | null;
        'aria-atomic'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-autocomplete'?: "none" | "inline" | "list" | "both" | undefined | null;
        'aria-busy'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-checked'?: boolean | "false" | "mixed" | "true" | undefined | null;
        'aria-colcount'?: number | undefined | null;
        'aria-colindex'?: number | undefined | null;
        'aria-colspan'?: number | undefined | null;
        'aria-controls'?: string | undefined | null;
        'aria-current'?: import("svelte/elements").Booleanish | "page" | "step" | "location" | "date" | "time" | undefined | null;
        'aria-describedby'?: string | undefined | null;
        'aria-details'?: string | undefined | null;
        'aria-disabled'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-dropeffect'?: "none" | "copy" | "execute" | "link" | "move" | "popup" | undefined | null;
        'aria-errormessage'?: string | undefined | null;
        'aria-expanded'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-flowto'?: string | undefined | null;
        'aria-grabbed'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-haspopup'?: import("svelte/elements").Booleanish | "menu" | "listbox" | "tree" | "grid" | "dialog" | undefined | null;
        'aria-hidden'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-invalid'?: import("svelte/elements").Booleanish | "grammar" | "spelling" | undefined | null;
        'aria-keyshortcuts'?: string | undefined | null;
        'aria-label'?: string | undefined | null;
        'aria-labelledby'?: string | undefined | null;
        'aria-level'?: number | undefined | null;
        'aria-live'?: "off" | "assertive" | "polite" | undefined | null;
        'aria-modal'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-multiline'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-multiselectable'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-orientation'?: "horizontal" | "vertical" | undefined | null;
        'aria-owns'?: string | undefined | null;
        'aria-placeholder'?: string | undefined | null;
        'aria-posinset'?: number | undefined | null;
        'aria-pressed'?: boolean | "false" | "mixed" | "true" | undefined | null;
        'aria-readonly'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-relevant'?: "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text" | "text additions" | "text removals" | undefined | null;
        'aria-required'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-roledescription'?: string | undefined | null;
        'aria-rowcount'?: number | undefined | null;
        'aria-rowindex'?: number | undefined | null;
        'aria-rowspan'?: number | undefined | null;
        'aria-selected'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-setsize'?: number | undefined | null;
        'aria-sort'?: "none" | "ascending" | "descending" | "other" | undefined | null;
        'aria-valuemax'?: number | undefined | null;
        'aria-valuemin'?: number | undefined | null;
        'aria-valuenow'?: number | undefined | null;
        'aria-valuetext'?: string | undefined | null;
        'on:copy'?: import("svelte/elements").ClipboardEventHandler<HTMLAnchorElement> | null | undefined;
        'on:cut'?: import("svelte/elements").ClipboardEventHandler<HTMLAnchorElement> | null | undefined;
        'on:paste'?: import("svelte/elements").ClipboardEventHandler<HTMLAnchorElement> | null | undefined;
        'on:compositionend'?: import("svelte/elements").CompositionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:compositionstart'?: import("svelte/elements").CompositionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:compositionupdate'?: import("svelte/elements").CompositionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:focus'?: import("svelte/elements").FocusEventHandler<HTMLAnchorElement> | null | undefined;
        'on:focusin'?: import("svelte/elements").FocusEventHandler<HTMLAnchorElement> | null | undefined;
        'on:focusout'?: import("svelte/elements").FocusEventHandler<HTMLAnchorElement> | null | undefined;
        'on:blur'?: import("svelte/elements").FocusEventHandler<HTMLAnchorElement> | null | undefined;
        'on:change'?: import("svelte/elements").FormEventHandler<HTMLAnchorElement> | null | undefined;
        'on:beforeinput'?: import("svelte/elements").EventHandler<InputEvent, HTMLAnchorElement> | null | undefined;
        'on:input'?: import("svelte/elements").FormEventHandler<HTMLAnchorElement> | null | undefined;
        'on:reset'?: import("svelte/elements").FormEventHandler<HTMLAnchorElement> | null | undefined;
        'on:submit'?: import("svelte/elements").EventHandler<SubmitEvent, HTMLAnchorElement> | null | undefined;
        'on:invalid'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:formdata'?: import("svelte/elements").EventHandler<FormDataEvent, HTMLAnchorElement> | null | undefined;
        'on:load'?: import("svelte/elements").EventHandler | undefined | null;
        'on:error'?: import("svelte/elements").EventHandler | undefined | null;
        'on:beforetoggle'?: import("svelte/elements").ToggleEventHandler<HTMLAnchorElement> | null | undefined;
        'on:toggle'?: import("svelte/elements").ToggleEventHandler<HTMLAnchorElement> | null | undefined;
        'on:keydown'?: import("svelte/elements").KeyboardEventHandler<HTMLAnchorElement> | null | undefined;
        'on:keypress'?: import("svelte/elements").KeyboardEventHandler<HTMLAnchorElement> | null | undefined;
        'on:keyup'?: import("svelte/elements").KeyboardEventHandler<HTMLAnchorElement> | null | undefined;
        'on:abort'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:canplay'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:canplaythrough'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:cuechange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:durationchange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:emptied'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:encrypted'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:ended'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:loadeddata'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:loadedmetadata'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:loadstart'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:pause'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:play'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:playing'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:progress'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:ratechange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:seeked'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:seeking'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:stalled'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:suspend'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:timeupdate'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:volumechange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:waiting'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:auxclick'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:click'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:contextmenu'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dblclick'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:drag'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dragend'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dragenter'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dragexit'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dragleave'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dragover'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:dragstart'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:drop'?: import("svelte/elements").DragEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mousedown'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mouseenter'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mouseleave'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mousemove'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mouseout'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mouseover'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:mouseup'?: import("svelte/elements").MouseEventHandler<HTMLAnchorElement> | null | undefined;
        'on:select'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:selectionchange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:selectstart'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:touchcancel'?: import("svelte/elements").TouchEventHandler<HTMLAnchorElement> | null | undefined;
        'on:touchend'?: import("svelte/elements").TouchEventHandler<HTMLAnchorElement> | null | undefined;
        'on:touchmove'?: import("svelte/elements").TouchEventHandler<HTMLAnchorElement> | null | undefined;
        'on:touchstart'?: import("svelte/elements").TouchEventHandler<HTMLAnchorElement> | null | undefined;
        'on:gotpointercapture'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointercancel'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointerdown'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointerenter'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointerleave'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointermove'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointerout'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointerover'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:pointerup'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:lostpointercapture'?: import("svelte/elements").PointerEventHandler<HTMLAnchorElement> | null | undefined;
        'on:gamepadconnected'?: import("svelte/elements").GamepadEventHandler<HTMLAnchorElement> | null | undefined;
        'on:gamepaddisconnected'?: import("svelte/elements").GamepadEventHandler<HTMLAnchorElement> | null | undefined;
        'on:scroll'?: import("svelte/elements").UIEventHandler<HTMLAnchorElement> | null | undefined;
        'on:scrollend'?: import("svelte/elements").UIEventHandler<HTMLAnchorElement> | null | undefined;
        'on:resize'?: import("svelte/elements").UIEventHandler<HTMLAnchorElement> | null | undefined;
        'on:wheel'?: import("svelte/elements").WheelEventHandler<HTMLAnchorElement> | null | undefined;
        'on:animationstart'?: import("svelte/elements").AnimationEventHandler<HTMLAnchorElement> | null | undefined;
        'on:animationend'?: import("svelte/elements").AnimationEventHandler<HTMLAnchorElement> | null | undefined;
        'on:animationiteration'?: import("svelte/elements").AnimationEventHandler<HTMLAnchorElement> | null | undefined;
        'on:transitionstart'?: import("svelte/elements").TransitionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:transitionrun'?: import("svelte/elements").TransitionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:transitionend'?: import("svelte/elements").TransitionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:transitioncancel'?: import("svelte/elements").TransitionEventHandler<HTMLAnchorElement> | null | undefined;
        'on:outrostart'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAnchorElement> | null | undefined;
        'on:outroend'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAnchorElement> | null | undefined;
        'on:introstart'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAnchorElement> | null | undefined;
        'on:introend'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAnchorElement> | null | undefined;
        'on:message'?: import("svelte/elements").MessageEventHandler<HTMLAnchorElement> | null | undefined;
        'on:messageerror'?: import("svelte/elements").MessageEventHandler<HTMLAnchorElement> | null | undefined;
        'on:visibilitychange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:cancel'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:close'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:fullscreenchange'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
        'on:fullscreenerror'?: import("svelte/elements").EventHandler<Event, HTMLAnchorElement> | null | undefined;
    };
    events: {
        click: CustomEvent<any>;
    } & {
        [evt: string]: CustomEvent<any>;
    };
    slots: {
        default: {};
    };
};
export type DownloadLinkProps = typeof __propDef.props;
export type DownloadLinkEvents = typeof __propDef.events;
export type DownloadLinkSlots = typeof __propDef.slots;
export default class DownloadLink extends SvelteComponent<DownloadLinkProps, DownloadLinkEvents, DownloadLinkSlots> {
}
export {};
