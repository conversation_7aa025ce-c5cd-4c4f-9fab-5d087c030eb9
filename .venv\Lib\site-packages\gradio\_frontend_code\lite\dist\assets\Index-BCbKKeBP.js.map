{"version": 3, "file": "Index-BCbKKeBP.js", "sources": ["../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/components/prism-typescript.js", "../../../paramviewer/ParamViewer.svelte", "../../../paramviewer/Index.svelte"], "sourcesContent": ["(function (Prism) {\n\n\tPrism.languages.typescript = Prism.languages.extend('javascript', {\n\t\t'class-name': {\n\t\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: null // see below\n\t\t},\n\t\t'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/,\n\t});\n\n\t// The keywords TypeScript adds to JavaScript\n\tPrism.languages.typescript.keyword.push(\n\t\t/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n\t\t// keywords that have to be followed by an identifier\n\t\t/\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n\t\t// This is for `import type *, {}`\n\t\t/\\btype\\b(?=\\s*(?:[\\{*]|$))/\n\t);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.typescript['parameter'];\n\tdelete Prism.languages.typescript['literal-property'];\n\n\t// a version of typescript specifically for highlighting types\n\tvar typeInside = Prism.languages.extend('typescript', {});\n\tdelete typeInside['class-name'];\n\n\tPrism.languages.typescript['class-name'].inside = typeInside;\n\n\tPrism.languages.insertBefore('typescript', 'function', {\n\t\t'decorator': {\n\t\t\tpattern: /@[$\\w\\xA0-\\uFFFF]+/,\n\t\t\tinside: {\n\t\t\t\t'at': {\n\t\t\t\t\tpattern: /^@/,\n\t\t\t\t\talias: 'operator'\n\t\t\t\t},\n\t\t\t\t'function': /^[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t\t'generic-function': {\n\t\t\t// e.g. foo<T extends \"bar\" | \"baz\">( ...\n\t\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: /<[\\s\\S]+/, // everything after the first <\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: typeInside\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.ts = Prism.languages.typescript;\n\n}(Prism));\n", "<script lang=\"ts\">\n\timport \"./prism.css\";\n\n\timport Prism from \"prismjs\";\n\timport \"prismjs/components/prism-python\";\n\timport \"prismjs/components/prism-typescript\";\n\n\timport { onMount } from \"svelte\";\n\n\tinterface Param {\n\t\ttype: string | null;\n\t\tdescription: string;\n\t\tdefault: string | null;\n\t\tname?: string;\n\t}\n\n\texport let docs: Record<string, Param>;\n\texport let lang: \"python\" | \"typescript\" = \"python\";\n\texport let linkify: string[] = [];\n\texport let header: string | null;\n\texport let anchor_links: string | boolean = false;\n\n\tlet component_root: HTMLElement;\n\tlet _docs: Param[];\n\tlet all_open = false;\n\n\t$: _docs = highlight_code(docs, lang);\n\n\tfunction create_slug(name: string, anchor_links: string | boolean): string {\n\t\tlet prefix = \"param-\";\n\t\tif (typeof anchor_links === \"string\") {\n\t\t\tprefix += anchor_links + \"-\";\n\t\t}\n\t\treturn prefix + name.toLowerCase().replace(/[^a-z0-9]+/g, \"-\");\n\t}\n\n\tfunction highlight(code: string, lang: \"python\" | \"typescript\"): string {\n\t\tlet highlighted = Prism.highlight(code, Prism.languages[lang], lang);\n\n\t\tfor (const link of linkify) {\n\t\t\thighlighted = highlighted.replace(\n\t\t\t\tnew RegExp(link, \"g\"),\n\t\t\t\t`<a href=\"#h-${link.toLocaleLowerCase()}\">${link}</a>`\n\t\t\t);\n\t\t}\n\n\t\treturn highlighted;\n\t}\n\n\tfunction highlight_code(\n\t\t_docs: typeof docs,\n\t\tlang: \"python\" | \"typescript\"\n\t): Param[] {\n\t\tif (!_docs) {\n\t\t\treturn [];\n\t\t}\n\t\treturn Object.entries(_docs).map(\n\t\t\t([name, { type, description, default: _default }]) => {\n\t\t\t\tlet highlighted_type = type ? highlight(type, lang) : null;\n\n\t\t\t\treturn {\n\t\t\t\t\tname: name,\n\t\t\t\t\ttype: highlighted_type,\n\t\t\t\t\tdescription: description,\n\t\t\t\t\tdefault: _default ? highlight(_default, lang) : null\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\t}\n\n\tfunction toggle_all(): void {\n\t\tall_open = !all_open;\n\t\tconst details = component_root.querySelectorAll(\".param\");\n\t\tdetails.forEach((detail) => {\n\t\t\tif (detail instanceof HTMLDetailsElement) {\n\t\t\t\tdetail.open = all_open;\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction render_links(description: string): string {\n\t\tconst escaped = description\n\t\t\t.replace(/&/g, \"&amp;\")\n\t\t\t.replace(/</g, \"&lt;\")\n\t\t\t.replace(/>/g, \"&gt;\")\n\t\t\t.replace(/\"/g, \"&quot;\")\n\t\t\t.replace(/'/g, \"&#039;\");\n\n\t\tconst markdown_links = escaped.replace(\n\t\t\t/\\[([^\\]]+)\\]\\(([^)]+)\\)/g,\n\t\t\t'<a href=\"$2\" target=\"_blank\">$1</a>'\n\t\t);\n\t\treturn markdown_links;\n\t}\n\n\tonMount(() => {\n\t\tif (window.location.hash) {\n\t\t\topen_parameter_from_hash(window.location.hash);\n\t\t}\n\n\t\twindow.addEventListener(\"hashchange\", (e) => {\n\t\t\topen_parameter_from_hash(window.location.hash);\n\t\t});\n\t});\n\n\tfunction open_parameter_from_hash(hash: string): void {\n\t\tif (!component_root) return;\n\n\t\tconst id = hash.slice(1);\n\t\tconst detail = component_root.querySelector(`#${id}`);\n\n\t\tif (detail instanceof HTMLDetailsElement) {\n\t\t\tdetail.open = true;\n\t\t\tdetail.scrollIntoView({ behavior: \"smooth\" });\n\t\t}\n\t}\n</script>\n\n<div class=\"wrap\" bind:this={component_root}>\n\t{#if header !== null}\n\t\t<div class=\"header\">\n\t\t\t<span class=\"title\">{header}</span>\n\t\t\t<button\n\t\t\t\tclass=\"toggle-all\"\n\t\t\t\ton:click={toggle_all}\n\t\t\t\ttitle={all_open ? \"Close All\" : \"Open All\"}\n\t\t\t>\n\t\t\t\t▼\n\t\t\t</button>\n\t\t</div>\n\t{/if}\n\t{#if _docs}\n\t\t{#each _docs as { type, description, default: _default, name } (name)}\n\t\t\t<details\n\t\t\t\tclass=\"param md\"\n\t\t\t\tid={anchor_links ? create_slug(name || \"\", anchor_links) : undefined}\n\t\t\t>\n\t\t\t\t<summary class=\"type\">\n\t\t\t\t\t{#if anchor_links}\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"#{create_slug(name || '', anchor_links)}\"\n\t\t\t\t\t\t\tclass=\"param-link\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"link-icon\">🔗</span>\n\t\t\t\t\t\t</a>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<pre class=\"language-{lang}\"><code\n\t\t\t\t\t\t\t>{name}{#if type}: {@html type}{/if}</code\n\t\t\t\t\t\t></pre>\n\t\t\t\t</summary>\n\t\t\t\t{#if _default}\n\t\t\t\t\t<div class=\"default\" class:last={!description}>\n\t\t\t\t\t\t<span style:padding-right={\"4px\"}>default</span>\n\t\t\t\t\t\t<code>= {@html _default}</code>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t{#if description}\n\t\t\t\t\t<div class=\"description\">\n\t\t\t\t\t\t<p>{@html render_links(description)}</p>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</details>\n\t\t{/each}\n\t{/if}\n</div>\n\n<style>\n\t.header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 0.7rem 1rem;\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t}\n\n\t.title {\n\t\tfont-size: var(--scale-0);\n\t\tfont-weight: 600;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.toggle-all {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\tpadding: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: 0.7em;\n\t\tline-height: 1;\n\t\topacity: 0.7;\n\t\ttransition:\n\t\t\topacity 0.2s ease,\n\t\t\ttransform 0.3s ease;\n\t}\n\n\t.toggle-all:hover {\n\t\topacity: 1;\n\t}\n\n\t:global(.wrap[data-all-open=\"true\"]) .toggle-all {\n\t\ttransform: rotate(180deg);\n\t}\n\n\t.default :global(pre),\n\t.default :global(.highlight) {\n\t\tdisplay: inline-block;\n\t}\n\n\t.wrap :global(pre),\n\t.wrap :global(.highlight) {\n\t\tmargin: 0 !important;\n\t\tbackground: transparent !important;\n\t\tfont-family: var(--font-mono);\n\t\tfont-weight: 400;\n\t\tpadding: 0 !important;\n\t}\n\n\t.wrap :global(pre a) {\n\t\tcolor: var(--link-text-color-hover);\n\t\ttext-decoration: underline;\n\t}\n\n\t.wrap :global(pre a:hover) {\n\t\tcolor: var(--link-text-color-hover);\n\t}\n\n\t.default > span {\n\t\ttext-transform: uppercase;\n\t\tfont-size: 0.7rem;\n\t\tfont-weight: 600;\n\t}\n\n\t.default > code {\n\t\tborder: none;\n\t}\n\tcode {\n\t\tbackground: none;\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.wrap {\n\t\tpadding: 0rem;\n\t\tborder-radius: 5px;\n\t\tborder: 1px solid #eee;\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tbox-shadow: var(--block-shadow);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\twidth: 100%;\n\t\tline-height: var(--line-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.type {\n\t\tposition: relative;\n\t\tpadding: 0.7rem 1rem;\n\t\tpadding-left: 2rem;\n\t\tbackground: var(--table-odd-background-fill);\n\t\tborder-bottom: 0px solid var(--table-border-color);\n\t\tlist-style: none;\n\t}\n\n\t.type::after {\n\t\tcontent: \"▼\";\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tright: 15px;\n\t\ttransform: translateY(-50%);\n\t\ttransition: transform 0.3s ease;\n\t\tfont-size: 0.7em;\n\t\topacity: 0.7;\n\t}\n\n\tdetails[open] .type::after {\n\t\ttransform: translateY(-50%) rotate(180deg);\n\t}\n\n\t.default {\n\t\tpadding: 0.2rem 1rem 0.3rem 1rem;\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.default.last {\n\t\tborder-bottom: none;\n\t}\n\n\t.description {\n\t\tpadding: 0.7rem 1rem;\n\t\tfont-size: var(--scale-00);\n\t\tfont-family: var(--font-sans);\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.param {\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t}\n\n\t.param:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\tdetails[open] .type {\n\t\tborder-bottom-width: 1px;\n\t}\n\n\t.param.md code {\n\t\tbackground: none;\n\t}\n\n\tdetails > summary {\n\t\tcursor: pointer;\n\t}\n\n\tdetails > summary::-webkit-details-marker {\n\t\tdisplay: none;\n\t}\n\n\t.param-link {\n\t\topacity: 0;\n\t\tposition: absolute;\n\t\tleft: 8px;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\ttransition: opacity 0.2s;\n\t\tcolor: var(--body-text-color);\n\t\ttext-decoration: none;\n\t}\n\n\t.link-icon {\n\t\tfont-size: 14px;\n\t}\n\n\t.type:hover .param-link {\n\t\topacity: 0.7;\n\t}\n\n\t.param-link:hover {\n\t\topacity: 1 !important;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport ParamViewer from \"./ParamViewer.svelte\";\n\n\texport let value: Record<\n\t\tstring,\n\t\t{\n\t\t\ttype: string;\n\t\t\tdescription: string;\n\t\t\tdefault: string;\n\t\t}\n\t>;\n\n\texport let linkify: string[] = [];\n\texport let header: string | null = null;\n\texport let anchor_links = false;\n</script>\n\n<ParamViewer docs={value} {linkify} {header} {anchor_links} />\n"], "names": ["Prism", "typeInside", "ctx", "insert", "target", "div", "anchor", "append", "span", "button", "i", "attr", "a", "a_href_value", "create_slug", "dirty", "html_tag", "raw_value", "code", "render_links", "p", "create_if_block_4", "create_if_block_3", "create_if_block_2", "create_if_block_1", "details", "summary", "pre", "set_data", "t1", "t1_value", "if_block0", "create_if_block_5", "create_if_block", "name", "anchor_links", "prefix", "description", "docs", "$$props", "lang", "linkify", "header", "component_root", "_docs", "all_open", "highlight", "highlighted", "link", "highlight_code", "type", "_default", "highlighted_type", "toggle_all", "detail", "onMount", "open_parameter_from_hash", "e", "hash", "id", "$$value", "$$invalidate", "value"], "mappings": "kPAAC,SAAUA,EAAO,CAEjBA,EAAM,UAAU,WAAaA,EAAM,UAAU,OAAO,aAAc,CACjE,aAAc,CACb,QAAS,+KACT,WAAY,GACZ,OAAQ,GACR,OAAQ,IACR,EACD,QAAW,uFACb,CAAE,EAGDA,EAAM,UAAU,WAAW,QAAQ,KAClC,qDAEA,2FAEA,4BACF,EAGC,OAAOA,EAAM,UAAU,WAAW,UAClC,OAAOA,EAAM,UAAU,WAAW,kBAAkB,EAGpD,IAAIC,EAAaD,EAAM,UAAU,OAAO,aAAc,CAAA,CAAE,EACxD,OAAOC,EAAW,YAAY,EAE9BD,EAAM,UAAU,WAAW,YAAY,EAAE,OAASC,EAElDD,EAAM,UAAU,aAAa,aAAc,WAAY,CACtD,UAAa,CACZ,QAAS,qBACT,OAAQ,CACP,GAAM,CACL,QAAS,KACT,MAAO,UACP,EACD,SAAY,UACZ,CACD,EACD,mBAAoB,CAEnB,QAAS,yGACT,OAAQ,GACR,OAAQ,CACP,SAAY,4DACZ,QAAW,CACV,QAAS,WACT,MAAO,aACP,OAAQC,CACR,CACD,CACD,CACH,CAAE,EAEDD,EAAM,UAAU,GAAKA,EAAM,UAAU,UAEtC,GAAE,KAAK,mMC8DiBE,EAAM,CAAA,CAAA,0BAK3B,GAEA,6FAHQA,EAAQ,CAAA,EAAG,YAAc,UAAU,+CAL5CC,EASKC,EAAAC,EAAAC,CAAA,EARJC,EAAkCF,EAAAG,CAAA,gBAClCD,EAMQF,EAAAI,CAAA,2BAJGP,EAAU,CAAA,CAAA,yBAHAA,EAAM,CAAA,CAAA,eAInBA,EAAQ,CAAA,EAAG,YAAc,4FAO3BA,EAAK,CAAA,CAAA,aAAoDA,EAAI,EAAA,kBAAlE,OAAIQ,GAAA,EAAA,oLAACR,EAAK,CAAA,CAAA,qNAQCS,EAAAC,EAAA,OAAAC,EAAA,IAAAC,EAAYZ,EAAQ,EAAA,GAAA,GAAIA,EAAY,CAAA,CAAA,CAAA,mDAD7CC,EAKGC,EAAAQ,EAAAN,CAAA,EADFC,EAAgCK,EAAAJ,CAAA,UAHxBO,EAAA,IAAAF,KAAAA,EAAA,IAAAC,EAAYZ,EAAQ,EAAA,GAAA,GAAIA,EAAY,CAAA,CAAA,0DAOlBA,EAAI,EAAA,EAAA,oBAAb,IAAE,oFAAOA,EAAI,EAAA,EAAA,KAAAc,EAAA,EAAAC,CAAA,+DAMhBf,EAAQ,EAAA,EAAA,mFAAjB,IAAE,iEADmB,KAAK,2FADCA,EAAW,EAAA,CAAA,UAA7CC,EAGKC,EAAAC,EAAAC,CAAA,EAFJC,EAA+CF,EAAAG,CAAA,SAC/CD,EAA8BF,EAAAa,CAAA,uCAAfhB,EAAQ,EAAA,EAAA,KAAAc,EAAA,EAAAC,CAAA,oBAFUf,EAAW,EAAA,CAAA,wCAOlCe,EAAAE,EAAajB,EAAW,EAAA,CAAA,EAAA,oFADnCC,EAEKC,EAAAC,EAAAC,CAAA,EADJC,EAAuCF,EAAAe,CAAA,wBAA7BL,EAAA,IAAAE,KAAAA,EAAAE,EAAajB,EAAW,EAAA,CAAA,EAAA,MAAAkB,EAAA,UAAAH,mDAX/Bf,EAAI,EAAA,EAAA,iBATHA,EAAY,CAAA,GAAAmB,EAAAnB,CAAA,IASHA,EAAI,EAAA,GAAAoB,EAAApB,CAAA,IAGdA,EAAQ,EAAA,GAAAqB,EAAArB,CAAA,IAMRA,EAAW,EAAA,GAAAsB,EAAAtB,CAAA,8MAVOA,EAAI,CAAA,EAAA,iBAAA,uFAXvBA,EAAY,CAAA,EAAGY,EAAYZ,EAAQ,EAAA,GAAA,GAAIA,EAAY,CAAA,CAAA,EAAI,MAAS,uBAFrEC,EA4BSC,EAAAqB,EAAAnB,CAAA,EAxBRC,EAYSkB,EAAAC,CAAA,wBAHRnB,EAEOmB,EAAAC,CAAA,EAFsBpB,EAE3BoB,EAAAT,CAAA,uFAVGhB,EAAY,CAAA,qEASbA,EAAI,EAAA,EAAA,KAAA0B,EAAAC,EAAAC,CAAA,EAAM5B,EAAI,EAAA,mFADIA,EAAI,CAAA,EAAA,mCAItBA,EAAQ,EAAA,wDAMRA,EAAW,EAAA,qEArBZA,EAAY,CAAA,EAAGY,EAAYZ,EAAQ,EAAA,GAAA,GAAIA,EAAY,CAAA,CAAA,EAAI,gGAhBzD6B,EAAA7B,OAAW,MAAI8B,EAAA9B,CAAA,IAYfA,EAAK,CAAA,GAAA+B,EAAA/B,CAAA,2FAbXC,EA8CKC,EAAAC,EAAAC,CAAA,yDA7CCJ,OAAW,2DAYXA,EAAK,CAAA,gHAvGD,SAAAY,EAAYoB,EAAcC,EAAAA,KAC9BC,EAAS,uBACFD,GAAiB,WAC3BC,GAAUD,EAAe,KAEnBC,EAASF,EAAK,YAAA,EAAc,QAAQ,cAAe,GAAG,WA+CrDf,EAAakB,EAAA,CAYd,OAXSA,EACd,QAAQ,KAAM,OAAO,EACrB,QAAQ,KAAM,MAAM,EACpB,QAAQ,KAAM,MAAM,EACpB,QAAQ,KAAM,QAAQ,EACtB,QAAQ,KAAM,QAAQ,EAEO,QAC9B,2BACA,qCAAA,qBA1ES,GAAA,CAAA,KAAAC,CAAA,EAAAC,GACA,KAAAC,EAAgC,QAAA,EAAAD,EAChC,CAAA,QAAAE,EAAA,EAAA,EAAAF,EACA,CAAA,OAAAG,CAAA,EAAAH,GACA,aAAAJ,EAAiC,EAAA,EAAAI,EAExCI,EACAC,EACAC,EAAW,GAYN,SAAAC,EAAU5B,EAAcsB,EAAAA,CAC5B,IAAAO,EAAc/C,EAAM,UAAUkB,EAAMlB,EAAM,UAAUwC,CAAI,EAAGA,CAAI,YAExDQ,KAAQP,EAClBM,EAAcA,EAAY,YACrB,OAAOC,EAAM,GAAG,EACL,eAAAA,EAAK,wBAAwBA,CAAI,MAAA,EAI3C,OAAAD,EAGC,SAAAE,EACRL,EACAJ,EAAAA,CAEKI,OAAAA,EAGE,OAAO,QAAQA,CAAK,EAAE,MAC1BV,EAAQ,CAAA,KAAAgB,EAAM,YAAAb,EAAa,QAASc,CAAA,CAAA,IAAA,CACjC,IAAAC,EAAmBF,EAAOJ,EAAUI,EAAMV,CAAI,EAAI,YAGrD,KAAAN,EACA,KAAMkB,EACN,YAAAf,EACA,QAASc,EAAWL,EAAUK,EAAUX,CAAI,EAAI,WAM3C,SAAAa,GAAA,KACRR,EAAY,CAAAA,CAAA,EACIF,EAAe,iBAAiB,QAAQ,EAChD,QAASW,GAAA,CACZA,aAAkB,qBACrBA,EAAO,KAAOT,KAoBjBU,EAAA,IAAA,CACK,OAAO,SAAS,MACnBC,EAAyB,OAAO,SAAS,IAAI,EAG9C,OAAO,iBAAiB,aAAeC,GAAA,CACtCD,EAAyB,OAAO,SAAS,IAAI,eAItCA,EAAyBE,EAAA,CAC5B,GAAA,CAAAf,EAAA,aAECgB,EAAKD,EAAK,MAAM,CAAC,EACjBJ,EAASX,EAAe,kBAAkBgB,CAAE,EAAA,EAE9CL,aAAkB,qBACrBA,EAAO,KAAO,GACdA,EAAO,gBAAiB,SAAU,QAAA,CAAA,6CAKRX,EAAciB,4NA5FvCC,EAAA,EAAAjB,EAAQK,EAAeX,EAAME,CAAI,CAAA,ikBCTlBtC,EAAK,CAAA,gIAALA,EAAK,CAAA,yLAdZ,GAAA,CAAA,MAAA4D,CAAA,EAAAvB,EASA,CAAA,QAAAE,EAAA,EAAA,EAAAF,GACA,OAAAG,EAAwB,IAAA,EAAAH,GACxB,aAAAJ,EAAe,EAAA,EAAAI", "x_google_ignoreList": [0]}