{"version": 3, "file": "Index-2-2t5tFc.js", "sources": ["../../../number/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Block, BlockTitle } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { afterUpdate, tick } from \"svelte\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tsubmit: never;\n\t\tblur: never;\n\t\tfocus: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let label = gradio.i18n(\"number.number\");\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let value = 0;\n\texport let show_label: boolean;\n\texport let minimum: number | undefined = undefined;\n\texport let maximum: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let value_is_output = false;\n\texport let step: number | null = null;\n\texport let interactive: boolean;\n\texport let root: string;\n\n\tfunction handle_change(): void {\n\t\tif (!isNaN(value) && value !== null) {\n\t\t\tgradio.dispatch(\"change\");\n\t\t\tif (!value_is_output) {\n\t\t\t\tgradio.dispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tasync function handle_keypress(e: KeyboardEvent): Promise<void> {\n\t\tawait tick();\n\t\tif (e.key === \"Enter\") {\n\t\t\te.preventDefault();\n\t\t\tgradio.dispatch(\"submit\");\n\t\t}\n\t}\n\n\t$: value, handle_change();\n\t$: disabled = !interactive;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={container}\n\tallow_overflow={false}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<label class=\"block\" class:container>\n\t\t<BlockTitle {root} {show_label} {info}>{label}</BlockTitle>\n\t\t<input\n\t\t\taria-label={label}\n\t\t\ttype=\"number\"\n\t\t\tbind:value\n\t\t\tmin={minimum}\n\t\t\tmax={maximum}\n\t\t\t{step}\n\t\t\ton:keypress={handle_keypress}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t\t{disabled}\n\t\t/>\n\t</label>\n</Block>\n\n<style>\n\tlabel:not(.container),\n\tlabel:not(.container) > input {\n\t\theight: 100%;\n\t\tborder: none;\n\t}\n\t.container > input {\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t}\n\tinput[type=\"number\"] {\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbox-shadow: var(--input-shadow);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--input-padding);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\tinput:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tinput:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t\tbackground: var(--input-background-fill-focus);\n\t}\n\n\tinput::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\tinput:out-of-range {\n\t\tborder: var(--input-border-width) solid var(--error-border-color);\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "label_1", "anchor", "append", "input", "dirty", "gradio", "$$props", "label", "info", "elem_id", "elem_classes", "visible", "container", "scale", "min_width", "value", "show_label", "minimum", "maximum", "loading_status", "value_is_output", "step", "interactive", "root", "handle_change", "afterUpdate", "handle_keypress", "e", "tick", "clear_status_handler", "$$invalidate", "disabled"], "mappings": "sXAyE0CA,EAAK,CAAA,CAAA,qCAALA,EAAK,CAAA,CAAA,gEANjC,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,ySAMLA,EAAK,CAAA,CAAA,iCAGZA,EAAO,EAAA,CAAA,YACPA,EAAO,EAAA,CAAA,qJAPdC,EAcOC,EAAAC,EAAAC,CAAA,qBAZNC,EAWCF,EAAAG,CAAA,0DAJaN,EAAe,EAAA,CAAA,0EAdjBO,EAAA,GAAA,CAAA,WAAAP,KAAO,UAAU,EACvBO,EAAA,GAAA,CAAA,KAAAP,KAAO,IAAI,YACbA,EAAc,EAAA,CAAA,kLAMLA,EAAK,CAAA,CAAA,0BAGZA,EAAO,EAAA,CAAA,0BACPA,EAAO,EAAA,CAAA,iYAlBLA,EAAS,CAAA,iBACF,oOADPA,EAAS,CAAA,8MAtDP,CAAA,OAAAQ,CAAA,EAAAC,GAQA,MAAAC,EAAQF,EAAO,KAAK,eAAe,CAAA,EAAAC,GACnC,KAAAE,EAA2B,MAAA,EAAAF,GAC3B,QAAAG,EAAU,EAAA,EAAAH,EACV,CAAA,aAAAI,EAAA,EAAA,EAAAJ,GACA,QAAAK,EAAU,EAAA,EAAAL,GACV,UAAAM,EAAY,EAAA,EAAAN,GACZ,MAAAO,EAAuB,IAAA,EAAAP,GACvB,UAAAQ,EAAgC,MAAA,EAAAR,GAChC,MAAAS,EAAQ,CAAA,EAAAT,EACR,CAAA,WAAAU,CAAA,EAAAV,GACA,QAAAW,EAA8B,MAAA,EAAAX,GAC9B,QAAAY,EAA8B,MAAA,EAAAZ,EAC9B,CAAA,eAAAa,CAAA,EAAAb,GACA,gBAAAc,EAAkB,EAAA,EAAAd,GAClB,KAAAe,EAAsB,IAAA,EAAAf,EACtB,CAAA,YAAAgB,CAAA,EAAAhB,EACA,CAAA,KAAAiB,CAAA,EAAAjB,EAEF,SAAAkB,GAAA,EACH,MAAMT,CAAK,GAAKA,IAAU,OAC9BV,EAAO,SAAS,QAAQ,EACnBe,GACJf,EAAO,SAAS,OAAO,GAI1BoB,EAAA,IAAA,MACCL,EAAkB,EAAA,mBAGJM,EAAgBC,EAAA,CACxB,MAAAC,GAAA,EACFD,EAAE,MAAQ,UACbA,EAAE,eAAA,EACFtB,EAAO,SAAS,QAAQ,GAqBF,MAAAwB,EAAA,IAAAxB,EAAO,SAAS,eAAgBc,CAAc,kDAYrDd,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,2pBA9B/BmB,EAAA,qBACVM,EAAA,GAAGC,EAAY,CAAAT,CAAA"}