import{a as b,i as k,s as w,f as u,c as z,m as B,k as q,t as C,n as I,w as S,d as j,x as A,l as D}from"../lite.js";import{B as E}from"./Button-BiPFvbFD.js";import"./Image-BPQ6A_U-.js";import"./file-url-CoOyVRgq.js";/* empty css                                                   */function F(l){let e=(l[3]?l[11].i18n(l[3]):"")+"",s;return{c(){s=S(e)},m(i,n){j(i,s,n)},p(i,n){n&2056&&e!==(e=(i[3]?i[11].i18n(i[3]):"")+"")&&A(s,e)},d(i){i&&D(s)}}}function G(l){let e,s;return e=new E({props:{value:l[3],variant:l[4],elem_id:l[0],elem_classes:l[1],size:l[6],scale:l[7],link:l[9],icon:l[8],min_width:l[10],visible:l[2],disabled:!l[5],$$slots:{default:[F]},$$scope:{ctx:l}}}),e.$on("click",l[12]),{c(){z(e.$$.fragment)},m(i,n){B(e,i,n),s=!0},p(i,[n]){const a={};n&8&&(a.value=i[3]),n&16&&(a.variant=i[4]),n&1&&(a.elem_id=i[0]),n&2&&(a.elem_classes=i[1]),n&64&&(a.size=i[6]),n&128&&(a.scale=i[7]),n&512&&(a.link=i[9]),n&256&&(a.icon=i[8]),n&1024&&(a.min_width=i[10]),n&4&&(a.visible=i[2]),n&32&&(a.disabled=!i[5]),n&10248&&(a.$$scope={dirty:n,ctx:i}),e.$set(a)},i(i){s||(q(e.$$.fragment,i),s=!0)},o(i){C(e.$$.fragment,i),s=!1},d(i){I(e,i)}}}function H(l,e,s){let{elem_id:i=""}=e,{elem_classes:n=[]}=e,{visible:a=!0}=e,{value:f}=e,{variant:m="secondary"}=e,{interactive:h}=e,{size:_="lg"}=e,{scale:r=null}=e,{icon:g=null}=e,{link:o=null}=e,{min_width:v=void 0}=e,{gradio:c}=e;const d=()=>c.dispatch("click");return l.$$set=t=>{"elem_id"in t&&s(0,i=t.elem_id),"elem_classes"in t&&s(1,n=t.elem_classes),"visible"in t&&s(2,a=t.visible),"value"in t&&s(3,f=t.value),"variant"in t&&s(4,m=t.variant),"interactive"in t&&s(5,h=t.interactive),"size"in t&&s(6,_=t.size),"scale"in t&&s(7,r=t.scale),"icon"in t&&s(8,g=t.icon),"link"in t&&s(9,o=t.link),"min_width"in t&&s(10,v=t.min_width),"gradio"in t&&s(11,c=t.gradio)},[i,n,a,f,m,h,_,r,g,o,v,c,d]}class O extends b{constructor(e){super(),k(this,e,H,G,w,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,link:9,min_width:10,gradio:11})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),u()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),u()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),u()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),u()}get variant(){return this.$$.ctx[4]}set variant(e){this.$$set({variant:e}),u()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),u()}get size(){return this.$$.ctx[6]}set size(e){this.$$set({size:e}),u()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),u()}get icon(){return this.$$.ctx[8]}set icon(e){this.$$set({icon:e}),u()}get link(){return this.$$.ctx[9]}set link(e){this.$$set({link:e}),u()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),u()}get gradio(){return this.$$.ctx[11]}set gradio(e){this.$$set({gradio:e}),u()}}export{E as BaseButton,O as default};
//# sourceMappingURL=Index-DL915-fj.js.map
