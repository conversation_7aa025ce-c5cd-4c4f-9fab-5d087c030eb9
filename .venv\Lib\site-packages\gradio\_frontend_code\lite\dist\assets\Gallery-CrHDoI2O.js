import{a as Ke,i as Qe,s as Ye,f as z,P as Ze,b as U,e as Ie,d as D,M as Z,k as h,h as N,t as d,j as C,l as E,o as xe,a5 as et,aB as tt,c as I,m as L,n as T,N as lt,p as ce,O as re,aq as le,y as S,z as B,$ as P,A,C as R,as as Le,U as nt,V as it,w as Te,x as De,I as Ee,a2 as rt,D as ot}from"../lite.js";import{B as st}from"./BlockLabel-DWW9BWN3.js";import{E as ft}from"./Empty-Bzq0Ew6m.js";import{S as ut}from"./ShareButton-Be-vgu5O.js";import{D as at}from"./Download-RUpc9r8A.js";import{I as Ae,F as ct}from"./FullscreenButton-DsVuMC2h.js";import{P as Re}from"./Play-BIkNyEKH.js";import{I as _t}from"./IconButtonWrapper-BqpIgNIH.js";/* empty css                                             */import{M as mt}from"./ModifyUpload-b77W1M2_.js";import{I as oe}from"./Image-BPQ6A_U-.js";/* empty css                                                   */import{V as se}from"./Video-DBVExGTx.js";import{u as ht}from"./utils-BsGrhMNe.js";import"./Community-BFnPJcwx.js";import"./Minimize-DOBO88I3.js";import"./Edit-fMGAgLsI.js";import"./Undo-50qkik3g.js";import"./DownloadLink-dHe4pFcz.js";import"./file-url-CoOyVRgq.js";import"./hls-CnVhpNcu.js";var _e=Object.prototype.hasOwnProperty;function me(l,e,n){for(n of l.keys())if(Y(n,e))return n}function Y(l,e){var n,t,r;if(l===e)return!0;if(l&&e&&(n=l.constructor)===e.constructor){if(n===Date)return l.getTime()===e.getTime();if(n===RegExp)return l.toString()===e.toString();if(n===Array){if((t=l.length)===e.length)for(;t--&&Y(l[t],e[t]););return t===-1}if(n===Set){if(l.size!==e.size)return!1;for(t of l)if(r=t,r&&typeof r=="object"&&(r=me(e,r),!r)||!e.has(r))return!1;return!0}if(n===Map){if(l.size!==e.size)return!1;for(t of l)if(r=t[0],r&&typeof r=="object"&&(r=me(e,r),!r)||!Y(t[1],e.get(r)))return!1;return!0}if(n===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(n===DataView){if((t=l.byteLength)===e.byteLength)for(;t--&&l.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(l)){if((t=l.byteLength)===e.byteLength)for(;t--&&l[t]===e[t];);return t===-1}if(!n||typeof l=="object"){t=0;for(n in l)if(_e.call(l,n)&&++t&&!_e.call(e,n)||!(n in e)||!Y(l[n],e[n]))return!1;return Object.keys(e).length===t}}return l!==l&&e!==e}async function gt(l){return l?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(l.map(async([n,t])=>n===null||!n.url?"":await ht(n.url)))).map(n=>`<img src="${n}" style="height: 400px" />`).join("")}</div>`:""}const{window:Ue}=tt;function he(l,e,n){const t=l.slice();return t[47]=e[n],t[49]=n,t}function ge(l,e,n){const t=l.slice();return t[50]=e[n],t[51]=e,t[49]=n,t}function pe(l){let e,n;return e=new st({props:{show_label:l[2],Icon:Ae,label:l[3]||"Gallery"}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&4&&(i.show_label=t[2]),r[0]&8&&(i.label=t[3]||"Gallery"),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function pt(l){let e,n,t,r,i,f,c=l[22]&&l[7]&&de(l),o=l[12]&&l[1]===null&&je(l),p=le(l[16]),u=[];for(let a=0;a<p.length;a+=1)u[a]=Be(he(l,p,a));const g=a=>d(u[a],1,1,()=>{u[a]=null});return{c(){e=S("div"),c&&c.c(),n=U(),t=S("div"),o&&o.c(),r=U(),i=S("div");for(let a=0;a<u.length;a+=1)u[a].c();B(i,"class","grid-container svelte-842rpi"),P(i,"--grid-cols",l[4]),P(i,"--grid-rows",l[5]),P(i,"--object-fit",l[8]),P(i,"height",l[6]),A(i,"pt-6",l[2]),B(t,"class","grid-wrap svelte-842rpi"),A(t,"minimal",l[13]==="minimal"),A(t,"fixed-height",l[13]!=="minimal"&&(!l[6]||l[6]=="auto")),A(t,"hidden",l[17]),B(e,"class","gallery-container")},m(a,_){D(a,e,_),c&&c.m(e,null),R(e,n),R(e,t),o&&o.m(t,null),R(t,r),R(t,i);for(let b=0;b<u.length;b+=1)u[b]&&u[b].m(i,null);l[43](e),f=!0},p(a,_){if(a[22]&&a[7]?c?(c.p(a,_),_[0]&4194432&&h(c,1)):(c=de(a),c.c(),h(c,1),c.m(e,n)):c&&(N(),d(c,1,1,()=>{c=null}),C()),a[12]&&a[1]===null?o?(o.p(a,_),_[0]&4098&&h(o,1)):(o=je(a),o.c(),h(o,1),o.m(t,r)):o&&(N(),d(o,1,1,()=>{o=null}),C()),_[0]&8454274){p=le(a[16]);let b;for(b=0;b<p.length;b+=1){const $=he(a,p,b);u[b]?(u[b].p($,_),h(u[b],1)):(u[b]=Be($),u[b].c(),h(u[b],1),u[b].m(i,null))}for(N(),b=p.length;b<u.length;b+=1)g(b);C()}(!f||_[0]&16)&&P(i,"--grid-cols",a[4]),(!f||_[0]&32)&&P(i,"--grid-rows",a[5]),(!f||_[0]&256)&&P(i,"--object-fit",a[8]),(!f||_[0]&64)&&P(i,"height",a[6]),(!f||_[0]&4)&&A(i,"pt-6",a[2]),(!f||_[0]&8192)&&A(t,"minimal",a[13]==="minimal"),(!f||_[0]&8256)&&A(t,"fixed-height",a[13]!=="minimal"&&(!a[6]||a[6]=="auto")),(!f||_[0]&131072)&&A(t,"hidden",a[17])},i(a){if(!f){h(c),h(o);for(let _=0;_<p.length;_+=1)h(u[_]);f=!0}},o(a){d(c),d(o),u=u.filter(Boolean);for(let _=0;_<u.length;_+=1)d(u[_]);f=!1},d(a){a&&E(e),c&&c.d(),o&&o.d(),Le(u,a),l[43](null)}}}function dt(l){let e,n;return e=new ft({props:{unpadded_box:!0,size:"large",$$slots:{default:[zt]},$$scope:{ctx:l}}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[1]&2097152&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function de(l){let e,n,t,r,i,f,c,o,p,u,g,a;n=new _t({props:{display_top_corner:l[15],$$slots:{default:[bt]},$$scope:{ctx:l}}});const _=[kt,wt],b=[];function $(k,v){return"image"in k[22]?0:1}i=$(l),f=b[i]=_[i](l);let w=l[22]?.caption&&ye(l),y=le(l[16]),m=[];for(let k=0;k<y.length;k+=1)m[k]=$e(ge(l,y,k));const x=k=>d(m[k],1,1,()=>{m[k]=null});return{c(){e=S("button"),I(n.$$.fragment),t=U(),r=S("button"),f.c(),c=U(),w&&w.c(),o=U(),p=S("div");for(let k=0;k<m.length;k+=1)m[k].c();B(r,"class","media-button svelte-842rpi"),P(r,"height","calc(100% - "+(l[22].caption?"80px":"60px")+")"),B(r,"aria-label","detailed view of selected image"),B(p,"class","thumbnails scroll-hide svelte-842rpi"),B(p,"data-testid","container_el"),B(e,"class","preview svelte-842rpi"),A(e,"minimal",l[13]==="minimal")},m(k,v){D(k,e,v),L(n,e,null),R(e,t),R(e,r),b[i].m(r,null),R(e,c),w&&w.m(e,null),R(e,o),R(e,p);for(let O=0;O<m.length;O+=1)m[O]&&m[O].m(p,null);l[40](p),u=!0,g||(a=[Z(r,"click",function(){nt("image"in l[22]?l[37]:null)&&("image"in l[22]?l[37]:null).apply(this,arguments)}),Z(e,"keydown",l[25])],g=!0)},p(k,v){l=k;const O={};v[0]&32768&&(O.display_top_corner=l[15]),v[0]&4673026|v[1]&2097152&&(O.$$scope={dirty:v,ctx:l}),n.$set(O);let G=i;if(i=$(l),i===G?b[i].p(l,v):(N(),d(b[G],1,1,()=>{b[G]=null}),C(),f=b[i],f?f.p(l,v):(f=b[i]=_[i](l),f.c()),h(f,1),f.m(r,null)),(!u||v[0]&4194304)&&P(r,"height","calc(100% - "+(l[22].caption?"80px":"60px")+")"),l[22]?.caption?w?w.p(l,v):(w=ye(l),w.c(),w.m(e,o)):w&&(w.d(1),w=null),v[0]&598018){y=le(l[16]);let j;for(j=0;j<y.length;j+=1){const H=ge(l,y,j);m[j]?(m[j].p(H,v),h(m[j],1)):(m[j]=$e(H),m[j].c(),h(m[j],1),m[j].m(p,null))}for(N(),j=y.length;j<m.length;j+=1)x(j);C()}(!u||v[0]&8192)&&A(e,"minimal",l[13]==="minimal")},i(k){if(!u){h(n.$$.fragment,k),h(f);for(let v=0;v<y.length;v+=1)h(m[v]);u=!0}},o(k){d(n.$$.fragment,k),d(f),m=m.filter(Boolean);for(let v=0;v<m.length;v+=1)d(m[v]);u=!1},d(k){k&&E(e),T(n),b[i].d(),w&&w.d(),Le(m,k),l[40](null),g=!1,it(a)}}}function be(l){let e,n;return e=new Ee({props:{Icon:at,label:l[11]("common.download")}}),e.$on("click",l[33]),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&2048&&(i.label=t[11]("common.download")),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function we(l){let e,n;return e=new ct({props:{container:l[18]}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&262144&&(i.container=t[18]),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function ke(l){let e,n,t;return n=new ut({props:{i18n:l[11],value:l[16],formatter:gt}}),n.$on("share",l[34]),n.$on("error",l[35]),{c(){e=S("div"),I(n.$$.fragment),B(e,"class","icon-button")},m(r,i){D(r,e,i),L(n,e,null),t=!0},p(r,i){const f={};i[0]&2048&&(f.i18n=r[11]),i[0]&65536&&(f.value=r[16]),n.$set(f)},i(r){t||(h(n.$$.fragment,r),t=!0)},o(r){d(n.$$.fragment,r),t=!1},d(r){r&&E(e),T(n)}}}function ve(l){let e,n;return e=new Ee({props:{Icon:rt,label:"Close"}}),e.$on("click",l[36]),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p:ot,i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function bt(l){let e,n,t,r,i,f=l[10]&&be(l),c=l[14]&&we(l),o=l[9]&&ke(l),p=!l[17]&&ve(l);return{c(){f&&f.c(),e=U(),c&&c.c(),n=U(),o&&o.c(),t=U(),p&&p.c(),r=Ie()},m(u,g){f&&f.m(u,g),D(u,e,g),c&&c.m(u,g),D(u,n,g),o&&o.m(u,g),D(u,t,g),p&&p.m(u,g),D(u,r,g),i=!0},p(u,g){u[10]?f?(f.p(u,g),g[0]&1024&&h(f,1)):(f=be(u),f.c(),h(f,1),f.m(e.parentNode,e)):f&&(N(),d(f,1,1,()=>{f=null}),C()),u[14]?c?(c.p(u,g),g[0]&16384&&h(c,1)):(c=we(u),c.c(),h(c,1),c.m(n.parentNode,n)):c&&(N(),d(c,1,1,()=>{c=null}),C()),u[9]?o?(o.p(u,g),g[0]&512&&h(o,1)):(o=ke(u),o.c(),h(o,1),o.m(t.parentNode,t)):o&&(N(),d(o,1,1,()=>{o=null}),C()),u[17]?p&&(N(),d(p,1,1,()=>{p=null}),C()):p?(p.p(u,g),g[0]&131072&&h(p,1)):(p=ve(u),p.c(),h(p,1),p.m(r.parentNode,r))},i(u){i||(h(f),h(c),h(o),h(p),i=!0)},o(u){d(f),d(c),d(o),d(p),i=!1},d(u){u&&(E(e),E(n),E(t),E(r)),f&&f.d(u),c&&c.d(u),o&&o.d(u),p&&p.d(u)}}}function wt(l){let e,n;return e=new se({props:{src:l[22].video.url,"data-testid":"detailed-video",alt:l[22].caption||"",loading:"lazy",loop:!1,is_stream:!1,muted:!1,controls:!0}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&4194304&&(i.src=t[22].video.url),r[0]&4194304&&(i.alt=t[22].caption||""),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function kt(l){let e,n;return e=new oe({props:{"data-testid":"detailed-image",src:l[22].image.url,alt:l[22].caption||"",title:l[22].caption||null,class:l[22].caption&&"with-caption",loading:"lazy"}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&4194304&&(i.src=t[22].image.url),r[0]&4194304&&(i.alt=t[22].caption||""),r[0]&4194304&&(i.title=t[22].caption||null),r[0]&4194304&&(i.class=t[22].caption&&"with-caption"),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function ye(l){let e,n=l[22].caption+"",t;return{c(){e=S("caption"),t=Te(n),B(e,"class","caption svelte-842rpi")},m(r,i){D(r,e,i),R(e,t)},p(r,i){i[0]&4194304&&n!==(n=r[22].caption+"")&&De(t,n)},d(r){r&&E(e)}}}function vt(l){let e,n,t,r;return e=new Re({}),t=new se({props:{src:l[50].video.url,title:l[50].caption||null,is_stream:!1,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy",loop:!1}}),{c(){I(e.$$.fragment),n=U(),I(t.$$.fragment)},m(i,f){L(e,i,f),D(i,n,f),L(t,i,f),r=!0},p(i,f){const c={};f[0]&65536&&(c.src=i[50].video.url),f[0]&65536&&(c.title=i[50].caption||null),t.$set(c)},i(i){r||(h(e.$$.fragment,i),h(t.$$.fragment,i),r=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),r=!1},d(i){i&&E(n),T(e,i),T(t,i)}}}function yt(l){let e,n;return e=new oe({props:{src:l[50].image.url,title:l[50].caption||null,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy"}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&65536&&(i.src=t[50].image.url),r[0]&65536&&(i.title=t[50].caption||null),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function $e(l){let e,n,t,r,i,f=l[49],c,o,p;const u=[yt,vt],g=[];function a(w,y){return"image"in w[50]?0:1}n=a(l),t=g[n]=u[n](l);const _=()=>l[38](e,f),b=()=>l[38](null,f);function $(){return l[39](l[49])}return{c(){e=S("button"),t.c(),r=U(),B(e,"class","thumbnail-item thumbnail-small svelte-842rpi"),B(e,"aria-label",i="Thumbnail "+(l[49]+1)+" of "+l[16].length),A(e,"selected",l[1]===l[49]&&l[13]!=="minimal")},m(w,y){D(w,e,y),g[n].m(e,null),R(e,r),_(),c=!0,o||(p=Z(e,"click",$),o=!0)},p(w,y){l=w;let m=n;n=a(l),n===m?g[n].p(l,y):(N(),d(g[m],1,1,()=>{g[m]=null}),C(),t=g[n],t?t.p(l,y):(t=g[n]=u[n](l),t.c()),h(t,1),t.m(e,r)),(!c||y[0]&65536&&i!==(i="Thumbnail "+(l[49]+1)+" of "+l[16].length))&&B(e,"aria-label",i),f!==l[49]&&(b(),f=l[49],_()),(!c||y[0]&8194)&&A(e,"selected",l[1]===l[49]&&l[13]!=="minimal")},i(w){c||(h(t),c=!0)},o(w){d(t),c=!1},d(w){w&&E(e),g[n].d(),b(),o=!1,p()}}}function je(l){let e,n;return e=new mt({props:{i18n:l[11]}}),e.$on("clear",l[41]),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&2048&&(i.i18n=t[11]),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function $t(l){let e,n,t,r;return e=new Re({}),t=new se({props:{src:l[47].video.url,title:l[47].caption||null,is_stream:!1,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy",loop:!1}}),{c(){I(e.$$.fragment),n=U(),I(t.$$.fragment)},m(i,f){L(e,i,f),D(i,n,f),L(t,i,f),r=!0},p(i,f){const c={};f[0]&65536&&(c.src=i[47].video.url),f[0]&65536&&(c.title=i[47].caption||null),t.$set(c)},i(i){r||(h(e.$$.fragment,i),h(t.$$.fragment,i),r=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),r=!1},d(i){i&&E(n),T(e,i),T(t,i)}}}function jt(l){let e,n;return e=new oe({props:{alt:l[47].caption||"",src:typeof l[47].image=="string"?l[47].image:l[47].image.url,loading:"lazy"}}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},p(t,r){const i={};r[0]&65536&&(i.alt=t[47].caption||""),r[0]&65536&&(i.src=typeof t[47].image=="string"?t[47].image:t[47].image.url),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function ze(l){let e,n=l[47].caption+"",t;return{c(){e=S("div"),t=Te(n),B(e,"class","caption-label svelte-842rpi")},m(r,i){D(r,e,i),R(e,t)},p(r,i){i[0]&65536&&n!==(n=r[47].caption+"")&&De(t,n)},d(r){r&&E(e)}}}function Be(l){let e,n,t,r,i,f,c,o,p;const u=[jt,$t],g=[];function a($,w){return"image"in $[47]?0:1}n=a(l),t=g[n]=u[n](l);let _=l[47].caption&&ze(l);function b(){return l[42](l[49])}return{c(){e=S("button"),t.c(),r=U(),_&&_.c(),i=U(),B(e,"class","thumbnail-item thumbnail-lg svelte-842rpi"),B(e,"aria-label",f="Thumbnail "+(l[49]+1)+" of "+l[16].length),A(e,"selected",l[1]===l[49])},m($,w){D($,e,w),g[n].m(e,null),R(e,r),_&&_.m(e,null),R(e,i),c=!0,o||(p=Z(e,"click",b),o=!0)},p($,w){l=$;let y=n;n=a(l),n===y?g[n].p(l,w):(N(),d(g[y],1,1,()=>{g[y]=null}),C(),t=g[n],t?t.p(l,w):(t=g[n]=u[n](l),t.c()),h(t,1),t.m(e,r)),l[47].caption?_?_.p(l,w):(_=ze(l),_.c(),_.m(e,i)):_&&(_.d(1),_=null),(!c||w[0]&65536&&f!==(f="Thumbnail "+(l[49]+1)+" of "+l[16].length))&&B(e,"aria-label",f),(!c||w[0]&2)&&A(e,"selected",l[1]===l[49])},i($){c||(h(t),c=!0)},o($){d(t),c=!1},d($){$&&E(e),g[n].d(),_&&_.d(),o=!1,p()}}}function zt(l){let e,n;return e=new Ae({}),{c(){I(e.$$.fragment)},m(t,r){L(e,t,r),n=!0},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){T(e,t)}}}function Bt(l){let e,n,t,r,i,f,c;Ze(l[32]);let o=l[2]&&pe(l);const p=[dt,pt],u=[];function g(a,_){return a[0]==null||a[16]==null||a[16].length===0?0:1}return n=g(l),t=u[n]=p[n](l),{c(){o&&o.c(),e=U(),t.c(),r=Ie()},m(a,_){o&&o.m(a,_),D(a,e,_),u[n].m(a,_),D(a,r,_),i=!0,f||(c=Z(Ue,"resize",l[32]),f=!0)},p(a,_){a[2]?o?(o.p(a,_),_[0]&4&&h(o,1)):(o=pe(a),o.c(),h(o,1),o.m(e.parentNode,e)):o&&(N(),d(o,1,1,()=>{o=null}),C());let b=n;n=g(a),n===b?u[n].p(a,_):(N(),d(u[b],1,1,()=>{u[b]=null}),C(),t=u[n],t?t.p(a,_):(t=u[n]=p[n](a),t.c()),h(t,1),t.m(r.parentNode,r))},i(a){i||(h(o),h(t),i=!0)},o(a){d(o),d(t),i=!1},d(a){a&&(E(e),E(r)),o&&o.d(a),u[n].d(a),f=!1,c()}}}function It(l,e,n){let t,r,i,{show_label:f=!0}=e,{label:c}=e,{value:o=null}=e,{columns:p=[2]}=e,{rows:u=void 0}=e,{height:g="auto"}=e,{preview:a}=e,{allow_preview:_=!0}=e,{object_fit:b="cover"}=e,{show_share_button:$=!1}=e,{show_download_button:w=!1}=e,{i18n:y}=e,{selected_index:m=null}=e,{interactive:x}=e,{_fetch:k}=e,{mode:v="normal"}=e,{show_fullscreen_button:O=!0}=e,{display_icon_button_wrapper_top_corner:G=!1}=e,j=!1,H;const J=xe();let K=!0,V=null,ne=o;m==null&&a&&o?.length&&(m=0);let ie=m;function fe(s){const M=s.target,F=s.offsetX,X=M.offsetWidth/2;F<X?n(1,m=t):n(1,m=r)}function Ne(s){switch(s.code){case"Escape":s.preventDefault(),n(1,m=null);break;case"ArrowLeft":s.preventDefault(),n(1,m=t);break;case"ArrowRight":s.preventDefault(),n(1,m=r);break}}let W=[],q;async function Ce(s){if(typeof s!="number"||(await lt(),W[s]===void 0))return;W[s]?.focus();const{left:M,width:F}=q.getBoundingClientRect(),{left:ee,width:X}=W[s].getBoundingClientRect(),Q=ee-M+X/2-F/2+q.scrollLeft;q&&typeof q.scrollTo=="function"&&q.scrollTo({left:Q<0?0:Q,behavior:"smooth"})}let ue=0;async function ae(s,M){let F;try{F=await k(s)}catch(Q){if(Q instanceof TypeError){window.open(s,"_blank","noreferrer");return}throw Q}const ee=await F.blob(),X=URL.createObjectURL(ee),te=document.createElement("a");te.href=X,te.download=M,te.click(),URL.revokeObjectURL(X)}et(()=>{document.addEventListener("fullscreenchange",()=>{n(17,j=!!document.fullscreenElement)})});function Oe(){n(21,ue=Ue.innerHeight)}const Se=()=>{const s="image"in i?i?.image:i?.video;if(s==null)return;const{url:M,orig_name:F}=s;M&&ae(M,F??"image")};function Me(s){ce.call(this,l,s)}function Pe(s){ce.call(this,l,s)}const Ve=()=>{n(1,m=null),J("preview_close")},qe=s=>fe(s);function Fe(s,M){re[s?"unshift":"push"](()=>{W[M]=s,n(19,W)})}const Ge=s=>n(1,m=s);function He(s){re[s?"unshift":"push"](()=>{q=s,n(20,q)})}const We=()=>n(0,o=[]),Xe=s=>{m===null&&_&&J("preview_open"),n(1,m=s)};function Je(s){re[s?"unshift":"push"](()=>{H=s,n(18,H)})}return l.$$set=s=>{"show_label"in s&&n(2,f=s.show_label),"label"in s&&n(3,c=s.label),"value"in s&&n(0,o=s.value),"columns"in s&&n(4,p=s.columns),"rows"in s&&n(5,u=s.rows),"height"in s&&n(6,g=s.height),"preview"in s&&n(27,a=s.preview),"allow_preview"in s&&n(7,_=s.allow_preview),"object_fit"in s&&n(8,b=s.object_fit),"show_share_button"in s&&n(9,$=s.show_share_button),"show_download_button"in s&&n(10,w=s.show_download_button),"i18n"in s&&n(11,y=s.i18n),"selected_index"in s&&n(1,m=s.selected_index),"interactive"in s&&n(12,x=s.interactive),"_fetch"in s&&n(28,k=s._fetch),"mode"in s&&n(13,v=s.mode),"show_fullscreen_button"in s&&n(14,O=s.show_fullscreen_button),"display_icon_button_wrapper_top_corner"in s&&n(15,G=s.display_icon_button_wrapper_top_corner)},l.$$.update=()=>{l.$$.dirty[0]&536870913&&n(29,K=o==null||o.length===0?!0:K),l.$$.dirty[0]&1&&n(16,V=o==null?null:o.map(s=>"video"in s?{video:s.video,caption:s.caption}:"image"in s?{image:s.image,caption:s.caption}:{})),l.$$.dirty[0]&1744830467&&(Y(ne,o)||(K?(n(1,m=a&&o?.length?0:null),n(29,K=!1)):n(1,m=m!=null&&o!=null&&m<o.length?m:null),J("change"),n(30,ne=o))),l.$$.dirty[0]&65538&&(t=((m??0)+(V?.length??0)-1)%(V?.length??0)),l.$$.dirty[0]&65538&&(r=((m??0)+1)%(V?.length??0)),l.$$.dirty[0]&65538|l.$$.dirty[1]&1&&m!==ie&&(n(31,ie=m),m!==null&&J("select",{index:m,value:V?.[m]})),l.$$.dirty[0]&130&&_&&Ce(m),l.$$.dirty[0]&65538&&n(22,i=m!=null&&V!=null?V[m]:null)},[o,m,f,c,p,u,g,_,b,$,w,y,x,v,O,G,V,j,H,W,q,ue,i,J,fe,Ne,ae,a,k,K,ne,ie,Oe,Se,Me,Pe,Ve,qe,Fe,Ge,He,We,Xe,Je]}class Kt extends Ke{constructor(e){super(),Qe(this,e,It,Bt,Ye,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:27,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:28,mode:13,show_fullscreen_button:14,display_icon_button_wrapper_top_corner:15},null,[-1,-1])}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),z()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),z()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get columns(){return this.$$.ctx[4]}set columns(e){this.$$set({columns:e}),z()}get rows(){return this.$$.ctx[5]}set rows(e){this.$$set({rows:e}),z()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),z()}get preview(){return this.$$.ctx[27]}set preview(e){this.$$set({preview:e}),z()}get allow_preview(){return this.$$.ctx[7]}set allow_preview(e){this.$$set({allow_preview:e}),z()}get object_fit(){return this.$$.ctx[8]}set object_fit(e){this.$$set({object_fit:e}),z()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),z()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),z()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),z()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),z()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),z()}get _fetch(){return this.$$.ctx[28]}set _fetch(e){this.$$set({_fetch:e}),z()}get mode(){return this.$$.ctx[13]}set mode(e){this.$$set({mode:e}),z()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),z()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[15]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),z()}}export{Kt as default};
//# sourceMappingURL=Gallery-CrHDoI2O.js.map
