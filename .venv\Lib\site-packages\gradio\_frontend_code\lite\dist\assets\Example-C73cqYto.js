import{a as h,i as c,s as g,f as m,y as d,c as b,z as k,A as _,d as z,m as y,k as v,t as w,l as x,n as q}from"../lite.js";import{M as C}from"./MarkdownCode-DVjr71R6.js";function M(a){let e,s,n;return s=new C({props:{message:a[0]?a[0]:"",latex_delimiters:a[5],sanitize_html:a[3],line_breaks:a[4],chatbot:!1,root:a[6]}}),{c(){e=d("div"),b(s.$$.fragment),k(e,"class","prose svelte-1ayixqk"),_(e,"table",a[1]==="table"),_(e,"gallery",a[1]==="gallery"),_(e,"selected",a[2])},m(t,i){z(t,e,i),y(s,e,null),n=!0},p(t,[i]){const r={};i&1&&(r.message=t[0]?t[0]:""),i&32&&(r.latex_delimiters=t[5]),i&8&&(r.sanitize_html=t[3]),i&16&&(r.line_breaks=t[4]),i&64&&(r.root=t[6]),s.$set(r),(!n||i&2)&&_(e,"table",t[1]==="table"),(!n||i&2)&&_(e,"gallery",t[1]==="gallery"),(!n||i&4)&&_(e,"selected",t[2])},i(t){n||(v(s.$$.fragment,t),n=!0)},o(t){w(s.$$.fragment,t),n=!1},d(t){t&&x(e),q(s)}}}function A(a,e,s){let{value:n}=e,{type:t}=e,{selected:i=!1}=e,{sanitize_html:r}=e,{line_breaks:f}=e,{latex_delimiters:o}=e,{root:u}=e;return a.$$set=l=>{"value"in l&&s(0,n=l.value),"type"in l&&s(1,t=l.type),"selected"in l&&s(2,i=l.selected),"sanitize_html"in l&&s(3,r=l.sanitize_html),"line_breaks"in l&&s(4,f=l.line_breaks),"latex_delimiters"in l&&s(5,o=l.latex_delimiters),"root"in l&&s(6,u=l.root)},[n,t,i,r,f,o,u]}class j extends h{constructor(e){super(),c(this,e,A,M,g,{value:0,type:1,selected:2,sanitize_html:3,line_breaks:4,latex_delimiters:5,root:6})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),m()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),m()}get sanitize_html(){return this.$$.ctx[3]}set sanitize_html(e){this.$$set({sanitize_html:e}),m()}get line_breaks(){return this.$$.ctx[4]}set line_breaks(e){this.$$set({line_breaks:e}),m()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),m()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),m()}}export{j as default};
//# sourceMappingURL=Example-C73cqYto.js.map
