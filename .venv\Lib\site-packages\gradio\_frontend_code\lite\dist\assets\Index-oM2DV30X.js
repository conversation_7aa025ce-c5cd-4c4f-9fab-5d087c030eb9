const __vite__fileDeps=["./Canvas3D-BNUzHJMJ.js","../lite.js","../lite.css","./file-url-CoOyVRgq.js","./Canvas3DGS-DKMFX1YO.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{a as ee,i as te,s as le,f as g,c as k,b as T,e as P,m as z,d as $,k as p,h as j,t as b,j as B,l as I,n as v,y as ne,z as y,C as ue,I as fe,az as H,O as S,au as O,a7 as G,a8 as Y,o as ze,N as ie,p as ve,q as De,u as $e,r as Ie,v as Ce,B as ce,Y as me,S as de,a0 as he,a6 as ge}from"../lite.js";import{B as se}from"./BlockLabel-DWW9BWN3.js";import{D as Ee}from"./Download-RUpc9r8A.js";import{F as J}from"./File-C5WPisji.js";import{U as Me}from"./Undo-50qkik3g.js";import{I as Ue}from"./IconButtonWrapper-BqpIgNIH.js";import{U as je}from"./Upload-Do_omv-N.js";import{M as Be}from"./ModifyUpload-b77W1M2_.js";import{E as Ne}from"./Empty-Bzq0Ew6m.js";import{U as Se}from"./UploadText-Chjc4Zy7.js";import{default as Mt}from"./Example-BWGr_ZA5.js";/* empty css                                             */import"./Edit-fMGAgLsI.js";import"./DownloadLink-dHe4pFcz.js";import"./file-url-CoOyVRgq.js";import"./Upload-CYshamIj.js";var ae=Object.prototype.hasOwnProperty;function oe(n,e,l){for(l of n.keys())if(F(l,e))return l}function F(n,e){var l,t,s;if(n===e)return!0;if(n&&e&&(l=n.constructor)===e.constructor){if(l===Date)return n.getTime()===e.getTime();if(l===RegExp)return n.toString()===e.toString();if(l===Array){if((t=n.length)===e.length)for(;t--&&F(n[t],e[t]););return t===-1}if(l===Set){if(n.size!==e.size)return!1;for(t of n)if(s=t,s&&typeof s=="object"&&(s=oe(e,s),!s)||!e.has(s))return!1;return!0}if(l===Map){if(n.size!==e.size)return!1;for(t of n)if(s=t[0],s&&typeof s=="object"&&(s=oe(e,s),!s)||!F(t[1],e.get(s)))return!1;return!0}if(l===ArrayBuffer)n=new Uint8Array(n),e=new Uint8Array(e);else if(l===DataView){if((t=n.byteLength)===e.byteLength)for(;t--&&n.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(n)){if((t=n.byteLength)===e.byteLength)for(;t--&&n[t]===e[t];);return t===-1}if(!l||typeof n=="object"){t=0;for(l in n)if(ae.call(n,l)&&++t&&!ae.call(e,l)||!(l in e)||!F(n[l],e[l]))return!1;return Object.keys(e).length===t}}return n!==n&&e!==e}function re(n){let e,l,t,s,a,r;l=new Ue({props:{$$slots:{default:[Ae]},$$scope:{ctx:n}}});const _=[Oe,Le],i=[];function u(o,f){return o[10]?0:1}return s=u(n),a=i[s]=_[s](n),{c(){e=ne("div"),k(l.$$.fragment),t=T(),a.c(),y(e,"class","model3D svelte-1mxwah3")},m(o,f){$(o,e,f),z(l,e,null),ue(e,t),i[s].m(e,null),r=!0},p(o,f){const d={};f&2115105&&(d.$$scope={dirty:f,ctx:o}),l.$set(d);let m=s;s=u(o),s===m?i[s].p(o,f):(j(),b(i[m],1,1,()=>{i[m]=null}),B(),a=i[s],a?a.p(o,f):(a=i[s]=_[s](o),a.c()),p(a,1),a.m(e,null))},i(o){r||(p(l.$$.fragment,o),p(a),r=!0)},o(o){b(l.$$.fragment,o),b(a),r=!1},d(o){o&&I(e),v(l),i[s].d()}}}function _e(n){let e,l;return e=new fe({props:{Icon:Me,label:"Undo",disabled:!n[9]}}),e.$on("click",n[17]),{c(){k(e.$$.fragment)},m(t,s){z(e,t,s),l=!0},p(t,s){const a={};s&512&&(a.disabled=!t[9]),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function Ae(n){let e,l,t,s,a,r=!n[10]&&_e(n);return t=new fe({props:{Icon:Ee,label:n[5]("common.download")}}),{c(){r&&r.c(),e=T(),l=ne("a"),k(t.$$.fragment),y(l,"href",n[14]),y(l,"target",window.__is_colab__?"_blank":null),y(l,"download",s=window.__is_colab__?null:n[0].orig_name||n[0].path)},m(_,i){r&&r.m(_,i),$(_,e,i),$(_,l,i),z(t,l,null),a=!0},p(_,i){_[10]?r&&(j(),b(r,1,1,()=>{r=null}),B()):r?(r.p(_,i),i&1024&&p(r,1)):(r=_e(_),r.c(),p(r,1),r.m(e.parentNode,e));const u={};i&32&&(u.label=_[5]("common.download")),t.$set(u),(!a||i&16384)&&y(l,"href",_[14]),(!a||i&1&&s!==(s=window.__is_colab__?null:_[0].orig_name||_[0].path))&&y(l,"download",s)},i(_){a||(p(r),p(t.$$.fragment,_),a=!0)},o(_){b(r),b(t.$$.fragment,_),a=!1},d(_){_&&(I(e),I(l)),r&&r.d(_),v(t)}}}function Le(n){let e,l,t,s;function a(i){n[20](i)}var r=n[13];function _(i,u){let o={value:i[0],display_mode:i[1],clear_color:i[2],camera_position:i[8],zoom_speed:i[6],pan_speed:i[7]};return i[14]!==void 0&&(o.resolved_url=i[14]),{props:o}}return r&&(e=O(r,_(n)),n[19](e),S.push(()=>G(e,"resolved_url",a))),{c(){e&&k(e.$$.fragment),t=P()},m(i,u){e&&z(e,i,u),$(i,t,u),s=!0},p(i,u){if(u&8192&&r!==(r=i[13])){if(e){j();const o=e;b(o.$$.fragment,1,0,()=>{v(o,1)}),B()}r?(e=O(r,_(i)),i[19](e),S.push(()=>G(e,"resolved_url",a)),k(e.$$.fragment),p(e.$$.fragment,1),z(e,t.parentNode,t)):e=null}else if(r){const o={};u&1&&(o.value=i[0]),u&2&&(o.display_mode=i[1]),u&4&&(o.clear_color=i[2]),u&256&&(o.camera_position=i[8]),u&64&&(o.zoom_speed=i[6]),u&128&&(o.pan_speed=i[7]),!l&&u&16384&&(l=!0,o.resolved_url=i[14],Y(()=>l=!1)),e.$set(o)}},i(i){s||(e&&p(e.$$.fragment,i),s=!0)},o(i){e&&b(e.$$.fragment,i),s=!1},d(i){i&&I(t),n[19](null),e&&v(e,i)}}}function Oe(n){let e,l,t,s;function a(i){n[18](i)}var r=n[12];function _(i,u){let o={value:i[0],zoom_speed:i[6],pan_speed:i[7]};return i[14]!==void 0&&(o.resolved_url=i[14]),{props:o}}return r&&(e=O(r,_(n)),S.push(()=>G(e,"resolved_url",a))),{c(){e&&k(e.$$.fragment),t=P()},m(i,u){e&&z(e,i,u),$(i,t,u),s=!0},p(i,u){if(u&4096&&r!==(r=i[12])){if(e){j();const o=e;b(o.$$.fragment,1,0,()=>{v(o,1)}),B()}r?(e=O(r,_(i)),S.push(()=>G(e,"resolved_url",a)),k(e.$$.fragment),p(e.$$.fragment,1),z(e,t.parentNode,t)):e=null}else if(r){const o={};u&1&&(o.value=i[0]),u&64&&(o.zoom_speed=i[6]),u&128&&(o.pan_speed=i[7]),!l&&u&16384&&(l=!0,o.resolved_url=i[14],Y(()=>l=!1)),e.$set(o)}},i(i){s||(e&&p(e.$$.fragment,i),s=!0)},o(i){e&&b(e.$$.fragment,i),s=!1},d(i){i&&I(t),e&&v(e,i)}}}function Te(n){let e,l,t,s;e=new se({props:{show_label:n[4],Icon:J,label:n[3]||n[5]("3D_model.3d_model")}});let a=n[0]&&re(n);return{c(){k(e.$$.fragment),l=T(),a&&a.c(),t=P()},m(r,_){z(e,r,_),$(r,l,_),a&&a.m(r,_),$(r,t,_),s=!0},p(r,[_]){const i={};_&16&&(i.show_label=r[4]),_&40&&(i.label=r[3]||r[5]("3D_model.3d_model")),e.$set(i),r[0]?a?(a.p(r,_),_&1&&p(a,1)):(a=re(r),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(j(),b(a,1,1,()=>{a=null}),B())},i(r){s||(p(e.$$.fragment,r),p(a),s=!0)},o(r){b(e.$$.fragment,r),b(a),s=!1},d(r){r&&(I(l),I(t)),v(e,r),a&&a.d(r)}}}async function Pe(){return(await H(()=>import("./Canvas3D-BNUzHJMJ.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)).default}async function Ve(){return(await H(()=>import("./Canvas3DGS-DKMFX1YO.js"),__vite__mapDeps([4,1,2,3]),import.meta.url)).default}function Re(n,e,l){let{value:t}=e,{display_mode:s="solid"}=e,{clear_color:a=[0,0,0,0]}=e,{label:r=""}=e,{show_label:_}=e,{i18n:i}=e,{zoom_speed:u=1}=e,{pan_speed:o=1}=e,{camera_position:f=[null,null,null]}=e,{has_change_history:d=!1}=e,m={camera_position:f,zoom_speed:u,pan_speed:o},E=!1,C,D,M;function A(){M?.reset_camera_position(f,u,o)}let U;const L=()=>A();function N(w){U=w,l(14,U)}function V(w){S[w?"unshift":"push"](()=>{M=w,l(11,M)})}function R(w){U=w,l(14,U)}return n.$$set=w=>{"value"in w&&l(0,t=w.value),"display_mode"in w&&l(1,s=w.display_mode),"clear_color"in w&&l(2,a=w.clear_color),"label"in w&&l(3,r=w.label),"show_label"in w&&l(4,_=w.show_label),"i18n"in w&&l(5,i=w.i18n),"zoom_speed"in w&&l(6,u=w.zoom_speed),"pan_speed"in w&&l(7,o=w.pan_speed),"camera_position"in w&&l(8,f=w.camera_position),"has_change_history"in w&&l(9,d=w.has_change_history)},n.$$.update=()=>{n.$$.dirty&1025&&t&&(l(10,E=t.path.endsWith(".splat")||t.path.endsWith(".ply")),E?Ve().then(w=>{l(12,C=w)}):Pe().then(w=>{l(13,D=w)})),n.$$.dirty&68032&&(!F(m.camera_position,f)||m.zoom_speed!==u||m.pan_speed!==o)&&(M?.reset_camera_position(f,u,o),l(16,m={camera_position:f,zoom_speed:u,pan_speed:o}))},[t,s,a,r,_,i,u,o,f,d,E,M,C,D,U,A,m,L,N,V,R]}class We extends ee{constructor(e){super(),te(this,e,Re,Te,le,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8,has_change_history:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get display_mode(){return this.$$.ctx[1]}set display_mode(e){this.$$set({display_mode:e}),g()}get clear_color(){return this.$$.ctx[2]}set clear_color(e){this.$$set({clear_color:e}),g()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),g()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),g()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(e){this.$$set({zoom_speed:e}),g()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(e){this.$$set({pan_speed:e}),g()}get camera_position(){return this.$$.ctx[8]}set camera_position(e){this.$$set({camera_position:e}),g()}get has_change_history(){return this.$$.ctx[9]}set has_change_history(e){this.$$set({has_change_history:e}),g()}}const ye=We;function Ge(n){let e,l,t,s,a,r;l=new Be({props:{undoable:!n[14],i18n:n[7]}}),l.$on("clear",n[20]),l.$on("undo",n[21]);const _=[Ye,Fe],i=[];function u(o,f){return o[14]?0:1}return s=u(n),a=i[s]=_[s](n),{c(){e=ne("div"),k(l.$$.fragment),t=T(),a.c(),y(e,"class","input-model svelte-jub4pj")},m(o,f){$(o,e,f),z(l,e,null),ue(e,t),i[s].m(e,null),r=!0},p(o,f){const d={};f&16384&&(d.undoable=!o[14]),f&128&&(d.i18n=o[7]),l.$set(d);let m=s;s=u(o),s===m?i[s].p(o,f):(j(),b(i[m],1,1,()=>{i[m]=null}),B(),a=i[s],a?a.p(o,f):(a=i[s]=_[s](o),a.c()),p(a,1),a.m(e,null))},i(o){r||(p(l.$$.fragment,o),p(a),r=!0)},o(o){b(l.$$.fragment,o),b(a),r=!1},d(o){o&&I(e),v(l),i[s].d()}}}function qe(n){let e,l,t,s;function a(i){n[23](i)}function r(i){n[24](i)}let _={upload:n[12],stream_handler:n[13],root:n[6],max_file_size:n[10],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],aria_label:n[7]("model3d.drop_to_upload"),$$slots:{default:[He]},$$scope:{ctx:n}};return n[15]!==void 0&&(_.dragging=n[15]),n[1]!==void 0&&(_.uploading=n[1]),e=new je({props:_}),S.push(()=>G(e,"dragging",a)),S.push(()=>G(e,"uploading",r)),e.$on("load",n[19]),e.$on("error",n[25]),{c(){k(e.$$.fragment)},m(i,u){z(e,i,u),s=!0},p(i,u){const o={};u&4096&&(o.upload=i[12]),u&8192&&(o.stream_handler=i[13]),u&64&&(o.root=i[6]),u&1024&&(o.max_file_size=i[10]),u&128&&(o.aria_label=i[7]("model3d.drop_to_upload")),u&134217728&&(o.$$scope={dirty:u,ctx:i}),!l&&u&32768&&(l=!0,o.dragging=i[15],Y(()=>l=!1)),!t&&u&2&&(t=!0,o.uploading=i[1],Y(()=>t=!1)),e.$set(o)},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){b(e.$$.fragment,i),s=!1},d(i){v(e,i)}}}function Fe(n){let e,l,t;var s=n[17];function a(r,_){return{props:{value:r[0],display_mode:r[2],clear_color:r[3],camera_position:r[11],zoom_speed:r[8],pan_speed:r[9]}}}return s&&(e=O(s,a(n)),n[26](e)),{c(){e&&k(e.$$.fragment),l=P()},m(r,_){e&&z(e,r,_),$(r,l,_),t=!0},p(r,_){if(_&131072&&s!==(s=r[17])){if(e){j();const i=e;b(i.$$.fragment,1,0,()=>{v(i,1)}),B()}s?(e=O(s,a(r)),r[26](e),k(e.$$.fragment),p(e.$$.fragment,1),z(e,l.parentNode,l)):e=null}else if(s){const i={};_&1&&(i.value=r[0]),_&4&&(i.display_mode=r[2]),_&8&&(i.clear_color=r[3]),_&2048&&(i.camera_position=r[11]),_&256&&(i.zoom_speed=r[8]),_&512&&(i.pan_speed=r[9]),e.$set(i)}},i(r){t||(e&&p(e.$$.fragment,r),t=!0)},o(r){e&&b(e.$$.fragment,r),t=!1},d(r){r&&I(l),n[26](null),e&&v(e,r)}}}function Ye(n){let e,l,t;var s=n[16];function a(r,_){return{props:{value:r[0],zoom_speed:r[8],pan_speed:r[9]}}}return s&&(e=O(s,a(n))),{c(){e&&k(e.$$.fragment),l=P()},m(r,_){e&&z(e,r,_),$(r,l,_),t=!0},p(r,_){if(_&65536&&s!==(s=r[16])){if(e){j();const i=e;b(i.$$.fragment,1,0,()=>{v(i,1)}),B()}s?(e=O(s,a(r)),k(e.$$.fragment),p(e.$$.fragment,1),z(e,l.parentNode,l)):e=null}else if(s){const i={};_&1&&(i.value=r[0]),_&256&&(i.zoom_speed=r[8]),_&512&&(i.pan_speed=r[9]),e.$set(i)}},i(r){t||(e&&p(e.$$.fragment,r),t=!0)},o(r){e&&b(e.$$.fragment,r),t=!1},d(r){r&&I(l),e&&v(e,r)}}}function He(n){let e;const l=n[22].default,t=De(l,n,n[27],null);return{c(){t&&t.c()},m(s,a){t&&t.m(s,a),e=!0},p(s,a){t&&t.p&&(!e||a&134217728)&&$e(t,l,s,s[27],e?Ce(l,s[27],a,null):Ie(s[27]),null)},i(s){e||(p(t,s),e=!0)},o(s){b(t,s),e=!1},d(s){t&&t.d(s)}}}function Je(n){let e,l,t,s,a,r;e=new se({props:{show_label:n[5],Icon:J,label:n[4]||"3D Model"}});const _=[qe,Ge],i=[];function u(o,f){return o[0]===null?0:1}return t=u(n),s=i[t]=_[t](n),{c(){k(e.$$.fragment),l=T(),s.c(),a=P()},m(o,f){z(e,o,f),$(o,l,f),i[t].m(o,f),$(o,a,f),r=!0},p(o,[f]){const d={};f&32&&(d.show_label=o[5]),f&16&&(d.label=o[4]||"3D Model"),e.$set(d);let m=t;t=u(o),t===m?i[t].p(o,f):(j(),b(i[m],1,1,()=>{i[m]=null}),B(),s=i[t],s?s.p(o,f):(s=i[t]=_[t](o),s.c()),p(s,1),s.m(a.parentNode,a))},i(o){r||(p(e.$$.fragment,o),p(s),r=!0)},o(o){b(e.$$.fragment,o),b(s),r=!1},d(o){o&&(I(l),I(a)),v(e,o),i[t].d(o)}}}async function Ke(){return(await H(()=>import("./Canvas3D-BNUzHJMJ.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)).default}async function Qe(){return(await H(()=>import("./Canvas3DGS-DKMFX1YO.js"),__vite__mapDeps([4,1,2,3]),import.meta.url)).default}function Xe(n,e,l){let{$$slots:t={},$$scope:s}=e,{value:a}=e,{display_mode:r="solid"}=e,{clear_color:_=[0,0,0,0]}=e,{label:i=""}=e,{show_label:u}=e,{root:o}=e,{i18n:f}=e,{zoom_speed:d=1}=e,{pan_speed:m=1}=e,{max_file_size:E=null}=e,{uploading:C=!1}=e,{camera_position:D=[null,null,null]}=e,{upload:M}=e,{stream_handler:A}=e;async function U({detail:h}){l(0,a=h),await ie(),W("change",a),W("load",a)}async function L(){l(0,a=null),await ie(),W("clear"),W("change")}let N=!1,V,R,w;async function K(){w?.reset_camera_position(D,d,m)}const W=ze();let q=!1;function Q(h){q=h,l(15,q)}function X(h){C=h,l(1,C)}function Z(h){ve.call(this,n,h)}function x(h){S[h?"unshift":"push"](()=>{w=h,l(18,w)})}return n.$$set=h=>{"value"in h&&l(0,a=h.value),"display_mode"in h&&l(2,r=h.display_mode),"clear_color"in h&&l(3,_=h.clear_color),"label"in h&&l(4,i=h.label),"show_label"in h&&l(5,u=h.show_label),"root"in h&&l(6,o=h.root),"i18n"in h&&l(7,f=h.i18n),"zoom_speed"in h&&l(8,d=h.zoom_speed),"pan_speed"in h&&l(9,m=h.pan_speed),"max_file_size"in h&&l(10,E=h.max_file_size),"uploading"in h&&l(1,C=h.uploading),"camera_position"in h&&l(11,D=h.camera_position),"upload"in h&&l(12,M=h.upload),"stream_handler"in h&&l(13,A=h.stream_handler),"$$scope"in h&&l(27,s=h.$$scope)},n.$$.update=()=>{n.$$.dirty&16385&&a&&(l(14,N=a.path.endsWith(".splat")||a.path.endsWith(".ply")),N?Qe().then(h=>{l(16,V=h)}):Ke().then(h=>{l(17,R=h)})),n.$$.dirty&32768&&W("drag",q)},[a,C,r,_,i,u,o,f,d,m,E,D,M,A,N,q,V,R,w,U,L,K,t,Q,X,Z,x,s]}class Ze extends ee{constructor(e){super(),te(this,e,Xe,Je,le,{value:0,display_mode:2,clear_color:3,label:4,show_label:5,root:6,i18n:7,zoom_speed:8,pan_speed:9,max_file_size:10,uploading:1,camera_position:11,upload:12,stream_handler:13})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get display_mode(){return this.$$.ctx[2]}set display_mode(e){this.$$set({display_mode:e}),g()}get clear_color(){return this.$$.ctx[3]}set clear_color(e){this.$$set({clear_color:e}),g()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),g()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),g()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),g()}get zoom_speed(){return this.$$.ctx[8]}set zoom_speed(e){this.$$set({zoom_speed:e}),g()}get pan_speed(){return this.$$.ctx[9]}set pan_speed(e){this.$$set({pan_speed:e}),g()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),g()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),g()}get camera_position(){return this.$$.ctx[11]}set camera_position(e){this.$$set({camera_position:e}),g()}get upload(){return this.$$.ctx[12]}set upload(e){this.$$set({upload:e}),g()}get stream_handler(){return this.$$.ctx[13]}set stream_handler(e){this.$$set({stream_handler:e}),g()}}const xe=Ze;function et(n){let e,l;return e=new ce({props:{visible:n[5],variant:n[0]===null?"dashed":"solid",border_mode:n[20]?"focus":"base",padding:!1,elem_id:n[3],elem_classes:n[4],container:n[11],scale:n[12],min_width:n[13],height:n[15],$$slots:{default:[nt]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,s){z(e,t,s),l=!0},p(t,s){const a={};s[0]&32&&(a.visible=t[5]),s[0]&1&&(a.variant=t[0]===null?"dashed":"solid"),s[0]&1048576&&(a.border_mode=t[20]?"focus":"base"),s[0]&8&&(a.elem_id=t[3]),s[0]&16&&(a.elem_classes=t[4]),s[0]&2048&&(a.container=t[11]),s[0]&4096&&(a.scale=t[12]),s[0]&8192&&(a.min_width=t[13]),s[0]&32768&&(a.height=t[15]),s[0]&1787847|s[1]&8&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function tt(n){let e,l;return e=new ce({props:{visible:n[5],variant:n[0]===null?"dashed":"solid",border_mode:n[20]?"focus":"base",padding:!1,elem_id:n[3],elem_classes:n[4],container:n[11],scale:n[12],min_width:n[13],height:n[15],$$slots:{default:[ot]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,s){z(e,t,s),l=!0},p(t,s){const a={};s[0]&32&&(a.visible=t[5]),s[0]&1&&(a.variant=t[0]===null?"dashed":"solid"),s[0]&1048576&&(a.border_mode=t[20]?"focus":"base"),s[0]&8&&(a.elem_id=t[3]),s[0]&16&&(a.elem_classes=t[4]),s[0]&2048&&(a.container=t[11]),s[0]&4096&&(a.scale=t[12]),s[0]&8192&&(a.min_width=t[13]),s[0]&32768&&(a.height=t[15]),s[0]&214919|s[1]&8&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function lt(n){let e,l;return e=new Se({props:{i18n:n[14].i18n,type:"file"}}),{c(){k(e.$$.fragment)},m(t,s){z(e,t,s),l=!0},p(t,s){const a={};s[0]&16384&&(a.i18n=t[14].i18n),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function nt(n){let e,l,t,s,a;const r=[{autoscroll:n[14].autoscroll},{i18n:n[14].i18n},n[1]];let _={};for(let o=0;o<r.length;o+=1)_=me(_,r[o]);e=new de({props:_}),e.$on("clear_status",n[24]);function i(o){n[27](o)}let u={label:n[9],show_label:n[10],root:n[6],display_mode:n[7],clear_color:n[8],value:n[0],camera_position:n[17],zoom_speed:n[16],i18n:n[14].i18n,max_file_size:n[14].max_file_size,upload:n[25],stream_handler:n[26],$$slots:{default:[lt]},$$scope:{ctx:n}};return n[19]!==void 0&&(u.uploading=n[19]),t=new xe({props:u}),S.push(()=>G(t,"uploading",i)),t.$on("change",n[28]),t.$on("drag",n[29]),t.$on("change",n[30]),t.$on("clear",n[31]),t.$on("load",n[32]),t.$on("error",n[33]),{c(){k(e.$$.fragment),l=T(),k(t.$$.fragment)},m(o,f){z(e,o,f),$(o,l,f),z(t,o,f),a=!0},p(o,f){const d=f[0]&16386?he(r,[f[0]&16384&&{autoscroll:o[14].autoscroll},f[0]&16384&&{i18n:o[14].i18n},f[0]&2&&ge(o[1])]):{};e.$set(d);const m={};f[0]&512&&(m.label=o[9]),f[0]&1024&&(m.show_label=o[10]),f[0]&64&&(m.root=o[6]),f[0]&128&&(m.display_mode=o[7]),f[0]&256&&(m.clear_color=o[8]),f[0]&1&&(m.value=o[0]),f[0]&131072&&(m.camera_position=o[17]),f[0]&65536&&(m.zoom_speed=o[16]),f[0]&16384&&(m.i18n=o[14].i18n),f[0]&16384&&(m.max_file_size=o[14].max_file_size),f[0]&16384&&(m.upload=o[25]),f[0]&16384&&(m.stream_handler=o[26]),f[0]&16384|f[1]&8&&(m.$$scope={dirty:f,ctx:o}),!s&&f[0]&524288&&(s=!0,m.uploading=o[19],Y(()=>s=!1)),t.$set(m)},i(o){a||(p(e.$$.fragment,o),p(t.$$.fragment,o),a=!0)},o(o){b(e.$$.fragment,o),b(t.$$.fragment,o),a=!1},d(o){o&&I(l),v(e,o),v(t,o)}}}function st(n){let e,l,t,s;return e=new se({props:{show_label:n[10],Icon:J,label:n[9]||"3D Model"}}),t=new Ne({props:{unpadded_box:!0,size:"large",$$slots:{default:[at]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment),l=T(),k(t.$$.fragment)},m(a,r){z(e,a,r),$(a,l,r),z(t,a,r),s=!0},p(a,r){const _={};r[0]&1024&&(_.show_label=a[10]),r[0]&512&&(_.label=a[9]||"3D Model"),e.$set(_);const i={};r[1]&8&&(i.$$scope={dirty:r,ctx:a}),t.$set(i)},i(a){s||(p(e.$$.fragment,a),p(t.$$.fragment,a),s=!0)},o(a){b(e.$$.fragment,a),b(t.$$.fragment,a),s=!1},d(a){a&&I(l),v(e,a),v(t,a)}}}function it(n){let e,l;return e=new ye({props:{value:n[0],i18n:n[14].i18n,display_mode:n[7],clear_color:n[8],label:n[9],show_label:n[10],camera_position:n[17],zoom_speed:n[16],has_change_history:n[2]}}),{c(){k(e.$$.fragment)},m(t,s){z(e,t,s),l=!0},p(t,s){const a={};s[0]&1&&(a.value=t[0]),s[0]&16384&&(a.i18n=t[14].i18n),s[0]&128&&(a.display_mode=t[7]),s[0]&256&&(a.clear_color=t[8]),s[0]&512&&(a.label=t[9]),s[0]&1024&&(a.show_label=t[10]),s[0]&131072&&(a.camera_position=t[17]),s[0]&65536&&(a.zoom_speed=t[16]),s[0]&4&&(a.has_change_history=t[2]),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function at(n){let e,l;return e=new J({}),{c(){k(e.$$.fragment)},m(t,s){z(e,t,s),l=!0},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){v(e,t)}}}function ot(n){let e,l,t,s,a,r;const _=[{autoscroll:n[14].autoscroll},{i18n:n[14].i18n},n[1]];let i={};for(let d=0;d<_.length;d+=1)i=me(i,_[d]);e=new de({props:i}),e.$on("clear_status",n[23]);const u=[it,st],o=[];function f(d,m){return d[0]&&d[21]?0:1}return t=f(n),s=o[t]=u[t](n),{c(){k(e.$$.fragment),l=T(),s.c(),a=P()},m(d,m){z(e,d,m),$(d,l,m),o[t].m(d,m),$(d,a,m),r=!0},p(d,m){const E=m[0]&16386?he(_,[m[0]&16384&&{autoscroll:d[14].autoscroll},m[0]&16384&&{i18n:d[14].i18n},m[0]&2&&ge(d[1])]):{};e.$set(E);let C=t;t=f(d),t===C?o[t].p(d,m):(j(),b(o[C],1,1,()=>{o[C]=null}),B(),s=o[t],s?s.p(d,m):(s=o[t]=u[t](d),s.c()),p(s,1),s.m(a.parentNode,a))},i(d){r||(p(e.$$.fragment,d),p(s),r=!0)},o(d){b(e.$$.fragment,d),b(s),r=!1},d(d){d&&(I(l),I(a)),v(e,d),o[t].d(d)}}}function rt(n){let e,l,t,s;const a=[tt,et],r=[];function _(i,u){return i[18]?1:0}return e=_(n),l=r[e]=a[e](n),{c(){l.c(),t=P()},m(i,u){r[e].m(i,u),$(i,t,u),s=!0},p(i,u){let o=e;e=_(i),e===o?r[e].p(i,u):(j(),b(r[o],1,1,()=>{r[o]=null}),B(),l=r[e],l?l.p(i,u):(l=r[e]=a[e](i),l.c()),p(l,1),l.m(t.parentNode,t))},i(i){s||(p(l),s=!0)},o(i){b(l),s=!1},d(i){i&&I(t),r[e].d(i)}}}function _t(n,e,l){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:a=!0}=e,{value:r=null}=e,{root:_}=e,{display_mode:i="solid"}=e,{clear_color:u}=e,{loading_status:o}=e,{label:f}=e,{show_label:d}=e,{container:m=!0}=e,{scale:E=null}=e,{min_width:C=void 0}=e,{gradio:D}=e,{height:M=void 0}=e,{zoom_speed:A=1}=e,{input_ready:U}=e,L=!1,{has_change_history:N=!1}=e,{camera_position:V=[null,null,null]}=e,{interactive:R}=e,w=!1;const K=typeof window<"u",W=()=>D.dispatch("clear_status",o),q=()=>D.dispatch("clear_status",o),Q=(...c)=>D.client.upload(...c),X=(...c)=>D.client.stream(...c);function Z(c){L=c,l(19,L)}const x=({detail:c})=>l(0,r=c),h=({detail:c})=>l(20,w=c),pe=({detail:c})=>{D.dispatch("change",c),l(2,N=!0)},be=()=>{l(0,r=null),D.dispatch("clear")},we=({detail:c})=>{l(0,r=c),D.dispatch("upload")},ke=({detail:c})=>{l(1,o=o||{}),l(1,o.status="error",o),D.dispatch("error",c)};return n.$$set=c=>{"elem_id"in c&&l(3,t=c.elem_id),"elem_classes"in c&&l(4,s=c.elem_classes),"visible"in c&&l(5,a=c.visible),"value"in c&&l(0,r=c.value),"root"in c&&l(6,_=c.root),"display_mode"in c&&l(7,i=c.display_mode),"clear_color"in c&&l(8,u=c.clear_color),"loading_status"in c&&l(1,o=c.loading_status),"label"in c&&l(9,f=c.label),"show_label"in c&&l(10,d=c.show_label),"container"in c&&l(11,m=c.container),"scale"in c&&l(12,E=c.scale),"min_width"in c&&l(13,C=c.min_width),"gradio"in c&&l(14,D=c.gradio),"height"in c&&l(15,M=c.height),"zoom_speed"in c&&l(16,A=c.zoom_speed),"input_ready"in c&&l(22,U=c.input_ready),"has_change_history"in c&&l(2,N=c.has_change_history),"camera_position"in c&&l(17,V=c.camera_position),"interactive"in c&&l(18,R=c.interactive)},n.$$.update=()=>{n.$$.dirty[0]&524288&&l(22,U=!L)},[r,o,N,t,s,a,_,i,u,f,d,m,E,C,D,M,A,V,R,L,w,K,U,W,q,Q,X,Z,x,h,pe,be,we,ke]}class It extends ee{constructor(e){super(),te(this,e,_t,rt,le,{elem_id:3,elem_classes:4,visible:5,value:0,root:6,display_mode:7,clear_color:8,loading_status:1,label:9,show_label:10,container:11,scale:12,min_width:13,gradio:14,height:15,zoom_speed:16,input_ready:22,has_change_history:2,camera_position:17,interactive:18},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),g()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),g()}get display_mode(){return this.$$.ctx[7]}set display_mode(e){this.$$set({display_mode:e}),g()}get clear_color(){return this.$$.ctx[8]}set clear_color(e){this.$$set({clear_color:e}),g()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),g()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),g()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),g()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),g()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),g()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),g()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),g()}get zoom_speed(){return this.$$.ctx[16]}set zoom_speed(e){this.$$set({zoom_speed:e}),g()}get input_ready(){return this.$$.ctx[22]}set input_ready(e){this.$$set({input_ready:e}),g()}get has_change_history(){return this.$$.ctx[2]}set has_change_history(e){this.$$set({has_change_history:e}),g()}get camera_position(){return this.$$.ctx[17]}set camera_position(e){this.$$set({camera_position:e}),g()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),g()}}export{Mt as BaseExample,ye as BaseModel3D,xe as BaseModel3DUpload,It as default};
//# sourceMappingURL=Index-oM2DV30X.js.map
