import{a as l,i as d,s as p,E as o,z as e,d as c,C as h,D as n,l as u}from"../lite.js";function f(r){let t,s;return{c(){t=o("svg"),s=o("path"),e(s,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-edit-2")},m(a,i){c(a,t,i),h(t,s)},p:n,i:n,o:n,d(a){a&&u(t)}}}class m extends l{constructor(t){super(),d(this,t,null,f,p,{})}}export{m as E};
//# sourceMappingURL=Edit-fMGAgLsI.js.map
