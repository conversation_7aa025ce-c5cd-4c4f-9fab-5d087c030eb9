import{a as g,i as o,s as m,f as n,y,w as v,z as _,A as c,d as b,C as x,x as q,D as r,l as C}from"../lite.js";function k(i){let e,a;return{c(){e=y("div"),a=v(i[2]),_(e,"class","svelte-1ayixqk"),c(e,"table",i[0]==="table"),c(e,"gallery",i[0]==="gallery"),c(e,"selected",i[1])},m(t,l){b(t,e,l),x(e,a)},p(t,[l]){l&4&&q(a,t[2]),l&1&&c(e,"table",t[0]==="table"),l&1&&c(e,"gallery",t[0]==="gallery"),l&2&&c(e,"selected",t[1])},i:r,o:r,d(t){t&&C(e)}}}function w(i,e,a){let{value:t}=e,{type:l}=e,{selected:h=!1}=e,{choices:f}=e,u;if(t===null)u="";else{let s=f.find(d=>d[1]===t);u=s?s[0]:""}return i.$$set=s=>{"value"in s&&a(3,t=s.value),"type"in s&&a(0,l=s.type),"selected"in s&&a(1,h=s.selected),"choices"in s&&a(4,f=s.choices)},[l,h,u,t,f]}class A extends g{constructor(e){super(),o(this,e,w,k,m,{value:3,type:0,selected:1,choices:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),n()}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),n()}get selected(){return this.$$.ctx[1]}set selected(e){this.$$set({selected:e}),n()}get choices(){return this.$$.ctx[4]}set choices(e){this.$$set({choices:e}),n()}}export{A as default};
//# sourceMappingURL=Example-B32gYPqN.js.map
