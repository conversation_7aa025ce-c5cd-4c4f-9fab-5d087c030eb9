import os
import subprocess
from config import get_config

def open_pdf(file_name: str):
    """Open a file (PDF/HTML) in browser based on configuration settings."""
    config = get_config()

    if not config.launch_pdf_in_browser:
        print(f"PDF/HTML launch in browser is disabled in configuration. File: {file_name}")
        return

    if not os.path.exists(file_name):
        print(f"File '{file_name}' does not exist.")
        return

    pdf_file_path = os.path.abspath(file_name)
    if os.path.exists(pdf_file_path):
        pdf_file_url = f"file:///{pdf_file_path.replace(os.sep, '/')}"
        print(f"Opening: {pdf_file_url}")

        edge_path = config.edge_path
        chrome_path = config.chrome_instance_path

        try:
            subprocess.run([edge_path, pdf_file_path])
        except FileNotFoundError:
            print("Microsoft Edge not found, trying Google Chrome.")
            try:
                subprocess.run([chrome_path, pdf_file_path])
            except FileNotFoundError:
                print("Google Chrome not found, please install one of the browsers.")
    else:
        print("File does not exist!")