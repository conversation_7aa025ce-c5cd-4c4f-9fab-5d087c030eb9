import os
import subprocess

def open_pdf(file_name: str):
    if not os.path.exists(file_name):
        print(f"PDF '{file_name}' does not exist.")
        return
    pdf_file_path = os.path.abspath(file_name)
    if os.path.exists(pdf_file_path):
        pdf_file_url = f"file:///{pdf_file_path.replace(os.sep, '/')}"  
        print(f"Opening: {pdf_file_url}")
        edge_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
        chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
        
        try:
            subprocess.run([edge_path, pdf_file_path])
        except FileNotFoundError:
            print("Microsoft Edge not found, trying Google Chrome.")
            try:
                subprocess.run([chrome_path, pdf_file_path])
            except FileNotFoundError:
                print("Google Chrome not found, please install one of the browsers.")
    else:
        print("PDF file does not exist!")