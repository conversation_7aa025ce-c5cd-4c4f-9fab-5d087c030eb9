import{a as C,i as S,s as z,f as c,y as L,c as D,z as v,A,$ as I,d as O,m as E,k as T,t as j,l as P,n as M,o as Q,b1 as R,ao as B,a5 as U,N as V,q as N,u as F,r as G,v as H}from"../lite.js";import{a as W}from"./Tabs-HYge2SpJ.js";import X from"./Index-BHNCUXBa.js";function Y(n){let e;const l=n[13].default,t=N(l,n,n[14],null);return{c(){t&&t.c()},m(s,i){t&&t.m(s,i),e=!0},p(s,i){t&&t.p&&(!e||i&16384)&&F(t,l,s,s[14],e?H(l,s[14],i,null):G(s[14]),null)},i(s){e||(T(t,s),e=!0)},o(s){j(t,s),e=!1},d(s){t&&t.d(s)}}}function Z(n){let e,l,t,s;return l=new X({props:{scale:n[4]>=1?n[4]:null,$$slots:{default:[Y]},$$scope:{ctx:n}}}),{c(){e=L("div"),D(l.$$.fragment),v(e,"id",n[0]),v(e,"class",t="tabitem "+n[1].join(" ")+" svelte-wv8on1"),v(e,"role","tabpanel"),A(e,"grow-children",n[4]>=1),I(e,"display",n[5]===n[2]&&n[3]?"flex":"none"),I(e,"flex-grow",n[4])},m(i,f){O(i,e,f),E(l,e,null),s=!0},p(i,[f]){const u={};f&16&&(u.scale=i[4]>=1?i[4]:null),f&16384&&(u.$$scope={dirty:f,ctx:i}),l.$set(u),(!s||f&1)&&v(e,"id",i[0]),(!s||f&2&&t!==(t="tabitem "+i[1].join(" ")+" svelte-wv8on1"))&&v(e,"class",t),(!s||f&18)&&A(e,"grow-children",i[4]>=1),f&44&&I(e,"display",i[5]===i[2]&&i[3]?"flex":"none"),f&16&&I(e,"flex-grow",i[4])},i(i){s||(T(l.$$.fragment,i),s=!0)},o(i){j(l.$$.fragment,i),s=!1},d(i){i&&P(e),M(l)}}}function y(n,e,l){let t,s,{$$slots:i={},$$scope:f}=e,{elem_id:u=""}=e,{elem_classes:b=[]}=e,{label:_}=e,{id:m={}}=e,{visible:d}=e,{interactive:h}=e,{order:o}=e,{scale:g}=e;const a=Q(),{register_tab:J,unregister_tab:K,selected_tab:k,selected_tab_index:q}=R(W);B(n,k,r=>l(5,s=r)),B(n,q,r=>l(12,t=r));let w;return U(()=>()=>K({label:_,id:m,elem_id:u},o)),n.$$set=r=>{"elem_id"in r&&l(0,u=r.elem_id),"elem_classes"in r&&l(1,b=r.elem_classes),"label"in r&&l(8,_=r.label),"id"in r&&l(2,m=r.id),"visible"in r&&l(3,d=r.visible),"interactive"in r&&l(9,h=r.interactive),"order"in r&&l(10,o=r.order),"scale"in r&&l(4,g=r.scale),"$$scope"in r&&l(14,f=r.$$scope)},n.$$.update=()=>{n.$$.dirty&1821&&l(11,w=J({label:_,id:m,elem_id:u,visible:d,interactive:h,scale:g},o)),n.$$.dirty&6400&&t===w&&V().then(()=>a("select",{value:_,index:w}))},[u,b,m,d,g,s,k,q,_,h,o,w,t,i,f]}class x extends C{constructor(e){super(),S(this,e,y,Z,z,{elem_id:0,elem_classes:1,label:8,id:2,visible:3,interactive:9,order:10,scale:4})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),c()}get id(){return this.$$.ctx[2]}set id(e){this.$$set({id:e}),c()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),c()}get interactive(){return this.$$.ctx[9]}set interactive(e){this.$$set({interactive:e}),c()}get order(){return this.$$.ctx[10]}set order(e){this.$$set({order:e}),c()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),c()}}const $=x;function p(n){let e;const l=n[9].default,t=N(l,n,n[11],null);return{c(){t&&t.c()},m(s,i){t&&t.m(s,i),e=!0},p(s,i){t&&t.p&&(!e||i&2048)&&F(t,l,s,s[11],e?H(l,s[11],i,null):G(s[11]),null)},i(s){e||(T(t,s),e=!0)},o(s){j(t,s),e=!1},d(s){t&&t.d(s)}}}function ee(n){let e,l;return e=new $({props:{elem_id:n[0],elem_classes:n[1],label:n[2],visible:n[5],interactive:n[6],id:n[3],order:n[7],scale:n[8],$$slots:{default:[p]},$$scope:{ctx:n}}}),e.$on("select",n[10]),{c(){D(e.$$.fragment)},m(t,s){E(e,t,s),l=!0},p(t,[s]){const i={};s&1&&(i.elem_id=t[0]),s&2&&(i.elem_classes=t[1]),s&4&&(i.label=t[2]),s&32&&(i.visible=t[5]),s&64&&(i.interactive=t[6]),s&8&&(i.id=t[3]),s&128&&(i.order=t[7]),s&256&&(i.scale=t[8]),s&2048&&(i.$$scope={dirty:s,ctx:t}),e.$set(i)},i(t){l||(T(e.$$.fragment,t),l=!0)},o(t){j(e.$$.fragment,t),l=!1},d(t){M(e,t)}}}function te(n,e,l){let{$$slots:t={},$$scope:s}=e,{elem_id:i=""}=e,{elem_classes:f=[]}=e,{label:u}=e,{id:b}=e,{gradio:_}=e,{visible:m=!0}=e,{interactive:d=!0}=e,{order:h}=e,{scale:o}=e;const g=({detail:a})=>_?.dispatch("select",a);return n.$$set=a=>{"elem_id"in a&&l(0,i=a.elem_id),"elem_classes"in a&&l(1,f=a.elem_classes),"label"in a&&l(2,u=a.label),"id"in a&&l(3,b=a.id),"gradio"in a&&l(4,_=a.gradio),"visible"in a&&l(5,m=a.visible),"interactive"in a&&l(6,d=a.interactive),"order"in a&&l(7,h=a.order),"scale"in a&&l(8,o=a.scale),"$$scope"in a&&l(11,s=a.$$scope)},[i,f,u,b,_,m,d,h,o,t,g,s]}class ne extends C{constructor(e){super(),S(this,e,te,ee,z,{elem_id:0,elem_classes:1,label:2,id:3,gradio:4,visible:5,interactive:6,order:7,scale:8})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),c()}get id(){return this.$$.ctx[3]}set id(e){this.$$set({id:e}),c()}get gradio(){return this.$$.ctx[4]}set gradio(e){this.$$set({gradio:e}),c()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),c()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),c()}get order(){return this.$$.ctx[7]}set order(e){this.$$set({order:e}),c()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),c()}}export{$ as BaseTabItem,ne as default};
//# sourceMappingURL=Index-VGkfHkD6.js.map
