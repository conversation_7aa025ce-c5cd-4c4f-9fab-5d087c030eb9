import{a as x,i as d,s as w,E as n,z as e,d as g,C as o,D as a,l as p}from"../lite.js";function m(h){let t,r,s;return{c(){t=n("svg"),r=n("rect"),s=n("rect"),e(r,"x","6"),e(r,"y","4"),e(r,"width","4"),e(r,"height","16"),e(s,"x","14"),e(s,"y","4"),e(s,"width","4"),e(s,"height","16"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","currentColor"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(i,l){g(i,t,l),o(t,r),o(t,s)},p:a,i:a,o:a,d(i){i&&p(t)}}}class k extends x{constructor(t){super(),d(this,t,null,m,w,{})}}function v(h){let t,r,s,i,l,c;return{c(){t=n("svg"),r=n("circle"),s=n("circle"),i=n("line"),l=n("line"),c=n("line"),e(r,"cx","6"),e(r,"cy","6"),e(r,"r","3"),e(s,"cx","6"),e(s,"cy","18"),e(s,"r","3"),e(i,"x1","20"),e(i,"y1","4"),e(i,"x2","8.12"),e(i,"y2","15.88"),e(l,"x1","14.47"),e(l,"y1","14.48"),e(l,"x2","20"),e(l,"y2","20"),e(c,"x1","8.12"),e(c,"y1","8.12"),e(c,"x2","12"),e(c,"y2","12"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","2"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-scissors")},m(u,f){g(u,t,f),o(t,r),o(t,s),o(t,i),o(t,l),o(t,c)},p:a,i:a,o:a,d(u){u&&p(t)}}}class C extends x{constructor(t){super(),d(this,t,null,v,w,{})}}export{k as P,C as T};
//# sourceMappingURL=Trim-CDsEvQ4G.js.map
