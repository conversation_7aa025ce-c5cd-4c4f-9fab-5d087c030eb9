{"version": 3, "file": "Index-EeKGzAVo.js", "sources": ["../../../icons/src/Chat.svelte", "../../../icons/src/DropdownCircularArrow.svelte", "../../../icons/src/Retry.svelte", "../../../icons/src/ScrollDownArrow.svelte", "../../../chatbot/shared/utils.ts", "../../../chatbot/shared/ThumbDownActive.svelte", "../../../chatbot/shared/ThumbDownDefault.svelte", "../../../chatbot/shared/ThumbUpActive.svelte", "../../../chatbot/shared/ThumbUpDefault.svelte", "../../../chatbot/shared/Flag.svelte", "../../../chatbot/shared/FlagActive.svelte", "../../../chatbot/shared/LikeDislike.svelte", "../../../chatbot/shared/Copy.svelte", "../../../chatbot/shared/ButtonPanel.svelte", "../../../chatbot/shared/Component.svelte", "../../../chatbot/shared/MessageContent.svelte", "../../../chatbot/shared/Thought.svelte", "../../../chatbot/shared/Message.svelte", "../../../chatbot/shared/Pending.svelte", "../../../chatbot/shared/Examples.svelte", "../../../chatbot/shared/CopyAll.svelte", "../../../chatbot/shared/ChatBot.svelte", "../../../chatbot/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "<svg\n\tclass=\"dropdown-arrow\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 18 18\"\n>\n\t<circle cx=\"9\" cy=\"9\" r=\"8\" class=\"circle\" />\n\t<path d=\"M5 8l4 4 4-4z\" />\n</svg>\n\n<style>\n\t.dropdown-arrow {\n\t\tfill: currentColor;\n\t}\n\n\t.circle {\n\t\tfill: currentColor;\n\t\topacity: 0.1;\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tcolor=\"currentColor\"\n>\n\t<path\n\t\td=\"M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M12 20L12 4M12 20L7 15M12 20L17 15\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n</svg>\n", "import type { FileData } from \"@gradio/client\";\nimport type { ComponentType, SvelteComponent } from \"svelte\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\nimport type {\n\tTupleFormat,\n\tComponentMessage,\n\tComponentData,\n\tTextMessage,\n\tNormalisedMessage,\n\tMessage,\n\tMessageRole,\n\tThoughtNode\n} from \"../types\";\nimport type { LoadedComponent } from \"../../core/src/types\";\nimport { Gradio } from \"@gradio/utils\";\n\nexport const format_chat_for_sharing = async (\n\tchat: NormalisedMessage[],\n\turl_length_limit = 1800\n): Promise<string> => {\n\tlet messages_to_share = [...chat];\n\tlet formatted = await format_messages(messages_to_share);\n\n\tif (formatted.length > url_length_limit && messages_to_share.length > 2) {\n\t\tconst first_message = messages_to_share[0];\n\t\tconst last_message = messages_to_share[messages_to_share.length - 1];\n\t\tmessages_to_share = [first_message, last_message];\n\t\tformatted = await format_messages(messages_to_share);\n\t}\n\n\tif (formatted.length > url_length_limit && messages_to_share.length > 0) {\n\t\tconst truncated_messages = messages_to_share.map((msg) => {\n\t\t\tif (msg.type === \"text\") {\n\t\t\t\tconst max_length =\n\t\t\t\t\tMath.floor(url_length_limit / messages_to_share.length) - 20;\n\t\t\t\tif (msg.content.length > max_length) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...msg,\n\t\t\t\t\t\tcontent: msg.content.substring(0, max_length) + \"...\"\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn msg;\n\t\t});\n\n\t\tmessages_to_share = truncated_messages;\n\t\tformatted = await format_messages(messages_to_share);\n\t}\n\n\treturn formatted;\n};\n\nconst format_messages = async (chat: NormalisedMessage[]): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message) => {\n\t\t\tif (message.role === \"system\") return \"\";\n\t\t\tlet speaker_emoji = message.role === \"user\" ? \"😃\" : \"🤖\";\n\t\t\tlet html_content = \"\";\n\n\t\t\tif (message.type === \"text\") {\n\t\t\t\tconst regexPatterns = {\n\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t};\n\n\t\t\t\thtml_content = message.content;\n\n\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\tlet match;\n\n\t\t\t\t\twhile ((match = regex.exec(message.content)) !== null) {\n\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (!message.content.value) return \"\";\n\t\t\t\tconst url =\n\t\t\t\t\tmessage.content.component === \"video\"\n\t\t\t\t\t\t? message.content.value?.video.path\n\t\t\t\t\t\t: message.content.value;\n\t\t\t\tconst file_url = await uploadToHuggingFace(url, \"url\");\n\t\t\t\tif (message.content.component === \"audio\") {\n\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t} else if (message.content.component === \"video\") {\n\t\t\t\t\thtml_content = file_url;\n\t\t\t\t} else if (message.content.component === \"image\") {\n\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t})\n\t);\n\treturn messages.filter((msg) => msg !== \"\").join(\"\\n\");\n};\n\nexport interface UndoRetryData {\n\tindex: number | [number, number];\n\tvalue: string | FileData | ComponentData;\n}\n\nexport interface EditData {\n\tindex: number | [number, number];\n\tvalue: string;\n\tprevious_value: string;\n}\n\nconst redirect_src_url = (src: string, root: string): string =>\n\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\nfunction get_component_for_mime_type(\n\tmime_type: string | null | undefined\n): string {\n\tif (!mime_type) return \"file\";\n\tif (mime_type.includes(\"audio\")) return \"audio\";\n\tif (mime_type.includes(\"video\")) return \"video\";\n\tif (mime_type.includes(\"image\")) return \"image\";\n\treturn \"file\";\n}\n\nfunction convert_file_message_to_component_message(\n\tmessage: any\n): ComponentData {\n\tconst _file = Array.isArray(message.file) ? message.file[0] : message.file;\n\treturn {\n\t\tcomponent: get_component_for_mime_type(_file?.mime_type),\n\t\tvalue: message.file,\n\t\talt_text: message.alt_text,\n\t\tconstructor_args: {},\n\t\tprops: {}\n\t} as ComponentData;\n}\n\nexport function normalise_messages(\n\tmessages: Message[] | null,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\n\tconst thought_map = new Map<string, ThoughtNode>();\n\n\treturn messages\n\t\t.map((message, i) => {\n\t\t\tlet normalized: NormalisedMessage =\n\t\t\t\ttypeof message.content === \"string\"\n\t\t\t\t\t? {\n\t\t\t\t\t\t\trole: message.role,\n\t\t\t\t\t\t\tmetadata: message.metadata,\n\t\t\t\t\t\t\tcontent: redirect_src_url(message.content, root),\n\t\t\t\t\t\t\ttype: \"text\",\n\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\toptions: message.options\n\t\t\t\t\t\t}\n\t\t\t\t\t: \"file\" in message.content\n\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\tcontent: convert_file_message_to_component_message(\n\t\t\t\t\t\t\t\t\tmessage.content\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\tmetadata: message.metadata,\n\t\t\t\t\t\t\t\trole: message.role,\n\t\t\t\t\t\t\t\ttype: \"component\",\n\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\toptions: message.options\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t: ({ type: \"component\", ...message } as ComponentMessage);\n\n\t\t\t// handle thoughts\n\t\t\tconst { id, title, parent_id } = message.metadata || {};\n\t\t\tif (parent_id) {\n\t\t\t\tconst parent = thought_map.get(String(parent_id));\n\t\t\t\tif (parent) {\n\t\t\t\t\tconst thought = { ...normalized, children: [] } as ThoughtNode;\n\t\t\t\t\tparent.children.push(thought);\n\t\t\t\t\tif (id && title) {\n\t\t\t\t\t\tthought_map.set(String(id), thought);\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (id && title) {\n\t\t\t\tconst thought = { ...normalized, children: [] } as ThoughtNode;\n\t\t\t\tthought_map.set(String(id), thought);\n\t\t\t\treturn thought;\n\t\t\t}\n\n\t\t\treturn normalized;\n\t\t})\n\t\t.filter((msg): msg is NormalisedMessage => msg !== null);\n}\n\nexport function normalise_tuples(\n\tmessages: TupleFormat,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\tconst msg = messages.flatMap((message_pair, i) => {\n\t\treturn message_pair.map((message, index) => {\n\t\t\tif (message == null) return null;\n\t\t\tconst role = index == 0 ? \"user\" : \"assistant\";\n\n\t\t\tif (typeof message === \"string\") {\n\t\t\t\treturn {\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"text\",\n\t\t\t\t\tcontent: redirect_src_url(message, root),\n\t\t\t\t\tmetadata: { title: null },\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as TextMessage;\n\t\t\t}\n\n\t\t\tif (\"file\" in message) {\n\t\t\t\treturn {\n\t\t\t\t\tcontent: convert_file_message_to_component_message(message),\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"component\",\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as ComponentMessage;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\trole: role,\n\t\t\t\tcontent: message,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: [i, index]\n\t\t\t} as ComponentMessage;\n\t\t});\n\t});\n\treturn msg.filter((message) => message != null) as NormalisedMessage[];\n}\n\nexport function is_component_message(\n\tmessage: NormalisedMessage\n): message is ComponentMessage {\n\treturn message.type === \"component\";\n}\n\nexport function is_last_bot_message(\n\tmessages: NormalisedMessage[],\n\tall_messages: NormalisedMessage[]\n): boolean {\n\tconst is_bot = messages[messages.length - 1].role === \"assistant\";\n\tconst last_index = messages[messages.length - 1].index;\n\t// use JSON.stringify to handle both the number and tuple cases\n\t// when msg_format is tuples, last_index is an array and when it is messages, it is a number\n\tconst is_last =\n\t\tJSON.stringify(last_index) ===\n\t\tJSON.stringify(all_messages[all_messages.length - 1].index);\n\treturn is_last && is_bot;\n}\n\nexport function group_messages(\n\tmessages: NormalisedMessage[],\n\tmsg_format: \"messages\" | \"tuples\"\n): NormalisedMessage[][] {\n\tconst groupedMessages: NormalisedMessage[][] = [];\n\tlet currentGroup: NormalisedMessage[] = [];\n\tlet currentRole: MessageRole | null = null;\n\n\tfor (const message of messages) {\n\t\tif (!(message.role === \"assistant\" || message.role === \"user\")) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (message.role === currentRole) {\n\t\t\tcurrentGroup.push(message);\n\t\t} else {\n\t\t\tif (currentGroup.length > 0) {\n\t\t\t\tgroupedMessages.push(currentGroup);\n\t\t\t}\n\t\t\tcurrentGroup = [message];\n\t\t\tcurrentRole = message.role;\n\t\t}\n\t}\n\n\tif (currentGroup.length > 0) {\n\t\tgroupedMessages.push(currentGroup);\n\t}\n\n\treturn groupedMessages;\n}\n\nexport async function load_components(\n\tcomponent_names: string[],\n\t_components: Record<string, ComponentType<SvelteComponent>>,\n\tload_component: Gradio[\"load_component\"]\n): Promise<Record<string, ComponentType<SvelteComponent>>> {\n\tlet names: string[] = [];\n\tlet components: ReturnType<typeof load_component>[\"component\"][] = [];\n\n\tcomponent_names.forEach((component_name) => {\n\t\tif (_components[component_name] || component_name === \"file\") {\n\t\t\treturn;\n\t\t}\n\t\tconst variant = component_name === \"dataframe\" ? \"component\" : \"base\";\n\t\tconst { name, component } = load_component(component_name, variant);\n\t\tnames.push(name);\n\t\tcomponents.push(component);\n\t\tcomponent_name;\n\t});\n\tconst loaded_components: LoadedComponent[] = await Promise.all(components);\n\tloaded_components.forEach((component, i) => {\n\t\t_components[names[i]] = component.default;\n\t});\n\n\treturn _components;\n}\n\nexport function get_components_from_messages(\n\tmessages: NormalisedMessage[] | null\n): string[] {\n\tif (!messages) return [];\n\tlet components: Set<string> = new Set();\n\tmessages.forEach((message) => {\n\t\tif (message.type === \"component\") {\n\t\t\tcomponents.add(message.content.component);\n\t\t}\n\t});\n\treturn Array.from(components);\n}\n\nexport function get_thought_content(msg: NormalisedMessage, depth = 0): string {\n\tlet content = \"\";\n\tconst indent = \"  \".repeat(depth);\n\n\tif (msg.metadata?.title) {\n\t\tcontent += `${indent}${depth > 0 ? \"- \" : \"\"}${msg.metadata.title}\\n`;\n\t}\n\tif (typeof msg.content === \"string\") {\n\t\tcontent += `${indent}  ${msg.content}\\n`;\n\t}\n\tconst thought = msg as ThoughtNode;\n\tif (thought.children?.length > 0) {\n\t\tcontent += thought.children\n\t\t\t.map((child) => get_thought_content(child, depth + 1))\n\t\t\t.join(\"\");\n\t}\n\treturn content;\n}\n\nexport function all_text(message: TextMessage[] | TextMessage): string {\n\tif (Array.isArray(message)) {\n\t\treturn message\n\t\t\t.map((m) => {\n\t\t\t\tif (m.metadata?.title) {\n\t\t\t\t\treturn get_thought_content(m);\n\t\t\t\t}\n\t\t\t\treturn m.content;\n\t\t\t})\n\t\t\t.join(\"\\n\");\n\t}\n\tif (message.metadata?.title) {\n\t\treturn get_thought_content(message);\n\t}\n\treturn message.content;\n}\n\nexport function is_all_text(\n\tmessage: NormalisedMessage[] | NormalisedMessage\n): message is TextMessage[] | TextMessage {\n\treturn (\n\t\t(Array.isArray(message) &&\n\t\t\tmessage.every((m) => typeof m.content === \"string\")) ||\n\t\t(!Array.isArray(message) && typeof message.content === \"string\")\n\t);\n}\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\tid=\"icon\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 32 32\"\n\tfill=\"none\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M6,30H4V2H28l-5.8,9L28,20H6ZM6,18H24.33L19.8,11l4.53-7H6Z\"\n\t/></svg\n>\n", "<svg\n\tid=\"icon\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 32 32\"\n\tfill=\"none\"\n\t><path fill=\"currentColor\" d=\"M4,2H28l-5.8,9L28,20H6v10H4V2z\" /></svg\n>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport ThumbDownActive from \"./ThumbDownActive.svelte\";\n\timport ThumbDownDefault from \"./ThumbDownDefault.svelte\";\n\timport ThumbUpActive from \"./ThumbUpActive.svelte\";\n\timport ThumbUpDefault from \"./ThumbUpDefault.svelte\";\n\timport Flag from \"./Flag.svelte\";\n\timport FlagActive from \"./FlagActive.svelte\";\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let feedback_options: string[];\n\texport let selected: string | null = null;\n\t$: extra_feedback = feedback_options.filter(\n\t\t(option) => option !== \"Like\" && option !== \"Dislike\"\n\t);\n\n\tfunction toggleSelection(newSelection: string): void {\n\t\tselected = selected === newSelection ? null : newSelection;\n\t\thandle_action(selected);\n\t}\n</script>\n\n{#if feedback_options.includes(\"Like\") || feedback_options.includes(\"Dislike\")}\n\t{#if feedback_options.includes(\"Dislike\")}\n\t\t<IconButton\n\t\t\tIcon={selected === \"Dislike\" ? ThumbDownActive : ThumbDownDefault}\n\t\t\tlabel={selected === \"Dislike\" ? \"clicked dislike\" : \"dislike\"}\n\t\t\tcolor={selected === \"Dislike\"\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--block-label-text-color)\"}\n\t\t\ton:click={() => toggleSelection(\"Dislike\")}\n\t\t/>\n\t{/if}\n\t{#if feedback_options.includes(\"Like\")}\n\t\t<IconButton\n\t\t\tIcon={selected === \"Like\" ? ThumbUpActive : ThumbUpDefault}\n\t\t\tlabel={selected === \"Like\" ? \"clicked like\" : \"like\"}\n\t\t\tcolor={selected === \"Like\"\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--block-label-text-color)\"}\n\t\t\ton:click={() => toggleSelection(\"Like\")}\n\t\t/>\n\t{/if}\n{/if}\n\n{#if extra_feedback.length > 0}\n\t<div class=\"extra-feedback no-border\">\n\t\t<IconButton\n\t\t\tIcon={selected && extra_feedback.includes(selected) ? FlagActive : Flag}\n\t\t\tlabel=\"Feedback\"\n\t\t\tcolor={selected && extra_feedback.includes(selected)\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--block-label-text-color)\"}\n\t\t/>\n\t\t<div class=\"extra-feedback-options\">\n\t\t\t{#each extra_feedback as option}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"extra-feedback-option\"\n\t\t\t\t\tstyle:font-weight={selected === option ? \"bold\" : \"normal\"}\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\ttoggleSelection(option);\n\t\t\t\t\t\thandle_action(selected ? selected : null);\n\t\t\t\t\t}}>{option}</button\n\t\t\t\t>\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n{/if}\n\n<style>\n\t.extra-feedback {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t}\n\t.extra-feedback-options {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tpadding: var(--spacing-md) 0;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\ttop: 100%;\n\t}\n\t.extra-feedback:hover .extra-feedback-options {\n\t\tdisplay: flex;\n\t}\n\t.extra-feedback-option {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--block-label-text-color);\n\t\tbackground-color: var(--block-background-fill);\n\t\tfont-size: var(--text-xs);\n\t\tpadding: var(--spacing-xxs) var(--spacing-sm);\n\t\twidth: max-content;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport type { CopyData } from \"@gradio/utils\";\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tcopy: CopyData;\n\t}>();\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tdispatch(\"copy\", { value: value });\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied message\" : \"Copy message\"}\n\tIcon={copied ? Check : Copy}\n/>\n", "<script lang=\"ts\">\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Copy from \"./Copy.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { NormalisedMessage, TextMessage, ThoughtNode } from \"../types\";\n\timport { Retry, Undo, Edit, Check, Clear } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport { all_text, is_all_text } from \"./utils\";\n\n\texport let likeable: boolean;\n\texport let feedback_options: string[];\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let show_edit: boolean;\n\texport let in_edit_mode: boolean;\n\texport let show_copy_button: boolean;\n\texport let message: NormalisedMessage | NormalisedMessage[];\n\texport let position: \"right\" | \"left\";\n\texport let avatar: FileData | null;\n\texport let generating: boolean;\n\texport let current_feedback: string | null;\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let layout: \"bubble\" | \"panel\";\n\texport let dispatch: any;\n\n\t$: message_text = is_all_text(message) ? all_text(message) : \"\";\n\t$: show_copy = show_copy_button && message && is_all_text(message);\n</script>\n\n{#if show_copy || show_retry || show_undo || show_edit || likeable}\n\t<div\n\t\tclass=\"message-buttons-{position} {layout} message-buttons {avatar !==\n\t\t\tnull && 'with-avatar'}\"\n\t>\n\t\t<IconButtonWrapper top_panel={false}>\n\t\t\t{#if in_edit_mode}\n\t\t\t\t<IconButton\n\t\t\t\t\tlabel=\"Submit\"\n\t\t\t\t\tIcon={Check}\n\t\t\t\t\ton:click={() => handle_action(\"edit_submit\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t\t<IconButton\n\t\t\t\t\tlabel=\"Cancel\"\n\t\t\t\t\tIcon={Clear}\n\t\t\t\t\ton:click={() => handle_action(\"edit_cancel\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t{:else}\n\t\t\t\t{#if show_copy}\n\t\t\t\t\t<Copy\n\t\t\t\t\t\tvalue={message_text}\n\t\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_retry}\n\t\t\t\t\t<IconButton\n\t\t\t\t\t\tIcon={Retry}\n\t\t\t\t\t\tlabel=\"Retry\"\n\t\t\t\t\t\ton:click={() => handle_action(\"retry\")}\n\t\t\t\t\t\tdisabled={generating}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_undo}\n\t\t\t\t\t<IconButton\n\t\t\t\t\t\tlabel=\"Undo\"\n\t\t\t\t\t\tIcon={Undo}\n\t\t\t\t\t\ton:click={() => handle_action(\"undo\")}\n\t\t\t\t\t\tdisabled={generating}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if show_edit}\n\t\t\t\t\t<IconButton\n\t\t\t\t\t\tlabel=\"Edit\"\n\t\t\t\t\t\tIcon={Edit}\n\t\t\t\t\t\ton:click={() => handle_action(\"edit\")}\n\t\t\t\t\t\tdisabled={generating}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t\t{#if likeable}\n\t\t\t\t\t<LikeDislike\n\t\t\t\t\t\t{handle_action}\n\t\t\t\t\t\t{feedback_options}\n\t\t\t\t\t\tselected={current_feedback}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t</div>\n{/if}\n\n<style>\n\t.bubble :global(.icon-button-wrapper) {\n\t\tmargin: 0px calc(var(--spacing-xl) * 2);\n\t}\n\n\t.message-buttons {\n\t\tz-index: var(--layer-1);\n\t}\n\t.message-buttons-left {\n\t\talign-self: flex-start;\n\t}\n\n\t.bubble.message-buttons-right {\n\t\talign-self: flex-end;\n\t}\n\n\t.message-buttons-right :global(.icon-button-wrapper) {\n\t\tmargin-left: auto;\n\t}\n\n\t.bubble.with-avatar {\n\t\tmargin-left: calc(var(--spacing-xl) * 5);\n\t\tmargin-right: calc(var(--spacing-xl) * 5);\n\t}\n\n\t.panel {\n\t\tdisplay: flex;\n\t\talign-self: flex-start;\n\t\tz-index: var(--layer-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let type:\n\t\t| \"gallery\"\n\t\t| \"plot\"\n\t\t| \"audio\"\n\t\t| \"video\"\n\t\t| \"image\"\n\t\t| \"dataframe\"\n\t\t| string;\n\texport let components;\n\texport let value;\n\texport let target;\n\texport let theme_mode;\n\texport let props;\n\texport let i18n;\n\texport let upload;\n\texport let _fetch;\n\texport let allow_file_downloads: boolean;\n\texport let display_icon_button_wrapper_top_corner = false;\n</script>\n\n{#if type === \"gallery\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\t{_fetch}\n\t\tallow_preview={false}\n\t\tinteractive={false}\n\t\tmode=\"minimal\"\n\t\tfixed_height={1}\n\t\ton:load\n\t/>\n{:else if type === \"dataframe\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\tinteractive={false}\n\t\tline_breaks={props.line_breaks}\n\t\twrap={true}\n\t\troot=\"\"\n\t\tgradio={{ dispatch: () => {} }}\n\t\tdatatype={props.datatype}\n\t\tlatex_delimiters={props.latex_delimiters}\n\t\tcol_count={props.col_count}\n\t\trow_count={props.row_count}\n\t\ton:load\n\t/>\n{:else if type === \"plot\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{target}\n\t\t{theme_mode}\n\t\tbokeh_version={props.bokeh_version}\n\t\tcaption=\"\"\n\t\tshow_actions_button={true}\n\t\ton:load\n\t/>\n{:else if type === \"audio\"}\n\t<div style=\"position: relative;\">\n\t\t<svelte:component\n\t\t\tthis={components[type]}\n\t\t\t{value}\n\t\t\tshow_label={false}\n\t\t\tshow_share_button={true}\n\t\t\t{i18n}\n\t\t\tlabel=\"\"\n\t\t\twaveform_settings={{ autoplay: props.autoplay }}\n\t\t\tshow_download_button={allow_file_downloads}\n\t\t\t{display_icon_button_wrapper_top_corner}\n\t\t\ton:load\n\t\t/>\n\t</div>\n{:else if type === \"video\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\tautoplay={props.autoplay}\n\t\tvalue={value.video || value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\t{upload}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\tshow_download_button={allow_file_downloads}\n\t\ton:load\n\t>\n\t\t<track kind=\"captions\" />\n\t</svelte:component>\n{:else if type === \"image\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_download_button={allow_file_downloads}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\ton:load\n\t\t{i18n}\n\t/>\n{:else if type === \"html\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tgradio={{ dispatch: () => {} }}\n\t\ton:load\n\t/>\n{/if}\n", "<script lang=\"ts\">\n\timport { File } from \"@gradio/icons\";\n\timport Component from \"./Component.svelte\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { Client } from \"@gradio/client\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let sanitize_html: boolean;\n\texport let _fetch: typeof fetch;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let upload: Client[\"upload\"];\n\texport let target: HTMLElement | null;\n\texport let root: string;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let render_markdown: boolean;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\texport let thought_index: number;\n\texport let allow_tags: string[] | null = null;\n\n\texport let message: NormalisedMessage;\n</script>\n\n{#if message.type === \"text\"}\n\t<div class=\"message-content\">\n\t\t<Markdown\n\t\t\tmessage={message.content}\n\t\t\t{latex_delimiters}\n\t\t\t{sanitize_html}\n\t\t\t{render_markdown}\n\t\t\t{line_breaks}\n\t\t\ton:load={scroll}\n\t\t\t{root}\n\t\t\t{allow_tags}\n\t\t/>\n\t</div>\n{:else if message.type === \"component\" && message.content.component in _components}\n\t<Component\n\t\t{target}\n\t\t{theme_mode}\n\t\tprops={message.content.props}\n\t\ttype={message.content.component}\n\t\tcomponents={_components}\n\t\tvalue={message.content.value}\n\t\tdisplay_icon_button_wrapper_top_corner={thought_index > 0 &&\n\t\t\tdisplay_consecutive_in_same_bubble}\n\t\t{i18n}\n\t\t{upload}\n\t\t{_fetch}\n\t\ton:load={() => scroll()}\n\t\t{allow_file_downloads}\n\t/>\n{:else if message.type === \"component\" && message.content.component === \"file\"}\n\t<div class=\"file-container\">\n\t\t<div class=\"file-icon\">\n\t\t\t<File />\n\t\t</div>\n\t\t<div class=\"file-info\">\n\t\t\t<a\n\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\tclass=\"file-link\"\n\t\t\t\thref={message.content.value.url}\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t? null\n\t\t\t\t\t: message.content.value?.orig_name ||\n\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\"file\"}\n\t\t\t>\n\t\t\t\t<span class=\"file-name\"\n\t\t\t\t\t>{message.content.value?.orig_name ||\n\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\"file\"}</span\n\t\t\t\t>\n\t\t\t</a>\n\t\t\t<span class=\"file-type\"\n\t\t\t\t>{(\n\t\t\t\t\tmessage.content.value?.orig_name ||\n\t\t\t\t\tmessage.content.value?.path ||\n\t\t\t\t\t\"\"\n\t\t\t\t)\n\t\t\t\t\t.split(\".\")\n\t\t\t\t\t.pop()\n\t\t\t\t\t.toUpperCase()}</span\n\t\t\t>\n\t\t</div>\n\t</div>\n{/if}\n\n<style>\n\t.file-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\tpadding: var(--spacing-lg);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: fit-content;\n\t\tmargin: var(--spacing-sm) 0;\n\t}\n\n\t.file-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.file-icon :global(svg) {\n\t\twidth: var(--size-7);\n\t\theight: var(--size-7);\n\t}\n\n\t.file-info {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.file-link {\n\t\ttext-decoration: none;\n\t\tcolor: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xs);\n\t}\n\n\t.file-name {\n\t\tfont-family: var(--font);\n\t\tfont-size: var(--text-md);\n\t\tfont-weight: 500;\n\t}\n\n\t.file-type {\n\t\tfont-family: var(--font);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color-subdued);\n\t\ttext-transform: uppercase;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Client } from \"@gradio/client\";\n\timport type { NormalisedMessage, ThoughtNode } from \"../types\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\timport MessageContent from \"./MessageContent.svelte\";\n\timport { DropdownCircularArrow } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { slide } from \"svelte/transition\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\n\texport let thought: NormalisedMessage;\n\texport let rtl = false;\n\texport let sanitize_html: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let render_markdown: boolean;\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let upload: Client[\"upload\"];\n\texport let thought_index: number;\n\texport let target: HTMLElement | null;\n\texport let root: string;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _fetch: typeof fetch;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\n\tfunction is_thought_node(msg: NormalisedMessage): msg is ThoughtNode {\n\t\treturn \"children\" in msg;\n\t}\n\n\tlet thought_node: ThoughtNode;\n\t$: thought_node = {\n\t\t...thought,\n\t\tchildren: is_thought_node(thought) ? thought.children : []\n\t} as ThoughtNode;\n\n\tfunction toggleExpanded(): void {\n\t\texpanded = !expanded;\n\t}\n\n\t$: expanded = thought_node.metadata?.status !== \"done\";\n</script>\n\n<div class=\"thought-group\">\n\t<div\n\t\tclass=\"title\"\n\t\tclass:expanded\n\t\ton:click|stopPropagation={toggleExpanded}\n\t\taria-busy={thought_node.content === \"\" || thought_node.content === null}\n\t\trole=\"button\"\n\t\ttabindex=\"0\"\n\t\ton:keydown={(e) => e.key === \"Enter\" && toggleExpanded()}\n\t>\n\t\t<span\n\t\t\tclass=\"arrow\"\n\t\t\tstyle:transform={expanded ? \"rotate(180deg)\" : \"rotate(0deg)\"}\n\t\t>\n\t\t\t<IconButton Icon={DropdownCircularArrow} />\n\t\t</span>\n\t\t<Markdown\n\t\t\tmessage={thought_node.metadata?.title || \"\"}\n\t\t\t{render_markdown}\n\t\t\t{latex_delimiters}\n\t\t\t{sanitize_html}\n\t\t\t{root}\n\t\t/>\n\t\t{#if thought_node.metadata?.status === \"pending\"}\n\t\t\t<span class=\"loading-spinner\"></span>\n\t\t{/if}\n\t\t{#if thought_node?.metadata?.log || thought_node?.metadata?.duration}\n\t\t\t<span class=\"duration\">\n\t\t\t\t{#if thought_node.metadata.log}\n\t\t\t\t\t{thought_node.metadata.log}\n\t\t\t\t{/if}\n\t\t\t\t{#if thought_node.metadata.duration !== undefined}\n\t\t\t\t\t({#if Number.isInteger(thought_node.metadata.duration)}{thought_node\n\t\t\t\t\t\t\t.metadata\n\t\t\t\t\t\t\t.duration}s{:else if thought_node.metadata.duration >= 0.1}{thought_node.metadata.duration.toFixed(\n\t\t\t\t\t\t\t1\n\t\t\t\t\t\t)}s{:else}{(thought_node.metadata.duration * 1000).toFixed(\n\t\t\t\t\t\t\t1\n\t\t\t\t\t\t)}ms{/if})\n\t\t\t\t{/if}\n\t\t\t</span>\n\t\t{/if}\n\t</div>\n\n\t{#if expanded}\n\t\t<div class=\"content\" transition:slide>\n\t\t\t<MessageContent\n\t\t\t\tmessage={thought_node}\n\t\t\t\t{sanitize_html}\n\t\t\t\t{latex_delimiters}\n\t\t\t\t{render_markdown}\n\t\t\t\t{_components}\n\t\t\t\t{upload}\n\t\t\t\t{thought_index}\n\t\t\t\t{target}\n\t\t\t\t{root}\n\t\t\t\t{theme_mode}\n\t\t\t\t{_fetch}\n\t\t\t\t{scroll}\n\t\t\t\t{allow_file_downloads}\n\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t{i18n}\n\t\t\t\t{line_breaks}\n\t\t\t/>\n\n\t\t\t{#if thought_node.children?.length > 0}\n\t\t\t\t<div class=\"children\">\n\t\t\t\t\t{#each thought_node.children as child, index}\n\t\t\t\t\t\t<svelte:self\n\t\t\t\t\t\t\tthought={child}\n\t\t\t\t\t\t\t{rtl}\n\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t{_components}\n\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\tthought_index={thought_index + 1}\n\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t{scroll}\n\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.thought-group {\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-md);\n\t\tmargin: var(--spacing-md) 0;\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.children :global(.thought-group) {\n\t\tborder: none;\n\t\tmargin: 0;\n\t\tpadding-bottom: 0;\n\t}\n\n\t.children {\n\t\tpadding-left: var(--spacing-md);\n\t}\n\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\twidth: 100%;\n\t}\n\n\t.title :global(.md) {\n\t\tfont-size: var(--text-sm) !important;\n\t}\n\n\t.content {\n\t\toverflow-wrap: break-word;\n\t\tword-break: break-word;\n\t\tmargin-left: var(--spacing-lg);\n\t\tmargin-bottom: var(--spacing-sm);\n\t}\n\t.content :global(*) {\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.thought-group :global(.thought:not(.nested)) {\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\n\t.duration {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-size: var(--text-sm);\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.arrow {\n\t\topacity: 0.8;\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.arrow :global(button) {\n\t\tbackground-color: transparent;\n\t}\n\n\t.loading-spinner {\n\t\tdisplay: inline-block;\n\t\twidth: 12px;\n\t\theight: 12px;\n\t\tborder: 2px solid var(--body-text-color);\n\t\tborder-radius: 50%;\n\t\tborder-top-color: transparent;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin: 0 var(--size-1) -1px var(--size-2);\n\t\topacity: 0.8;\n\t}\n\n\t@keyframes spin {\n\t\tto {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t.thought-group :global(.message-content) {\n\t\topacity: 0.8;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { is_component_message } from \"../shared/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { NormalisedMessage } from \"../types\";\n\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\timport ButtonPanel from \"./ButtonPanel.svelte\";\n\timport MessageContent from \"./MessageContent.svelte\";\n\timport Thought from \"./Thought.svelte\";\n\n\texport let value: NormalisedMessage[];\n\texport let avatar_img: FileData | null;\n\texport let opposite_avatar_img: FileData | null = null;\n\texport let role = \"user\";\n\texport let messages: NormalisedMessage[] = [];\n\texport let layout: \"bubble\" | \"panel\";\n\texport let render_markdown: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let sanitize_html: boolean;\n\texport let selectable: boolean;\n\texport let _fetch: typeof fetch;\n\texport let rtl: boolean;\n\texport let dispatch: any;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let upload: Client[\"upload\"];\n\texport let target: HTMLElement | null;\n\texport let root: string;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let i: number;\n\texport let show_copy_button: boolean;\n\texport let generating: boolean;\n\texport let feedback_options: string[];\n\texport let show_like: boolean;\n\texport let show_edit: boolean;\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let msg_format: \"tuples\" | \"messages\";\n\texport let handle_action: (selected: string | null) => void;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let in_edit_mode: boolean;\n\texport let edit_message: string;\n\texport let display_consecutive_in_same_bubble: boolean;\n\texport let current_feedback: string | null = null;\n\texport let allow_tags: string[] | null = null;\n\tlet messageElements: HTMLDivElement[] = [];\n\tlet previous_edit_mode = false;\n\tlet last_message_width = 0;\n\tlet last_message_height = 0;\n\n\t$: if (in_edit_mode && !previous_edit_mode) {\n\t\tlast_message_width =\n\t\t\tmessageElements[messageElements.length - 1]?.clientWidth;\n\t\tlast_message_height =\n\t\t\tmessageElements[messageElements.length - 1]?.clientHeight;\n\t}\n\n\tfunction handle_select(i: number, message: NormalisedMessage): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: message.index,\n\t\t\tvalue: message.content\n\t\t});\n\t}\n\n\tfunction get_message_label_data(message: NormalisedMessage): string {\n\t\tif (message.type === \"text\") {\n\t\t\treturn message.content;\n\t\t} else if (\n\t\t\tmessage.type === \"component\" &&\n\t\t\tmessage.content.component === \"file\"\n\t\t) {\n\t\t\tif (Array.isArray(message.content.value)) {\n\t\t\t\treturn `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n\t\t\t}\n\t\t\treturn (\n\t\t\t\t`file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` +\n\t\t\t\t(message.content.value?.orig_name ?? \"\")\n\t\t\t);\n\t\t}\n\t\treturn `a component of type ${message.content.component ?? \"unknown\"}`;\n\t}\n\n\ttype ButtonPanelProps = {\n\t\thandle_action: (selected: string | null) => void;\n\t\tlikeable: boolean;\n\t\tfeedback_options: string[];\n\t\tshow_retry: boolean;\n\t\tshow_undo: boolean;\n\t\tshow_edit: boolean;\n\t\tin_edit_mode: boolean;\n\t\tgenerating: boolean;\n\t\tshow_copy_button: boolean;\n\t\tmessage: NormalisedMessage[] | NormalisedMessage;\n\t\tposition: \"left\" | \"right\";\n\t\tlayout: \"bubble\" | \"panel\";\n\t\tavatar: FileData | null;\n\t\tdispatch: any;\n\t\tcurrent_feedback: string | null;\n\t};\n\n\tlet button_panel_props: ButtonPanelProps;\n\t$: button_panel_props = {\n\t\thandle_action,\n\t\tlikeable: show_like,\n\t\tfeedback_options,\n\t\tshow_retry,\n\t\tshow_undo,\n\t\tshow_edit,\n\t\tin_edit_mode,\n\t\tgenerating,\n\t\tshow_copy_button,\n\t\tmessage: msg_format === \"tuples\" ? messages[0] : messages,\n\t\tposition: role === \"user\" ? \"right\" : \"left\",\n\t\tavatar: avatar_img,\n\t\tlayout,\n\t\tdispatch,\n\t\tcurrent_feedback\n\t};\n</script>\n\n<div\n\tclass=\"message-row {layout} {role}-row\"\n\tclass:with_avatar={avatar_img !== null}\n\tclass:with_opposite_avatar={opposite_avatar_img !== null}\n>\n\t{#if avatar_img !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_img?.url} alt=\"{role} avatar\" />\n\t\t</div>\n\t{/if}\n\t<div\n\t\tclass:role\n\t\tclass=\"flex-wrap\"\n\t\tclass:component-wrap={messages[0].type === \"component\"}\n\t>\n\t\t<div\n\t\t\tclass:message={display_consecutive_in_same_bubble}\n\t\t\tclass={display_consecutive_in_same_bubble ? role : \"\"}\n\t\t>\n\t\t\t{#each messages as message, thought_index}\n\t\t\t\t<div\n\t\t\t\t\tclass=\"message {!display_consecutive_in_same_bubble ? role : ''}\"\n\t\t\t\t\tclass:panel-full-width={true}\n\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\tclass:component={message.type === \"component\"}\n\t\t\t\t\tclass:html={is_component_message(message) &&\n\t\t\t\t\t\tmessage.content.component === \"html\"}\n\t\t\t\t\tclass:thought={thought_index > 0}\n\t\t\t\t>\n\t\t\t\t\t{#if in_edit_mode && thought_index === messages.length - 1 && message.type === \"text\"}\n\t\t\t\t\t\t<!-- svelte-ignore a11y-autofocus -->\n\t\t\t\t\t\t<textarea\n\t\t\t\t\t\t\tclass=\"edit-textarea\"\n\t\t\t\t\t\t\tstyle:width={`max(${last_message_width}px, 160px)`}\n\t\t\t\t\t\t\tstyle:min-height={`${last_message_height}px`}\n\t\t\t\t\t\t\tautofocus\n\t\t\t\t\t\t\tbind:value={edit_message}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tdata-testid={role}\n\t\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\t\tstyle:user-select=\"text\"\n\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\tstyle:cursor={selectable ? \"pointer\" : \"auto\"}\n\t\t\t\t\t\t\tstyle:text-align={rtl ? \"right\" : \"left\"}\n\t\t\t\t\t\t\tbind:this={messageElements[thought_index]}\n\t\t\t\t\t\t\ton:click={() => handle_select(i, message)}\n\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\thandle_select(i, message);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\t\taria-label={role +\n\t\t\t\t\t\t\t\t\"'s message: \" +\n\t\t\t\t\t\t\t\tget_message_label_data(message)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if message?.metadata?.title}\n\t\t\t\t\t\t\t\t<Thought\n\t\t\t\t\t\t\t\t\tthought={message}\n\t\t\t\t\t\t\t\t\t{rtl}\n\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t{_components}\n\t\t\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t\t\t{thought_index}\n\t\t\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t\t\t{scroll}\n\t\t\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<MessageContent\n\t\t\t\t\t\t\t\t\t{message}\n\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t{allow_tags}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t{_components}\n\t\t\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t\t\t{thought_index}\n\t\t\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t\t\t{scroll}\n\t\t\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t</div>\n\n\t\t\t\t{#if layout === \"panel\"}\n\t\t\t\t\t<ButtonPanel\n\t\t\t\t\t\t{...button_panel_props}\n\t\t\t\t\t\t{current_feedback}\n\t\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t</div>\n\t</div>\n</div>\n\n{#if layout === \"bubble\"}\n\t<ButtonPanel {...button_panel_props} />\n{/if}\n\n<style>\n\t.message {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tmargin-top: var(--spacing-sm);\n\t}\n\n\t.message.display_consecutive_in_same_bubble {\n\t\tmargin-top: 0;\n\t}\n\n\t/* avatar styles */\n\t.avatar-container {\n\t\tflex-shrink: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\toverflow: hidden;\n\t}\n\n\t.avatar-container :global(img) {\n\t\tobject-fit: cover;\n\t}\n\n\t/* message wrapper */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.component {\n\t\tpadding: 0;\n\t\tborder-radius: var(--radius-md);\n\t\twidth: fit-content;\n\t\toverflow: hidden;\n\t}\n\n\t.component.gallery {\n\t\tborder: none;\n\t}\n\n\t.message-row :not(.avatar-container) :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 300px;\n\t}\n\n\t.file-pil {\n\t\tdisplay: block;\n\t\twidth: fit-content;\n\t\tpadding: var(--spacing-sm) var(--spacing-lg);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t\ttext-decoration: none;\n\t\tmargin: 0;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.file {\n\t\twidth: auto !important;\n\t\tmax-width: fit-content !important;\n\t}\n\n\t@media (max-width: 600px) or (max-width: 480px) {\n\t\t.component {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.message :global(.prose) {\n\t\tfont-size: var(--chatbot-text-size);\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t.panel-full-width {\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t.user {\n\t\tborder-radius: var(--radius-md);\n\t\talign-self: flex-end;\n\t\tborder-bottom-right-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t}\n\n\t.bot {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t\tborder-bottom-left-radius: 0;\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t}\n\n\t.bot:has(.table-wrap) {\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t\tbackground: none;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* Colors */\n\n\t.message-row {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t}\n\n\t/* bubble mode styles */\n\t.bubble {\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.user-row {\n\t\talign-self: flex-end;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble.bot-row {\n\t\talign-self: flex-start;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble .user-row {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.bubble .with_avatar.user-row {\n\t\tmargin-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_avatar.bot-row {\n\t\tmargin-left: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_opposite_avatar.user-row {\n\t\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t/* panel mode styles */\n\t.panel {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t}\n\n\t.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.panel .with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.panel .panel-full-width {\n\t\twidth: 100%;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* message content */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\t.user-row > .avatar-container {\n\t\torder: 2;\n\t}\n\n\t.user-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.panel.user-row > .avatar-container {\n\t\torder: 0;\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-right: var(--spacing-xxl);\n\t\tmargin-left: 0;\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: var(--size-1-5);\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t/* Image preview */\n\t.message :global(.preview) {\n\t\tobject-fit: contain;\n\t\twidth: 95%;\n\t\tmax-height: 93%;\n\t}\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.image-preview :global(svg) {\n\t\tstroke: white;\n\t}\n\t.image-preview-close-button {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tright: 10px;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tfont-size: 1.5em;\n\t\tcursor: pointer;\n\t\theight: 30px;\n\t\twidth: 30px;\n\t\tpadding: 3px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.message > div {\n\t\twidth: 100%;\n\t}\n\t.html {\n\t\tpadding: 0;\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\n\t.panel .bot,\n\t.panel .user {\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\ttextarea {\n\t\tbackground: none;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder: none;\n\t\tdisplay: block;\n\t\tmax-width: 100%;\n\t}\n\t.user textarea {\n\t\tborder-bottom-right-radius: 0;\n\t}\n\t.bot textarea {\n\t\tborder-bottom-left-radius: 0;\n\t}\n\t.user textarea:focus {\n\t\toutline: 2px solid var(--border-color-accent);\n\t}\n\t.bot textarea:focus {\n\t\toutline: 2px solid var(--border-color-primary);\n\t}\n\n\t.panel.user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row,\n\t.panel .bot-row {\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .user :global(*),\n\t.panel .bot :global(*) {\n\t\ttext-align: left;\n\t}\n\n\t.panel .user {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .message {\n\t\tmargin-bottom: var(--spacing-md);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let layout = \"bubble\";\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n</script>\n\n<div class=\"container\">\n\t{#if avatar_images[1] !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_images[1].url} alt=\"bot avatar\" />\n\t\t</div>\n\t{/if}\n\n\t<div\n\t\tclass=\"message bot pending {layout}\"\n\t\tclass:with_avatar={avatar_images[1] !== null}\n\t\tclass:with_opposite_avatar={avatar_images[0] !== null}\n\t\trole=\"status\"\n\t\taria-label=\"Loading response\"\n\t\taria-live=\"polite\"\n\t>\n\t\t<div class=\"message-content\">\n\t\t\t<span class=\"sr-only\">Loading content</span>\n\t\t\t<div class=\"dots\">\n\t\t\t\t<div class=\"dot\" />\n\t\t\t\t<div class=\"dot\" />\n\t\t\t\t<div class=\"dot\" />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t}\n\n\t.bubble.pending {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder-bottom-left-radius: 0;\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\twidth: fit-content;\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.with_opposite_avatar {\n\t\tmargin-right: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t.panel.pending {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t\twidth: 100%;\n\t\tborder: none;\n\t\tbackground: none;\n\t\tbox-shadow: none;\n\t\tborder-radius: 0;\n\t}\n\n\t.panel.with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tmargin-right: var(--spacing-xxl);\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: var(--size-1-5);\n\t}\n\n\t.message-content {\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tmin-height: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.dots {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-xs);\n\t\talign-items: center;\n\t}\n\n\t.dot {\n\t\twidth: var(--size-1-5);\n\t\theight: var(--size-1-5);\n\t\tmargin-right: var(--spacing-xs);\n\t\tborder-radius: 50%;\n\t\tbackground-color: var(--body-text-color);\n\t\topacity: 0.5;\n\t\tanimation: pulse 1.5s infinite;\n\t}\n\n\t.dot:nth-child(2) {\n\t\tanimation-delay: 0.2s;\n\t}\n\n\t.dot:nth-child(3) {\n\t\tanimation-delay: 0.4s;\n\t}\n\n\t@keyframes pulse {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 0.4;\n\t\t\ttransform: scale(1);\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t\ttransform: scale(1.1);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Image } from \"@gradio/image/shared\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport { File, Music, Video } from \"@gradio/icons\";\n\timport type { ExampleMessage } from \"../types\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let examples: ExampleMessage[] | null = null;\n\texport let placeholder: string | null = null;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let root: string;\n\n\tconst dispatch = createEventDispatcher<{\n\t\texample_select: SelectData;\n\t}>();\n\n\tfunction handle_example_select(\n\t\ti: number,\n\t\texample: ExampleMessage | string\n\t): void {\n\t\tconst example_obj =\n\t\t\ttypeof example === \"string\" ? { text: example } : example;\n\t\tdispatch(\"example_select\", {\n\t\t\tindex: i,\n\t\t\tvalue: { text: example_obj.text, files: example_obj.files }\n\t\t});\n\t}\n</script>\n\n<div class=\"placeholder-content\" role=\"complementary\">\n\t{#if placeholder !== null}\n\t\t<div class=\"placeholder\">\n\t\t\t<Markdown message={placeholder} {latex_delimiters} {root} />\n\t\t</div>\n\t{/if}\n\t{#if examples !== null}\n\t\t<div class=\"examples\" role=\"list\">\n\t\t\t{#each examples as example, i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"example\"\n\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\thandle_example_select(\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\ttypeof example === \"string\" ? { text: example } : example\n\t\t\t\t\t\t)}\n\t\t\t\t\taria-label={`Select example ${i + 1}: ${example.display_text || example.text}`}\n\t\t\t\t>\n\t\t\t\t\t<div class=\"example-content\">\n\t\t\t\t\t\t{#if example?.icon?.url}\n\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\tsrc={example.icon.url}\n\t\t\t\t\t\t\t\t\talt=\"Example icon\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if example?.icon?.mime_type === \"text\"}\n\t\t\t\t\t\t\t<div class=\"example-icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t\t<span class=\"text-icon-aa\">Aa</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if example.files !== undefined && example.files.length > 0}\n\t\t\t\t\t\t\t{#if example.files.length > 1}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icons-grid\"\n\t\t\t\t\t\t\t\t\trole=\"group\"\n\t\t\t\t\t\t\t\t\taria-label=\"Example attachments\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#each example.files.slice(0, 4) as file, i}\n\t\t\t\t\t\t\t\t\t\t{#if file.mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={file.orig_name || `Example image ${i + 1}`}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if i === 3 && example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{:else if file.mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if i === 3 && example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\t\t\t\taria-label={`File: ${file.orig_name}`}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if file.mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t\t\t{#if example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t<div class=\"example-icon\">\n\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"file-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\talt={example.files[0].orig_name || \"Example image\"}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\taria-label={`File: ${example.files[0].orig_name}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\taria-label={`File: ${example.files[0].orig_name}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t<div class=\"example-text-content\">\n\t\t\t\t\t\t\t<span class=\"example-text\"\n\t\t\t\t\t\t\t\t>{example.display_text || example.text}</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.placeholder-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100%;\n\t}\n\n\t.placeholder {\n\t\talign-items: center;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t.examples :global(img) {\n\t\tpointer-events: none;\n\t}\n\n\t.examples {\n\t\tmargin: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n\t\tgap: var(--spacing-xl);\n\t\tmax-width: calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%));\n\t}\n\n\t.example {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\tpadding: var(--spacing-xxl);\n\t\tborder: none;\n\t\tborder-radius: var(--radius-lg);\n\t\tbackground-color: var(--block-background-fill);\n\t\tcursor: pointer;\n\t\ttransition: all 150ms ease-in-out;\n\t\twidth: 100%;\n\t\tgap: var(--spacing-sm);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t\ttransform: translateY(0px);\n\t}\n\n\t.example:hover {\n\t\ttransform: translateY(-2px);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.example-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.example-text-content {\n\t\tmargin-top: auto;\n\t\ttext-align: left;\n\t}\n\n\t.example-text {\n\t\tfont-size: var(--text-md);\n\t\ttext-align: left;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.example-icons-grid {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-bottom: var(--spacing-lg);\n\t\twidth: 100%;\n\t}\n\n\t.example-icon {\n\t\tflex-shrink: 0;\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t\tbackground-color: var(--block-background-fill);\n\t\tposition: relative;\n\t}\n\n\t.example-icon :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tcolor: var(--color-text-secondary);\n\t}\n\n\t.text-icon-aa {\n\t\tfont-size: var(--text-sm);\n\t\tfont-weight: var(--weight-semibold);\n\t\tcolor: var(--color-text-secondary);\n\t\tline-height: 1;\n\t}\n\n\t.example-image-container {\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t.example-image-container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\t.image-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-lg);\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.file-overlay {\n\t\tposition: absolute;\n\t\tinset: 0;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-sm);\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-radius: var(--radius-lg);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\tlet copied = false;\n\texport let value: NormalisedMessage[] | null;\n\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tconst copy_conversation = (): void => {\n\t\tif (value) {\n\t\t\tconst conversation_value = value\n\t\t\t\t.map((message) => {\n\t\t\t\t\tif (message.type === \"text\") {\n\t\t\t\t\t\treturn `${message.role}: ${message.content}`;\n\t\t\t\t\t}\n\t\t\t\t\treturn `${message.role}: ${message.content.value.url}`;\n\t\t\t\t})\n\t\t\t\t.join(\"\\n\\n\");\n\n\t\t\tnavigator.clipboard.writeText(conversation_value).catch((err) => {\n\t\t\t\tconsole.error(\"Failed to copy conversation: \", err);\n\t\t\t});\n\t\t}\n\t};\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tcopy_conversation();\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\tIcon={copied ? Check : Copy}\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied conversation\" : \"Copy conversation\"}\n></IconButton>\n", "<script lang=\"ts\">\n\timport {\n\t\tformat_chat_for_sharing,\n\t\ttype UndoRetryData,\n\t\ttype EditData,\n\t\tis_last_bot_message,\n\t\tgroup_messages,\n\t\tload_components,\n\t\tget_components_from_messages\n\t} from \"./utils\";\n\timport type { NormalisedMessage, Option } from \"../types\";\n\timport { copy } from \"@gradio/utils\";\n\timport type { CopyData } from \"@gradio/utils\";\n\timport Message from \"./Message.svelte\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport {\n\t\tcreateEventDispatcher,\n\t\ttype SvelteComponent,\n\t\ttype ComponentType,\n\t\ttick,\n\t\tonMount\n\t} from \"svelte\";\n\n\timport { Trash, Community, ScrollDownArrow } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport type { ExampleMessage } from \"../types\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport Pending from \"./Pending.svelte\";\n\timport { ShareError } from \"@gradio/utils\";\n\timport { Gradio } from \"@gradio/utils\";\n\n\timport Examples from \"./Examples.svelte\";\n\n\texport let value: NormalisedMessage[] | null = [];\n\tlet old_value: NormalisedMessage[] | null = null;\n\n\timport CopyAll from \"./CopyAll.svelte\";\n\n\texport let _fetch: typeof fetch;\n\texport let load_component: Gradio[\"load_component\"];\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\n\tlet _components: Record<string, ComponentType<SvelteComponent>> = {};\n\n\tconst is_browser = typeof window !== \"undefined\";\n\n\tasync function update_components(): Promise<void> {\n\t\t_components = await load_components(\n\t\t\tget_components_from_messages(value),\n\t\t\t_components,\n\t\t\tload_component\n\t\t);\n\t}\n\n\t$: value, update_components();\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let generating = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let feedback_options: string[];\n\texport let feedback_value: (string | null)[] | null = null;\n\texport let editable: \"user\" | \"all\" | null = null;\n\texport let show_share_button = false;\n\texport let show_copy_all_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let i18n: I18nFormatter;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let placeholder: string | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let msg_format: \"tuples\" | \"messages\" = \"tuples\";\n\texport let examples: ExampleMessage[] | null = null;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let like_user_message = false;\n\texport let root: string;\n\texport let allow_tags: string[] | null = null;\n\n\tlet target: HTMLElement | null = null;\n\tlet edit_index: number | null = null;\n\tlet edit_message = \"\";\n\n\tonMount(() => {\n\t\ttarget = document.querySelector(\"div.gradio-container\");\n\t});\n\n\tlet div: HTMLDivElement;\n\n\tlet show_scroll_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t\tedit: EditData;\n\t\tundo: UndoRetryData;\n\t\tretry: UndoRetryData;\n\t\tclear: undefined;\n\t\tshare: any;\n\t\terror: string;\n\t\texample_select: SelectData;\n\t\toption_select: SelectData;\n\t\tcopy: CopyData;\n\t}>();\n\n\tfunction is_at_bottom(): boolean {\n\t\treturn div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t}\n\n\tfunction scroll_to_bottom(): void {\n\t\tif (!div) return;\n\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\tshow_scroll_button = false;\n\t}\n\n\tlet scroll_after_component_load = false;\n\n\tasync function scroll_on_value_update(): Promise<void> {\n\t\tif (!autoscroll) return;\n\n\t\tif (is_at_bottom()) {\n\t\t\t// Child components may be loaded asynchronously,\n\t\t\t// so trigger the scroll again after they load.\n\t\t\tscroll_after_component_load = true;\n\n\t\t\tawait tick(); // Wait for the DOM to update so that the scrollHeight is correct\n\t\t\tscroll_to_bottom();\n\t\t} else {\n\t\t\tshow_scroll_button = true;\n\t\t}\n\t}\n\tonMount(() => {\n\t\tscroll_on_value_update();\n\t});\n\t$: if (value || pending_message || _components) {\n\t\tscroll_on_value_update();\n\t}\n\n\tonMount(() => {\n\t\tfunction handle_scroll(): void {\n\t\t\tif (is_at_bottom()) {\n\t\t\t\tshow_scroll_button = false;\n\t\t\t} else {\n\t\t\t\tscroll_after_component_load = false;\n\t\t\t}\n\t\t}\n\n\t\tdiv?.addEventListener(\"scroll\", handle_scroll);\n\t\treturn () => {\n\t\t\tdiv?.removeEventListener(\"scroll\", handle_scroll);\n\t\t};\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\t$: groupedMessages = value && group_messages(value, msg_format);\n\t$: options = value && get_last_bot_options();\n\n\tfunction handle_action(\n\t\ti: number,\n\t\tmessage: NormalisedMessage,\n\t\tselected: string | null\n\t): void {\n\t\tif (selected === \"undo\" || selected === \"retry\") {\n\t\t\tconst val_ = value as NormalisedMessage[];\n\t\t\t// iterate through messages until we find the last user message\n\t\t\t// the index of this message is where the user needs to edit the chat history\n\t\t\tlet last_index = val_.length - 1;\n\t\t\twhile (val_[last_index].role === \"assistant\") {\n\t\t\t\tlast_index--;\n\t\t\t}\n\t\t\tdispatch(selected, {\n\t\t\t\tindex: val_[last_index].index,\n\t\t\t\tvalue: val_[last_index].content\n\t\t\t});\n\t\t} else if (selected == \"edit\") {\n\t\t\tedit_index = i;\n\t\t\tedit_message = message.content as string;\n\t\t} else if (selected == \"edit_cancel\") {\n\t\t\tedit_index = null;\n\t\t} else if (selected == \"edit_submit\") {\n\t\t\tedit_index = null;\n\t\t\tdispatch(\"edit\", {\n\t\t\t\tindex: message.index,\n\t\t\t\tvalue: edit_message,\n\t\t\t\tprevious_value: message.content as string\n\t\t\t});\n\t\t} else {\n\t\t\tlet feedback =\n\t\t\t\tselected === \"Like\"\n\t\t\t\t\t? true\n\t\t\t\t\t: selected === \"Dislike\"\n\t\t\t\t\t\t? false\n\t\t\t\t\t\t: selected || \"\";\n\t\t\tif (msg_format === \"tuples\") {\n\t\t\t\tdispatch(\"like\", {\n\t\t\t\t\tindex: message.index,\n\t\t\t\t\tvalue: message.content,\n\t\t\t\t\tliked: feedback\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tif (!groupedMessages) return;\n\n\t\t\t\tconst message_group = groupedMessages[i];\n\t\t\t\tconst [first, last] = [\n\t\t\t\t\tmessage_group[0],\n\t\t\t\t\tmessage_group[message_group.length - 1]\n\t\t\t\t];\n\n\t\t\t\tdispatch(\"like\", {\n\t\t\t\t\tindex: first.index as number,\n\t\t\t\t\tvalue: message_group.map((m) => m.content),\n\t\t\t\t\tliked: feedback\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction get_last_bot_options(): Option[] | undefined {\n\t\tif (!value || !groupedMessages || groupedMessages.length === 0)\n\t\t\treturn undefined;\n\t\tconst last_group = groupedMessages[groupedMessages.length - 1];\n\t\tif (last_group[0].role !== \"assistant\") return undefined;\n\t\treturn last_group[last_group.length - 1].options;\n\t}\n</script>\n\n{#if value !== null && value.length > 0}\n\t<IconButtonWrapper>\n\t\t{#if show_share_button}\n\t\t\t<IconButton\n\t\t\t\tIcon={Community}\n\t\t\t\ton:click={async () => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tconst formatted = await format_chat_for_sharing(value);\n\t\t\t\t\t\tdispatch(\"share\", {\n\t\t\t\t\t\t\tdescription: formatted\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\tlet message = e instanceof ShareError ? e.message : \"Share failed.\";\n\t\t\t\t\t\tdispatch(\"error\", message);\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t/>\n\t\t{/if}\n\t\t<IconButton Icon={Trash} on:click={() => dispatch(\"clear\")} label={\"Clear\"}\n\t\t></IconButton>\n\t\t{#if show_copy_all_button}\n\t\t\t<CopyAll {value} />\n\t\t{/if}\n\t</IconButtonWrapper>\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t{#if value !== null && value.length > 0 && groupedMessages !== null}\n\t\t<div class=\"message-wrap\" use:copy>\n\t\t\t{#each groupedMessages as messages, i}\n\t\t\t\t{@const role = messages[0].role === \"user\" ? \"user\" : \"bot\"}\n\t\t\t\t{@const avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const feedback_index = groupedMessages\n\t\t\t\t\t.slice(0, i)\n\t\t\t\t\t.filter((m) => m[0].role === \"assistant\").length}\n\t\t\t\t{@const current_feedback =\n\t\t\t\t\trole === \"bot\" && feedback_value && feedback_value[feedback_index]\n\t\t\t\t\t\t? feedback_value[feedback_index]\n\t\t\t\t\t\t: null}\n\t\t\t\t<Message\n\t\t\t\t\t{messages}\n\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t{opposite_avatar_img}\n\t\t\t\t\t{avatar_img}\n\t\t\t\t\t{role}\n\t\t\t\t\t{layout}\n\t\t\t\t\t{dispatch}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{_fetch}\n\t\t\t\t\t{line_breaks}\n\t\t\t\t\t{theme_mode}\n\t\t\t\t\t{target}\n\t\t\t\t\t{root}\n\t\t\t\t\t{upload}\n\t\t\t\t\t{selectable}\n\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t{render_markdown}\n\t\t\t\t\t{rtl}\n\t\t\t\t\t{i}\n\t\t\t\t\t{value}\n\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t{_components}\n\t\t\t\t\t{generating}\n\t\t\t\t\t{msg_format}\n\t\t\t\t\t{feedback_options}\n\t\t\t\t\t{current_feedback}\n\t\t\t\t\t{allow_tags}\n\t\t\t\t\tshow_like={role === \"user\" ? likeable && like_user_message : likeable}\n\t\t\t\t\tshow_retry={_retryable && is_last_bot_message(messages, value)}\n\t\t\t\t\tshow_undo={_undoable && is_last_bot_message(messages, value)}\n\t\t\t\t\tshow_edit={editable === \"all\" ||\n\t\t\t\t\t\t(editable == \"user\" &&\n\t\t\t\t\t\t\trole === \"user\" &&\n\t\t\t\t\t\t\tmessages.length > 0 &&\n\t\t\t\t\t\t\tmessages[messages.length - 1].type == \"text\")}\n\t\t\t\t\tin_edit_mode={edit_index === i}\n\t\t\t\t\tbind:edit_message\n\t\t\t\t\t{show_copy_button}\n\t\t\t\t\thandle_action={(selected) => handle_action(i, messages[0], selected)}\n\t\t\t\t\tscroll={is_browser ? scroll : () => {}}\n\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t/>\n\t\t\t\t{#if generating && messages[messages.length - 1].role === \"assistant\" && messages[messages.length - 1].metadata?.status === \"done\"}\n\t\t\t\t\t<Pending {layout} {avatar_images} />\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t\t{#if pending_message}\n\t\t\t\t<Pending {layout} {avatar_images} />\n\t\t\t{:else if options}\n\t\t\t\t<div class=\"options\">\n\t\t\t\t\t{#each options as option, index}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"option\"\n\t\t\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\t\t\tdispatch(\"option_select\", {\n\t\t\t\t\t\t\t\t\tindex: index,\n\t\t\t\t\t\t\t\t\tvalue: option.value\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{option.label || option.value}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t{:else}\n\t\t<Examples\n\t\t\t{examples}\n\t\t\t{placeholder}\n\t\t\t{latex_delimiters}\n\t\t\t{root}\n\t\t\ton:example_select={(e) => dispatch(\"example_select\", e.detail)}\n\t\t/>\n\t{/if}\n</div>\n\n{#if show_scroll_button}\n\t<div class=\"scroll-down-button-container\">\n\t\t<IconButton\n\t\t\tIcon={ScrollDownArrow}\n\t\t\tlabel=\"Scroll down\"\n\t\t\tsize=\"large\"\n\t\t\ton:click={scroll_to_bottom}\n\t\t/>\n\t</div>\n{/if}\n\n<style>\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.bubble-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t\theight: 100%;\n\t\tpadding-top: var(--spacing-xxl);\n\t}\n\n\t@media (prefers-color-scheme: dark) {\n\t\t.bubble-wrap {\n\t\t\tbackground: var(--background-fill-secondary);\n\t\t}\n\t}\n\n\t.message-wrap :global(.prose.chatbot.md) {\n\t\topacity: 0.8;\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.message-wrap :global(.message-row .md img) {\n\t\tborder-radius: var(--radius-xl);\n\t\tmargin: var(--size-2);\n\t\twidth: 400px;\n\t\tmax-width: 30vw;\n\t\tmax-height: 30vw;\n\t}\n\n\t/* link styles */\n\t.message-wrap :global(.message a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t/* table styles */\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) table),\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) tr),\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) td),\n\t.message-wrap :global(.bot:not(:has(.table-wrap)) th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap :global(.user table),\n\t.message-wrap :global(.user tr),\n\t.message-wrap :global(.user td),\n\t.message-wrap :global(.user th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t.message-wrap :global(span.katex-display) {\n\t\tmargin-top: 0;\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n\n\t.message-wrap :global(.grid-wrap) {\n\t\tmax-height: 80% !important;\n\t\tmax-width: 600px;\n\t\tobject-fit: contain;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.panel-wrap :global(.message-row:first-child) {\n\t\tpadding-top: calc(var(--spacing-xxl) * 2);\n\t}\n\n\t.scroll-down-button-container {\n\t\tposition: absolute;\n\t\tbottom: 10px;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tz-index: var(--layer-top);\n\t}\n\t.scroll-down-button-container :global(button) {\n\t\tborder-radius: 50%;\n\t\tbox-shadow: var(--shadow-drop);\n\t\ttransition:\n\t\t\tbox-shadow 0.2s ease-in-out,\n\t\t\ttransform 0.2s ease-in-out;\n\t}\n\t.scroll-down-button-container :global(button:hover) {\n\t\tbox-shadow:\n\t\t\tvar(--shadow-drop),\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);\n\t\ttransform: translateY(-2px);\n\t}\n\n\t.options {\n\t\tmargin-left: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n\t\tgap: var(--spacing-xxl);\n\t\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\n\t\tjustify-content: end;\n\t}\n\n\t.option {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: var(--spacing-xl);\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tcursor: pointer;\n\t\ttransition: var(--button-transition);\n\t\tmax-width: var(--size-56);\n\t\twidth: 100%;\n\t\tjustify-content: center;\n\t}\n\n\t.option:hover {\n\t\tbackground-color: var(--color-accent-soft);\n\t\tborder-color: var(--border-color-accent);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseChatBot } from \"./shared/ChatBot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData, CopyData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./shared/ChatBot.svelte\";\n\timport type { UndoRetryData } from \"./shared/utils\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type {\n\t\tMessage,\n\t\tExampleMessage,\n\t\tTupleFormat,\n\t\tNormalisedMessage\n\t} from \"./types\";\n\n\timport { normalise_tuples, normalise_messages } from \"./shared/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: TupleFormat | Message[] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let _selectable = false;\n\texport let likeable = false;\n\texport let feedback_options: string[] = [\"Like\", \"Dislike\"];\n\texport let feedback_value: (string | null)[] | null = null;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = true;\n\texport let show_copy_all_button = false;\n\texport let sanitize_html = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let type: \"tuples\" | \"messages\" = \"tuples\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let group_consecutive_messages = true;\n\texport let allow_tags: string[] | null = null;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t\tclear_status: LoadingStatus;\n\t\texample_select: SelectData;\n\t\toption_select: SelectData;\n\t\tedit: SelectData;\n\t\tretry: UndoRetryData;\n\t\tundo: UndoRetryData;\n\t\tclear: null;\n\t\tcopy: CopyData;\n\t}>;\n\n\tlet _value: NormalisedMessage[] | null = [];\n\n\t$: _value =\n\t\ttype === \"tuples\"\n\t\t\t? normalise_tuples(value as TupleFormat, root)\n\t\t\t: normalise_messages(value as Message[], root);\n\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let like_user_message = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | string | undefined;\n\texport let resizable: boolean;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\texport let editable: \"user\" | \"all\" | null = null;\n\texport let placeholder: string | null = null;\n\texport let examples: ExampleMessage[] | null = null;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let allow_file_downloads = true;\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\t{resizable}\n\t{min_height}\n\t{max_height}\n\tallow_overflow={true}\n\tflex={true}\n\toverflow_behavior=\"auto\"\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={true}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\ti18n={gradio.i18n}\n\t\t\tselectable={_selectable}\n\t\t\t{likeable}\n\t\t\t{feedback_options}\n\t\t\t{feedback_value}\n\t\t\t{show_share_button}\n\t\t\t{show_copy_all_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\tdisplay_consecutive_in_same_bubble={group_consecutive_messages}\n\t\t\t{render_markdown}\n\t\t\t{theme_mode}\n\t\t\t{editable}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\tgenerating={loading_status?.status === \"generating\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\t{like_user_message}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:example_select={(e) => gradio.dispatch(\"example_select\", e.detail)}\n\t\t\ton:option_select={(e) => gradio.dispatch(\"option_select\", e.detail)}\n\t\t\ton:retry={(e) => gradio.dispatch(\"retry\", e.detail)}\n\t\t\ton:undo={(e) => gradio.dispatch(\"undo\", e.detail)}\n\t\t\ton:clear={() => {\n\t\t\t\tvalue = [];\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:copy={(e) => gradio.dispatch(\"copy\", e.detail)}\n\t\t\ton:edit={(e) => {\n\t\t\t\tif (value === null || value.length === 0) return;\n\t\t\t\tif (type === \"messages\") {\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tvalue[e.detail.index].content = e.detail.value;\n\t\t\t\t} else {\n\t\t\t\t\t//@ts-ignore\n\t\t\t\t\tvalue[e.detail.index[0]][e.detail.index[1]] = e.detail.value;\n\t\t\t\t}\n\t\t\t\tvalue = value;\n\t\t\t\tgradio.dispatch(\"edit\", e.detail);\n\t\t\t}}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{line_breaks}\n\t\t\t{autoscroll}\n\t\t\t{layout}\n\t\t\t{placeholder}\n\t\t\t{examples}\n\t\t\t{_retryable}\n\t\t\t{_undoable}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\t_fetch={(...args) => gradio.client.fetch(...args)}\n\t\t\tload_component={gradio.load_component}\n\t\t\tmsg_format={type}\n\t\t\troot={gradio.root}\n\t\t\t{allow_file_downloads}\n\t\t\t{allow_tags}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t:global(.progress-text) {\n\t\tright: auto;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "circle", "path", "path2", "path3", "format_chat_for_sharing", "chat", "url_length_limit", "messages_to_share", "formatted", "format_messages", "first_message", "last_message", "msg", "max_length", "message", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "url", "file_url", "redirect_src_url", "src", "root", "get_component_for_mime_type", "mime_type", "convert_file_message_to_component_message", "_file", "normalise_messages", "messages", "thought_map", "normalized", "id", "title", "parent_id", "parent", "thought", "normalise_tuples", "message_pair", "index", "role", "is_component_message", "is_last_bot_message", "all_messages", "is_bot", "last_index", "group_messages", "msg_format", "groupedMessages", "currentGroup", "currentRole", "load_components", "component_names", "_components", "load_component", "names", "components", "component_name", "variant", "name", "component", "i", "get_components_from_messages", "get_thought_content", "depth", "content", "indent", "child", "all_text", "m", "is_all_text", "ctx", "ThumbDownActive", "ThumbDownDefault", "dirty", "iconbutton_changes", "ThumbUpActive", "ThumbUpDefault", "FlagActive", "Flag", "div1", "div0", "set_style", "button", "set_data", "t", "t_value", "create_if_block", "handle_action", "$$props", "feedback_options", "selected", "toggleSelection", "newSelection", "click_handler", "click_handler_1", "option", "$$invalidate", "extra_feedback", "Check", "Copy", "dispatch", "createEventDispatcher", "copied", "value", "timer", "copy_feedback", "handle_copy", "textArea", "error", "onDestroy", "attr", "div", "div_class_value", "current", "create_if_block_6", "create_if_block_5", "create_if_block_4", "create_if_block_3", "create_if_block_2", "Clear", "Retry", "Undo", "Edit", "if_block", "likeable", "show_retry", "show_undo", "show_edit", "in_edit_mode", "show_copy_button", "position", "avatar", "generating", "current_feedback", "layout", "copy_handler", "e", "click_handler_2", "click_handler_3", "click_handler_4", "message_text", "show_copy", "switch_value", "func_1", "switch_instance_changes", "func", "track", "type", "theme_mode", "props", "i18n", "upload", "_fetch", "allow_file_downloads", "display_icon_button_wrapper_top_corner", "a", "a_href_value", "a_download_value", "div2", "span0", "span1", "t1", "t1_value", "t3", "t3_value", "is_function", "markdown_changes", "latex_delimiters", "sanitize_html", "line_breaks", "render_markdown", "scroll", "display_consecutive_in_same_bubble", "thought_index", "allow_tags", "span", "if_block1", "show_if", "t0", "t0_value", "create_if_block_1", "each_value", "ensure_array_like", "each_blocks", "thought_1_changes", "DropdownCircularArrow", "if_block0", "create_if_block_7", "is_thought_node", "rtl", "thought_node", "toggleExpanded", "expanded", "keydown_handler", "image_changes", "div_aria_label_value", "get_message_label_data", "toggle_class", "textarea", "div2_class_value", "previous_edit_mode", "avatar_img", "opposite_avatar_img", "selectable", "show_like", "edit_message", "messageElements", "last_message_width", "last_message_height", "handle_select", "button_panel_props", "$$value", "div6", "div5", "div4", "avatar_images", "src_url_equal", "video", "video_src_value", "each_value_1", "create_if_block_9", "show_if_1", "div0_aria_label_value", "create_if_block_14", "examples", "placeholder", "handle_example_select", "example", "example_obj", "copy_conversation", "conversation_value", "err", "constants_0", "child_ctx", "constants_1", "constants_2", "constants_3", "constants_4", "Community", "iconbutton", "IconButton", "Trash", "div_1", "message_changes", "ScrollDownArrow", "div_1_class_value", "null_to_empty", "old_value", "is_browser", "update_components", "pending_message", "feedback_value", "editable", "show_share_button", "show_copy_all_button", "autoscroll", "_retryable", "_undoable", "like_user_message", "edit_index", "onMount", "show_scroll_button", "is_at_bottom", "scroll_to_bottom", "scroll_on_value_update", "tick", "handle_scroll", "val_", "feedback", "message_group", "first", "last", "get_last_bot_options", "last_group", "ShareError", "example_select_handler", "dequal", "options", "Cha<PERSON>", "blocklabel_changes", "chatbot_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "_selectable", "group_consecutive_messages", "gradio", "_value", "loading_status", "height", "resizable", "min_height", "max_height", "clear_status_handler", "args", "change_handler"], "mappings": "mxDAAAA,EAgBKC,EAAAC,EAAAC,CAAA,EALJC,EAGCF,EAAAG,CAAA,EACDD,EAAyDF,EAAAI,CAAA,mbCf1DN,EASKC,EAAAC,EAAAC,CAAA,EAFJC,EAA4CF,EAAAK,CAAA,EAC5CH,EAAyBF,EAAAM,CAAA,2pCCR1BR,EAqCKC,EAAAC,EAAAC,CAAA,EA5BJC,EAMOF,EAAAG,CAAA,EACPD,EAMOF,EAAAI,CAAA,EACPF,EAMOF,EAAAO,CAAA,EACPL,EAMOF,EAAAQ,CAAA,kcCpCRV,EAcKC,EAAAC,EAAAC,CAAA,EAPJC,EAMCF,EAAAM,CAAA,gGCGK,MAAMG,GAA0B,MACtCC,EACAC,EAAmB,OACE,CACjB,IAAAC,EAAoB,CAAC,GAAGF,CAAI,EAC5BG,EAAY,MAAMC,GAAgBF,CAAiB,EAEvD,GAAIC,EAAU,OAASF,GAAoBC,EAAkB,OAAS,EAAG,CAClE,MAAAG,EAAgBH,EAAkB,CAAC,EACnCI,EAAeJ,EAAkBA,EAAkB,OAAS,CAAC,EAC/CA,EAAA,CAACG,EAAeC,CAAY,EACpCH,EAAA,MAAMC,GAAgBF,CAAiB,CACpD,CAEA,OAAIC,EAAU,OAASF,GAAoBC,EAAkB,OAAS,IAejDA,EAdOA,EAAkB,IAAKK,GAAQ,CACrD,GAAAA,EAAI,OAAS,OAAQ,CACxB,MAAMC,EACL,KAAK,MAAMP,EAAmBC,EAAkB,MAAM,EAAI,GACvD,GAAAK,EAAI,QAAQ,OAASC,EACjB,MAAA,CACN,GAAGD,EACH,QAASA,EAAI,QAAQ,UAAU,EAAGC,CAAU,EAAI,KAAA,CAGnD,CACO,OAAAD,CAAA,CACP,EAGWJ,EAAA,MAAMC,GAAgBF,CAAiB,GAG7CC,CACR,EAEMC,GAAkB,MAAOJ,IACf,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOS,GAAY,CAC3B,GAAIA,EAAQ,OAAS,SAAiB,MAAA,GACtC,IAAIC,EAAgBD,EAAQ,OAAS,OAAS,KAAO,KACjDE,EAAe,GAEf,GAAAF,EAAQ,OAAS,OAAQ,CAC5B,MAAMG,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGRD,EAAeF,EAAQ,QAEvB,OAAS,CAACI,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKL,EAAQ,OAAO,KAAO,MAAM,CACtD,MAAMO,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,CAAc,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,CACpD,CACD,CAAA,KACM,CACF,GAAA,CAACR,EAAQ,QAAQ,MAAc,MAAA,GAC7B,MAAAU,EACLV,EAAQ,QAAQ,YAAc,QAC3BA,EAAQ,QAAQ,OAAO,MAAM,KAC7BA,EAAQ,QAAQ,MACdW,EAAW,MAAMF,GAAoBC,CAAU,EACjDV,EAAQ,QAAQ,YAAc,QACjCE,EAAe,wBAAwBS,CAAQ,aACrCX,EAAQ,QAAQ,YAAc,QACzBE,EAAAS,EACLX,EAAQ,QAAQ,YAAc,UACxCE,EAAe,aAAaS,CAAQ,OAEtC,CAEO,MAAA,GAAGV,CAAa,KAAKC,CAAY,EAAA,CACxC,CAAA,GAEc,OAAQJ,GAAQA,IAAQ,EAAE,EAAE,KAAK;AAAA,CAAI,EAchDc,GAAmB,CAACC,EAAaC,IACtCD,EAAI,QAAQ,aAAc,QAAQC,CAAI,MAAM,EAE7C,SAASC,GACRC,EACS,CACT,OAAKA,EACDA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACjC,OAJgB,MAKxB,CAEA,SAASC,GACRjB,EACgB,CACV,MAAAkB,EAAQ,MAAM,QAAQlB,EAAQ,IAAI,EAAIA,EAAQ,KAAK,CAAC,EAAIA,EAAQ,KAC/D,MAAA,CACN,UAAWe,GAA4BG,GAAO,SAAS,EACvD,MAAOlB,EAAQ,KACf,SAAUA,EAAQ,SAClB,iBAAkB,CAAC,EACnB,MAAO,CAAC,CAAA,CAEV,CAEgB,SAAAmB,GACfC,EACAN,EAC6B,CAC7B,GAAIM,IAAa,KAAa,OAAAA,EAExB,MAAAC,MAAkB,IAExB,OAAOD,EACL,IAAI,CAACpB,EAAS,IAAM,CACpB,IAAIsB,EACH,OAAOtB,EAAQ,SAAY,SACxB,CACA,KAAMA,EAAQ,KACd,SAAUA,EAAQ,SAClB,QAASY,GAAiBZ,EAAQ,QAASc,CAAI,EAC/C,KAAM,OACN,MAAO,EACP,QAASd,EAAQ,OAAA,EAEjB,SAAUA,EAAQ,QACjB,CACA,QAASiB,GACRjB,EAAQ,OACT,EACA,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,KACd,KAAM,YACN,MAAO,EACP,QAASA,EAAQ,OAEhB,EAAA,CAAE,KAAM,YAAa,GAAGA,CAAQ,EAGtC,KAAM,CAAE,GAAAuB,EAAI,MAAAC,EAAO,UAAAC,CAAc,EAAAzB,EAAQ,UAAY,GACrD,GAAIyB,EAAW,CACd,MAAMC,EAASL,EAAY,IAAI,OAAOI,CAAS,CAAC,EAChD,GAAIC,EAAQ,CACX,MAAMC,EAAU,CAAE,GAAGL,EAAY,SAAU,CAAG,CAAA,EACvC,OAAAI,EAAA,SAAS,KAAKC,CAAO,EACxBJ,GAAMC,GACTH,EAAY,IAAI,OAAOE,CAAE,EAAGI,CAAO,EAE7B,IACR,CACD,CACA,GAAIJ,GAAMC,EAAO,CAChB,MAAMG,EAAU,CAAE,GAAGL,EAAY,SAAU,CAAG,CAAA,EAC9C,OAAAD,EAAY,IAAI,OAAOE,CAAE,EAAGI,CAAO,EAC5BA,CACR,CAEO,OAAAL,CACP,CAAA,EACA,OAAQxB,GAAkCA,IAAQ,IAAI,CACzD,CAEgB,SAAA8B,GACfR,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EAClBA,EAAS,QAAQ,CAACS,EAAc,IACpCA,EAAa,IAAI,CAAC7B,EAAS8B,IAAU,CAC3C,GAAI9B,GAAW,KAAa,OAAA,KACtB,MAAA+B,EAAOD,GAAS,EAAI,OAAS,YAE/B,OAAA,OAAO9B,GAAY,SACf,CACN,KAAA+B,EACA,KAAM,OACN,QAASnB,GAAiBZ,EAASc,CAAI,EACvC,SAAU,CAAE,MAAO,IAAK,EACxB,MAAO,CAAC,EAAGgB,CAAK,CAAA,EAId,SAAU9B,EACN,CACN,QAASiB,GAA0CjB,CAAO,EAC1D,KAAA+B,EACA,KAAM,YACN,MAAO,CAAC,EAAGD,CAAK,CAAA,EAIX,CACN,KAAAC,EACA,QAAS/B,EACT,KAAM,YACN,MAAO,CAAC,EAAG8B,CAAK,CAAA,CACjB,CACA,CACD,EACU,OAAQ9B,GAAYA,GAAW,IAAI,CAC/C,CAEO,SAASgC,GACfhC,EAC8B,CAC9B,OAAOA,EAAQ,OAAS,WACzB,CAEgB,SAAAiC,GACfb,EACAc,EACU,CACV,MAAMC,EAASf,EAASA,EAAS,OAAS,CAAC,EAAE,OAAS,YAChDgB,EAAahB,EAASA,EAAS,OAAS,CAAC,EAAE,MAMjD,OAFC,KAAK,UAAUgB,CAAU,IACzB,KAAK,UAAUF,EAAaA,EAAa,OAAS,CAAC,EAAE,KAAK,GACzCC,CACnB,CAEgB,SAAAE,GACfjB,EACAkB,EACwB,CACxB,MAAMC,EAAyC,CAAA,EAC/C,IAAIC,EAAoC,CAAA,EACpCC,EAAkC,KAEtC,UAAWzC,KAAWoB,GACfpB,EAAQ,OAAS,aAAeA,EAAQ,OAAS,UAGnDA,EAAQ,OAASyC,EACpBD,EAAa,KAAKxC,CAAO,GAErBwC,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAElCA,EAAe,CAACxC,CAAO,EACvByC,EAAczC,EAAQ,OAIpB,OAAAwC,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAG3BD,CACR,CAEsB,eAAAG,GACrBC,EACAC,EACAC,EAC0D,CAC1D,IAAIC,EAAkB,CAAA,EAClBC,EAA+D,CAAA,EAEnD,OAAAJ,EAAA,QAASK,GAAmB,CAC3C,GAAIJ,EAAYI,CAAc,GAAKA,IAAmB,OACrD,OAEK,MAAAC,EAAUD,IAAmB,YAAc,YAAc,OACzD,CAAE,KAAAE,EAAM,UAAAC,CAAA,EAAcN,EAAeG,EAAgBC,CAAO,EAClEH,EAAM,KAAKI,CAAI,EACfH,EAAW,KAAKI,CAAS,CACzB,CACA,GAC4C,MAAM,QAAQ,IAAIJ,CAAU,GACvD,QAAQ,CAACI,EAAWC,IAAM,CAC3CR,EAAYE,EAAMM,CAAC,CAAC,EAAID,EAAU,OAAA,CAClC,EAEMP,CACR,CAEO,SAASS,GACfjC,EACW,CACX,GAAI,CAACA,EAAU,MAAO,GAClB,IAAA2B,MAA8B,IACzB,OAAA3B,EAAA,QAASpB,GAAY,CACzBA,EAAQ,OAAS,aACT+C,EAAA,IAAI/C,EAAQ,QAAQ,SAAS,CACzC,CACA,EACM,MAAM,KAAK+C,CAAU,CAC7B,CAEgB,SAAAO,GAAoBxD,EAAwByD,EAAQ,EAAW,CAC9E,IAAIC,EAAU,GACR,MAAAC,EAAS,KAAK,OAAOF,CAAK,EAE5BzD,EAAI,UAAU,QACN0D,GAAA,GAAGC,CAAM,GAAGF,EAAQ,EAAI,KAAO,EAAE,GAAGzD,EAAI,SAAS,KAAK;AAAA,GAE9D,OAAOA,EAAI,SAAY,WAC1B0D,GAAW,GAAGC,CAAM,KAAK3D,EAAI,OAAO;AAAA,GAErC,MAAM6B,EAAU7B,EACZ,OAAA6B,EAAQ,UAAU,OAAS,IAC9B6B,GAAW7B,EAAQ,SACjB,IAAK+B,GAAUJ,GAAoBI,EAAOH,EAAQ,CAAC,CAAC,EACpD,KAAK,EAAE,GAEHC,CACR,CAEO,SAASG,GAAS3D,EAA8C,CAClE,OAAA,MAAM,QAAQA,CAAO,EACjBA,EACL,IAAK4D,GACDA,EAAE,UAAU,MACRN,GAAoBM,CAAC,EAEtBA,EAAE,OACT,EACA,KAAK;AAAA,CAAI,EAER5D,EAAQ,UAAU,MACdsD,GAAoBtD,CAAO,EAE5BA,EAAQ,OAChB,CAEO,SAAS6D,GACf7D,EACyC,CAEvC,OAAA,MAAM,QAAQA,CAAO,GACrBA,EAAQ,MAAO4D,GAAM,OAAOA,EAAE,SAAY,QAAQ,GAClD,CAAC,MAAM,QAAQ5D,CAAO,GAAK,OAAOA,EAAQ,SAAY,QAEzD,suBC9WArB,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAM,CAAA,qqCCVFR,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAM,CAAA,+zBCVFR,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAM,CAAA,iqCCVFR,EAWKC,EAAAC,EAAAC,CAAA,EAJJC,EAGCF,EAAAM,CAAA,0WCVFR,EASAC,EAAAC,EAAAC,CAAA,EAJEC,EAGCF,EAAAM,CAAA,+UCRHR,EAMAC,EAAAC,EAAAC,CAAA,EADEC,EAA+DF,EAAAM,CAAA,6KCkB3D2E,EAAgB,CAAA,EAAC,SAAS,SAAS,MAUnCA,EAAgB,CAAA,EAAC,SAAS,MAAM,4IAVhCA,EAAgB,CAAA,EAAC,SAAS,SAAS,oHAUnCA,EAAgB,CAAA,EAAC,SAAS,MAAM,qPAR7B,KAAAA,OAAa,UAAYC,GAAkBC,GAC1C,MAAAF,OAAa,UAAY,kBAAoB,UAC7C,MAAAA,OAAa,UACjB,sBACA,sHAJGG,EAAA,IAAAC,EAAA,KAAAJ,OAAa,UAAYC,GAAkBC,IAC1CC,EAAA,IAAAC,EAAA,MAAAJ,OAAa,UAAY,kBAAoB,WAC7CG,EAAA,IAAAC,EAAA,MAAAJ,OAAa,UACjB,sBACA,0KAMG,KAAAA,OAAa,OAASK,GAAgBC,GACrC,MAAAN,EAAa,CAAA,IAAA,OAAS,eAAiB,OACvC,MAAAA,OAAa,OACjB,sBACA,sHAJGG,EAAA,IAAAC,EAAA,KAAAJ,OAAa,OAASK,GAAgBC,IACrCH,EAAA,IAAAC,EAAA,MAAAJ,EAAa,CAAA,IAAA,OAAS,eAAiB,QACvCG,EAAA,IAAAC,EAAA,MAAAJ,OAAa,OACjB,sBACA,yKASG,KAAAA,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAAIO,GAAaC,oBAE5D,MAAAR,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAChD,sBACA,4CAGIA,EAAc,CAAA,CAAA,uBAAnB,OAAIV,GAAA,sOATRzE,EAoBKC,EAAA2F,EAAAzF,CAAA,qBAZJC,EAWKwF,EAAAC,CAAA,8EAjBEP,EAAA,IAAAC,EAAA,KAAAJ,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAAIO,GAAaC,IAE5DL,EAAA,IAAAC,EAAA,MAAAJ,EAAY,CAAA,GAAAA,EAAe,CAAA,EAAA,SAASA,EAAQ,CAAA,CAAA,EAChD,sBACA,sDAGIA,EAAc,CAAA,CAAA,oBAAnB,OAAIV,GAAA,EAAA,mHAAJ,6HAOIU,EAAM,CAAA,EAAA,4HAJSW,GAAAC,EAAA,cAAAZ,OAAaA,EAAM,CAAA,EAAG,OAAS,QAAQ,UAF3DnF,EAOAC,EAAA8F,EAAA5F,CAAA,6DADKgF,EAAM,CAAA,EAAA,KAAAa,GAAAC,EAAAC,CAAA,OAJSJ,GAAAC,EAAA,cAAAZ,OAAaA,EAAM,CAAA,EAAG,OAAS,QAAQ,gDApC1DA,EAAgB,CAAA,EAAC,SAAS,MAAM,GAAKA,EAAgB,CAAA,EAAC,SAAS,SAAS,qBAuBxEA,EAAc,CAAA,EAAC,OAAS,GAACgB,GAAAhB,CAAA,oHAvBzBA,EAAgB,CAAA,EAAC,SAAS,MAAM,GAAKA,EAAgB,CAAA,EAAC,SAAS,SAAS,4GAuBxEA,EAAc,CAAA,EAAC,OAAS,4NApCjB,CAAA,cAAAiB,CAAA,EAAAC,EACA,CAAA,iBAAAC,CAAA,EAAAD,GACA,SAAAE,EAA0B,IAAA,EAAAF,WAK5BG,EAAgBC,EAAA,KACxBF,EAAWA,IAAaE,EAAe,KAAOA,CAAA,EAC9CL,EAAcG,CAAQ,EAYL,MAAAG,EAAA,IAAAF,EAAgB,SAAS,EAUzBG,EAAA,IAAAH,EAAgB,MAAM,QAoBnCA,EAAgBI,CAAM,EACtBR,EAAcG,GAAsB,IAAI,sLAjD7CM,EAAA,EAAGC,EAAiBR,EAAiB,OACnCM,GAAWA,IAAW,QAAUA,IAAW,SAAA,CAAA,odC2CtCzB,EAAM,CAAA,EAAG,iBAAmB,oBAC7BA,EAAM,CAAA,EAAG4B,GAAQC,oBAFb7B,EAAW,CAAA,CAAA,iFACdA,EAAM,CAAA,EAAG,iBAAmB,6BAC7BA,EAAM,CAAA,EAAG4B,GAAQC,uHAnDjBC,EAAWC,SAKbC,EAAS,GACF,CAAA,MAAAC,CAAA,EAAAf,EACPgB,EAEK,SAAAC,GAAA,KACRH,EAAS,EAAA,EACLE,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPF,EAAS,EAAA,GACP,KAGW,eAAAI,GAAA,IACV,cAAe,UAClBN,EAAS,OAAU,CAAA,MAAAG,CAAA,CAAA,QACb,UAAU,UAAU,UAAUA,CAAK,EACzCE,eAEME,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQJ,EAEjBI,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAA,MAGR,SAAS,YAAY,MAAM,EAC3BF,GACQ,OAAAG,EAAA,CACR,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAA,IAKZ,OAAAE,GAAA,IAAA,CACKL,GAAO,aAAaA,CAAK,kQCfC,8EAHNM,EAAAC,EAAA,QAAAC,EAAA,mBAAA1C,SAAWA,EAAM,EAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,gBAAA,UAFvBnF,EA0DKC,EAAA2H,EAAAzH,CAAA,wFAzDoB,CAAA2H,GAAAxC,EAAA,MAAAuC,KAAAA,EAAA,mBAAA1C,SAAWA,EAAM,EAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,0JAiBfA,EAAS,EAAA,GAAA4C,GAAA5C,CAAA,IAMTA,EAAU,CAAA,GAAA6C,GAAA7C,CAAA,IAQVA,EAAS,CAAA,GAAA8C,GAAA9C,CAAA,IAQTA,EAAS,CAAA,GAAA+C,GAAA/C,CAAA,IAQTA,EAAQ,CAAA,GAAAgD,GAAAhD,CAAA,sNA9BRA,EAAS,EAAA,4GAMTA,EAAU,CAAA,yGAQVA,EAAS,CAAA,yGAQTA,EAAS,CAAA,0GAQTA,EAAQ,CAAA,qVAzCN4B,YAEI5B,EAAU,CAAA,+DAIdiD,YAEIjD,EAAU,CAAA,qJANVA,EAAU,CAAA,2CAMVA,EAAU,CAAA,uMAKZA,EAAY,EAAA,CAAA,yGAAZA,EAAY,EAAA,iJAMbkD,0BAGIlD,EAAU,CAAA,4GAAVA,EAAU,CAAA,8JAMdmD,YAEInD,EAAU,CAAA,4GAAVA,EAAU,CAAA,8JAMdoD,YAEIpD,EAAU,CAAA,4GAAVA,EAAU,CAAA,+LAOVA,EAAgB,CAAA,sJAAhBA,EAAgB,CAAA,oKAhDxBA,EAAY,CAAA,EAAA,6TANfqD,GAAArD,OAAaA,EAAU,CAAA,GAAIA,EAAa,CAAA,GAAAA,MAAaA,EAAQ,CAAA,IAAAgB,GAAAhB,CAAA,wEAA7DA,OAAaA,EAAU,CAAA,GAAIA,EAAa,CAAA,GAAAA,MAAaA,EAAQ,CAAA,sMArBtD,CAAA,SAAAsD,CAAA,EAAApC,EACA,CAAA,iBAAAC,CAAA,EAAAD,EACA,CAAA,WAAAqC,CAAA,EAAArC,EACA,CAAA,UAAAsC,CAAA,EAAAtC,EACA,CAAA,UAAAuC,CAAA,EAAAvC,EACA,CAAA,aAAAwC,CAAA,EAAAxC,EACA,CAAA,iBAAAyC,CAAA,EAAAzC,EACA,CAAA,QAAAhF,CAAA,EAAAgF,EACA,CAAA,SAAA0C,CAAA,EAAA1C,EACA,CAAA,OAAA2C,CAAA,EAAA3C,EACA,CAAA,WAAA4C,CAAA,EAAA5C,EACA,CAAA,iBAAA6C,CAAA,EAAA7C,EAEA,CAAA,cAAAD,CAAA,EAAAC,EACA,CAAA,OAAA8C,CAAA,EAAA9C,EACA,CAAA,SAAAY,CAAA,EAAAZ,EAgBS,MAAAK,EAAA,IAAAN,EAAc,aAAa,EAM3BO,EAAA,IAAAP,EAAc,aAAa,EAOhCgD,EAAAC,GAAMpC,EAAS,OAAQoC,EAAE,MAAM,EAOzBC,EAAA,IAAAlD,EAAc,OAAO,EAQrBmD,EAAA,IAAAnD,EAAc,MAAM,EAQpBoD,GAAA,IAAApD,EAAc,MAAM,goBAlDzCS,EAAA,GAAG4C,EAAevE,GAAY7D,CAAO,EAAI2D,GAAS3D,CAAO,EAAI,EAAA,oBAC7DwF,EAAA,GAAG6C,EAAYZ,GAAoBzH,GAAW6D,GAAY7D,CAAO,CAAA,ipDCiF1D,IAAAsI,EAAAxE,KAAWA,EAAI,CAAA,CAAA,sDAET,2CAEO,qBAET,SAAQyE,EAAA,0HANZ,GAAAtE,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,kWAXf,IAAAwE,EAAAxE,KAAWA,EAAI,CAAA,CAAA,sDAET,8CAEUA,EAAoB,CAAA,iLAJpC,GAAAG,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,wOAICA,EAAoB,CAAA,qNAnBpC,IAAAwE,EAAAxE,KAAWA,EAAI,CAAA,CAAA,gCACX,SAAAA,KAAM,eACTA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,aACf,qBACO,2FAIGA,EAAoB,CAAA,iKARpC,GAAAG,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,qLACXG,EAAA,KAAAuE,EAAA,SAAA1E,KAAM,wBACTA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,sIAMLA,EAAoB,CAAA,8KAtBnC,IAAAwE,EAAAxE,KAAWA,EAAI,CAAA,CAAA,sDAET,qBACO,yCAGE,SAAUA,EAAK,CAAA,EAAC,QAAQ,uBACvBA,EAAoB,CAAA,sKAT5CnF,EAaKC,EAAA2H,EAAAzH,CAAA,8BAXG,GAAAmF,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,iPAMA,SAAUA,EAAK,CAAA,EAAC,0CACfA,EAAoB,CAAA,+LAnBrC,IAAAwE,EAAAxE,KAAWA,EAAI,CAAA,CAAA,uEAIN,cAAAA,KAAM,6CAEA,2HANf,GAAAG,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,wPAING,EAAA,KAAAuE,EAAA,cAAA1E,KAAM,oJAtBf,IAAAwE,EAAAxE,KAAWA,EAAI,CAAA,CAAA,sDAET,kCAGC,GACA,YAAAA,KAAM,iBACb,mBAEI,SAAQ2E,EAAA,EACR,SAAA3E,KAAM,SACE,iBAAAA,KAAM,iBACb,UAAAA,KAAM,UACN,UAAAA,KAAM,kIAbX,GAAAG,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,6NAMRG,EAAA,KAAAuE,EAAA,YAAA1E,KAAM,aAITG,EAAA,KAAAuE,EAAA,SAAA1E,KAAM,UACEG,EAAA,KAAAuE,EAAA,iBAAA1E,KAAM,kBACbG,EAAA,KAAAuE,EAAA,UAAA1E,KAAM,WACNG,EAAA,KAAAuE,EAAA,UAAA1E,KAAM,gJA5BX,IAAAwE,EAAAxE,KAAWA,EAAI,CAAA,CAAA,mGAGT,gDAIG,eACF,+BAEC,0HAVR,GAAAG,EAAA,GAAAqE,KAAAA,EAAAxE,KAAWA,EAAI,CAAA,CAAA,GAAA,qeAsErBnF,EAAwBC,EAAA8J,EAAA5J,CAAA,qGAxErB,OAAAgF,OAAS,UAAS,EAebA,OAAS,YAAW,EAkBpBA,OAAS,OAAM,EAWfA,OAAS,QAAO,EAehBA,OAAS,QAAO,EAehBA,OAAS,QAAO,EAWhBA,OAAS,OAAM,2XAzGb,GAAA,CAAA,KAAA6E,CAAA,EAAA3D,EAQA,CAAA,WAAAjC,CAAA,EAAAiC,EACA,CAAA,MAAAe,CAAA,EAAAf,EACA,CAAA,OAAApG,CAAA,EAAAoG,EACA,CAAA,WAAA4D,CAAA,EAAA5D,EACA,CAAA,MAAA6D,CAAA,EAAA7D,EACA,CAAA,KAAA8D,CAAA,EAAA9D,EACA,CAAA,OAAA+D,CAAA,EAAA/D,EACA,CAAA,OAAAgE,CAAA,EAAAhE,EACA,CAAA,qBAAAiE,CAAA,EAAAjE,GACA,uCAAAkE,EAAyC,EAAA,EAAAlE,m5DC8D9ClB,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,QAAM,gBAKPA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAO,EAAA,EAAC,QAAQ,OAAO,MACvB,IAEC,MAAM,GAAG,EACT,IAAG,EACH,YAAW,EAAA,oSAtBPwC,EAAA6C,EAAA,OAAAC,EAAAtF,EAAQ,EAAA,EAAA,QAAQ,MAAM,GAAG,yBAErBwC,EAAA6C,EAAA,WAAAE,EAAA,OAAO,aACd,KACAvF,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,MAAM,oIAdXnF,EAiCKC,EAAA0K,EAAAxK,CAAA,EAhCJC,EAEKuK,EAAA9E,CAAA,qBACLzF,EA4BKuK,EAAA/E,CAAA,EA3BJxF,EAgBGwF,EAAA4E,CAAA,EALFpK,EAIAoK,EAAAI,CAAA,gBAEDxK,EASAwF,EAAAiF,CAAA,6CAdI1F,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,OACvC,QAAM,KAAAa,GAAA8E,EAAAC,CAAA,GAXF,CAAAjD,GAAAxC,EAAA,OAAAmF,KAAAA,EAAAtF,EAAQ,EAAA,EAAA,QAAQ,MAAM,sBAElB,CAAA2C,GAAAxC,EAAA,OAAAoF,KAAAA,EAAA,OAAO,aACd,KACAvF,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,mDAUDA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAO,EAAA,EAAC,QAAQ,OAAO,MACvB,IAEC,MAAM,GAAG,EACT,IAAG,EACH,YAAW,EAAA,KAAAa,GAAAgF,EAAAC,CAAA,yKA3CR9F,EAAO,EAAA,EAAC,QAAQ,WACjBA,EAAO,EAAA,EAAC,QAAQ,qBACVA,EAAW,CAAA,QAChBA,EAAO,EAAA,EAAC,QAAQ,6CACiBA,EAAa,EAAA,EAAG,GACvDA,EAAkC,EAAA,wNAL5BA,EAAO,EAAA,EAAC,QAAQ,wBACjBA,EAAO,EAAA,EAAC,QAAQ,gCACVA,EAAW,CAAA,qBAChBA,EAAO,EAAA,EAAC,QAAQ,0DACiBA,EAAa,EAAA,EAAG,GACvDA,EAAkC,EAAA,mPAnBzB,QAAAA,MAAQ,8IAKR+F,GAAA/F,QAAAA,EAAM,EAAA,EAAA,MAAA,KAAA,SAAA,4EAPjBnF,EAWKC,EAAA2H,EAAAzH,CAAA,0CATMmF,EAAA,QAAA6F,EAAA,QAAAhG,MAAQ,8VAHfA,EAAO,EAAA,EAAC,OAAS,OAAM,EAalBA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,aAAaA,EAAW,CAAA,EAAA,EAgBxEA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,YAAc,OAAM,iWArDlE,GAAA,CAAA,iBAAAiG,CAAA,EAAA/E,EAKA,CAAA,cAAAgF,CAAA,EAAAhF,EACA,CAAA,OAAAgE,CAAA,EAAAhE,EACA,CAAA,KAAA8D,CAAA,EAAA9D,EACA,CAAA,YAAAiF,CAAA,EAAAjF,EACA,CAAA,OAAA+D,CAAA,EAAA/D,EACA,CAAA,OAAApG,CAAA,EAAAoG,EACA,CAAA,KAAAlE,CAAA,EAAAkE,EACA,CAAA,WAAA4D,CAAA,EAAA5D,EACA,CAAA,YAAApC,CAAA,EAAAoC,EACA,CAAA,gBAAAkF,CAAA,EAAAlF,EACA,CAAA,OAAAmF,CAAA,EAAAnF,EACA,CAAA,qBAAAiE,CAAA,EAAAjE,EACA,CAAA,mCAAAoF,CAAA,EAAApF,EACA,CAAA,cAAAqF,CAAA,EAAArF,GACA,WAAAsF,EAA8B,IAAA,EAAAtF,EAE9B,CAAA,QAAAhF,CAAA,EAAAgF,cA6BKmF,mvFCedxL,EAAoCC,EAAA2L,EAAAzL,CAAA,2CAI9BgF,EAAY,EAAA,EAAC,SAAS,KAAG4C,GAAA5C,CAAA,EAGzB0G,EAAA1G,EAAa,EAAA,EAAA,SAAS,WAAa,QAAS+C,GAAA/C,CAAA,+FAJlDnF,EAaMC,EAAA2L,EAAAzL,CAAA,+CAZAgF,EAAY,EAAA,EAAC,SAAS,2DAGtBA,EAAa,EAAA,EAAA,SAAS,WAAa,wHAFtCA,EAAY,EAAA,EAAC,SAAS,IAAG,gEAAzBA,EAAY,EAAA,EAAC,SAAS,IAAG,KAAAa,GAAA,EAAAE,CAAA,oFAGpB4F,GAAA,OAAAA,EAAA,CAAA,CAAA,OAAO,UAAU3G,EAAa,EAAA,EAAA,SAAS,QAAQ,QAE9BA,EAAa,EAAA,EAAA,SAAS,UAAY,GAAG6C,0CAHZ,GAC/C,YAMS,GACX,2KAHc7C,EAAY,EAAA,EAAC,SAAS,SAAW,KAAM,QAClD,CAAA,EAAA,6BACC,IAAE,qDAFQA,EAAY,EAAA,EAAC,SAAS,SAAW,KAAM,QAClD,CAAA,EAAA,KAAAa,GAAA+F,EAAAC,CAAA,wCAH4D,IAAAA,EAAA7G,MAAa,SAAS,SAAS,QAC3F,CAAA,EAAA,6BACC,GAAC,oCAF0DG,EAAA,OAAA0G,KAAAA,EAAA7G,MAAa,SAAS,SAAS,QAC3F,CAAA,EAAA,KAAAa,GAAA+F,EAAAC,CAAA,wCAHsD,IAAAA,EAAA7G,EAAA,EAAA,EACrD,SACA,SAAQ,6BAAC,GAAC,oCAF2CG,EAAA,OAAA0G,KAAAA,EAAA7G,EAAA,EAAA,EACrD,SACA,SAAQ,KAAAa,GAAA+F,EAAAC,CAAA,+EAaH7G,EAAY,EAAA,oRAkBjB,IAAAqD,EAAArD,EAAa,EAAA,EAAA,UAAU,OAAS,GAAC8G,GAAA9G,CAAA,qGApBvCnF,EA6CKC,EAAA2H,EAAAzH,CAAA,gFA3CMgF,EAAY,EAAA,gcAkBjBA,EAAa,EAAA,EAAA,UAAU,OAAS,wUAE5B+G,EAAAC,GAAAhH,MAAa,QAAQ,uBAA1B,OAAIV,GAAA,qKADPzE,EAsBKC,EAAA2H,EAAAzH,CAAA,6EArBG+L,EAAAC,GAAAhH,MAAa,QAAQ,oBAA1B,OAAIV,GAAA,EAAA,2GAAJ,OAAIA,EAAA2H,EAAA,OAAA3H,GAAA,yCAAJ,OAAIA,GAAA,kKAEKU,EAAK,EAAA,sGAOC,cAAAA,KAAgB,yPAPtBA,EAAK,EAAA,+JAOCG,EAAA,KAAA+G,EAAA,cAAAlH,KAAgB,4aA9DjBmH,EAAqB,CAAA,CAAA,mBAG9B,QAAAnH,EAAa,EAAA,EAAA,UAAU,OAAS,8EAMrC,IAAAoH,EAAApH,EAAa,EAAA,EAAA,UAAU,SAAW,WAASqH,GAAA,KAG3CrH,EAAY,EAAA,GAAE,UAAU,KAAOA,EAAY,EAAA,GAAE,UAAU,WAAQgD,GAAAhD,CAAA,IAkBhEA,EAAQ,EAAA,GAAAgB,GAAAhB,CAAA,wLAhCMA,EAAQ,EAAA,EAAG,iBAAmB,cAAc,uDAPnDA,EAAY,EAAA,EAAC,UAAY,IAAMA,EAAY,EAAA,EAAC,UAAY,IAAI,oHALzEnF,EA4FKC,EAAA2F,EAAAzF,CAAA,EA3FJC,EAyCKwF,EAAAC,CAAA,EAhCJzF,EAKMyF,EAAA+F,CAAA,+HAXoBzG,EAAc,EAAA,CAAA,CAAA,oEAQtBA,EAAQ,EAAA,EAAG,iBAAmB,cAAc,aAKpDG,EAAA,QAAA6F,EAAA,QAAAhG,EAAa,EAAA,EAAA,UAAU,OAAS,4HAMrCA,EAAa,EAAA,EAAA,UAAU,SAAW,wDAGlCA,EAAY,EAAA,GAAE,UAAU,KAAOA,EAAY,EAAA,GAAE,UAAU,wFArBjDA,EAAY,EAAA,EAAC,UAAY,IAAMA,EAAY,EAAA,EAAC,UAAY,iEAuC/DA,EAAQ,EAAA,gSA7DJsH,GAAgBtL,EAAA,OACjB,aAAcA,2BAvBX,CAAA,QAAA6B,CAAA,EAAAqD,GACA,IAAAqG,EAAM,EAAA,EAAArG,EACN,CAAA,cAAAgF,CAAA,EAAAhF,EACA,CAAA,iBAAA+E,CAAA,EAAA/E,EAKA,CAAA,gBAAAkF,CAAA,EAAAlF,EACA,CAAA,YAAApC,CAAA,EAAAoC,EACA,CAAA,OAAA+D,CAAA,EAAA/D,EACA,CAAA,cAAAqF,CAAA,EAAArF,EACA,CAAA,OAAApG,CAAA,EAAAoG,EACA,CAAA,KAAAlE,CAAA,EAAAkE,EACA,CAAA,WAAA4D,CAAA,EAAA5D,EACA,CAAA,OAAAgE,CAAA,EAAAhE,EACA,CAAA,OAAAmF,CAAA,EAAAnF,EACA,CAAA,qBAAAiE,CAAA,EAAAjE,EACA,CAAA,mCAAAoF,CAAA,EAAApF,EACA,CAAA,KAAA8D,CAAA,EAAA9D,EACA,CAAA,YAAAiF,CAAA,EAAAjF,EAMPsG,EAMK,SAAAC,GAAA,MACRC,EAAY,CAAAA,CAAA,EAcC,MAAAC,EAAAzD,GAAMA,EAAE,MAAQ,SAAWuD,uuBApBtC/F,EAAA,GAAA8F,EAAA,CACC,GAAA3J,EACH,SAAUyJ,GAAgBzJ,CAAO,EAAIA,EAAQ,SAAA,CAAA,sBAO3C6D,EAAA,GAAAgG,EAAWF,EAAa,UAAU,SAAW,MAAA,4/DCwFb,IAAAxH,MAAY,QAAWA,EAAI,CAAA,EAAA,mGAD7DnF,EAEKC,EAAA2H,EAAAzH,CAAA,sCAD6BmF,EAAA,CAAA,EAAA,IAAAyH,EAAA,IAAA5H,MAAY,qBAAWA,EAAI,CAAA,EAAA,kMAqDnDA,EAAO,EAAA,GAAE,UAAU,MAAK,8LAnBhBA,EAAI,CAAA,CAAA,cAcZA,EAAG,EAAA,EAAG,MAAQ,KAAK,EACZwC,EAAAC,EAAA,aAAAoF,EAAA7H,EACX,CAAA,EAAA,eACA8H,GAAuB9H,EAAO,EAAA,CAAA,CAAA,8BAhBjB+H,EAAAtF,EAAA,SAAAzC,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,CAAA,CAAA,mEAGnCA,EAAU,EAAA,EAAG,UAAY,MAAM,oBAC3BA,EAAG,EAAA,EAAG,QAAU,MAAM,UAPzCnF,EA6DKC,EAAA2H,EAAAzH,CAAA,0PA5DSgF,EAAI,CAAA,CAAA,yBAcZA,EAAG,EAAA,EAAG,MAAQ,uBACP,CAAA2C,GAAAxC,EAAA,CAAA,EAAA,IAAA0H,KAAAA,EAAA7H,EACX,CAAA,EAAA,eACA8H,GAAuB9H,EAAO,EAAA,CAAA,0EAhBjB+H,EAAAtF,EAAA,SAAAzC,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mDACFA,EAAe,CAAA,CAAA,oEAGnCA,EAAU,EAAA,EAAG,UAAY,MAAM,+BAC3BA,EAAG,EAAA,EAAG,QAAU,MAAM,gNAdpBA,EAAkB,EAAA,CAAA,YAAA,uBACjBA,EAAmB,EAAA,CAAA,IAAA,UAHzCnF,EAMCC,EAAAkN,EAAAhN,CAAA,OADYgF,EAAY,CAAA,CAAA,iEAAZA,EAAY,CAAA,CAAA,sCAHJA,EAAkB,EAAA,CAAA,YAAA,wCACjBA,EAAmB,EAAA,CAAA,IAAA,+nCA4B7BA,EAAO,EAAA,uXAAPA,EAAO,EAAA,4nBA6CfA,EAAkB,EAAA,+NAAlBA,EAAkB,EAAA,CAAA,+MA9ElB,OAAAA,EAAgB,EAAA,GAAAA,EAAkB,EAAA,IAAAA,EAAS,CAAA,EAAA,OAAS,GAAKA,EAAQ,EAAA,EAAA,OAAS,OAAM,0BA4EjF,IAAA0G,EAAA1G,OAAW,SAAO8G,GAAA9G,CAAA,6EApFLA,EAAkC,EAAA,EAAU,GAAPA,EAAI,CAAA,GAAK,eAAA,yBACvC,EAAI,mCACMA,EAAe,CAAA,CAAA,kBAChCA,EAAO,EAAA,EAAC,OAAS,WAAW,aACjC9B,GAAqB8B,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,EACtB+H,EAAAtF,EAAA,UAAAzC,MAAgB,CAAC,UAPjCnF,EAmFKC,EAAA2H,EAAAzH,CAAA,sOAlFagF,EAAkC,EAAA,EAAU,GAAPA,EAAI,CAAA,GAAK,8EACvC,EAAI,wDACMA,EAAe,CAAA,CAAA,uCAChCA,EAAO,EAAA,EAAC,OAAS,WAAW,kCACjC9B,GAAqB8B,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,uBACtB+H,EAAAtF,EAAA,UAAAzC,MAAgB,CAAC,EA8E5BA,OAAW,iPAaFA,EAAkB,EAAA,CAAA,2JAAlBA,EAAkB,EAAA,CAAA,CAAA,CAAA,oIAjH9BoH,EAAApH,OAAe,MAAI8C,GAAA9C,CAAA,OAcfA,EAAQ,CAAA,CAAA,uBAAb,OAAIV,GAAA,4DAkGJ,IAAAoH,EAAA1G,OAAW,UAAQgB,GAAAhB,CAAA,wIApGdA,EAAkC,EAAA,EAAGA,EAAI,CAAA,EAAG,EAAE,EAAA,eAAA,gBADtCA,EAAkC,EAAA,CAAA,yDAH5B+H,EAAAtH,EAAA,iBAAAT,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,EAZnCwC,EAAAgD,EAAA,QAAAyC,EAAA,eAAAjI,SAASA,EAAI,CAAA,EAAA,mBAAA,EACd+H,EAAAvC,EAAA,cAAAxF,OAAe,IAAI,EACV+H,EAAAvC,EAAA,uBAAAxF,OAAwB,IAAI,UAHzDnF,EAmHKC,EAAA0K,EAAAxK,CAAA,wBAzGJC,EAwGKuK,EAAA/E,CAAA,EAnGJxF,EAkGKwF,EAAAC,CAAA,iGA5GDV,OAAe,iIAcXA,EAAQ,CAAA,CAAA,oBAAb,OAAIV,GAAA,EAAA,2GAAJ,OAAIA,EAAA2H,EAAA,OAAA3H,GAAA,yCAFCU,EAAkC,EAAA,EAAGA,EAAI,CAAA,EAAG,EAAE,EAAA,qEADtCA,EAAkC,EAAA,CAAA,iDAH5B+H,EAAAtH,EAAA,iBAAAT,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,GAZnC,CAAA2C,GAAAxC,EAAA,CAAA,EAAA,IAAA8H,KAAAA,EAAA,eAAAjI,SAASA,EAAI,CAAA,EAAA,qDACd+H,EAAAvC,EAAA,cAAAxF,OAAe,IAAI,iBACV+H,EAAAvC,EAAA,uBAAAxF,OAAwB,IAAI,EAkHpDA,OAAW,sJAlGX,OAAIV,GAAA,6JA7FJ4I,GAAqB,YAkBhBJ,GAAuB5L,EAAA,CAC3B,OAAAA,EAAQ,OAAS,OACbA,EAAQ,QAEfA,EAAQ,OAAS,aACjBA,EAAQ,QAAQ,YAAc,OAE1B,MAAM,QAAQA,EAAQ,QAAQ,KAAK,EACJ,2BAAAA,EAAQ,QAAQ,MAAM,CAAC,EAAE,WAAW,MAAM,GAAG,EAAE,IAAA,CAAA,8BAGtDA,EAAQ,QAAQ,OAAO,WAAW,MAAM,GAAG,EAAE,IACvE,CAAA,IAAAA,EAAQ,QAAQ,OAAO,WAAa,2BAGTA,EAAQ,QAAQ,WAAa,SAAS,sBA3E1D,GAAA,CAAA,MAAA+F,CAAA,EAAAf,EACA,CAAA,WAAAiH,CAAA,EAAAjH,GACA,oBAAAkH,EAAuC,IAAA,EAAAlH,GACvC,KAAAjD,EAAO,MAAA,EAAAiD,EACP,CAAA,SAAA5D,EAAA,EAAA,EAAA4D,EACA,CAAA,OAAA8C,CAAA,EAAA9C,EACA,CAAA,gBAAAkF,CAAA,EAAAlF,EACA,CAAA,iBAAA+E,CAAA,EAAA/E,EAKA,CAAA,cAAAgF,CAAA,EAAAhF,EACA,CAAA,WAAAmH,CAAA,EAAAnH,EACA,CAAA,OAAAgE,CAAA,EAAAhE,EACA,CAAA,IAAAqG,CAAA,EAAArG,EACA,CAAA,SAAAY,CAAA,EAAAZ,EACA,CAAA,KAAA8D,CAAA,EAAA9D,EACA,CAAA,YAAAiF,CAAA,EAAAjF,EACA,CAAA,OAAA+D,CAAA,EAAA/D,EACA,CAAA,OAAApG,CAAA,EAAAoG,EACA,CAAA,KAAAlE,CAAA,EAAAkE,EACA,CAAA,WAAA4D,CAAA,EAAA5D,EACA,CAAA,YAAApC,CAAA,EAAAoC,EACA,CAAA,EAAA5B,CAAA,EAAA4B,EACA,CAAA,iBAAAyC,CAAA,EAAAzC,EACA,CAAA,WAAA4C,EAAA,EAAA5C,EACA,CAAA,iBAAAC,CAAA,EAAAD,EACA,CAAA,UAAAoH,EAAA,EAAApH,EACA,CAAA,UAAAuC,EAAA,EAAAvC,EACA,CAAA,WAAAqC,EAAA,EAAArC,EACA,CAAA,UAAAsC,EAAA,EAAAtC,EACA,CAAA,WAAA1C,CAAA,EAAA0C,EACA,CAAA,cAAAD,EAAA,EAAAC,EACA,CAAA,OAAAmF,EAAA,EAAAnF,EACA,CAAA,qBAAAiE,EAAA,EAAAjE,EACA,CAAA,aAAAwC,EAAA,EAAAxC,EACA,CAAA,aAAAqH,EAAA,EAAArH,EACA,CAAA,mCAAAoF,EAAA,EAAApF,GACA,iBAAA6C,GAAkC,IAAA,EAAA7C,GAClC,WAAAsF,GAA8B,IAAA,EAAAtF,EACrCsH,GAAA,CAAA,EAEAC,GAAqB,EACrBC,GAAsB,EASjB,SAAAC,GAAcrJ,EAAWpD,GAAA,CACjC4F,EAAS,SAAA,CACR,MAAO5F,GAAQ,MACf,MAAOA,GAAQ,UAwCb,IAAA0M,iBAwDcL,GAAY,KAAA,2DAYbC,GAAgBjC,EAAa,EAAAsC,yBACxBF,GAAcrJ,EAAGpD,CAAO,OAC3BgI,KAAC,CACTA,GAAE,MAAQ,SACbyE,GAAcrJ,EAAGpD,CAAO,GAyDjB+H,GAAAC,GAAMpC,EAAS,OAAQoC,EAAE,MAAM,0gDAnLvCR,IAAiB,CAAAwE,KACvBxG,EAAA,GAAA+G,GACCD,GAAgBA,GAAgB,OAAS,CAAC,GAAG,WAAA,EAC9C9G,EAAA,GAAAgH,GACCF,GAAgBA,GAAgB,OAAS,CAAC,GAAG,YAAA,8CA+C5C9G,EAAA,GAAAkH,GAAA,CACF,cAAA3H,GACA,SAAUqH,GACV,iBAAAnH,EACA,WAAAoC,GACA,UAAAC,GACA,UAAAC,GACA,aAAAC,GACA,WAAAI,GACA,iBAAAH,EACA,QAASnF,IAAe,SAAWlB,EAAS,CAAC,EAAIA,EACjD,SAAUW,IAAS,OAAS,QAAU,OACtC,OAAQkK,EACR,OAAAnE,EACA,SAAAlC,EACA,iBAAAiC,wiICjHkC/D,EAAa,CAAA,EAAC,CAAC,EAAE,gHADnDnF,EAEKC,EAAA2H,EAAAzH,CAAA,kDAD6BgF,EAAa,CAAA,EAAC,CAAC,EAAE,sIAF/CA,EAAa,CAAA,EAAC,CAAC,IAAM,MAAIgB,GAAAhB,CAAA,uWAODA,EAAM,CAAA,EAAA,iBAAA,wGACfA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,6BAChBA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,kDAVvDnF,EAwBKC,EAAAgO,EAAA9N,CAAA,wBAjBJC,EAgBK6N,EAAAC,CAAA,EARJ9N,EAOK8N,EAAAC,CAAA,iBArBDhJ,EAAa,CAAA,EAAC,CAAC,IAAM,yIAOGA,EAAM,CAAA,EAAA,iEACfA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,wCAChBA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,uFAd3C,OAAAgE,EAAS,QAAA,EAAA9C,EACT,CAAA,cAAA+H,EAAA,CAAqD,KAAM,IAAI,CAAA,EAAA/H,miBCiCrDlB,EAAW,CAAA,uHAD/BnF,EAEKC,EAAA2H,EAAAzH,CAAA,sDADegF,EAAW,CAAA,iLAKvBA,EAAQ,CAAA,CAAA,uBAAb,OAAIV,GAAA,wLADPzE,EAoIKC,EAAA2H,EAAAzH,CAAA,8EAnIGgF,EAAQ,CAAA,CAAA,oBAAb,OAAIV,GAAA,EAAA,2GAAJ,OAAIA,EAAA2H,EAAA,OAAA3H,GAAA,yCAAJ,OAAIA,GAAA,0LAwBG,iDAAAU,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,iBAkEnBA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,sBAQ5CA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,sBAQ5CA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,2eAtFtDnF,EAEKC,EAAA2H,EAAAzH,CAAA,yGAPEgF,EAAO,CAAA,EAAC,KAAK,wHAHpBnF,EAMKC,EAAA2H,EAAAzH,CAAA,kDAHEgF,EAAO,CAAA,EAAC,KAAK,+NAqGGwC,EAAAC,EAAA,aAAAoF,EAAA,SAAA7H,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,EAAA,UAFhDnF,EAKKC,EAAA2H,EAAAzH,CAAA,4BAHiB,CAAA2H,GAAAxC,EAAA,GAAA0H,KAAAA,EAAA,SAAA7H,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,0OAP1BwC,EAAAC,EAAA,aAAAoF,EAAA,SAAA7H,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,EAAA,UAFhDnF,EAKKC,EAAA2H,EAAAzH,CAAA,4BAHiB,CAAA2H,GAAAxC,EAAA,GAAA0H,KAAAA,EAAA,SAAA7H,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,yMAPzCkJ,GAAAC,EAAA,IAAAC,EAAApJ,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,GAAG,GAAAwC,EAAA2G,EAAA,MAAAC,CAAA,yFAH3BvO,EAMKC,EAAA2H,EAAAzH,CAAA,EALJC,EAICwH,EAAA0G,CAAA,UAFKhJ,EAAA,GAAA,CAAA+I,GAAAC,EAAA,IAAAC,EAAApJ,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,GAAG,+GARpB,IAAAA,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,IACjB,IAAAA,KAAQ,MAAM,CAAC,EAAE,WAAa,iHAJrCnF,EAMKC,EAAA2H,EAAAzH,CAAA,sCAHEmF,EAAA,IAAAyH,EAAA,IAAA5H,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,KACjBG,EAAA,IAAAyH,EAAA,IAAA5H,KAAQ,MAAM,CAAC,EAAE,WAAa,0IAjE7BqJ,EAAArC,GAAAhH,KAAQ,MAAM,MAAM,EAAG,CAAC,CAAA,uBAA7B,OAAIV,GAAA,4DAgDD,IAAA+D,EAAArD,EAAQ,CAAA,EAAA,MAAM,OAAS,GAAC6C,GAAA7C,CAAA,gMArD9BnF,EAgEKC,EAAA2H,EAAAzH,CAAA,8FA3DGqO,EAAArC,GAAAhH,KAAQ,MAAM,MAAM,EAAG,CAAC,CAAA,oBAA7B,OAAIV,GAAA,EAAA,wGAAJ,OAAIA,EAAA2H,EAAA,OAAA3H,GAAA,WAgDDU,EAAQ,CAAA,EAAA,MAAM,OAAS,yFAhD1B,OAAIV,GAAA,6MAwCEqH,GAAA,OAAAA,EAAA,CAAA,CAAA3G,EAAK,EAAA,EAAA,WAAW,SAAS,OAAO,yGAFhBwC,EAAAC,EAAA,aAAAoF,EAAA,SAAA7H,MAAK,SAAS,EAAA,UAFpCnF,EASKC,EAAA2H,EAAAzH,CAAA,iJAPiB,CAAA2H,GAAAxC,EAAA,GAAA0H,KAAAA,EAAA,SAAA7H,MAAK,SAAS,qHAb9BA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,GAACsJ,GAAAtJ,CAAA,iFAHlCkJ,GAAAC,EAAA,IAAAC,EAAApJ,MAAK,GAAG,GAAAwC,EAAA2G,EAAA,MAAAC,CAAA,yFAHfvO,EAeKC,EAAA2H,EAAAzH,CAAA,EAdJC,EAICwH,EAAA0G,CAAA,gCAFKhJ,EAAA,GAAA,CAAA+I,GAAAC,EAAA,IAAAC,EAAApJ,MAAK,GAAG,gBAGTA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,+JApBjC,IAAAA,MAAK,IACL,IAAAA,EAAK,EAAA,EAAA,WAA8B,iBAAAA,KAAI,CAAC,YAEzCA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,GAACqH,GAAArH,CAAA,qHANzCnF,EAeKC,EAAA2H,EAAAzH,CAAA,4DAZEmF,EAAA,IAAAyH,EAAA,IAAA5H,MAAK,KACLG,EAAA,IAAAyH,EAAA,IAAA5H,EAAK,EAAA,EAAA,WAA8B,iBAAAA,KAAI,CAAC,cAEzCA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,ggBAuBnC4F,EAAA5F,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,iCAD3B,GACE,yEAFcwC,EAAAC,EAAA,aAAAoF,EAAA,GAAA7H,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,UAHxCnF,EAMKC,EAAA2H,EAAAzH,CAAA,wBADFmF,EAAA,GAAAyF,KAAAA,EAAA5F,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAAa,GAAA8E,EAAAC,CAAA,EAFXzF,EAAA,GAAA0H,KAAAA,EAAA,GAAA7H,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,2EAfrC4F,EAAA5F,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,iCAD3B,GACE,yEAFcwC,EAAAC,EAAA,aAAAoF,EAAA,GAAA7H,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,UAHxCnF,EAMKC,EAAA2H,EAAAzH,CAAA,wBADFmF,EAAA,GAAAyF,KAAAA,EAAA5F,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAAa,GAAA8E,EAAAC,CAAA,EAFXzF,EAAA,GAAA0H,KAAAA,EAAA,GAAA7H,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,8JAXtC2G,GAAA,OAAAA,EAAA,CAAA,CAAA3G,EAAK,EAAA,EAAA,WAAW,SAAS,OAAO,QAiB3BuJ,GAAA,OAAAA,EAAA,CAAA,CAAAvJ,EAAK,EAAA,EAAA,WAAW,SAAS,OAAO,0UAqCtC4F,EAAA5F,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,4CAD3B,GACE,wEAFcwC,EAAA9B,EAAA,aAAA8I,EAAA,GAAAxJ,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,oDAJzCnF,EAQKC,EAAA2F,EAAAzF,CAAA,EAPJC,EAMKwF,EAAAC,CAAA,wBADFP,EAAA,GAAAyF,KAAAA,EAAA5F,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAAa,GAAA8E,EAAAC,CAAA,EAFXzF,EAAA,GAAAqJ,KAAAA,EAAA,GAAAxJ,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,qFA0CzC4F,GAAA5F,EAAQ,CAAA,EAAA,cAAgBA,KAAQ,MAAI,8DAlHnCA,EAAO,CAAA,GAAE,MAAM,IAAG,EAQbA,EAAS,CAAA,GAAA,MAAM,YAAc,OAAM,EAInCA,EAAO,CAAA,EAAC,QAAU,QAAaA,EAAO,CAAA,EAAC,MAAM,OAAS,EAAC,+WAfpCA,EAAC,CAAA,EAAG,CAAC,KAAKA,KAAQ,cAAgBA,EAAO,CAAA,EAAC,IAAI,EAAA,UAP7EnF,EAgIQC,EAAA8F,EAAA5F,CAAA,EAvHPC,EAsHK2F,EAAAH,CAAA,4BALJxF,EAIKwF,EAAAC,CAAA,EAHJzF,EAEAyF,EAAA+F,CAAA,mNADG,CAAA9D,GAAAxC,EAAA,IAAAyF,KAAAA,GAAA5F,EAAQ,CAAA,EAAA,cAAgBA,KAAQ,MAAI,KAAAa,GAAA8E,EAAAC,CAAA,qCArHX5F,EAAC,CAAA,EAAG,CAAC,KAAKA,KAAQ,cAAgBA,EAAO,CAAA,EAAC,IAAI,8HAf3EoH,EAAApH,OAAgB,MAAIyJ,GAAAzJ,CAAA,EAKpB0G,EAAA1G,OAAa,MAAIgB,GAAAhB,CAAA,qIANvBnF,EA6IKC,EAAA2H,EAAAzH,CAAA,sDA5ICgF,OAAgB,iGAKhBA,OAAa,4MAhCP,SAAA0J,EAAoC,IAAA,EAAAxI,GACpC,YAAAyI,EAA6B,IAAA,EAAAzI,EAC7B,CAAA,iBAAA+E,CAAA,EAAA/E,EAKA,CAAA,KAAAlE,CAAA,EAAAkE,QAELY,EAAWC,KAIR,SAAA6H,EACRtK,EACAuK,EAAA,CAEM,MAAAC,EAAA,OACED,GAAY,UAAa,KAAMA,CAAA,EAAYA,EACnD/H,EAAS,iBAAA,CACR,MAAOxC,EACP,MAAA,CAAS,KAAMwK,EAAY,KAAM,MAAOA,EAAY,SAiBjD,MAAAvI,EAAA,CAAAjC,EAAAuK,IAAAD,EACCtK,EACO,OAAAuK,GAAY,SAAa,CAAA,KAAMA,CAAO,EAAKA,CAAA,6rBCAlD7J,EAAM,CAAA,EAAG4B,GAAQC,SAEhB7B,EAAM,CAAA,EAAG,sBAAwB,qCAD9BA,EAAW,CAAA,CAAA,gFADfA,EAAM,CAAA,EAAG4B,GAAQC,kBAEhB7B,EAAM,CAAA,EAAG,sBAAwB,sIA7CpCgC,EAAS,GACF,CAAA,MAAAC,CAAA,EAAAf,EAEPgB,EAEK,SAAAC,GAAA,KACRH,EAAS,EAAA,EACLE,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPF,EAAS,EAAA,GACP,KAGE,MAAA+H,EAAA,IAAA,CACD,GAAA9H,EAAA,CACG,MAAA+H,EAAqB/H,EACzB,IAAK/F,GACDA,EAAQ,OAAS,UACVA,EAAQ,IAAI,KAAKA,EAAQ,OAAO,GAEjC,GAAAA,EAAQ,IAAI,KAAKA,EAAQ,QAAQ,MAAM,GAAG,EAEpD,EAAA,KAAK;AAAA;AAAA,CAAM,EAEb,UAAU,UAAU,UAAU8N,CAAkB,EAAE,MAAOC,GAAA,CACxD,QAAQ,MAAM,gCAAiCA,CAAG,MAKtC,eAAA7H,GAAA,CACV,cAAe,YAClB2H,IACA5H,KAIF,OAAAI,GAAA,IAAA,CACKL,GAAO,aAAaA,CAAK,8TCiPZ,MAAAgI,EAAAC,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,OAAS,OAAS,cACjC,MAAAC,EAAAD,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC5B,MAAAE,EAAAF,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC1C,MAAAG,EAAAH,EAAA,EAAA,EACvB,MAAM,EAAGA,EAAC,EAAA,CAAA,EACV,OAAQrK,GAAMA,EAAE,CAAC,EAAE,OAAS,WAAW,EAAE,eAE1C,MAAAyK,EAAAJ,QAAS,OAASA,EAAkB,EAAA,GAAAA,MAAeA,EAAc,EAAA,CAAA,EAC9DA,MAAeA,EAAc,EAAA,CAAA,EAC7B,6WA3CEK,EAAS,CAAA,CAAA,maAFZxK,EAAiB,EAAA,GAAAqH,GAAArH,CAAA,EAkBJyK,EAAA,IAAAC,GAAA,CAAA,MAAA,CAAA,KAAAC,SAAiD,OAAO,CAAA,CAAA,6BAErE3K,EAAoB,EAAA,GAAA4C,GAAA5C,CAAA,mJApBpBA,EAAiB,EAAA,+GAoBjBA,EAAoB,EAAA,qtBAejBA,EAAe,EAAA,CAAA,uBAApB,OAAIV,GAAA,kGA2DDU,EAAe,CAAA,EAAA,EAEVA,EAAO,EAAA,EAAA,6JA9DlBnF,EA8EKC,EAAA8P,EAAA5P,CAAA,6JA7EGgF,EAAe,EAAA,CAAA,oBAApB,OAAIV,GAAA,EAAA,wGAAJ,OAAIA,EAAA2H,EAAA,OAAA3H,GAAA,gMAAJ,OAAIA,GAAA,2+BAuCO,UAAAU,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,EACzD,WAAAA,EAAc,EAAA,GAAA7B,GAAoB6B,MAAUA,EAAK,CAAA,CAAA,EAClD,UAAAA,EAAa,EAAA,GAAA7B,GAAoB6B,MAAUA,EAAK,CAAA,CAAA,YAChDA,EAAQ,EAAA,IAAK,OACtBA,EAAQ,EAAA,GAAI,QACZA,EAAI,EAAA,IAAK,QACTA,EAAQ,EAAA,EAAC,OAAS,GAClBA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,MAAQ,OAC1B,aAAAA,QAAeA,EAAC,EAAA,yCAItB,OAAAA,MAAa,OAAMyE,uJAIvBzE,EAAU,CAAA,GAAIA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,OAAS,aAAeA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,UAAU,SAAW,QAAM8C,GAAA9C,CAAA,q8BAhBtHG,EAAA,CAAA,EAAA,UAAAA,EAAA,CAAA,EAAA,MAAA0K,EAAA,UAAA7K,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,GACzDG,EAAA,CAAA,EAAA,UAAAA,EAAA,CAAA,EAAA,MAAA0K,EAAA,WAAA7K,EAAc,EAAA,GAAA7B,GAAoB6B,MAAUA,EAAK,CAAA,CAAA,GAClDG,EAAA,CAAA,EAAA,UAAAA,EAAA,CAAA,EAAA,MAAA0K,EAAA,UAAA7K,EAAa,EAAA,GAAA7B,GAAoB6B,MAAUA,EAAK,CAAA,CAAA,oCAChDA,EAAQ,EAAA,IAAK,OACtBA,EAAQ,EAAA,GAAI,QACZA,EAAI,EAAA,IAAK,QACTA,EAAQ,EAAA,EAAC,OAAS,GAClBA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,MAAQ,QAC1BG,EAAA,CAAA,EAAA,IAAA0K,EAAA,aAAA7K,QAAeA,EAAC,EAAA,8KAQ1BA,EAAU,CAAA,GAAIA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,OAAS,aAAeA,EAAQ,EAAA,EAACA,EAAQ,EAAA,EAAC,OAAS,CAAC,EAAE,UAAU,SAAW,wQAQpHA,EAAO,EAAA,CAAA,uBAAZ,OAAI,GAAA,6HADPnF,EAaKC,EAAA8P,EAAA5P,CAAA,8EAZGgF,EAAO,EAAA,CAAA,oBAAZ,OAAIV,GAAA,EAAA,mHAAJ,4WASCuH,GAAA7G,EAAO,EAAA,EAAA,OAASA,MAAO,OAAK,oIAR9BnF,EASQC,EAAA8F,EAAA5F,CAAA,wDADNmF,EAAA,CAAA,EAAA,KAAA0G,KAAAA,GAAA7G,EAAO,EAAA,EAAA,OAASA,MAAO,OAAK,KAAAa,GAAA+F,EAAAC,CAAA,iFAoB3BiE,qDAGI9K,EAAgB,EAAA,CAAA,qGAL5BnF,EAOKC,EAAA8P,EAAA5P,CAAA,gJAtIDoM,EAAApH,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,GAAC6C,GAAA7C,CAAA,8CAmCjCA,EAAK,CAAA,IAAK,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAKA,EAAe,EAAA,IAAK,KAAI,gCA2F/DA,EAAkB,EAAA,GAAAgB,GAAAhB,CAAA,kEAjGfwC,EAAAoI,EAAA,QAAAG,EAAAC,GAAAhL,QAAW,SAAW,cAAgB,YAAY,EAAA,gBAAA,oHAD1DnF,EAgGKC,EAAA8P,EAAA5P,CAAA,qEA5HAgF,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,gPA6B9B,CAAA2C,GAAAxC,EAAA,CAAA,EAAA,SAAA4K,KAAAA,EAAAC,GAAAhL,QAAW,SAAW,cAAgB,YAAY,EAAA,mCAiGrDA,EAAkB,EAAA,2RAjVX,CAAA,MAAAiC,EAAA,EAAA,EAAAf,EACP+J,EAAwC,KAIjC,CAAA,OAAA/F,CAAA,EAAAhE,EACA,CAAA,eAAAnC,CAAA,EAAAmC,EACA,CAAA,qBAAAiE,CAAA,EAAAjE,EACA,CAAA,mCAAAoF,CAAA,EAAApF,EAEPpC,EAAA,CAAA,EAEE,MAAAoM,EAAA,OAAoB,OAAW,IAEtB,eAAAC,GAAA,CACdzJ,EAAA,GAAA5C,EAAA,MAAoBF,GACnBW,GAA6B0C,CAAK,EAClCnD,EACAC,CAAA,CAAA,EAMS,GAAA,CAAA,iBAAAkH,CAAA,EAAA/E,GAKA,gBAAAkK,EAAkB,EAAA,EAAAlK,GAClB,WAAA4C,EAAa,EAAA,EAAA5C,GACb,WAAAmH,EAAa,EAAA,EAAAnH,GACb,SAAAoC,EAAW,EAAA,EAAApC,EACX,CAAA,iBAAAC,CAAA,EAAAD,GACA,eAAAmK,EAA2C,IAAA,EAAAnK,GAC3C,SAAAoK,EAAkC,IAAA,EAAApK,GAClC,kBAAAqK,EAAoB,EAAA,EAAArK,GACpB,qBAAAsK,EAAuB,EAAA,EAAAtK,GACvB,IAAAqG,EAAM,EAAA,EAAArG,GACN,iBAAAyC,GAAmB,EAAA,EAAAzC,EACnB,CAAA,cAAA+H,EAAA,CAAqD,KAAM,IAAI,CAAA,EAAA/H,GAC/D,cAAAgF,GAAgB,EAAA,EAAAhF,GAChB,gBAAAkF,GAAkB,EAAA,EAAAlF,GAClB,YAAAiF,GAAc,EAAA,EAAAjF,GACd,WAAAuK,GAAa,EAAA,EAAAvK,EACb,CAAA,WAAA4D,CAAA,EAAA5D,EACA,CAAA,KAAA8D,EAAA,EAAA9D,GACA,OAAA8C,GAA6B,QAAA,EAAA9C,GAC7B,YAAAyI,GAA6B,IAAA,EAAAzI,EAC7B,CAAA,OAAA+D,EAAA,EAAA/D,GACA,WAAA1C,GAAoC,QAAA,EAAA0C,GACpC,SAAAwI,GAAoC,IAAA,EAAAxI,GACpC,WAAAwK,GAAa,EAAA,EAAAxK,GACb,UAAAyK,GAAY,EAAA,EAAAzK,GACZ,kBAAA0K,GAAoB,EAAA,EAAA1K,EACpB,CAAA,KAAAlE,EAAA,EAAAkE,GACA,WAAAsF,GAA8B,IAAA,EAAAtF,EAErCpG,GAA6B,KAC7B+Q,GAA4B,KAC5BtD,GAAe,GAEnBuD,GAAA,IAAA,MACChR,GAAS,SAAS,cAAc,sBAAsB,CAAA,IAGnD,IAAA2H,EAEAsJ,GAAqB,SAEnBjK,EAAWC,KAeR,SAAAiK,IAAA,CACD,OAAAvJ,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,IAG5D,SAAAwJ,GAAA,CACHxJ,IACLA,EAAI,SAAS,EAAGA,EAAI,YAAY,OAChCsJ,GAAqB,EAAA,GAKP,eAAAG,IAAA,CACTT,KAEDO,GAAA,GAKG,MAAAG,GAAA,EACNF,UAEAF,GAAqB,EAAA,GAGvBD,GAAA,IAAA,CACCI,OAMDJ,GAAA,IAAA,CACU,SAAAM,GAAA,CACJJ,GAAA,QACHD,GAAqB,EAAA,EAMvB,OAAAtJ,GAAK,iBAAiB,SAAU2J,CAAa,OAE5C3J,GAAK,oBAAoB,SAAU2J,CAAa,KAazC,SAAAnL,GACR3B,EACApD,GACAkF,GAAA,CAEI,GAAAA,KAAa,QAAUA,KAAa,QAAA,OACjCiL,GAAOpK,EAGT,IAAA3D,GAAa+N,GAAK,OAAS,OACxBA,GAAK/N,EAAU,EAAE,OAAS,aAChCA,KAEDwD,EAASV,GAAA,CACR,MAAOiL,GAAK/N,EAAU,EAAE,MACxB,MAAO+N,GAAK/N,EAAU,EAAE,kBAEf8C,IAAY,YACtByK,GAAavM,CAAA,EACboC,EAAA,GAAA6G,GAAerM,GAAQ,OAAA,UACbkF,IAAY,mBACtByK,GAAa,IAAA,UACHzK,IAAY,mBACtByK,GAAa,IAAA,EACb/J,EAAS,OAAA,CACR,MAAO5F,GAAQ,MACf,MAAOqM,GACP,eAAgBrM,GAAQ,eAGrB,IAAAoQ,GACHlL,KAAa,OACV,GACAA,KAAa,UACZ,GACAA,IAAY,MACb5C,KAAe,SAClBsD,EAAS,OAAA,CACR,MAAO5F,GAAQ,MACf,MAAOA,GAAQ,QACf,MAAOoQ,UAGH,GAAA,CAAA7N,EAAA,OAEC,MAAA8N,GAAgB9N,EAAgBa,CAAC,GAChCkN,GAAOC,EAAI,GACjBF,GAAc,CAAC,EACfA,GAAcA,GAAc,OAAS,CAAC,CAAA,EAGvCzK,EAAS,OAAA,CACR,MAAO0K,GAAM,MACb,MAAOD,GAAc,IAAKzM,IAAMA,GAAE,OAAO,EACzC,MAAOwM,OAMF,SAAAI,IAAA,KACHzK,GAAU,CAAAxD,GAAmBA,EAAgB,SAAW,EACrD,aACFkO,EAAalO,EAAgBA,EAAgB,OAAS,CAAC,KACzDkO,EAAW,CAAC,EAAE,OAAS,mBACpBA,EAAWA,EAAW,OAAS,CAAC,EAAE,qCAY/B/Q,EAAS,MAASJ,GAAwByG,CAAK,EACrDH,EAAS,QACR,CAAA,YAAalG,CAAA,CAAA,QAENsI,EAAC,CACT,QAAQ,MAAMA,CAAC,MACXhI,GAAUgI,aAAa0I,GAAa1I,EAAE,QAAU,gBACpDpC,EAAS,QAAS5F,EAAO,IAKYsF,GAAA,IAAAM,EAAS,OAAO,WAmEtCV,KAAaH,GAAc3B,EAAGhC,GAAS,CAAC,EAAG8D,EAAQ,+BAGzD,MAAA6C,GAAAC,GAAMpC,EAAS,OAAQoC,EAAE,MAAM,aActCpC,EAAS,gBACD,CAAA,MAAA9D,EACP,MAAOyD,GAAO,KAAA,CAAA,EAeAoL,EAAA3I,GAAMpC,EAAS,iBAAkBoC,EAAE,MAAM,6CA3FpDzB,EAAGoG,4+CA3NJsC,EAAA,qCA4FHlJ,GAASmJ,GAAmBtM,IAClCoN,2CAmBKY,GAAO7K,EAAOgJ,CAAS,SAC3BA,EAAYhJ,CAAA,EACZH,EAAS,QAAQ,4BAGnBJ,EAAA,GAAGjD,EAAkBwD,GAAS1D,GAAe0D,CAAiB,CAAA,mBAC9DP,EAAA,GAAGqL,EAAU9K,GAASyK,GAAA,CAAA,i5HCnER,WAAA1M,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,wNALS,WAAAA,MAAO,YACbG,EAAA,CAAA,EAAA,WAAA,CAAA,KAAAH,MAAO,IAAI,sBACbA,EAAc,EAAA,CAAA,kCACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,8KAQIgN,SACC,GACA,MAAAhN,MAAS,0GAATG,EAAA,CAAA,EAAA,KAAA8M,EAAA,MAAAjN,MAAS,oIAjBdA,EAAc,EAAA,GAAA8G,GAAA9G,CAAA,IAYbA,EAAU,CAAA,GAAAgB,GAAAhB,CAAA,0BASR,KAAAA,MAAO,gBACDA,EAAW,CAAA,qHAMhBA,EAAM,EAAA,4DAEuBA,EAA0B,EAAA,wEAI7CA,EAAc,EAAA,GAAE,SAAW,qBAChCA,EAAc,EAAA,GAAE,SAAW,0PAyCvB,eAAAA,MAAO,0BACXA,EAAI,EAAA,EACV,KAAAA,MAAO,gcAnEfnF,EAuEKC,EAAA2H,EAAAzH,CAAA,iDAlFAgF,EAAc,EAAA,qHAYbA,EAAU,CAAA,8GASRG,EAAA,CAAA,EAAA,YAAA+M,EAAA,KAAAlN,MAAO,8BACDA,EAAW,CAAA,8MAMhBA,EAAM,EAAA,kGAEuBA,EAA0B,EAAA,yIAI7CA,EAAc,EAAA,GAAE,SAAW,0CAChCA,EAAc,EAAA,GAAE,SAAW,6eAyCvBG,EAAA,CAAA,EAAA,YAAA+M,EAAA,eAAAlN,MAAO,2CACXA,EAAI,EAAA,GACVG,EAAA,CAAA,EAAA,YAAA+M,EAAA,KAAAlN,MAAO,oUAzFN,2GAOO,QACV,oiBAjFK,QAAAmN,EAAU,EAAA,EAAAjM,EACV,CAAA,aAAAkM,EAAA,EAAA,EAAAlM,GACA,QAAAmM,EAAU,EAAA,EAAAnM,EACV,CAAA,MAAAe,EAAA,EAAA,EAAAf,GACA,MAAAoM,EAAuB,IAAA,EAAApM,GACvB,UAAAqM,EAAgC,MAAA,EAAArM,EAChC,CAAA,MAAAsM,CAAA,EAAAtM,GACA,WAAAuM,EAAa,EAAA,EAAAvM,EACb,CAAA,KAAAlE,CAAA,EAAAkE,GACA,YAAAwM,EAAc,EAAA,EAAAxM,GACd,SAAAoC,EAAW,EAAA,EAAApC,EACX,CAAA,iBAAAC,EAAA,CAA8B,OAAQ,SAAS,CAAA,EAAAD,GAC/C,eAAAmK,EAA2C,IAAA,EAAAnK,GAC3C,kBAAAqK,EAAoB,EAAA,EAAArK,GACpB,IAAAqG,EAAM,EAAA,EAAArG,GACN,iBAAAyC,EAAmB,EAAA,EAAAzC,GACnB,qBAAAsK,EAAuB,EAAA,EAAAtK,GACvB,cAAAgF,EAAgB,EAAA,EAAAhF,GAChB,OAAA8C,EAA6B,QAAA,EAAA9C,GAC7B,KAAA2D,EAA8B,QAAA,EAAA3D,GAC9B,gBAAAkF,EAAkB,EAAA,EAAAlF,GAClB,YAAAiF,EAAc,EAAA,EAAAjF,GACd,WAAAuK,GAAa,EAAA,EAAAvK,GACb,WAAAwK,EAAa,EAAA,EAAAxK,GACb,UAAAyK,GAAY,EAAA,EAAAzK,GACZ,2BAAAyM,GAA6B,EAAA,EAAAzM,GAC7B,WAAAsF,GAA8B,IAAA,EAAAtF,EAC9B,CAAA,iBAAA+E,EAAA,EAAA/E,EAKA,CAAA,OAAA0M,CAAA,EAAA1M,EAgBP2M,GAAA,CAAA,EAOO,CAAA,cAAA5E,GAAA,CAAqD,KAAM,IAAI,CAAA,EAAA/H,GAC/D,kBAAA0K,GAAoB,EAAA,EAAA1K,GACpB,eAAA4M,GAA4C,MAAA,EAAA5M,EAC5C,CAAA,OAAA6M,EAAA,EAAA7M,EACA,CAAA,UAAA8M,EAAA,EAAA9M,EACA,CAAA,WAAA+M,EAAA,EAAA/M,EACA,CAAA,WAAAgN,EAAA,EAAAhN,GACA,SAAAoK,GAAkC,IAAA,EAAApK,GAClC,YAAAyI,GAA6B,IAAA,EAAAzI,GAC7B,SAAAwI,GAAoC,IAAA,EAAAxI,EACpC,CAAA,WAAA4D,EAAA,EAAA5D,GACA,qBAAAiE,GAAuB,EAAA,EAAAjE,EA0BT,MAAAiN,GAAA,IAAAP,EAAO,SAAS,eAAgBE,EAAc,EAkEzDnJ,EAAA,IAAAyJ,IAASR,EAAO,OAAO,UAAUQ,CAAI,EACrC3J,GAAA,IAAA2J,IAASR,EAAO,OAAO,SAASQ,CAAI,EApC/BC,EAAA,IAAAT,EAAO,SAAS,SAAU3L,CAAK,KACpCiC,GAAM0J,EAAO,SAAS,SAAU1J,EAAE,MAAM,IAC1CA,GAAM0J,EAAO,SAAS,OAAQ1J,EAAE,MAAM,KACrCA,GAAM0J,EAAO,SAAS,QAAS1J,EAAE,MAAM,KACvCA,GAAM0J,EAAO,SAAS,QAAS1J,EAAE,MAAM,KAC9BA,GAAM0J,EAAO,SAAS,iBAAkB1J,EAAE,MAAM,KACjDA,GAAM0J,EAAO,SAAS,gBAAiB1J,EAAE,MAAM,KACvDA,GAAM0J,EAAO,SAAS,QAAS1J,EAAE,MAAM,KACxCA,GAAM0J,EAAO,SAAS,OAAQ1J,EAAE,MAAM,cAE/CjC,EAAK,CAAA,CAAA,EACL2L,EAAO,SAAS,OAAO,MAEd1J,GAAM0J,EAAO,SAAS,OAAQ1J,EAAE,MAAM,KACtCA,GAAC,CACNjC,IAAU,MAAQA,EAAM,SAAW,IACnC4C,IAAS,WAEZnD,EAAA,EAAAO,EAAMiC,EAAE,OAAO,KAAK,EAAE,QAAUA,EAAE,OAAO,MAAKjC,CAAA,MAG9CA,EAAMiC,EAAE,OAAO,MAAM,CAAC,CAAG,EAAAA,EAAE,OAAO,MAAM,CAAC,GAAKA,EAAE,OAAO,MAAKjC,CAAA,SAG7D2L,EAAO,SAAS,OAAQ1J,EAAE,MAAM,stDAjGnCxC,EAAA,GAAGmM,GACFhJ,IAAS,SACN/G,GAAiBmE,EAAsBjF,CAAI,EAC3CK,GAAmB4E,EAAoBjF,CAAI,CAAA"}