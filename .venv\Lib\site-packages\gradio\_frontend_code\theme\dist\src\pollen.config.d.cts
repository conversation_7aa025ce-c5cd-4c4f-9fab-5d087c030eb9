declare const _exports: {
    output?: string | {
        css?: string;
        json?: string;
    };
    selector?: string;
    modules: {
        color: {
            [key: string]: string | number;
        };
        grid: {
            [key: string]: string | number;
        };
        radius: {
            [key: string]: string | number;
        };
        blur: {
            [key: string]: string | number;
        };
        layer: {
            [key: string]: string | number;
        };
        shadow: {
            [key: string]: string | number;
        };
        ease: {
            [key: string]: string | number;
        };
        easing: {
            [key: string]: string | number;
        };
        elevation: {
            [key: string]: string | number;
        };
        size: {
            [key: string]: string | number;
        };
        width: {
            [key: string]: string | number;
        };
        ratio: {
            [key: string]: string | number;
        };
        scale: {
            [key: string]: string | number;
        };
        scaleFluid: {
            [key: string]: string | number;
        };
        font: {
            [key: string]: string | number;
        };
        weight: {
            [key: string]: string | number;
        };
        line: {
            [key: string]: string | number;
        };
        letter: {
            [key: string]: string | number;
        };
        prose: {
            [key: string]: string | number;
        };
    } & {
        [rule: string]: {
            [key: string]: string | number;
        };
    };
    media?: {
        [query: string]: {
            color: {
                [key: string]: string | number;
            };
            grid: {
                [key: string]: string | number;
            };
            radius: {
                [key: string]: string | number;
            };
            blur: {
                [key: string]: string | number;
            };
            layer: {
                [key: string]: string | number;
            };
            shadow: {
                [key: string]: string | number;
            };
            ease: {
                [key: string]: string | number;
            };
            easing: {
                [key: string]: string | number;
            };
            elevation: {
                [key: string]: string | number;
            };
            size: {
                [key: string]: string | number;
            };
            width: {
                [key: string]: string | number;
            };
            ratio: {
                [key: string]: string | number;
            };
            scale: {
                [key: string]: string | number;
            };
            scaleFluid: {
                [key: string]: string | number;
            };
            font: {
                [key: string]: string | number;
            };
            weight: {
                [key: string]: string | number;
            };
            line: {
                [key: string]: string | number;
            };
            letter: {
                [key: string]: string | number;
            };
            prose: {
                [key: string]: string | number;
            };
        } & {
            [rule: string]: {
                [key: string]: string | number;
            };
        };
    };
    supports?: {
        [query: string]: {
            color: {
                [key: string]: string | number;
            };
            grid: {
                [key: string]: string | number;
            };
            radius: {
                [key: string]: string | number;
            };
            blur: {
                [key: string]: string | number;
            };
            layer: {
                [key: string]: string | number;
            };
            shadow: {
                [key: string]: string | number;
            };
            ease: {
                [key: string]: string | number;
            };
            easing: {
                [key: string]: string | number;
            };
            elevation: {
                [key: string]: string | number;
            };
            size: {
                [key: string]: string | number;
            };
            width: {
                [key: string]: string | number;
            };
            ratio: {
                [key: string]: string | number;
            };
            scale: {
                [key: string]: string | number;
            };
            scaleFluid: {
                [key: string]: string | number;
            };
            font: {
                [key: string]: string | number;
            };
            weight: {
                [key: string]: string | number;
            };
            line: {
                [key: string]: string | number;
            };
            letter: {
                [key: string]: string | number;
            };
            prose: {
                [key: string]: string | number;
            };
        } & {
            [rule: string]: {
                [key: string]: string | number;
            };
        };
    };
};
export = _exports;
