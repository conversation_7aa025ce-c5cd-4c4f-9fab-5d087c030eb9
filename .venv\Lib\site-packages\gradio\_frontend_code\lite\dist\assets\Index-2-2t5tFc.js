import{a as P,i as Q,s as W,f,B as X,c as S,m as A,k as C,t as j,n as q,K as Z,Y as x,S as y,b as M,y as R,z as r,A as T,d as z,C as U,R as V,M as k,a0 as p,a6 as $,aA as Y,l as D,V as ee,N as te,w as se,x as ie}from"../lite.js";import{B as ae}from"./BlockTitle-DvFB_De3.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";function le(i){let e;return{c(){e=se(i[2])},m(s,l){z(s,e,l)},p(s,l){l&4&&ie(e,s[2])},d(s){s&&D(e)}}}function ne(i){let e,s,l,n,_,m,o,h,d;const g=[{autoscroll:i[1].autoscroll},{i18n:i[1].i18n},i[13]];let b={};for(let a=0;a<g.length;a+=1)b=x(b,g[a]);return e=new y({props:b}),e.$on("clear_status",i[20]),n=new ae({props:{root:i[15],show_label:i[10],info:i[3],$$slots:{default:[le]},$$scope:{ctx:i}}}),{c(){S(e.$$.fragment),s=M(),l=R("label"),S(n.$$.fragment),_=M(),m=R("input"),r(m,"aria-label",i[2]),r(m,"type","number"),r(m,"min",i[11]),r(m,"max",i[12]),r(m,"step",i[14]),m.disabled=i[16],r(m,"class","svelte-7ha85a"),r(l,"class","block svelte-7ha85a"),T(l,"container",i[7])},m(a,u){A(e,a,u),z(a,s,u),z(a,l,u),A(n,l,null),U(l,_),U(l,m),V(m,i[0]),o=!0,h||(d=[k(m,"input",i[21]),k(m,"keypress",i[17]),k(m,"blur",i[22]),k(m,"focus",i[23])],h=!0)},p(a,u){const w=u&8194?p(g,[u&2&&{autoscroll:a[1].autoscroll},u&2&&{i18n:a[1].i18n},u&8192&&$(a[13])]):{};e.$set(w);const c={};u&32768&&(c.root=a[15]),u&1024&&(c.show_label=a[10]),u&8&&(c.info=a[3]),u&33554436&&(c.$$scope={dirty:u,ctx:a}),n.$set(c),(!o||u&4)&&r(m,"aria-label",a[2]),(!o||u&2048)&&r(m,"min",a[11]),(!o||u&4096)&&r(m,"max",a[12]),(!o||u&16384)&&r(m,"step",a[14]),(!o||u&65536)&&(m.disabled=a[16]),u&1&&Y(m.value)!==a[0]&&V(m,a[0]),(!o||u&128)&&T(l,"container",a[7])},i(a){o||(C(e.$$.fragment,a),C(n.$$.fragment,a),o=!0)},o(a){j(e.$$.fragment,a),j(n.$$.fragment,a),o=!1},d(a){a&&(D(s),D(l)),q(e,a),q(n),h=!1,ee(d)}}}function ue(i){let e,s;return e=new X({props:{visible:i[6],elem_id:i[4],elem_classes:i[5],padding:i[7],allow_overflow:!1,scale:i[8],min_width:i[9],$$slots:{default:[ne]},$$scope:{ctx:i}}}),{c(){S(e.$$.fragment)},m(l,n){A(e,l,n),s=!0},p(l,[n]){const _={};n&64&&(_.visible=l[6]),n&16&&(_.elem_id=l[4]),n&32&&(_.elem_classes=l[5]),n&128&&(_.padding=l[7]),n&256&&(_.scale=l[8]),n&512&&(_.min_width=l[9]),n&33684623&&(_.$$scope={dirty:n,ctx:l}),e.$set(_)},i(l){s||(C(e.$$.fragment,l),s=!0)},o(l){j(e.$$.fragment,l),s=!1},d(l){q(e,l)}}}function me(i,e,s){let l,{gradio:n}=e,{label:_=n.i18n("number.number")}=e,{info:m=void 0}=e,{elem_id:o=""}=e,{elem_classes:h=[]}=e,{visible:d=!0}=e,{container:g=!0}=e,{scale:b=null}=e,{min_width:a=void 0}=e,{value:u=0}=e,{show_label:w}=e,{minimum:c=void 0}=e,{maximum:E=void 0}=e,{loading_status:B}=e,{value_is_output:v=!1}=e,{step:I=null}=e,{interactive:N}=e,{root:K}=e;function F(){!isNaN(u)&&u!==null&&(n.dispatch("change"),v||n.dispatch("input"))}Z(()=>{s(18,v=!1)});async function G(t){await te(),t.key==="Enter"&&(t.preventDefault(),n.dispatch("submit"))}const H=()=>n.dispatch("clear_status",B);function J(){u=Y(this.value),s(0,u)}const L=()=>n.dispatch("blur"),O=()=>n.dispatch("focus");return i.$$set=t=>{"gradio"in t&&s(1,n=t.gradio),"label"in t&&s(2,_=t.label),"info"in t&&s(3,m=t.info),"elem_id"in t&&s(4,o=t.elem_id),"elem_classes"in t&&s(5,h=t.elem_classes),"visible"in t&&s(6,d=t.visible),"container"in t&&s(7,g=t.container),"scale"in t&&s(8,b=t.scale),"min_width"in t&&s(9,a=t.min_width),"value"in t&&s(0,u=t.value),"show_label"in t&&s(10,w=t.show_label),"minimum"in t&&s(11,c=t.minimum),"maximum"in t&&s(12,E=t.maximum),"loading_status"in t&&s(13,B=t.loading_status),"value_is_output"in t&&s(18,v=t.value_is_output),"step"in t&&s(14,I=t.step),"interactive"in t&&s(19,N=t.interactive),"root"in t&&s(15,K=t.root)},i.$$.update=()=>{i.$$.dirty&1&&F(),i.$$.dirty&524288&&s(16,l=!N)},[u,n,_,m,o,h,d,g,b,a,w,c,E,B,I,K,l,G,v,N,H,J,L,O]}class ce extends P{constructor(e){super(),Q(this,e,me,ue,W,{gradio:1,label:2,info:3,elem_id:4,elem_classes:5,visible:6,container:7,scale:8,min_width:9,value:0,show_label:10,minimum:11,maximum:12,loading_status:13,value_is_output:18,step:14,interactive:19,root:15})}get gradio(){return this.$$.ctx[1]}set gradio(e){this.$$set({gradio:e}),f()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),f()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),f()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),f()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),f()}get minimum(){return this.$$.ctx[11]}set minimum(e){this.$$set({minimum:e}),f()}get maximum(){return this.$$.ctx[12]}set maximum(e){this.$$set({maximum:e}),f()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),f()}get value_is_output(){return this.$$.ctx[18]}set value_is_output(e){this.$$set({value_is_output:e}),f()}get step(){return this.$$.ctx[14]}set step(e){this.$$set({step:e}),f()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),f()}get root(){return this.$$.ctx[15]}set root(e){this.$$set({root:e}),f()}}export{ce as default};
//# sourceMappingURL=Index-2-2t5tFc.js.map
