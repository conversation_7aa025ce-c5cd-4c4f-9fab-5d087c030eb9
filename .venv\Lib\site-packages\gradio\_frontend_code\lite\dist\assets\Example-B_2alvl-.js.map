{"version": 3, "file": "Example-B_2alvl-.js", "sources": ["../../../dataframe/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: (string | number)[][] | string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\texport let index: number;\n\n\tlet hovered = false;\n\tlet loaded = Array.isArray(value);\n\tlet is_empty = loaded && (value.length === 0 || value[0].length === 0);\n</script>\n\n{#if loaded}\n\t<!-- TODO: fix-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tclass:table={type === \"table\"}\n\t\tclass:gallery={type === \"gallery\"}\n\t\tclass:selected\n\t\ton:mouseenter={() => (hovered = true)}\n\t\ton:mouseleave={() => (hovered = false)}\n\t>\n\t\t{#if typeof value === \"string\"}\n\t\t\t{value}\n\t\t{:else if is_empty}\n\t\t\t<table class=\"\">\n\t\t\t\t<tr>\n\t\t\t\t\t<td>Empty</td>\n\t\t\t\t</tr>\n\t\t\t</table>\n\t\t{:else}\n\t\t\t<table class=\"\">\n\t\t\t\t{#each value.slice(0, 3) as row, i}\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t{#each row.slice(0, 3) as cell, j}\n\t\t\t\t\t\t\t<td>{cell}</td>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t{#if row.length > 3}\n\t\t\t\t\t\t\t<td>…</td>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</tr>\n\t\t\t\t{/each}\n\t\t\t\t{#if value.length > 3}\n\t\t\t\t\t<div\n\t\t\t\t\t\tclass=\"overlay\"\n\t\t\t\t\t\tclass:odd={index % 2 != 0}\n\t\t\t\t\t\tclass:even={index % 2 == 0}\n\t\t\t\t\t\tclass:button={type === \"gallery\"}\n\t\t\t\t\t/>\n\t\t\t\t{/if}\n\t\t\t</table>\n\t\t{/if}\n\t</div>\n{/if}\n\n<style>\n\ttable {\n\t\tposition: relative;\n\t\tborder-collapse: collapse;\n\t}\n\n\ttd {\n\t\tborder: 1px solid var(--table-border-color);\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-sm);\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.selected td {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.table {\n\t\tdisplay: inline-block;\n\t\tmargin: 0 auto;\n\t}\n\n\t.gallery td:first-child {\n\t\tborder-left: none;\n\t}\n\n\t.gallery tr:first-child td {\n\t\tborder-top: none;\n\t}\n\n\t.gallery td:last-child {\n\t\tborder-right: none;\n\t}\n\n\t.gallery tr:last-child td {\n\t\tborder-bottom: none;\n\t}\n\n\t.overlay {\n\t\t--gradient-to: transparent;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(to bottom, transparent, var(--gradient-to));\n\t\twidth: var(--size-full);\n\t\theight: 50%;\n\t}\n\n\t/* i dont know what i've done here but it is what it is */\n\t.odd {\n\t\t--gradient-to: var(--table-even-background-fill);\n\t}\n\n\t.even {\n\t\t--gradient-to: var(--table-odd-background-fill);\n\t}\n\n\t.button {\n\t\t--gradient-to: var(--background-fill-primary);\n\t}\n</style>\n"], "names": ["ctx", "create_if_block_1", "create_if_block_2", "toggle_class", "div", "insert", "target", "anchor", "each_value", "ensure_array_like", "i", "create_if_block_3", "table", "td", "set_data", "t", "t_value", "each_value_1", "create_if_block_4", "tr", "create_if_block", "if_block", "dirty", "value", "$$props", "type", "selected", "index", "hovered", "loaded", "is_empty", "mouseenter_handler", "$$invalidate", "mouseleave_handler"], "mappings": "sUAqBc,OAAA,OAAAA,MAAU,SAAQC,EAEpBD,EAAQ,CAAA,EAAAE,+EARLC,EAAAC,EAAA,QAAAJ,OAAS,OAAO,EACdG,EAAAC,EAAA,UAAAJ,OAAS,SAAS,+BAFlCK,EAqCKC,EAAAF,EAAAG,CAAA,uJApCSJ,EAAAC,EAAA,QAAAJ,OAAS,OAAO,OACdG,EAAAC,EAAA,UAAAJ,OAAS,SAAS,kFAexBQ,EAAAC,EAAAT,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAArB,OAAIU,GAAA,yBAUDV,EAAK,CAAA,EAAC,OAAS,GAACW,EAAAX,CAAA,oHAXtBK,EAmBOC,EAAAM,EAAAL,CAAA,yFAlBCC,EAAAC,EAAAT,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAArB,OAAI,GAAA,EAAA,8GAAJ,OAUGA,EAAK,CAAA,EAAC,OAAS,yOAjBrBK,EAIOC,EAAAM,EAAAL,CAAA,yDANNP,EAAK,CAAA,CAAA,oCAALA,EAAK,CAAA,CAAA,wCAYGA,EAAI,EAAA,EAAA,sEAATK,EAAcC,EAAAO,EAAAN,CAAA,6BAATP,EAAI,EAAA,EAAA,KAAAc,EAAAC,EAAAC,CAAA,kHAGTX,EAASC,EAAAO,EAAAN,CAAA,wCAJHU,EAAAR,EAAAT,EAAI,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,uBAAnB,OAAIU,GAAA,yBAGDV,EAAG,CAAA,EAAC,OAAS,GAACkB,EAAA,mFAJpBb,EAOIC,EAAAa,EAAAZ,CAAA,yFANIU,EAAAR,EAAAT,EAAI,CAAA,EAAA,MAAM,EAAG,CAAC,CAAA,oBAAnB,OAAI,GAAA,EAAA,8GAAJ,OAGGA,EAAG,CAAA,EAAC,OAAS,2KAQPA,EAAK,CAAA,EAAG,GAAK,CAAC,aACbA,EAAK,CAAA,EAAG,GAAK,CAAC,EACZG,EAAAC,EAAA,SAAAJ,OAAS,SAAS,UAJjCK,EAKCC,EAAAF,EAAAG,CAAA,yBAHWP,EAAK,CAAA,EAAG,GAAK,CAAC,kBACbA,EAAK,CAAA,EAAG,GAAK,CAAC,OACZG,EAAAC,EAAA,SAAAJ,OAAS,SAAS,wCAnCjCA,EAAM,CAAA,GAAAoB,EAAApB,CAAA,mEAANA,EAAM,CAAA,GAAAqB,EAAA,EAAArB,EAAAsB,CAAA,sDAVC,GAAA,CAAA,MAAAC,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF,EACX,CAAA,MAAAG,CAAA,EAAAH,EAEPI,EAAU,GACVC,EAAS,MAAM,QAAQN,CAAK,EAC5BO,EAAWD,IAAWN,EAAM,SAAW,GAAKA,EAAM,CAAC,EAAE,SAAW,GAU7C,MAAAQ,EAAA,IAAAC,EAAA,EAAAJ,EAAU,EAAI,EACdK,EAAA,IAAAD,EAAA,EAAAJ,EAAU,EAAK"}