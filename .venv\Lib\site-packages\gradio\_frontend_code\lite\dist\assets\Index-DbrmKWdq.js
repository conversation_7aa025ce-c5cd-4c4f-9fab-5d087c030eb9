import{a as J,i as K,s as L,f as N,q as O,y as w,b as A,z as p,A as q,d as k,C as b,M as z,U as F,u as Q,r as R,v as T,k as W,t as X,l as v,w as D,x as M,e as G,V as P,D as Y,aq as S,as as Z}from"../lite.js";function U(t,e,i){const n=t.slice();return n[16]=e[i],n}function V(t){let e,i,n,l,r,a;return{c(){e=w("div"),i=w("span"),n=D(t[2]),l=D(":"),r=D(" "),a=D(t[3]),p(i,"class","svelte-1ccw3kh"),p(e,"class","component-name svelte-1ccw3kh")},m(o,u){k(o,e,u),b(e,i),b(i,n),b(i,l),b(e,r),b(e,a)},p(o,u){u&4&&M(n,o[2]),u&8&&M(a,o[3])},d(o){o&&v(e)}}}function x(t){let e,i,n,l,r,a,o,u,c,f,d,s=!t[1]&&j(t);return{c(){e=w("button"),e.textContent="+",i=A(),n=w("button"),n.textContent="+",l=A(),r=w("button"),r.textContent="+",a=A(),o=w("button"),o.textContent="+",u=A(),s&&s.c(),c=G(),p(e,"class","add up svelte-1ccw3kh"),p(n,"class","add left svelte-1ccw3kh"),p(r,"class","add right svelte-1ccw3kh"),p(o,"class","add down svelte-1ccw3kh")},m(_,m){k(_,e,m),k(_,i,m),k(_,n,m),k(_,l,m),k(_,r,m),k(_,a,m),k(_,o,m),k(_,u,m),s&&s.m(_,m),k(_,c,m),f||(d=[z(e,"click",t[11]("up")),z(n,"click",t[11]("left")),z(r,"click",t[11]("right")),z(o,"click",t[11]("down"))],f=!0)},p(_,m){_[1]?s&&(s.d(1),s=null):s?s.p(_,m):(s=j(_),s.c(),s.m(c.parentNode,c))},d(_){_&&(v(e),v(i),v(n),v(l),v(r),v(a),v(o),v(u),v(c)),s&&s.d(_),f=!1,P(d)}}}function $(t){let e,i=!t[10]&&!t[1]&&B(t);return{c(){i&&i.c(),e=G()},m(n,l){i&&i.m(n,l),k(n,e,l)},p(n,l){!n[10]&&!n[1]?i?i.p(n,l):(i=B(n),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null)},d(n){n&&v(e),i&&i.d(n)}}}function j(t){let e,i,n,l,r;return{c(){e=w("button"),e.textContent="✎",i=A(),n=w("button"),n.textContent="✗",p(e,"class","action modify svelte-1ccw3kh"),p(n,"class","action delete svelte-1ccw3kh")},m(a,o){k(a,e,o),k(a,i,o),k(a,n,o),l||(r=[z(e,"click",t[11]("modify")),z(n,"click",t[11]("delete"))],l=!0)},p:Y,d(a){a&&(v(e),v(i),v(n)),l=!1,P(r)}}}function B(t){let e,i,n,l,r,a,o,u=S(t[6]),c=[];for(let f=0;f<u.length;f+=1)c[f]=E(U(t,u,f));return{c(){e=w("div"),i=w("button"),i.textContent="input",n=A(),l=w("button"),l.textContent="output",r=D(`
					| `);for(let f=0;f<c.length;f+=1)c[f].c();p(i,"class","function input svelte-1ccw3kh"),q(i,"selected",t[7]),p(l,"class","function output svelte-1ccw3kh"),q(l,"selected",t[8]),p(e,"class","button-set svelte-1ccw3kh")},m(f,d){k(f,e,d),b(e,i),b(e,n),b(e,l),b(e,r);for(let s=0;s<c.length;s+=1)c[s]&&c[s].m(e,null);a||(o=[z(i,"click",t[11]("input")),z(l,"click",t[11]("output"))],a=!0)},p(f,d){if(d&128&&q(i,"selected",f[7]),d&256&&q(l,"selected",f[8]),d&2624){u=S(f[6]);let s;for(s=0;s<u.length;s+=1){const _=U(f,u,s);c[s]?c[s].p(_,d):(c[s]=E(_),c[s].c(),c[s].m(e,null))}for(;s<c.length;s+=1)c[s].d(1);c.length=u.length}},d(f){f&&v(e),Z(c,f),a=!1,P(o)}}}function E(t){let e,i,n=t[16]+"",l,r,a;return{c(){e=w("button"),i=D("on:"),l=D(n),p(e,"class","function event svelte-1ccw3kh"),q(e,"selected",t[9].includes(t[16]))},m(o,u){k(o,e,u),b(e,i),b(e,l),r||(a=z(e,"click",function(){F(t[11]("on:"+t[16]))&&t[11]("on:"+t[16]).apply(this,arguments)}),r=!0)},p(o,u){t=o,u&64&&n!==(n=t[16]+"")&&M(l,n),u&576&&q(e,"selected",t[9].includes(t[16]))},d(o){o&&v(e),r=!1,a()}}}function ee(t){let e,i,n,l,r=t[12].includes(t[2]),a,o,u,c,f,d=r&&V(t);function s(C,y){return C[5]?$:x}let _=s(t),m=_(t);const I=t[15].default,g=O(I,t,t[14],null);return{c(){e=w("div"),i=w("div"),n=A(),l=w("div"),d&&d.c(),a=A(),m.c(),o=A(),g&&g.c(),p(i,"class","cover svelte-1ccw3kh"),p(l,"class","interaction svelte-1ccw3kh"),p(e,"class","sketchbox svelte-1ccw3kh"),q(e,"function_mode",t[5]),q(e,"row",t[0]),q(e,"active",t[4])},m(C,y){k(C,e,y),b(e,i),b(e,n),b(e,l),d&&d.m(l,null),b(l,a),m.m(l,null),b(e,o),g&&g.m(e,null),u=!0,c||(f=z(l,"click",function(){F(t[1]?void 0:t[11]("modify"))&&(t[1]?void 0:t[11]("modify")).apply(this,arguments)}),c=!0)},p(C,[y]){t=C,y&4&&(r=t[12].includes(t[2])),r?d?d.p(t,y):(d=V(t),d.c(),d.m(l,a)):d&&(d.d(1),d=null),_===(_=s(t))&&m?m.p(t,y):(m.d(1),m=_(t),m&&(m.c(),m.m(l,null))),g&&g.p&&(!u||y&16384)&&Q(g,I,t,t[14],u?T(I,t[14],y,null):R(t[14]),null),(!u||y&32)&&q(e,"function_mode",t[5]),(!u||y&1)&&q(e,"row",t[0]),(!u||y&16)&&q(e,"active",t[4])},i(C){u||(W(g,C),u=!0)},o(C){X(g,C),u=!1},d(C){C&&v(e),d&&d.d(),m.d(),g&&g.d(C),c=!1,f()}}}function te(t,e,i){let n,{$$slots:l={},$$scope:r}=e,{row:a}=e,{is_container:o}=e,{component_type:u}=e,{var_name:c}=e,{active:f=!1}=e,{function_mode:d=!1}=e,{event_list:s}=e,{is_input:_=!1}=e,{is_output:m=!1}=e,{triggers:I=[]}=e,{gradio:g}=e;const C=h=>H=>{H.stopPropagation(),g.dispatch("select",{index:0,value:h})},y=["state","browserstate","function"];return t.$$set=h=>{"row"in h&&i(0,a=h.row),"is_container"in h&&i(1,o=h.is_container),"component_type"in h&&i(2,u=h.component_type),"var_name"in h&&i(3,c=h.var_name),"active"in h&&i(4,f=h.active),"function_mode"in h&&i(5,d=h.function_mode),"event_list"in h&&i(6,s=h.event_list),"is_input"in h&&i(7,_=h.is_input),"is_output"in h&&i(8,m=h.is_output),"triggers"in h&&i(9,I=h.triggers),"gradio"in h&&i(13,g=h.gradio),"$$scope"in h&&i(14,r=h.$$scope)},t.$$.update=()=>{t.$$.dirty&4&&i(10,n=u==="function")},[a,o,u,c,f,d,s,_,m,I,n,C,y,g,r,l]}class ne extends J{constructor(e){super(),K(this,e,te,ee,L,{row:0,is_container:1,component_type:2,var_name:3,active:4,function_mode:5,event_list:6,is_input:7,is_output:8,triggers:9,gradio:13})}get row(){return this.$$.ctx[0]}set row(e){this.$$set({row:e}),N()}get is_container(){return this.$$.ctx[1]}set is_container(e){this.$$set({is_container:e}),N()}get component_type(){return this.$$.ctx[2]}set component_type(e){this.$$set({component_type:e}),N()}get var_name(){return this.$$.ctx[3]}set var_name(e){this.$$set({var_name:e}),N()}get active(){return this.$$.ctx[4]}set active(e){this.$$set({active:e}),N()}get function_mode(){return this.$$.ctx[5]}set function_mode(e){this.$$set({function_mode:e}),N()}get event_list(){return this.$$.ctx[6]}set event_list(e){this.$$set({event_list:e}),N()}get is_input(){return this.$$.ctx[7]}set is_input(e){this.$$set({is_input:e}),N()}get is_output(){return this.$$.ctx[8]}set is_output(e){this.$$set({is_output:e}),N()}get triggers(){return this.$$.ctx[9]}set triggers(e){this.$$set({triggers:e}),N()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),N()}}export{ne as default};
//# sourceMappingURL=Index-DbrmKWdq.js.map
