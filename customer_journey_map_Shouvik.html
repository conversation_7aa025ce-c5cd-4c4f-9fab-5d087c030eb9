<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Shouvik
        </h5>
        <p>
         Retired
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigate to Amazon.com, search for 'wireless headphones under $100', filter by customer ratings (4+ stars), open a product details, read reviews
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Find a wireless headphone under $100 with good reviews, explore product details, and read customer feedback."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Navigate seamlessly through Amazon.com with no technical issues, locate a suitable product, and access reviews to make an informed decision."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Initial Navigation
     </div>
     <div class="stage" style="background-color: #F55050">
      Search
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Search Submission
     </div>
     <div class="stage" style="background-color: #F7C934">
      Filter Application
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Product Exploration
     </div>
     <div class="stage" style="background-color: #5E2590">
      Review Reading
     </div>
     <div class="stage" style="background-color: #F55050">
      Task Completion
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Attempted to open Amazon.com.
     </div>
     <div class="stage" style="background-color: #F55050">
      Entered a search query 'wireless headphones under $100' in the search bar.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Clicked on search button to see results for 'wireless headphones under $100'.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Applied the '4 Stars &amp; Up' filter on search results.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Opened product details for the first headphone in the filtered results.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Accessed and read product reviews.
     </div>
     <div class="stage" style="background-color: #F55050">
      Concluded the task after reviewing the final product details and ratings.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Uncertainty
     </div>
     <div class="stage" style="background-color: #F55050">
      Optimism
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Relief
     </div>
     <div class="stage" style="background-color: #F7C934">
      Confidence
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Excitement
     </div>
     <div class="stage" style="background-color: #5E2590">
      Engagement
     </div>
     <div class="stage" style="background-color: #F55050">
      Satisfaction
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Page was blank initially, taking time to load or rendering issues.
     </div>
     <div class="stage" style="background-color: #F55050">
      Search was not submitted, requiring additional interaction to proceed.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Additional click required due to interruption in form submission.
     </div>
     <div class="stage" style="background-color: #F7C934">
      No significant pain points observed.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      No significant pain points observed.
     </div>
     <div class="stage" style="background-color: #5E2590">
      No significant pain points observed.
     </div>
     <div class="stage" style="background-color: #F55050">
      No significant pain points observed.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
