<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Customer Journey Map
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
   .header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
  </style>
 </head>
 <body>
  <div class="main">
   <div class="container-fluid header-section">
    <div class="container">
     <h1 class="text-white fw-bold">
      Customer Journey Map
     </h1>
     <div class="row mt-4">
      <!-- Persona Section -->
      <div class="col-md-3">
       <div class="persona-card text-center">
        <img alt="Persona" src="./images/boy.png"/>
        <h5 class="mt-3">
         Shouvik
        </h5>
        <p>
         Retired
        </p>
       </div>
      </div>
      <!-- Scenario Section -->
      <div class="col-md-5">
       <div class="scenario-card">
        <h4 class="fw-bold">
         Scenario
        </h4>
        <p>
         Navigating Amazon.com to search for wireless headphones under $100, filter by customer ratings (4+ stars), view product details, and read reviews.
        </p>
       </div>
      </div>
      <!-- Goals Section -->
      <div class="col-md-4">
       <div class="goal-card">
        <input class="goal" placeholder="Main Goal" type="text" value="Find a pair of wireless headphones meeting the criteria of budget, rating, and features."/>
        <input class="expectation" placeholder="Expectation 1" type="text" value="Efficient navigation through the website with easy filtering, helpful product details, and informative customer reviews to make a confident purchase decision."/>
       </div>
      </div>
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Stages
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Homepage Navigation
     </div>
     <div class="stage" style="background-color: #F55050">
      Product Search
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Search Refinement
     </div>
     <div class="stage" style="background-color: #F7C934">
      Filter Application
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Product Selection
     </div>
     <div class="stage" style="background-color: #5E2590">
      Customer Review Exploration
     </div>
     <div class="stage" style="background-color: #F55050">
      Task Completion
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Customer Actions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Navigated to Amazon.com using a direct URL.
     </div>
     <div class="stage" style="background-color: #F55050">
      Entered 'wireless headphones under $100' into the search box.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Selected a relevant suggestion to refine search results.
     </div>
     <div class="stage" style="background-color: #F7C934">
      Applied the '4 Stars &amp; Up' filter for customer ratings.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Navigated to a product detail page of a highly rated option.
     </div>
     <div class="stage" style="background-color: #5E2590">
      Scrolled to and read customer reviews on the product details page.
     </div>
     <div class="stage" style="background-color: #F55050">
      Extracted reviews and highlighted positive aspects to validate product quality and finalize the task.
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Emotions
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      Neutral (focused on task initiation).
     </div>
     <div class="stage" style="background-color: #F55050">
      Neutral (focused on initiating the search).
     </div>
     <div class="stage" style="background-color: #F78D1E">
      Neutral → Positive (progress in narrowing search results).
     </div>
     <div class="stage" style="background-color: #F7C934">
      Positive (filter aligned with expectations).
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Positive (focused on exploring product details).
     </div>
     <div class="stage" style="background-color: #5E2590">
      Positive → Confident (reviews highlighted favorable aspects).
     </div>
     <div class="stage" style="background-color: #F55050">
      Confident → Satisfied (successfully completed all subtasks).
     </div>
    </div>
   </div>
   <div class="container mt-4 mb-4">
    <h4 class="fw-bold">
     Pain Points
    </h4>
    <div class="stage-bar">
     <div class="stage" style="background-color: #5E2590">
      No significant issues; navigation was smooth.
     </div>
     <div class="stage" style="background-color: #F55050">
      Search action required refinement; initial search was incomplete.
     </div>
     <div class="stage" style="background-color: #F78D1E">
      None; refinement was successful and straightforward.
     </div>
     <div class="stage" style="background-color: #F7C934">
      None; filter application worked as intended.
     </div>
     <div class="stage" style="background-color: #8BC34A">
      Possibly overwhelmed by multiple product options available prior to selection.
     </div>
     <div class="stage" style="background-color: #5E2590">
      None noted; reviews were comprehensive and informative.
     </div>
     <div class="stage" style="background-color: #F55050">
      None; final review exploration confirmed product suitability.
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
