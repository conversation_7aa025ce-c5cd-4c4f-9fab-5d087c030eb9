{"version": 3, "mappings": ";8dA+hBe,YAAAA,KAAO,UAAU,EACvB,MAAAA,KAAO,IAAI,EACbA,EAAc,0LAFNC,EAAA,kBAAAD,KAAO,UAAU,EACvBC,EAAA,YAAAD,KAAO,IAAI,gBACbA,EAAc,yIAI8BA,EAAK,2CAALA,EAAK,gFAQjC,4QAJhBA,EAAO,IAAAE,GAAAF,CAAA,uFAFZG,EAAoCC,EAAAC,EAAAC,CAAA,iDAE/BN,EAAO,yVACSA,EAAO,mDAA3BG,EAA+BC,EAAAG,EAAAD,CAAA,+BAAXN,EAAO,uDAbxBA,EAAc,KAAAQ,GAAAR,CAAA,kDAQmB,sFACjC,OAAAA,MAASA,EAAU,gLATnBA,EAAc,6oBAJH,WACP,kdAvgBE,OAAAS,CAAA,EAAAC,EACA,GAAAC,CAAA,EAAAD,EACA,GAAAE,CAAA,EAAAF,GACA,MAAAG,EAAuB,MAAAH,EACvB,MAAAI,CAAA,EAAAJ,GAMA,MAAAK,EAAuB,MAAAL,GACvB,QAAAM,EAAyB,MAAAN,GACzB,QAAAO,EAAyB,MAAAP,GACzB,YAAAQ,GAA6B,MAAAR,GAC7B,MAAAS,EAAgC,MAAAT,GAChC,YAAAU,EAMI,QAAAV,GACJ,UAAAW,EAA2C,MAAAX,GAC3C,MAAAY,EAAiC,MAAAZ,GACjC,MAAAa,EAAiC,MAAAb,GACjC,cAAAc,EAA+B,MAAAd,GAC/B,cAAAe,EAA+B,MAAAf,GAC/B,sBAAAgB,EAAwB,IAAAhB,GACxB,QAAAiB,GAAyB,MAAAjB,GACzB,KAAAkB,GAAkD,MAAAlB,GAClD,QAAAmB,EAA8C,QAAAnB,WAChDoB,GACRC,MAQIA,IAAU,IACN,qBACGA,IAAU,KACb,sBACGA,IAAU,IACX,aAAOnB,EAAG,MAAO,gBAChBmB,IAAU,KACX,aAAOnB,EAAG,MAAO,iBAChBmB,IAAU,KACb,OACG,SAAM,QAAQA,CAAK,EACtBA,aAIE,YAAAC,EAAc,IAAAtB,EACrBuB,EAGO,QAAAC,CAAA,EAAAxB,EAQPyB,EACAC,GAAsB,SACpBC,GACL,GAAG,EACH,EAAG,GACH,EAAG,GAAK,GACR,EAAG,GAAK,GAAK,IASV,IAAAC,EACAC,WAYKC,GAAcC,EAAA,IAGlBZ,GAAW,OAAS,MAAM,QAAQA,CAAO,EACrC,OAAAY,EAAK,KAAK,IAAKC,GAAA,CACf,MAAAC,EAAA,GACN,OAAAF,EAAK,QAAQ,SAASG,EAAKC,IAAA,CAC1BF,EAAIC,CAAG,EAAIF,EAAIG,CAAC,IAEVF,QAGLG,EAAUL,EAAK,QAAQ,QAAQ9B,CAAC,EAChCoC,EAAUN,EAAK,QAAQ,QAAQ7B,CAAC,EAChCoC,EAAcnC,EAAQ4B,EAAK,QAAQ,QAAQ5B,CAAK,EAAI,KACjD,OAAA4B,EAAK,KAAK,IAAKC,GAAA,OACfC,EACJ,EAAAhC,CAAC,EAAG+B,EAAII,CAAO,GACflC,CAAC,EAAG8B,EAAIK,CAAO,GAEb,OAAAlC,GAASmC,IAAgB,OAC5BL,EAAI9B,CAAK,EAAI6B,EAAIM,CAAW,GAEtBL,IAKH,MAAAM,GAAA,OAAoB,OAAW,IACjC,IAAAC,EAIAC,EACAC,EAAU,GACVC,GACAC,EAEAC,GACW,eAAAC,IAAA,IACVL,GACHA,EAAK,YAED1C,GAAU,CAAAyC,EAAA,OACfG,GAAYH,EAAc,kBACpBO,EAAOC,KACRD,IACLH,EAAA,IAAqB,eAAgBK,GAAA,CAC/B,CAAAA,EAAG,CAAC,EAAE,UAAYA,EAAG,CAAC,EAAE,kBAAkB,eAE9CN,KAAc,GACdH,EAAc,cAAgB,GAC9BzC,EAAM,UAAUE,CAAC,IAAM,UAGvB6C,KAEAL,EAAK,OAAO,QAASQ,EAAG,CAAC,EAAE,OAAO,WAAW,EAAE,SAI5CJ,KACJA,IAAA,MAAAK,GAAA,WAA0B,iCAAY,iDAAG,SAE1CL,GAAUL,EAAeO,EAAA,CAAQ,QAAS,KAAS,KAAe,SAAAI,EAAA,CACjEV,EAAOU,EAAO,KAEdP,EAAe,QAAQJ,CAAa,EAChC,IAAAY,EACJX,EAAK,iBAAiB,gBACrBjB,EAAO,SAAS,cAAc,IAG/BgB,EAAc,iBACb,YACU,SAAAa,EAAA,CACLA,EAAE,OAAS,GACdA,EAAE,kBAGJ,IAEG/B,GACHmB,EAAK,kBAAkB,iBAAmBa,EAAGvD,MACxC,OAAO,KAAKA,CAAK,EAAE,SAAW,SAClC,aAAaqD,CAAe,EACxB,IAAAG,EAA0BxD,EAAM,OAAO,KAAKA,CAAK,EAAE,CAAC,GACpDyD,IACHD,EAAA,CAASA,EAAM,CAAC,EAAI,IAAMA,EAAM,CAAC,EAAI,GAAI,GAStC7B,QACH+B,EARG,KACHjC,EAAO,SAAS,UACf,MAAO+B,EACP,MAAOA,EACP,SAAU,KAIQ,EAEnBH,EAAkB,sBACjB5B,EAAO,SAAS,UACf,MAAO+B,EACP,MAAOA,EACP,SAAU,MAET,cAOJE,EAAwC,KAC5CC,GAAA,UACChB,EAAU,cAETA,EAAU,IACND,GACHA,EAAK,WAEFG,GACHA,EAAe,gBAqCT,SAAAI,IAAA,CACH,IAAAjD,GAAA,CAAU4D,EAAuB,gBAClCC,EAAeD,EAAe,iBAAiB,gBAAgB,EAC/DE,EAAkBF,EAAe,iBAAiB,mBAAmB,EACrEG,EAAqBH,EAAe,iBACvC,0BAEGI,EAAcJ,EAAe,WAC7BK,EAAeL,EAAe,iBACjC,mCAaKM,EAAkBC,GAChBA,EAAK,SAAS,IAAI,EAAI,WAAWA,EAAK,MAAM,IAAK,CAAK,SAE1DC,EAAeF,EAClBN,EAAe,iBAAiB,WAAW,GAExCS,EAAeH,EAClBN,EAAe,iBAAiB,WAAW,UAK3C,QAAS,uDACT,WAAY,cACZ,QACC,SAAY,MAAM,MAAO,SAAU,WACnC,MACC,UAAWI,EACX,WAAYF,EACZ,UAAWE,EACX,WAAYF,EACZ,aAAc,EACd,UAAWC,EACX,cAAeM,EACf,UAAWN,EACX,gBAAiB,SACjB,cAAeM,EACf,gBAAiB,SACjB,OAAQ,GACR,WAAY,GAEb,QACC,WAAYP,EACZ,UAAWE,EACX,WAAYF,EACZ,UAAWE,EACX,gBAAiB,SACjB,cAAeK,EACf,gBAAiB,SACjB,OAAQ,GAET,OACC,MAAOP,EACP,KAAME,EACN,SAAUI,EACV,WAAYH,EACZ,OAAQ,UAET,MAAQ,OAAQF,CAAA,EAChB,MACC,OAAQ/D,EAAM,OAAS,MAAQ6D,EAAe,OAC9C,KAAM7D,EAAM,OAAS,MAAQ6D,EAAe,OAC5C,OAAQ,cAGV,MAAQ,KAAM,QACd,UACC,KAAMrC,CAAA,EAEP,OAAQ,UAAYxB,EAAM,OAAS,OAAU,QAAO,MAAS,IAC3DsE,KAEC,UACC,KACCtE,EAAM,OAAS,OACZsE,GAAQ,QAEP,WACC,MAAO,GACP,MAAO,YACP,MAAO,GAER,MAAO,IAGP,WAAa,MAAO,GAAO,MAAO,QAAS,MAAO,KAClD,MAAO,GAER,OACJ,QACCA,IAAS,OACN,QAEA,WAAa,MAAO,GAAO,MAAO,QAAS,MAAO,GAClD,MAAO,GAEX,GACC,MACK,GAAAvD,IAAkB,OAAU,WAAYA,CAAA,EAC5C,OAAQE,EACR,MAAOA,GAER,MAAOf,EACP,MAAOK,GAAWL,EAClB,KAAMF,EAAM,UAAUE,CAAC,EACvB,MAAOqE,EAAA,CAAW,OAAQA,CAAW,SACrC,IAAK7C,EAAA,CAAW,KAAMA,CAAW,SACjC,KAAMJ,GAEP,GACC,KAAMN,GAAkB,WAAYA,CAAA,KACpC,MAAOb,EACP,MAAOK,GAAWL,EAClB,KAAMH,EAAM,UAAUG,CAAC,EACvB,MAAOW,EAAA,CAAU,OAAQA,CAAU,SACnC,UAAWgB,EAAcD,EAAe,QAEzC,MAAOzB,GAEJ,MAAOA,EACP,OAAU,QAAQ,SAAU,MAAOK,EAAA,EACnC,MACCT,EAAM,UAAUI,CAAK,IAAM,WAExB,OAAQoE,EACR,MAAO5D,EACJ4D,EAAc,IAAKC,GAAM7D,EAAU6D,CAAC,GACpC,SAGH,OACC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvC,IAAKC,GACNd,EAAe,iBAAiB,aAAec,CAAC,GAEjD,YAAa,OAEjB,KAAM1E,EAAM,UAAUI,CAAK,GAE3B,OACH,QACCgB,GAAW,OACR,SAGC,MAAOjB,EACP,KAAMH,EAAM,UAAUG,CAAC,EACvB,UAAW2B,EAAcD,EAAe,OACxC,MAAOrB,GAAWL,IAGlB,MAAOD,EACP,KAAMF,EAAM,UAAUE,CAAC,EACvB,MAAOK,GAAWL,EAClB,OAAQuD,EAAa,oBAAsB,OAC3C,IAAK/B,EAAA,CAAW,KAAMA,CAAW,UAE9B,GAAAtB,IAGA,MAAOA,EACP,KAAMJ,EAAM,UAAUI,CAAK,UAI3BgB,IAAY,UAEbpB,GAAO,QACN,OACCmC,GACAA,IAAQjC,GACRiC,IAAQhC,GACRgC,IAAQ/B,IACPgB,IAAY,OAASA,EAAQ,SAASe,CAAG,IAE3C,IAAKwC,IAAA,CACL,MAAOA,EACP,KAAM3E,EAAM,UAAU2E,CAAM,OAIrC,cACA,MAAQ,KAAM,GAAM,KAAML,IAAS,QAAU,QAAUtE,EAAM,MAC7D,KAAMsE,KAKT,QACK,GAAAtE,EAAM,OAAS,SAGf,KAAM,YACN,QACC,MAAO,WACP,OAAQI,GAASA,CAAK,KACtB,QAAS,GACT,GAAI,YACJ,KAAM,SAEP,OAAQ,OAAO,IAGf,KAAM,QACN,QACC,MAAO,WACP,QAAS,GACT,GAAI,YACJ,KAAM,SAEP,OAAQ,OAAO,OAIf,GAAAmB,IAGA,KAAM,QACN,QACC,WAAY,GAAG,EACf,MAAQ,KAAM,OAAQ,YAAa,GAAK,OAAQ,QAChD,KAAM,YAEP,OAAQ,MAAM,QAKnB,MAAOkB,EAAc,YACrB,OAAQmC,GAAS,YAAc,OAC/B,MAAOtE,GAAS,YAKP,MAAAuE,GAAQ,WAAA5E,GACR,QAAA6E,GAAU,IAAA7E,EACV,cAAA8E,GAAA,IAAA9E,GACA,QAAA+E,GAAU,IAAA/E,EACV,YAAAgF,EAAA,EAAAhF,GACA,MAAAiF,GAAuB,MAAAjF,GACvB,UAAAkF,GAAgC,QAAAlF,GAChC,eAAAmF,GAA4C,QAAAnF,GAC5C,OAAA2E,GAA6B,QAAA3E,EAkBf,MAAAoF,GAAA,IAAA5D,EAAO,SAAS,eAAgB2D,EAAc,6CAKtD3C,EAAa6C,6mCA3Z3BC,EAAA,GAAA/D,EAAQxB,EAAQ+B,GAAc/B,CAAK,6CApHnCwE,EACFpE,GAASJ,GAASA,EAAM,UAAUI,CAAK,IAAM,UAC1C,MAAM,SAAS,IAAIoB,EAAM,IAAKgE,GAAMA,EAAEpF,CAAK,oCA+C5CkB,EAAQD,GAAcF,EAAI,wBAW7BoE,EAAA,GAAG9B,EAAazD,GAASA,EAAM,UAAUE,CAAC,IAAM,uDAC7CqE,EAAS1D,GAAS4C,EAAc,CAAA5C,EAAM,CAAC,EAAI,IAAMA,EAAM,CAAC,EAAI,GAAI,EAAIA,0BASvE0E,EAAA,GAAG7D,EAAShB,SACFA,GAAU,SAChB,IACD,SAASA,EAAM,UAAU,EAAGA,EAAM,OAAS,CAAC,GAC5CkB,GAAgBlB,EAAMA,EAAM,OAAS,CAAC,GACrCA,EACD,gDAIEV,IACCA,EAAM,OAAS,SAClBuF,EAAA,GAAAzD,EAAcJ,IAAW,aACzBG,EAAelB,GAAemB,EAAc,MAAQ,UAEpDyD,EAAA,GAAAzD,EAAcJ,IAAW,QAAa1B,EAAM,UAAUE,CAAC,IAAM,WAC7DqF,EAAA,GAAA1D,EAAelB,GAA4B,6BAkC9C4E,EAAA,GAAG3B,EAAiBnB,EACjB,OAAO,iBAAiBA,CAAa,EACrC,4CA+FIE,GAAWF,IACjBA,EAAc,iBAAiB,iBAC9Bd,GAAsB,KAEvBc,EAAc,iBAAiB,eAC9Bd,GAAsB,GAClB+B,IACHA,SACAA,EAAmB,sDAsBrBE,GAAkB,sBAAsBb,EAAU", "names": ["ctx", "dirty", "create_if_block_1", "insert", "target", "div", "anchor", "p", "create_if_block_2", "value", "$$props", "x", "y", "color", "root", "title", "x_title", "y_title", "color_title", "x_bin", "y_aggregate", "color_map", "x_lim", "y_lim", "x_label_angle", "y_label_angle", "x_axis_labels_visible", "caption", "sort", "tooltip", "reformat_sort", "_sort", "_selectable", "_data", "gradio", "_x_bin", "mouse_down_on_chart", "SUFFIX_DURATION", "_y_aggregate", "aggregating", "reformat_data", "data", "row", "obj", "col", "i", "x_index", "y_index", "color_index", "is_browser", "chart_element", "view", "mounted", "old_width", "resizeObserver", "vegaEmbed", "load_chart", "spec", "create_vega_lite_spec", "el", "__vitePreload", "result", "debounceTimeout", "e", "_", "range", "x_temporal", "release_callback", "onMount", "computed_style", "accent_color", "body_text_color", "borderColorPrimary", "font_family", "title_weight", "font_to_px_val", "font", "text_size_md", "text_size_sm", "mode", "_x_lim", "unique_colors", "c", "n", "column", "height", "label", "elem_id", "elem_classes", "visible", "show_label", "scale", "min_width", "loading_status", "clear_status_handler", "$$value", "$$invalidate", "d"], "ignoreList": [], "sources": ["../../../nativeplot/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { onMount } from \"svelte\";\n\n\timport type { TopLevelSpec as Spec } from \"vega-lite\";\n\timport type { View } from \"vega\";\n\timport { LineChart as LabelIcon } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\n\tinterface PlotData {\n\t\tcolumns: string[];\n\t\tdata: [string | number][];\n\t\tdatatypes: Record<string, \"quantitative\" | \"temporal\" | \"nominal\">;\n\t\tmark: \"line\" | \"point\" | \"bar\";\n\t}\n\texport let value: PlotData | null;\n\texport let x: string;\n\texport let y: string;\n\texport let color: string | null = null;\n\texport let root: string;\n\t$: unique_colors =\n\t\tcolor && value && value.datatypes[color] === \"nominal\"\n\t\t\t? Array.from(new Set(_data.map((d) => d[color])))\n\t\t\t: [];\n\n\texport let title: string | null = null;\n\texport let x_title: string | null = null;\n\texport let y_title: string | null = null;\n\texport let color_title: string | null = null;\n\texport let x_bin: string | number | null = null;\n\texport let y_aggregate:\n\t\t| \"sum\"\n\t\t| \"mean\"\n\t\t| \"median\"\n\t\t| \"min\"\n\t\t| \"max\"\n\t\t| undefined = undefined;\n\texport let color_map: Record<string, string> | null = null;\n\texport let x_lim: [number, number] | null = null;\n\texport let y_lim: [number, number] | null = null;\n\texport let x_label_angle: number | null = null;\n\texport let y_label_angle: number | null = null;\n\texport let x_axis_labels_visible = true;\n\texport let caption: string | null = null;\n\texport let sort: \"x\" | \"y\" | \"-x\" | \"-y\" | string[] | null = null;\n\texport let tooltip: \"axis\" | \"none\" | \"all\" | string[] = \"axis\";\n\tfunction reformat_sort(\n\t\t_sort: typeof sort\n\t):\n\t\t| string\n\t\t| \"ascending\"\n\t\t| \"descending\"\n\t\t| { field: string; order: \"ascending\" | \"descending\" }\n\t\t| string[]\n\t\t| undefined {\n\t\tif (_sort === \"x\") {\n\t\t\treturn \"ascending\";\n\t\t} else if (_sort === \"-x\") {\n\t\t\treturn \"descending\";\n\t\t} else if (_sort === \"y\") {\n\t\t\treturn { field: y, order: \"ascending\" };\n\t\t} else if (_sort === \"-y\") {\n\t\t\treturn { field: y, order: \"descending\" };\n\t\t} else if (_sort === null) {\n\t\t\treturn undefined;\n\t\t} else if (Array.isArray(_sort)) {\n\t\t\treturn _sort;\n\t\t}\n\t}\n\t$: _sort = reformat_sort(sort);\n\texport let _selectable = false;\n\tlet _data: {\n\t\t[x: string]: string | number;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tdouble_click: undefined;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\n\t$: x_temporal = value && value.datatypes[x] === \"temporal\";\n\t$: _x_lim = x_lim && x_temporal ? [x_lim[0] * 1000, x_lim[1] * 1000] : x_lim;\n\tlet _x_bin: number | undefined;\n\tlet mouse_down_on_chart = false;\n\tconst SUFFIX_DURATION: Record<string, number> = {\n\t\ts: 1,\n\t\tm: 60,\n\t\th: 60 * 60,\n\t\td: 24 * 60 * 60\n\t};\n\t$: _x_bin = x_bin\n\t\t? typeof x_bin === \"string\"\n\t\t\t? 1000 *\n\t\t\t\tparseInt(x_bin.substring(0, x_bin.length - 1)) *\n\t\t\t\tSUFFIX_DURATION[x_bin[x_bin.length - 1]]\n\t\t\t: x_bin\n\t\t: undefined;\n\tlet _y_aggregate: typeof y_aggregate;\n\tlet aggregating: boolean;\n\t$: {\n\t\tif (value) {\n\t\t\tif (value.mark === \"point\") {\n\t\t\t\taggregating = _x_bin !== undefined;\n\t\t\t\t_y_aggregate = y_aggregate || aggregating ? \"sum\" : undefined;\n\t\t\t} else {\n\t\t\t\taggregating = _x_bin !== undefined || value.datatypes[x] === \"nominal\";\n\t\t\t\t_y_aggregate = y_aggregate ? y_aggregate : \"sum\";\n\t\t\t}\n\t\t}\n\t}\n\tfunction reformat_data(data: PlotData): {\n\t\t[x: string]: string | number;\n\t}[] {\n\t\tif (tooltip == \"all\" || Array.isArray(tooltip)) {\n\t\t\treturn data.data.map((row) => {\n\t\t\t\tconst obj: { [x: string]: string | number } = {};\n\t\t\t\tdata.columns.forEach((col, i) => {\n\t\t\t\t\tobj[col] = row[i];\n\t\t\t\t});\n\t\t\t\treturn obj;\n\t\t\t});\n\t\t}\n\t\tlet x_index = data.columns.indexOf(x);\n\t\tlet y_index = data.columns.indexOf(y);\n\t\tlet color_index = color ? data.columns.indexOf(color) : null;\n\t\treturn data.data.map((row) => {\n\t\t\tconst obj = {\n\t\t\t\t[x]: row[x_index],\n\t\t\t\t[y]: row[y_index]\n\t\t\t};\n\t\t\tif (color && color_index !== null) {\n\t\t\t\tobj[color] = row[color_index];\n\t\t\t}\n\t\t\treturn obj;\n\t\t});\n\t}\n\t$: _data = value ? reformat_data(value) : [];\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tlet chart_element: HTMLDivElement;\n\t$: computed_style = chart_element\n\t\t? window.getComputedStyle(chart_element)\n\t\t: null;\n\tlet view: View;\n\tlet mounted = false;\n\tlet old_width: number;\n\tlet resizeObserver: ResizeObserver;\n\n\tlet vegaEmbed: typeof import(\"vega-embed\").default;\n\tasync function load_chart(): Promise<void> {\n\t\tif (view) {\n\t\t\tview.finalize();\n\t\t}\n\t\tif (!value || !chart_element) return;\n\t\told_width = chart_element.offsetWidth;\n\t\tconst spec = create_vega_lite_spec();\n\t\tif (!spec) return;\n\t\tresizeObserver = new ResizeObserver((el) => {\n\t\t\tif (!el[0].target || !(el[0].target instanceof HTMLElement)) return;\n\t\t\tif (\n\t\t\t\told_width === 0 &&\n\t\t\t\tchart_element.offsetWidth !== 0 &&\n\t\t\t\tvalue.datatypes[x] === \"nominal\"\n\t\t\t) {\n\t\t\t\t// a bug where when a nominal chart is first loaded, the width is 0, it doesn't resize\n\t\t\t\tload_chart();\n\t\t\t} else {\n\t\t\t\tview.signal(\"width\", el[0].target.offsetWidth).run();\n\t\t\t}\n\t\t});\n\n\t\tif (!vegaEmbed) {\n\t\t\tvegaEmbed = (await import(\"vega-embed\")).default;\n\t\t}\n\t\tvegaEmbed(chart_element, spec, { actions: false }).then(function (result) {\n\t\t\tview = result.view;\n\n\t\t\tresizeObserver.observe(chart_element);\n\t\t\tvar debounceTimeout: NodeJS.Timeout;\n\t\t\tview.addEventListener(\"dblclick\", () => {\n\t\t\t\tgradio.dispatch(\"double_click\");\n\t\t\t});\n\t\t\t// prevent double-clicks from highlighting text\n\t\t\tchart_element.addEventListener(\n\t\t\t\t\"mousedown\",\n\t\t\t\tfunction (e) {\n\t\t\t\t\tif (e.detail > 1) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfalse\n\t\t\t);\n\t\t\tif (_selectable) {\n\t\t\t\tview.addSignalListener(\"brush\", function (_, value) {\n\t\t\t\t\tif (Object.keys(value).length === 0) return;\n\t\t\t\t\tclearTimeout(debounceTimeout);\n\t\t\t\t\tlet range: [number, number] = value[Object.keys(value)[0]];\n\t\t\t\t\tif (x_temporal) {\n\t\t\t\t\t\trange = [range[0] / 1000, range[1] / 1000];\n\t\t\t\t\t}\n\t\t\t\t\tlet callback = (): void => {\n\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\tvalue: range,\n\t\t\t\t\t\t\tindex: range,\n\t\t\t\t\t\t\tselected: true\n\t\t\t\t\t\t});\n\t\t\t\t\t};\n\t\t\t\t\tif (mouse_down_on_chart) {\n\t\t\t\t\t\trelease_callback = callback;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdebounceTimeout = setTimeout(function () {\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\tvalue: range,\n\t\t\t\t\t\t\t\tindex: range,\n\t\t\t\t\t\t\t\tselected: true\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 250);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n\n\tlet release_callback: (() => void) | null = null;\n\tonMount(() => {\n\t\tmounted = true;\n\t\treturn () => {\n\t\t\tmounted = false;\n\t\t\tif (view) {\n\t\t\t\tview.finalize();\n\t\t\t}\n\t\t\tif (resizeObserver) {\n\t\t\t\tresizeObserver.disconnect();\n\t\t\t}\n\t\t};\n\t});\n\n\t$: if (mounted && chart_element) {\n\t\tchart_element.addEventListener(\"mousedown\", () => {\n\t\t\tmouse_down_on_chart = true;\n\t\t});\n\t\tchart_element.addEventListener(\"mouseup\", () => {\n\t\t\tmouse_down_on_chart = false;\n\t\t\tif (release_callback) {\n\t\t\t\trelease_callback();\n\t\t\t\trelease_callback = null;\n\t\t\t}\n\t\t});\n\t}\n\n\t$: title,\n\t\tx_title,\n\t\ty_title,\n\t\tcolor_title,\n\t\tx,\n\t\ty,\n\t\tcolor,\n\t\tx_bin,\n\t\t_y_aggregate,\n\t\tcolor_map,\n\t\tx_lim,\n\t\ty_lim,\n\t\tcaption,\n\t\tsort,\n\t\tvalue,\n\t\tmounted,\n\t\tchart_element,\n\t\tcomputed_style && requestAnimationFrame(load_chart);\n\n\tfunction create_vega_lite_spec(): Spec | null {\n\t\tif (!value || !computed_style) return null;\n\t\tlet accent_color = computed_style.getPropertyValue(\"--color-accent\");\n\t\tlet body_text_color = computed_style.getPropertyValue(\"--body-text-color\");\n\t\tlet borderColorPrimary = computed_style.getPropertyValue(\n\t\t\t\"--border-color-primary\"\n\t\t);\n\t\tlet font_family = computed_style.fontFamily;\n\t\tlet title_weight = computed_style.getPropertyValue(\n\t\t\t\"--block-title-text-weight\"\n\t\t) as\n\t\t\t| \"bold\"\n\t\t\t| \"normal\"\n\t\t\t| 100\n\t\t\t| 200\n\t\t\t| 300\n\t\t\t| 400\n\t\t\t| 500\n\t\t\t| 600\n\t\t\t| 700\n\t\t\t| 800\n\t\t\t| 900;\n\t\tconst font_to_px_val = (font: string): number => {\n\t\t\treturn font.endsWith(\"px\") ? parseFloat(font.slice(0, -2)) : 12;\n\t\t};\n\t\tlet text_size_md = font_to_px_val(\n\t\t\tcomputed_style.getPropertyValue(\"--text-md\")\n\t\t);\n\t\tlet text_size_sm = font_to_px_val(\n\t\t\tcomputed_style.getPropertyValue(\"--text-sm\")\n\t\t);\n\n\t\t/* eslint-disable complexity */\n\t\treturn {\n\t\t\t$schema: \"https://vega.github.io/schema/vega-lite/v5.17.0.json\",\n\t\t\tbackground: \"transparent\",\n\t\t\tconfig: {\n\t\t\t\tautosize: { type: \"fit\", contains: \"padding\" },\n\t\t\t\taxis: {\n\t\t\t\t\tlabelFont: font_family,\n\t\t\t\t\tlabelColor: body_text_color,\n\t\t\t\t\ttitleFont: font_family,\n\t\t\t\t\ttitleColor: body_text_color,\n\t\t\t\t\ttitlePadding: 8,\n\t\t\t\t\ttickColor: borderColorPrimary,\n\t\t\t\t\tlabelFontSize: text_size_sm,\n\t\t\t\t\tgridColor: borderColorPrimary,\n\t\t\t\t\ttitleFontWeight: \"normal\",\n\t\t\t\t\ttitleFontSize: text_size_sm,\n\t\t\t\t\tlabelFontWeight: \"normal\",\n\t\t\t\t\tdomain: false,\n\t\t\t\t\tlabelAngle: 0\n\t\t\t\t},\n\t\t\t\tlegend: {\n\t\t\t\t\tlabelColor: body_text_color,\n\t\t\t\t\tlabelFont: font_family,\n\t\t\t\t\ttitleColor: body_text_color,\n\t\t\t\t\ttitleFont: font_family,\n\t\t\t\t\ttitleFontWeight: \"normal\",\n\t\t\t\t\ttitleFontSize: text_size_sm,\n\t\t\t\t\tlabelFontWeight: \"normal\",\n\t\t\t\t\toffset: 2\n\t\t\t\t},\n\t\t\t\ttitle: {\n\t\t\t\t\tcolor: body_text_color,\n\t\t\t\t\tfont: font_family,\n\t\t\t\t\tfontSize: text_size_md,\n\t\t\t\t\tfontWeight: title_weight,\n\t\t\t\t\tanchor: \"middle\"\n\t\t\t\t},\n\t\t\t\tview: { stroke: borderColorPrimary },\n\t\t\t\tmark: {\n\t\t\t\t\tstroke: value.mark !== \"bar\" ? accent_color : undefined,\n\t\t\t\t\tfill: value.mark === \"bar\" ? accent_color : undefined,\n\t\t\t\t\tcursor: \"crosshair\"\n\t\t\t\t}\n\t\t\t},\n\t\t\tdata: { name: \"data\" },\n\t\t\tdatasets: {\n\t\t\t\tdata: _data\n\t\t\t},\n\t\t\tlayer: [\"plot\", ...(value.mark === \"line\" ? [\"hover\"] : [])].map(\n\t\t\t\t(mode) => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tencoding: {\n\t\t\t\t\t\t\tsize:\n\t\t\t\t\t\t\t\tvalue.mark === \"line\"\n\t\t\t\t\t\t\t\t\t? mode == \"plot\"\n\t\t\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\t\t\tcondition: {\n\t\t\t\t\t\t\t\t\t\t\t\t\tempty: false,\n\t\t\t\t\t\t\t\t\t\t\t\t\tparam: \"hoverPlot\",\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: 3\n\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t\tvalue: 2\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\t\tcondition: { empty: false, param: \"hover\", value: 100 },\n\t\t\t\t\t\t\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t: undefined,\n\t\t\t\t\t\t\topacity:\n\t\t\t\t\t\t\t\tmode === \"plot\"\n\t\t\t\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\tcondition: { empty: false, param: \"hover\", value: 1 },\n\t\t\t\t\t\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tx: {\n\t\t\t\t\t\t\t\taxis: {\n\t\t\t\t\t\t\t\t\t...(x_label_angle !== null && { labelAngle: x_label_angle }),\n\t\t\t\t\t\t\t\t\tlabels: x_axis_labels_visible,\n\t\t\t\t\t\t\t\t\tticks: x_axis_labels_visible\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfield: x,\n\t\t\t\t\t\t\t\ttitle: x_title || x,\n\t\t\t\t\t\t\t\ttype: value.datatypes[x],\n\t\t\t\t\t\t\t\tscale: _x_lim ? { domain: _x_lim } : undefined,\n\t\t\t\t\t\t\t\tbin: _x_bin ? { step: _x_bin } : undefined,\n\t\t\t\t\t\t\t\tsort: _sort\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\ty: {\n\t\t\t\t\t\t\t\taxis: y_label_angle ? { labelAngle: y_label_angle } : {},\n\t\t\t\t\t\t\t\tfield: y,\n\t\t\t\t\t\t\t\ttitle: y_title || y,\n\t\t\t\t\t\t\t\ttype: value.datatypes[y],\n\t\t\t\t\t\t\t\tscale: y_lim ? { domain: y_lim } : undefined,\n\t\t\t\t\t\t\t\taggregate: aggregating ? _y_aggregate : undefined\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tcolor: color\n\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\tfield: color,\n\t\t\t\t\t\t\t\t\t\tlegend: { orient: \"bottom\", title: color_title },\n\t\t\t\t\t\t\t\t\t\tscale:\n\t\t\t\t\t\t\t\t\t\t\tvalue.datatypes[color] === \"nominal\"\n\t\t\t\t\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdomain: unique_colors,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trange: color_map\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? unique_colors.map((c) => color_map[c])\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trange: [\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t100, 200, 300, 400, 500, 600, 700, 800, 900\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t].map((n) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcomputed_style.getPropertyValue(\"--primary-\" + n)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tinterpolate: \"hsl\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[color]\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t: undefined,\n\t\t\t\t\t\t\ttooltip:\n\t\t\t\t\t\t\t\ttooltip == \"none\"\n\t\t\t\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t\t\t\t: [\n\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\tfield: y,\n\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[y],\n\t\t\t\t\t\t\t\t\t\t\t\taggregate: aggregating ? _y_aggregate : undefined,\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: y_title || y\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\tfield: x,\n\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[x],\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: x_title || x,\n\t\t\t\t\t\t\t\t\t\t\t\tformat: x_temporal ? \"%Y-%m-%d %H:%M:%S\" : undefined,\n\t\t\t\t\t\t\t\t\t\t\t\tbin: _x_bin ? { step: _x_bin } : undefined\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t...(color\n\t\t\t\t\t\t\t\t\t\t\t\t? [\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield: color,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[color]\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t\t\t\t: []),\n\t\t\t\t\t\t\t\t\t\t\t...(tooltip === \"axis\"\n\t\t\t\t\t\t\t\t\t\t\t\t? []\n\t\t\t\t\t\t\t\t\t\t\t\t: value?.columns\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(col) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcol !== x &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcol !== y &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcol !== color &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(tooltip === \"all\" || tooltip.includes(col))\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((column) => ({\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield: column,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype: value.datatypes[column]\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})))\n\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t},\n\t\t\t\t\t\tstrokeDash: {},\n\t\t\t\t\t\tmark: { clip: true, type: mode === \"hover\" ? \"point\" : value.mark },\n\t\t\t\t\t\tname: mode\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t),\n\t\t\t// @ts-ignore\n\t\t\tparams: [\n\t\t\t\t...(value.mark === \"line\"\n\t\t\t\t\t? [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"hoverPlot\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tclear: \"mouseout\",\n\t\t\t\t\t\t\t\t\tfields: color ? [color] : [],\n\t\t\t\t\t\t\t\t\tnearest: true,\n\t\t\t\t\t\t\t\t\ton: \"mouseover\",\n\t\t\t\t\t\t\t\t\ttype: \"point\" as \"point\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"hover\"]\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"hover\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tclear: \"mouseout\",\n\t\t\t\t\t\t\t\t\tnearest: true,\n\t\t\t\t\t\t\t\t\ton: \"mouseover\",\n\t\t\t\t\t\t\t\t\ttype: \"point\" as \"point\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"hover\"]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t: []),\n\t\t\t\t...(_selectable\n\t\t\t\t\t? [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tname: \"brush\",\n\t\t\t\t\t\t\t\tselect: {\n\t\t\t\t\t\t\t\t\tencodings: [\"x\"],\n\t\t\t\t\t\t\t\t\tmark: { fill: \"gray\", fillOpacity: 0.3, stroke: \"none\" },\n\t\t\t\t\t\t\t\t\ttype: \"interval\" as \"interval\"\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tviews: [\"plot\"]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t: [])\n\t\t\t],\n\t\t\twidth: chart_element.offsetWidth,\n\t\t\theight: height ? \"container\" : undefined,\n\t\t\ttitle: title || undefined\n\t\t};\n\t\t/* eslint-enable complexity */\n\t}\n\n\texport let label = \"Textbox\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let show_label: boolean;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | undefined = undefined;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tpadding={true}\n\t{height}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<BlockTitle {root} {show_label} info={undefined}>{label}</BlockTitle>\n\t{#if value && is_browser}\n\t\t<div bind:this={chart_element}></div>\n\n\t\t{#if caption}\n\t\t\t<p class=\"caption\">{caption}</p>\n\t\t{/if}\n\t{:else}\n\t\t<Empty unpadded_box={true}><LabelIcon /></Empty>\n\t{/if}\n</Block>\n\n<style>\n\tdiv {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\t:global(#vg-tooltip-element) {\n\t\tfont-family: var(--font) !important;\n\t\tfont-size: var(--text-xs) !important;\n\t\tbox-shadow: none !important;\n\t\tbackground-color: var(--block-background-fill) !important;\n\t\tborder: 1px solid var(--border-color-primary) !important;\n\t\tcolor: var(--body-text-color) !important;\n\t}\n\t:global(#vg-tooltip-element .key) {\n\t\tcolor: var(--body-text-color-subdued) !important;\n\t}\n\t.caption {\n\t\tpadding: 0 4px;\n\t\tmargin: 0;\n\t\ttext-align: center;\n\t}\n</style>\n"], "file": "assets/Index-ClXU3hrF.js"}