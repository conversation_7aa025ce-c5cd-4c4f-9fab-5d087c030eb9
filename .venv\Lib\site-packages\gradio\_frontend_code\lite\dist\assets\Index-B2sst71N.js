import{a as $,i as y,s as A,f as h,q as D,y as L,b as H,z as b,A as w,d as C,C as S,M as G,u as N,r as O,v as Q,k as g,t as m,l as z,o as J,a5 as K,O as R,Y as U,S as V,c as j,e as X,m as q,a0 as Z,a6 as x,h as ee,j as te,n as B,a7 as I,a8 as P}from"../lite.js";import se from"./Index-BHNCUXBa.js";function ne(n){let e,s,i,o,f,c,a,t;const l=n[10].default,u=D(l,n,n[9],null);return{c(){e=L("div"),s=L("button"),s.innerHTML='<div class="chevron svelte-y4v1h1"><span class="chevron-left svelte-y4v1h1"></span></div>',i=H(),o=L("div"),u&&u.c(),b(s,"class","toggle-button svelte-y4v1h1"),b(s,"aria-label","Toggle Sidebar"),b(o,"class","sidebar-content svelte-y4v1h1"),b(e,"class","sidebar svelte-y4v1h1"),b(e,"style",f="width: "+n[5]+"; "+n[0]+": calc("+n[5]+" * -1)"),w(e,"open",n[1]),w(e,"right",n[0]==="right"),w(e,"reduce-motion",n[3])},m(r,p){C(r,e,p),S(e,s),S(e,i),S(e,o),u&&u.m(o,null),n[12](e),c=!0,a||(t=G(s,"click",n[11]),a=!0)},p(r,[p]){u&&u.p&&(!c||p&512)&&N(u,l,r,r[9],c?Q(l,r[9],p,null):O(r[9]),null),(!c||p&1&&f!==(f="width: "+r[5]+"; "+r[0]+": calc("+r[5]+" * -1)"))&&b(e,"style",f),(!c||p&2)&&w(e,"open",r[1]),(!c||p&1)&&w(e,"right",r[0]==="right"),(!c||p&8)&&w(e,"reduce-motion",r[3])},i(r){c||(g(u,r),c=!0)},o(r){m(u,r),c=!1},d(r){r&&z(e),u&&u.d(r),n[12](null),a=!1,t()}}}function ie(n,e,s){let{$$slots:i={},$$scope:o}=e;const f=J();let{open:c=!0}=e,{width:a}=e,{position:t="left"}=e,l=!1,u=!1,r,p=0,E=typeof a=="number"?`${a}px`:a,v;function d(){if(!r.closest(".wrap"))return;const _=r.closest(".wrap")?.getBoundingClientRect();if(!_)return;const k=r.getBoundingClientRect(),M=t==="left"?_.left:window.innerWidth-_.right;p=Math.max(0,k.width-M+30)}K(()=>{r.closest(".wrap")?.classList.add("sidebar-parent"),d(),window.addEventListener("resize",d),(()=>{document.documentElement.style.setProperty("--overlap-amount",`${p}px`)})(),s(8,l=!0);const k=window.matchMedia("(prefers-reduced-motion: reduce)");s(3,v=k.matches);const M=F=>{s(3,v=F.matches)};return k.addEventListener("change",M),()=>{window.removeEventListener("resize",d),k.removeEventListener("change",M)}});const W=()=>{s(1,u=!u),f(u?"expand":"collapse")};function Y(_){R[_?"unshift":"push"](()=>{r=_,s(2,r)})}return n.$$set=_=>{"open"in _&&s(6,c=_.open),"width"in _&&s(7,a=_.width),"position"in _&&s(0,t=_.position),"$$scope"in _&&s(9,o=_.$$scope)},n.$$.update=()=>{n.$$.dirty&320&&l&&s(1,u=c)},[t,u,r,v,f,E,c,a,l,o,i,W,Y]}class oe extends ${constructor(e){super(),y(this,e,ie,ne,A,{open:6,width:7,position:0})}get open(){return this.$$.ctx[6]}set open(e){this.$$set({open:e}),h()}get width(){return this.$$.ctx[7]}set width(e){this.$$set({width:e}),h()}get position(){return this.$$.ctx[0]}set position(e){this.$$set({position:e}),h()}}function T(n){let e,s,i,o;function f(t){n[7](t)}function c(t){n[8](t)}let a={width:n[4],$$slots:{default:[le]},$$scope:{ctx:n}};return n[0]!==void 0&&(a.open=n[0]),n[1]!==void 0&&(a.position=n[1]),e=new oe({props:a}),R.push(()=>I(e,"open",f)),R.push(()=>I(e,"position",c)),e.$on("expand",n[9]),e.$on("collapse",n[10]),{c(){j(e.$$.fragment)},m(t,l){q(e,t,l),o=!0},p(t,l){const u={};l&16&&(u.width=t[4]),l&2048&&(u.$$scope={dirty:l,ctx:t}),!s&&l&1&&(s=!0,u.open=t[0],P(()=>s=!1)),!i&&l&2&&(i=!0,u.position=t[1],P(()=>i=!1)),e.$set(u)},i(t){o||(g(e.$$.fragment,t),o=!0)},o(t){m(e.$$.fragment,t),o=!1},d(t){B(e,t)}}}function ae(n){let e;const s=n[6].default,i=D(s,n,n[11],null);return{c(){i&&i.c()},m(o,f){i&&i.m(o,f),e=!0},p(o,f){i&&i.p&&(!e||f&2048)&&N(i,s,o,o[11],e?Q(s,o[11],f,null):O(o[11]),null)},i(o){e||(g(i,o),e=!0)},o(o){m(i,o),e=!1},d(o){i&&i.d(o)}}}function le(n){let e,s;return e=new se({props:{$$slots:{default:[ae]},$$scope:{ctx:n}}}),{c(){j(e.$$.fragment)},m(i,o){q(e,i,o),s=!0},p(i,o){const f={};o&2048&&(f.$$scope={dirty:o,ctx:i}),e.$set(f)},i(i){s||(g(e.$$.fragment,i),s=!0)},o(i){m(e.$$.fragment,i),s=!1},d(i){B(e,i)}}}function re(n){let e,s,i,o;const f=[{autoscroll:n[3].autoscroll},{i18n:n[3].i18n},n[2]];let c={};for(let t=0;t<f.length;t+=1)c=U(c,f[t]);e=new V({props:c});let a=n[5]&&T(n);return{c(){j(e.$$.fragment),s=H(),a&&a.c(),i=X()},m(t,l){q(e,t,l),C(t,s,l),a&&a.m(t,l),C(t,i,l),o=!0},p(t,[l]){const u=l&12?Z(f,[l&8&&{autoscroll:t[3].autoscroll},l&8&&{i18n:t[3].i18n},l&4&&x(t[2])]):{};e.$set(u),t[5]?a?(a.p(t,l),l&32&&g(a,1)):(a=T(t),a.c(),g(a,1),a.m(i.parentNode,i)):a&&(ee(),m(a,1,1,()=>{a=null}),te())},i(t){o||(g(e.$$.fragment,t),g(a),o=!0)},o(t){m(e.$$.fragment,t),m(a),o=!1},d(t){t&&(z(s),z(i)),B(e,t),a&&a.d(t)}}}function ue(n,e,s){let{$$slots:i={},$$scope:o}=e,{open:f=!0}=e,{position:c="left"}=e,{loading_status:a}=e,{gradio:t}=e,{width:l}=e,{visible:u=!0}=e;function r(d){f=d,s(0,f)}function p(d){c=d,s(1,c)}const E=()=>t.dispatch("expand"),v=()=>t.dispatch("collapse");return n.$$set=d=>{"open"in d&&s(0,f=d.open),"position"in d&&s(1,c=d.position),"loading_status"in d&&s(2,a=d.loading_status),"gradio"in d&&s(3,t=d.gradio),"width"in d&&s(4,l=d.width),"visible"in d&&s(5,u=d.visible),"$$scope"in d&&s(11,o=d.$$scope)},[f,c,a,t,l,u,i,r,p,E,v,o]}class de extends ${constructor(e){super(),y(this,e,ue,re,A,{open:0,position:1,loading_status:2,gradio:3,width:4,visible:5})}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),h()}get position(){return this.$$.ctx[1]}set position(e){this.$$set({position:e}),h()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),h()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),h()}get width(){return this.$$.ctx[4]}set width(e){this.$$set({width:e}),h()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),h()}}export{de as default};
//# sourceMappingURL=Index-B2sst71N.js.map
