import{D as v,av as ut,be as ct,bf as ft,bg as dt,bh as ht,a as S,i as A,s as R,E as M,z as s,d as T,C as _,l as H,U as gt,f as x,y as $,b as I,w as _t,A as tt,M as K,ay as et,h as lt,t as j,j as ot,k as z,x as mt,P as vt,Q as pt,bi as bt,V as kt,bj as nt,o as wt,a5 as yt,p as N,c as D,m as O,n as U,aq as st,b8 as $t,$ as qt,bk as Ct}from"../lite.js";import{c as Mt,f as it}from"./index-B9I6rkKj.js";function jt(i,t,e,n){if(!t)return v;const r=i.getBoundingClientRect();if(t.left===r.left&&t.right===r.right&&t.top===r.top&&t.bottom===r.bottom)return v;const{delay:c=0,duration:h=300,easing:o=ut,start:a=ct()+c,end:l=a+h,tick:d=v,css:w}=e(i,{from:t,to:r},n);let k=!0,g=!1,p;function q(){w&&(p=dt(i,0,1,h,c,o,w)),c||(g=!0)}function u(){w&&ht(i,p),k=!1}return ft(y=>{if(!g&&y>=a&&(g=!0),g&&y>=l&&(d(1,0),u()),!k)return!1;if(g){const C=y-a,E=0+1*o(C/h);d(E,1-E)}return!0}),q(),d(0,1),u}function zt(i){const t=getComputedStyle(i);if(t.position!=="absolute"&&t.position!=="fixed"){const{width:e,height:n}=t,r=i.getBoundingClientRect();i.style.position="absolute",i.style.width=e,i.style.height=n,Bt(i,r)}}function Bt(i,t){const e=i.getBoundingClientRect();if(t.left!==e.left||t.top!==e.top){const n=getComputedStyle(i),r=n.transform==="none"?"":n.transform;i.style.transform=`${r} translate(${t.left-e.left}px, ${t.top-e.top}px)`}}function xt(i){let t,e;return{c(){t=M("svg"),e=M("path"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),s(t,"fill","none"),s(t,"stroke","currentColor"),s(t,"viewBox","0 0 24 24"),s(t,"width","100%"),s(t,"height","100%"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"aria-hidden","true"),s(t,"stroke-width","2"),s(t,"stroke-linecap","round"),s(t,"stroke-linejoin","round")},m(n,r){T(n,t,r),_(t,e)},p:v,i:v,o:v,d(n){n&&H(t)}}}class Tt extends S{constructor(t){super(),A(this,t,null,xt,R,{})}}function Ht(i){let t,e;return{c(){t=M("svg"),e=M("path"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),s(t,"fill","none"),s(t,"stroke","currentColor"),s(t,"viewBox","0 0 24 24"),s(t,"width","100%"),s(t,"height","100%"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"aria-hidden","true"),s(t,"stroke-width","2"),s(t,"stroke-linecap","round"),s(t,"stroke-linejoin","round")},m(n,r){T(n,t,r),_(t,e)},p:v,i:v,o:v,d(n){n&&H(t)}}}class Lt extends S{constructor(t){super(),A(this,t,null,Ht,R,{})}}function St(i){let t,e;return{c(){t=M("svg"),e=M("path"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"d","M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"),s(t,"fill","none"),s(t,"stroke","currentColor"),s(t,"viewBox","0 0 24 24"),s(t,"width","100%"),s(t,"height","100%"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"aria-hidden","true"),s(t,"stroke-width","2"),s(t,"stroke-linecap","round"),s(t,"stroke-linejoin","round")},m(n,r){T(n,t,r),_(t,e)},p:v,i:v,o:v,d(n){n&&H(t)}}}class At extends S{constructor(t){super(),A(this,t,null,St,R,{})}}function Rt(i){let t,e;return{c(){t=M("svg"),e=M("path"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),s(t,"fill","none"),s(t,"stroke","currentColor"),s(t,"stroke-width","2"),s(t,"viewBox","0 0 24 24"),s(t,"width","100%"),s(t,"height","100%"),s(t,"xmlns","http://www.w3.org/2000/svg"),s(t,"aria-hidden","true"),s(t,"stroke-linecap","round"),s(t,"stroke-linejoin","round")},m(n,r){T(n,t,r),_(t,e)},p:v,i:v,o:v,d(n){n&&H(t)}}}class Et extends S{constructor(t){super(),A(this,t,null,Rt,R,{})}}function Ft(i,{from:t,to:e},n={}){const r=getComputedStyle(i),c=r.transform==="none"?"":r.transform,[h,o]=r.transformOrigin.split(" ").map(parseFloat),a=t.left+t.width*h/e.width-(e.left+h),l=t.top+t.height*o/e.height-(e.top+o),{delay:d=0,duration:w=g=>Math.sqrt(g)*120,easing:k=Mt}=n;return{delay:d,duration:gt(w)?w(Math.sqrt(a*a+l*l)):w,easing:k,css:(g,p)=>{const q=p*a,u=p*l,y=g+p*t.width/e.width,C=g+p*t.height/e.height;return`transform: ${c} translate(${q}px, ${u}px) scale(${y}, ${C});`}}}function It(i){let t,e;return t=new Tt({}),{c(){D(t.$$.fragment)},m(n,r){O(t,n,r),e=!0},i(n){e||(z(t.$$.fragment,n),e=!0)},o(n){j(t.$$.fragment,n),e=!1},d(n){U(t,n)}}}function Dt(i){let t,e;return t=new At({}),{c(){D(t.$$.fragment)},m(n,r){O(t,n,r),e=!0},i(n){e||(z(t.$$.fragment,n),e=!0)},o(n){j(t.$$.fragment,n),e=!1},d(n){U(t,n)}}}function Ot(i){let t,e;return t=new Lt({}),{c(){D(t.$$.fragment)},m(n,r){O(t,n,r),e=!0},i(n){e||(z(t.$$.fragment,n),e=!0)},o(n){j(t.$$.fragment,n),e=!1},d(n){U(t,n)}}}function Ut(i){let t,e;return t=new Et({}),{c(){D(t.$$.fragment)},m(n,r){O(t,n,r),e=!0},i(n){e||(z(t.$$.fragment,n),e=!0)},o(n){j(t.$$.fragment,n),e=!1},d(n){U(t,n)}}}function Vt(i){let t,e,n,r,c,h,o,a,l,d,w,k,g,p,q,u,y,C,E,L,V,P,Q,W,F,m,G,X;const Y=[Ut,Ot,Dt,It],B=[];function Z(f,b){return f[2]==="warning"?0:f[2]==="info"?1:f[2]==="success"?2:f[2]==="error"?3:-1}return~(n=Z(i))&&(r=B[n]=Y[n](i)),{c(){t=$("div"),e=$("div"),r&&r.c(),h=I(),o=$("div"),a=$("div"),l=_t(i[1]),w=I(),k=$("div"),q=I(),u=$("button"),y=$("span"),y.textContent="×",E=I(),L=$("div"),s(e,"class",c="toast-icon "+i[2]+" svelte-15qa81b"),s(a,"class",d="toast-title "+i[2]+" svelte-15qa81b"),s(k,"class",g="toast-text "+i[2]+" svelte-15qa81b"),s(o,"class",p="toast-details "+i[2]+" svelte-15qa81b"),s(y,"aria-hidden","true"),s(u,"class",C="toast-close "+i[2]+" svelte-15qa81b"),s(u,"type","button"),s(u,"aria-label","Close"),s(u,"data-testid","toast-close"),s(L,"class",V="timer "+i[2]+" svelte-15qa81b"),s(L,"style",P=`animation-duration: ${i[3]};`),s(t,"class",Q="toast-body "+i[2]+" svelte-15qa81b"),s(t,"role","alert"),s(t,"data-testid","toast-body"),tt(t,"hidden",!i[4])},m(f,b){T(f,t,b),_(t,e),~n&&B[n].m(e,null),_(t,h),_(t,o),_(o,a),_(a,l),_(o,w),_(o,k),k.innerHTML=i[0],_(t,q),_(t,u),_(u,y),_(t,E),_(t,L),m=!0,G||(X=[K(u,"click",i[5]),K(t,"click",et(i[9])),K(t,"keydown",et(i[10]))],G=!0)},p(f,[b]){let J=n;n=Z(f),n!==J&&(r&&(lt(),j(B[J],1,1,()=>{B[J]=null}),ot()),~n?(r=B[n],r||(r=B[n]=Y[n](f),r.c()),z(r,1),r.m(e,null)):r=null),(!m||b&4&&c!==(c="toast-icon "+f[2]+" svelte-15qa81b"))&&s(e,"class",c),(!m||b&2)&&mt(l,f[1]),(!m||b&4&&d!==(d="toast-title "+f[2]+" svelte-15qa81b"))&&s(a,"class",d),(!m||b&1)&&(k.innerHTML=f[0]),(!m||b&4&&g!==(g="toast-text "+f[2]+" svelte-15qa81b"))&&s(k,"class",g),(!m||b&4&&p!==(p="toast-details "+f[2]+" svelte-15qa81b"))&&s(o,"class",p),(!m||b&4&&C!==(C="toast-close "+f[2]+" svelte-15qa81b"))&&s(u,"class",C),(!m||b&4&&V!==(V="timer "+f[2]+" svelte-15qa81b"))&&s(L,"class",V),(!m||b&8&&P!==(P=`animation-duration: ${f[3]};`))&&s(L,"style",P),(!m||b&4&&Q!==(Q="toast-body "+f[2]+" svelte-15qa81b"))&&s(t,"class",Q),(!m||b&20)&&tt(t,"hidden",!f[4])},i(f){m||(z(r),f&&vt(()=>{m&&(F&&F.end(1),W=pt(t,it,{duration:200,delay:100}),W.start())}),m=!0)},o(f){j(r),W&&W.invalidate(),f&&(F=bt(t,it,{duration:200})),m=!1},d(f){f&&H(t),~n&&B[n].d(),f&&F&&F.end(),G=!1,kt(X)}}}function Pt(i,t,e){let n,r,{title:c=""}=t,{message:h=""}=t,{type:o}=t,{id:a}=t,{duration:l=10}=t,{visible:d=!0}=t;const w=u=>{try{return!!u&&new URL(u,location.href).origin!==location.origin}catch{return!1}};nt.addHook("afterSanitizeAttributes",function(u){"target"in u&&w(u.getAttribute("href"))&&(u.setAttribute("target","_blank"),u.setAttribute("rel","noopener noreferrer"))});const k=wt();function g(){k("close",a)}yt(()=>{l!==null&&setTimeout(()=>{g()},l*1e3)});function p(u){N.call(this,i,u)}function q(u){N.call(this,i,u)}return i.$$set=u=>{"title"in u&&e(1,c=u.title),"message"in u&&e(0,h=u.message),"type"in u&&e(2,o=u.type),"id"in u&&e(7,a=u.id),"duration"in u&&e(6,l=u.duration),"visible"in u&&e(8,d=u.visible)},i.$$.update=()=>{i.$$.dirty&1&&e(0,h=nt.sanitize(h)),i.$$.dirty&256&&e(4,n=d),i.$$.dirty&64&&e(6,l=l||null),i.$$.dirty&64&&e(3,r=`${l||0}s`)},[h,c,o,r,n,g,l,a,d,p,q]}class Qt extends S{constructor(t){super(),A(this,t,Pt,Vt,R,{title:1,message:0,type:2,id:7,duration:6,visible:8})}get title(){return this.$$.ctx[1]}set title(t){this.$$set({title:t}),x()}get message(){return this.$$.ctx[0]}set message(t){this.$$set({message:t}),x()}get type(){return this.$$.ctx[2]}set type(t){this.$$set({type:t}),x()}get id(){return this.$$.ctx[7]}set id(t){this.$$set({id:t}),x()}get duration(){return this.$$.ctx[6]}set duration(t){this.$$set({duration:t}),x()}get visible(){return this.$$.ctx[8]}set visible(t){this.$$set({visible:t}),x()}}function rt(i,t,e){const n=i.slice();return n[2]=t[e].type,n[3]=t[e].title,n[4]=t[e].message,n[5]=t[e].id,n[6]=t[e].duration,n[7]=t[e].visible,n}function at(i,t){let e,n,r,c,h=v,o;return n=new Qt({props:{type:t[2],title:t[3],message:t[4],duration:t[6],visible:t[7],id:t[5]}}),n.$on("close",t[1]),{key:i,first:null,c(){e=$("div"),D(n.$$.fragment),r=I(),qt(e,"width","100%"),this.first=e},m(a,l){T(a,e,l),O(n,e,null),_(e,r),o=!0},p(a,l){t=a;const d={};l&1&&(d.type=t[2]),l&1&&(d.title=t[3]),l&1&&(d.message=t[4]),l&1&&(d.duration=t[6]),l&1&&(d.visible=t[7]),l&1&&(d.id=t[5]),n.$set(d)},r(){c=e.getBoundingClientRect()},f(){zt(e),h()},a(){h(),h=jt(e,c,Ft,{duration:300})},i(a){o||(z(n.$$.fragment,a),o=!0)},o(a){j(n.$$.fragment,a),o=!1},d(a){a&&H(e),U(n)}}}function Wt(i){let t,e=[],n=new Map,r,c=st(i[0]);const h=o=>o[5];for(let o=0;o<c.length;o+=1){let a=rt(i,c,o),l=h(a);n.set(l,e[o]=at(l,a))}return{c(){t=$("div");for(let o=0;o<e.length;o+=1)e[o].c();s(t,"class","toast-wrap svelte-pu0yf1")},m(o,a){T(o,t,a);for(let l=0;l<e.length;l+=1)e[l]&&e[l].m(t,null);r=!0},p(o,[a]){if(a&1){c=st(o[0]),lt();for(let l=0;l<e.length;l+=1)e[l].r();e=$t(e,a,h,1,o,c,n,t,Ct,at,null,rt);for(let l=0;l<e.length;l+=1)e[l].a();ot()}},i(o){if(!r){for(let a=0;a<c.length;a+=1)z(e[a]);r=!0}},o(o){for(let a=0;a<e.length;a+=1)j(e[a]);r=!1},d(o){o&&H(t);for(let a=0;a<e.length;a+=1)e[a].d()}}}function Gt(i){i.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Jt(i,t,e){let{messages:n=[]}=t;function r(c){N.call(this,i,c)}return i.$$set=c=>{"messages"in c&&e(0,n=c.messages)},i.$$.update=()=>{i.$$.dirty&1&&Gt(n)},[n,r]}class Xt extends S{constructor(t){super(),A(this,t,Jt,Wt,R,{messages:0})}get messages(){return this.$$.ctx[0]}set messages(t){this.$$set({messages:t}),x()}}export{Xt as T};
//# sourceMappingURL=Toast-DP7Rbu7A.js.map
