import{a as ce,i as fe,s as _e,f as w,c as O,b as N,y as E,e as Xe,z as R,$ as S,m as U,d as A,M as L,k as P,h as he,t as j,j as de,l as I,n as V,V as me,o as De,a5 as Te,K as qe,aB as Le,w as Pe,x as Ye,aq as x,C as M,R as $,T as je,as as Ge,A as ee,N as Ne,p as te,O as W,B as Ae,Y as Ie,S as Ke,a7 as se,a0 as Oe,a6 as Ue,a8 as le}from"../lite.js";import{t as Q,E as Ve}from"./tinycolor-BpDABgnX.js";import{B as Fe}from"./BlockTitle-DvFB_De3.js";import{default as ft}from"./Example-DLWuukIu.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";function Je(t,e){const s=l=>{t&&!t.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("mousedown",s,!0),{destroy(){document.removeEventListener("mousedown",s,!0)}}}function ne(t){const e=t.s,s=t.v;let l=e*s;const u=t.h/60;let a=l*(1-Math.abs(u%2-1));const f=s-l;l=l+f,a=a+f;const b=Math.floor(u)%6,v=[l,a,f,f,a,l][b],_=[a,l,l,a,f,f][b],r=[f,f,a,l,l,a][b];return`rgba(${v*255}, ${_*255}, ${r*255}, ${t.a})`}function Qe(t,e){return e==="hex"?Q(t).toHexString():e==="rgb"?Q(t).toRgbString():Q(t).toHslString()}const{window:ie}=Le;function oe(t,e,s){const l=t.slice();return l[7]=e[s][0],l[1]=e[s][1],l}function We(t){let e;return{c(){e=Pe(t[7])},m(s,l){A(s,e,l)},p(s,l){l[0]&128&&Ye(e,s[7])},d(s){s&&I(e)}}}function ue(t){let e,s,l,u=`translate(${t[12][0]}px,${t[12][1]}px)`,a,f,b,v=`translateX(${t[14]}px)`,_,r,c,i,m,C,g,p,H,k,T,q,Y,G,d=t[9]&&ae(),X=x(t[21]),z=[];for(let h=0;h<X.length;h+=1)z[h]=re(oe(t,X,h));return{c(){e=E("div"),s=E("div"),l=E("div"),a=N(),f=E("div"),b=E("div"),_=N(),r=E("div"),c=E("button"),i=N(),m=E("div"),C=E("div"),g=E("input"),p=N(),H=E("button"),d&&d.c(),k=N(),T=E("div");for(let h=0;h<z.length;h+=1)z[h].c();R(l,"class","marker svelte-1oxhzww"),S(l,"transform",u),S(l,"background",t[1]),R(s,"class","color-gradient svelte-1oxhzww"),S(s,"--hue",t[13]),R(b,"class","marker svelte-1oxhzww"),S(b,"background","hsl("+t[13]+", 100%, 50%)"),S(b,"transform",v),R(f,"class","hue-slider svelte-1oxhzww"),R(c,"class","swatch svelte-1oxhzww"),S(c,"background",t[1]),R(g,"type","text"),R(g,"class","svelte-1oxhzww"),R(H,"class","eyedropper svelte-1oxhzww"),R(C,"class","input-wrap svelte-1oxhzww"),R(T,"class","buttons svelte-1oxhzww"),R(r,"class","input svelte-1oxhzww"),R(e,"class","color-picker svelte-1oxhzww")},m(h,n){A(h,e,n),M(e,s),M(s,l),t[28](s),M(e,a),M(e,f),M(f,b),t[29](f),M(e,_),M(e,r),M(r,c),M(r,i),M(r,m),M(m,C),M(C,g),$(g,t[8]),M(C,p),M(C,H),d&&d.m(H,null),M(m,k),M(m,T);for(let B=0;B<z.length;B+=1)z[B]&&z[B].m(T,null);q=!0,Y||(G=[L(s,"mousedown",t[16]),L(f,"mousedown",t[15]),L(c,"click",t[23]),L(g,"input",t[30]),L(g,"change",t[31]),L(H,"click",t[20]),L(e,"focus",t[25]),L(e,"blur",t[26]),je(Je.call(null,e,t[22]))],Y=!0)},p(h,n){if(n[0]&4096&&u!==(u=`translate(${h[12][0]}px,${h[12][1]}px)`)&&S(l,"transform",u),n[0]&2&&S(l,"background",h[1]),(!q||n[0]&8192)&&S(s,"--hue",h[13]),n[0]&8192&&S(b,"background","hsl("+h[13]+", 100%, 50%)"),n[0]&16384&&v!==(v=`translateX(${h[14]}px)`)&&S(b,"transform",v),n[0]&2&&S(c,"background",h[1]),n[0]&256&&g.value!==h[8]&&$(g,h[8]),h[9]?d?n[0]&512&&P(d,1):(d=ae(),d.c(),P(d,1),d.m(H,null)):d&&(he(),j(d,1,1,()=>{d=null}),de()),n[0]&2097153){X=x(h[21]);let B;for(B=0;B<X.length;B+=1){const F=oe(h,X,B);z[B]?z[B].p(F,n):(z[B]=re(F),z[B].c(),z[B].m(T,null))}for(;B<z.length;B+=1)z[B].d(1);z.length=X.length}},i(h){q||(P(d),q=!0)},o(h){j(d),q=!1},d(h){h&&I(e),t[28](null),t[29](null),d&&d.d(),Ge(z,h),Y=!1,me(G)}}}function ae(t){let e,s;return e=new Ve({}),{c(){O(e.$$.fragment)},m(l,u){U(e,l,u),s=!0},i(l){s||(P(e.$$.fragment,l),s=!0)},o(l){j(e.$$.fragment,l),s=!1},d(l){V(e,l)}}}function re(t){let e,s,l;function u(){return t[32](t[1])}return{c(){e=E("button"),e.textContent=`${t[7]}`,R(e,"class","button svelte-1oxhzww"),ee(e,"active",t[0]===t[1])},m(a,f){A(a,e,f),s||(l=L(e,"click",u),s=!0)},p(a,f){t=a,f[0]&2097153&&ee(e,"active",t[0]===t[1])},d(a){a&&I(e),s=!1,l()}}}function Ze(t){let e,s,l,u,a,f,b,v;e=new Fe({props:{root:t[6],show_label:t[5],info:t[3],$$slots:{default:[We]},$$scope:{ctx:t}}});let _=t[2]&&ue(t);return{c(){O(e.$$.fragment),s=N(),l=E("button"),u=N(),_&&_.c(),a=Xe(),R(l,"class","dialog-button svelte-1oxhzww"),l.disabled=t[4],S(l,"background",t[1])},m(r,c){U(e,r,c),A(r,s,c),A(r,l,c),A(r,u,c),_&&_.m(r,c),A(r,a,c),f=!0,b||(v=[L(ie,"mousemove",t[17]),L(ie,"mouseup",t[18]),L(l,"click",t[27])],b=!0)},p(r,c){const i={};c[0]&64&&(i.root=r[6]),c[0]&32&&(i.show_label=r[5]),c[0]&8&&(i.info=r[3]),c[0]&128|c[1]&8192&&(i.$$scope={dirty:c,ctx:r}),e.$set(i),(!f||c[0]&16)&&(l.disabled=r[4]),c[0]&2&&S(l,"background",r[1]),r[2]?_?(_.p(r,c),c[0]&4&&P(_,1)):(_=ue(r),_.c(),P(_,1),_.m(a.parentNode,a)):_&&(he(),j(_,1,1,()=>{_=null}),de())},i(r){f||(P(e.$$.fragment,r),P(_),f=!0)},o(r){j(e.$$.fragment,r),j(_),f=!1},d(r){r&&(I(s),I(l),I(u),I(a)),V(e,r),_&&_.d(r),b=!1,me(v)}}}function ye(t,e,s){let l,{value:u="#000000"}=e,{value_is_output:a=!1}=e,{label:f}=e,{info:b=void 0}=e,{disabled:v=!1}=e,{show_label:_=!0}=e,{root:r}=e,{current_mode:c="hex"}=e,{dialog_open:i=!1}=e,m=!1,C,g;const p=De();let H=[0,0],k=null,T=!1,q=[0,0],Y=0,G=0,d=null,X=!1;function z(o){d=o.currentTarget.getBoundingClientRect(),X=!0,h(o.clientX)}function h(o){if(!d)return;const D=Math.max(0,Math.min(o-d.left,d.width));s(14,G=D);const K=D/d.width*360;s(13,Y=K),s(1,u=ne({h:K,s:q[0],v:q[1],a:1}))}function n(o,D){if(!k)return;const K=Math.max(0,Math.min(o-k.left,k.width)),J=Math.max(0,Math.min(D-k.top,k.height));s(12,H=[K,J]);const y={h:Y*1,s:K/k.width,v:1-J/k.height,a:1};q=[y.s,y.v],s(1,u=ne(y))}function B(o){T=!0,k=o.currentTarget.getBoundingClientRect(),n(o.clientX,o.clientY)}function F(o){T&&n(o.clientX,o.clientY),X&&h(o.clientX)}function ge(){T=!1,X=!1}async function Z(o){if(T||X||(await Ne(),!o)||(!k&&C&&(k=C.getBoundingClientRect()),!d&&g&&(d=g.getBoundingClientRect()),!k||!d))return;const D=Q(o).toHsv(),K=D.s*k.width,J=(1-D.v)*k.height;s(12,H=[K,J]),q=[D.s,D.v],s(13,Y=D.h),s(14,G=D.h/360*d.width)}function be(){new EyeDropper().open().then(D=>{s(1,u=D.sRGBHex)})}const we=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];Te(async()=>{s(9,m=window!==void 0&&!!window.EyeDropper)});function ve(){s(2,i=!1)}function ke(){p("change",u),a||p("input")}qe(()=>{s(24,a=!1)});function ze(){p("selected",l),p("close")}function Be(o){te.call(this,t,o)}function Ce(o){te.call(this,t,o)}const pe=()=>{Z(u),s(2,i=!i)};function Me(o){W[o?"unshift":"push"](()=>{C=o,s(10,C)})}function Ee(o){W[o?"unshift":"push"](()=>{g=o,s(11,g)})}function Re(){l=this.value,s(8,l),s(1,u),s(0,c)}const Se=o=>s(1,u=o.currentTarget.value),He=o=>s(0,c=o);return t.$$set=o=>{"value"in o&&s(1,u=o.value),"value_is_output"in o&&s(24,a=o.value_is_output),"label"in o&&s(7,f=o.label),"info"in o&&s(3,b=o.info),"disabled"in o&&s(4,v=o.disabled),"show_label"in o&&s(5,_=o.show_label),"root"in o&&s(6,r=o.root),"current_mode"in o&&s(0,c=o.current_mode),"dialog_open"in o&&s(2,i=o.dialog_open)},t.$$.update=()=>{t.$$.dirty[0]&3&&s(8,l=Qe(u,c)),t.$$.dirty[0]&256&&l&&p("selected",l),t.$$.dirty[0]&2&&Z(u),t.$$.dirty[0]&2&&ke()},[c,u,i,b,v,_,r,f,l,m,C,g,H,Y,G,z,B,F,ge,Z,be,we,ve,ze,a,Be,Ce,pe,Me,Ee,Re,Se,He]}class xe extends ce{constructor(e){super(),fe(this,e,ye,Ze,_e,{value:1,value_is_output:24,label:7,info:3,disabled:4,show_label:5,root:6,current_mode:0,dialog_open:2},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[24]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),w()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),w()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),w()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),w()}get current_mode(){return this.$$.ctx[0]}set current_mode(e){this.$$set({current_mode:e}),w()}get dialog_open(){return this.$$.ctx[2]}set dialog_open(e){this.$$set({dialog_open:e}),w()}}const $e=xe;function et(t){let e,s,l,u,a,f;const b=[{autoscroll:t[13].autoscroll},{i18n:t[13].i18n},t[11]];let v={};for(let i=0;i<b.length;i+=1)v=Ie(v,b[i]);e=new Ke({props:v}),e.$on("clear_status",t[16]);function _(i){t[17](i)}function r(i){t[18](i)}let c={root:t[12],label:t[2],info:t[3],show_label:t[7],disabled:!t[14]||t[15]};return t[0]!==void 0&&(c.value=t[0]),t[1]!==void 0&&(c.value_is_output=t[1]),l=new $e({props:c}),W.push(()=>se(l,"value",_)),W.push(()=>se(l,"value_is_output",r)),l.$on("change",t[19]),l.$on("input",t[20]),l.$on("submit",t[21]),l.$on("blur",t[22]),l.$on("focus",t[23]),{c(){O(e.$$.fragment),s=N(),O(l.$$.fragment)},m(i,m){U(e,i,m),A(i,s,m),U(l,i,m),f=!0},p(i,m){const C=m&10240?Oe(b,[m&8192&&{autoscroll:i[13].autoscroll},m&8192&&{i18n:i[13].i18n},m&2048&&Ue(i[11])]):{};e.$set(C);const g={};m&4096&&(g.root=i[12]),m&4&&(g.label=i[2]),m&8&&(g.info=i[3]),m&128&&(g.show_label=i[7]),m&49152&&(g.disabled=!i[14]||i[15]),!u&&m&1&&(u=!0,g.value=i[0],le(()=>u=!1)),!a&&m&2&&(a=!0,g.value_is_output=i[1],le(()=>a=!1)),l.$set(g)},i(i){f||(P(e.$$.fragment,i),P(l.$$.fragment,i),f=!0)},o(i){j(e.$$.fragment,i),j(l.$$.fragment,i),f=!1},d(i){i&&I(s),V(e,i),V(l,i)}}}function tt(t){let e,s;return e=new Ae({props:{visible:t[6],elem_id:t[4],elem_classes:t[5],container:t[8],scale:t[9],min_width:t[10],$$slots:{default:[et]},$$scope:{ctx:t}}}),{c(){O(e.$$.fragment)},m(l,u){U(e,l,u),s=!0},p(l,[u]){const a={};u&64&&(a.visible=l[6]),u&16&&(a.elem_id=l[4]),u&32&&(a.elem_classes=l[5]),u&256&&(a.container=l[8]),u&512&&(a.scale=l[9]),u&1024&&(a.min_width=l[10]),u&16840847&&(a.$$scope={dirty:u,ctx:l}),e.$set(a)},i(l){s||(P(e.$$.fragment,l),s=!0)},o(l){j(e.$$.fragment,l),s=!1},d(l){V(e,l)}}}function st(t,e,s){let{label:l="ColorPicker"}=e,{info:u=void 0}=e,{elem_id:a=""}=e,{elem_classes:f=[]}=e,{visible:b=!0}=e,{value:v}=e,{value_is_output:_=!1}=e,{show_label:r}=e,{container:c=!0}=e,{scale:i=null}=e,{min_width:m=void 0}=e,{loading_status:C}=e,{root:g}=e,{gradio:p}=e,{interactive:H}=e,{disabled:k=!1}=e;const T=()=>p.dispatch("clear_status",C);function q(n){v=n,s(0,v)}function Y(n){_=n,s(1,_)}const G=()=>p.dispatch("change"),d=()=>p.dispatch("input"),X=()=>p.dispatch("submit"),z=()=>p.dispatch("blur"),h=()=>p.dispatch("focus");return t.$$set=n=>{"label"in n&&s(2,l=n.label),"info"in n&&s(3,u=n.info),"elem_id"in n&&s(4,a=n.elem_id),"elem_classes"in n&&s(5,f=n.elem_classes),"visible"in n&&s(6,b=n.visible),"value"in n&&s(0,v=n.value),"value_is_output"in n&&s(1,_=n.value_is_output),"show_label"in n&&s(7,r=n.show_label),"container"in n&&s(8,c=n.container),"scale"in n&&s(9,i=n.scale),"min_width"in n&&s(10,m=n.min_width),"loading_status"in n&&s(11,C=n.loading_status),"root"in n&&s(12,g=n.root),"gradio"in n&&s(13,p=n.gradio),"interactive"in n&&s(14,H=n.interactive),"disabled"in n&&s(15,k=n.disabled)},[v,_,l,u,a,f,b,r,c,i,m,C,g,p,H,k,T,q,Y,G,d,X,z,h]}class at extends ce{constructor(e){super(),fe(this,e,st,tt,_e,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,value:0,value_is_output:1,show_label:7,container:8,scale:9,min_width:10,loading_status:11,root:12,gradio:13,interactive:14,disabled:15})}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),w()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),w()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),w()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),w()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),w()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),w()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),w()}get disabled(){return this.$$.ctx[15]}set disabled(e){this.$$set({disabled:e}),w()}}export{$e as BaseColorPicker,ft as BaseExample,at as default};
//# sourceMappingURL=Index-BkGbjVqe.js.map
