import{a as x,i as ee,s as le,E as fe,z as _,d as v,C as M,D as W,l as w,bl as he,f as E,y as N,o as Je,aq as A,b as B,as as Q,w as P,x as X,M as T,V as U,e as G,A as H,$ as D,h as Z,t as z,j as K,k as p,a5 as Ml,O as ce,a7 as ue,c as q,m as F,a8 as _e,n as Y,B as Qe,Y as We,S as Xe,a0 as $e,a6 as xe}from"../lite.js";import{g as el}from"./color-DS9FAt0c.js";import{B as ll}from"./BlockLabel-DWW9BWN3.js";import{E as nl}from"./Empty-Bzq0Ew6m.js";function Vl(l){let e,n,t;return{c(){e=fe("svg"),n=fe("path"),t=fe("path"),_(n,"fill","currentColor"),_(n,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),_(t,"fill","currentColor"),_(t,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),_(e,"aria-hidden","true"),_(e,"role","img"),_(e,"class","iconify iconify--carbon"),_(e,"width","100%"),_(e,"height","100%"),_(e,"preserveAspectRatio","xMidYMid meet"),_(e,"viewBox","0 0 32 32")},m(o,i){v(o,e,i),M(e,n),M(e,t)},p:W,i:W,o:W,d(o){o&&w(e)}}}class ie extends x{constructor(e){super(),ee(this,e,null,Vl,le,{})}}function de(l,e,n){if(!n){var t=document.createElement("canvas");n=t.getContext("2d")}n.fillStyle=l,n.fillRect(0,0,1,1);const[o,i,s]=n.getImageData(0,0,1,1).data;return n.clearRect(0,0,1,1),`rgba(${o}, ${i}, ${s}, ${255/e})`}function tl(l,e,n,t){for(const o in l){const i=l[o].trim();i in he?e[o]=he[i]:e[o]={primary:n?de(l[o],1,t):l[o],secondary:n?de(l[o],.5,t):l[o]}}}function sl(l,e){let n=[],t=null,o=null;for(const i of l)o===i.class_or_confidence?t=t?t+i.token:i.token:(t!==null&&n.push({token:t,class_or_confidence:o}),t=i.token,o=i.class_or_confidence);return t!==null&&n.push({token:t,class_or_confidence:o}),n}function ge(l,e,n){const t=l.slice();t[18]=e[n];const o=typeof t[18].class_or_confidence=="string"?parseInt(t[18].class_or_confidence):t[18].class_or_confidence;return t[27]=o,t}function me(l,e,n){const t=l.slice();return t[18]=e[n],t[20]=n,t}function be(l,e,n){const t=l.slice();return t[21]=e[n],t[23]=n,t}function ke(l,e,n){const t=l.slice();return t[24]=e[n][0],t[25]=e[n][1],t[20]=n,t}function Ll(l){let e,n,t=l[1]&&pe(),o=A(l[0]),i=[];for(let s=0;s<o.length;s+=1)i[s]=ve(ge(l,o,s));return{c(){t&&t.c(),e=B(),n=N("div");for(let s=0;s<i.length;s+=1)i[s].c();_(n,"class","textfield svelte-ju12zg"),_(n,"data-testid","highlighted-text:textfield")},m(s,a){t&&t.m(s,a),v(s,e,a),v(s,n,a);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(n,null)},p(s,a){if(s[1]?t||(t=pe(),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),a&1){o=A(s[0]);let r;for(r=0;r<o.length;r+=1){const c=ge(s,o,r);i[r]?i[r].p(c,a):(i[r]=ve(c),i[r].c(),i[r].m(n,null))}for(;r<i.length;r+=1)i[r].d(1);i.length=o.length}},d(s){s&&(w(e),w(n)),t&&t.d(s),Q(i,s)}}}function Hl(l){let e,n,t=l[1]&&we(l),o=A(l[0]),i=[];for(let s=0;s<o.length;s+=1)i[s]=Te(me(l,o,s));return{c(){t&&t.c(),e=B(),n=N("div");for(let s=0;s<i.length;s+=1)i[s].c();_(n,"class","textfield svelte-ju12zg")},m(s,a){t&&t.m(s,a),v(s,e,a),v(s,n,a);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(n,null)},p(s,a){if(s[1]?t?t.p(s,a):(t=we(s),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),a&223){o=A(s[0]);let r;for(r=0;r<o.length;r+=1){const c=me(s,o,r);i[r]?i[r].p(c,a):(i[r]=Te(c),i[r].c(),i[r].m(n,null))}for(;r<i.length;r+=1)i[r].d(1);i.length=o.length}},d(s){s&&(w(e),w(n)),t&&t.d(s),Q(i,s)}}}function pe(l){let e;return{c(){e=N("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",_(e,"class","color-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:color-legend")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function ve(l){let e,n,t=l[18].token+"",o,i,s;return{c(){e=N("span"),n=N("span"),o=P(t),i=B(),_(n,"class","text svelte-ju12zg"),_(e,"class","textspan score-text svelte-ju12zg"),_(e,"style",s="background-color: rgba("+(l[27]&&l[27]<0?"128, 90, 213,"+-l[27]:"239, 68, 60,"+l[27])+")")},m(a,r){v(a,e,r),M(e,n),M(n,o),M(e,i)},p(a,r){r&1&&t!==(t=a[18].token+"")&&X(o,t),r&1&&s!==(s="background-color: rgba("+(a[27]&&a[27]<0?"128, 90, 213,"+-a[27]:"239, 68, 60,"+a[27])+")")&&_(e,"style",s)},d(a){a&&w(e)}}}function we(l){let e,n=A(Object.entries(l[6])),t=[];for(let o=0;o<n.length;o+=1)t[o]=ye(ke(l,n,o));return{c(){e=N("div");for(let o=0;o<t.length;o+=1)t[o].c();_(e,"class","category-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:category-legend")},m(o,i){v(o,e,i);for(let s=0;s<t.length;s+=1)t[s]&&t[s].m(e,null)},p(o,i){if(i&832){n=A(Object.entries(o[6]));let s;for(s=0;s<n.length;s+=1){const a=ke(o,n,s);t[s]?t[s].p(a,i):(t[s]=ye(a),t[s].c(),t[s].m(e,null))}for(;s<t.length;s+=1)t[s].d(1);t.length=n.length}},d(o){o&&w(e),Q(t,o)}}}function ye(l){let e,n=l[24]+"",t,o,i,s;function a(){return l[11](l[24])}function r(){return l[12](l[24])}return{c(){e=N("div"),t=P(n),o=B(),_(e,"class","category-label svelte-ju12zg"),_(e,"style","background-color:"+l[25].secondary)},m(c,f){v(c,e,f),M(e,t),M(e,o),i||(s=[T(e,"mouseover",a),T(e,"focus",r),T(e,"mouseout",l[13]),T(e,"blur",l[14])],i=!0)},p(c,f){l=c},d(c){c&&w(e),i=!1,U(s)}}}function je(l){let e,n,t=l[21]+"",o,i,s,a,r=!l[1]&&l[2]&&l[18].class_or_confidence!==null&&ze(l);function c(){return l[15](l[20],l[18])}return{c(){e=N("span"),n=N("span"),o=P(t),i=B(),r&&r.c(),_(n,"class","text svelte-ju12zg"),H(n,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),_(e,"class","textspan svelte-ju12zg"),H(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),H(e,"hl",l[18].class_or_confidence!==null),H(e,"selectable",l[3]),D(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},m(f,m){v(f,e,m),M(e,n),M(n,o),M(e,i),r&&r.m(e,null),s||(a=T(e,"click",c),s=!0)},p(f,m){l=f,m&1&&t!==(t=l[21]+"")&&X(o,t),m&65&&H(n,"no-label",l[18].class_or_confidence===null||!l[6][l[18].class_or_confidence]),!l[1]&&l[2]&&l[18].class_or_confidence!==null?r?r.p(l,m):(r=ze(l),r.c(),r.m(e,null)):r&&(r.d(1),r=null),m&17&&H(e,"no-cat",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence),m&1&&H(e,"hl",l[18].class_or_confidence!==null),m&8&&H(e,"selectable",l[3]),m&17&&D(e,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].secondary)},d(f){f&&w(e),r&&r.d(),s=!1,a()}}}function ze(l){let e,n,t=l[18].class_or_confidence+"",o;return{c(){e=P(` 
								`),n=N("span"),o=P(t),_(n,"class","label svelte-ju12zg"),D(n,"background-color",l[18].class_or_confidence===null||l[4]&&l[4]!==l[18].class_or_confidence?"":l[6][l[18].class_or_confidence].primary)},m(i,s){v(i,e,s),v(i,n,s),M(n,o)},p(i,s){s&1&&t!==(t=i[18].class_or_confidence+"")&&X(o,t),s&17&&D(n,"background-color",i[18].class_or_confidence===null||i[4]&&i[4]!==i[18].class_or_confidence?"":i[6][i[18].class_or_confidence].primary)},d(i){i&&(w(e),w(n))}}}function Se(l){let e;return{c(){e=N("br")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function Ee(l){let e=l[21].trim()!=="",n,t=l[23]<se(l[18].token).length-1,o,i=e&&je(l),s=t&&Se();return{c(){i&&i.c(),n=B(),s&&s.c(),o=G()},m(a,r){i&&i.m(a,r),v(a,n,r),s&&s.m(a,r),v(a,o,r)},p(a,r){r&1&&(e=a[21].trim()!==""),e?i?i.p(a,r):(i=je(a),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null),r&1&&(t=a[23]<se(a[18].token).length-1),t?s||(s=Se(),s.c(),s.m(o.parentNode,o)):s&&(s.d(1),s=null)},d(a){a&&(w(n),w(o)),i&&i.d(a),s&&s.d(a)}}}function Te(l){let e,n=A(se(l[18].token)),t=[];for(let o=0;o<n.length;o+=1)t[o]=Ee(be(l,n,o));return{c(){for(let o=0;o<t.length;o+=1)t[o].c();e=G()},m(o,i){for(let s=0;s<t.length;s+=1)t[s]&&t[s].m(o,i);v(o,e,i)},p(o,i){if(i&223){n=A(se(o[18].token));let s;for(s=0;s<n.length;s+=1){const a=be(o,n,s);t[s]?t[s].p(a,i):(t[s]=Ee(a),t[s].c(),t[s].m(e.parentNode,e))}for(;s<t.length;s+=1)t[s].d(1);t.length=n.length}},d(o){o&&w(e),Q(t,o)}}}function Bl(l){let e;function n(i,s){return i[5]==="categories"?Hl:Ll}let t=n(l),o=t(l);return{c(){e=N("div"),o.c(),_(e,"class","container svelte-ju12zg")},m(i,s){v(i,e,s),o.m(e,null)},p(i,[s]){t===(t=n(i))&&o?o.p(i,s):(o.d(1),o=t(i),o&&(o.c(),o.m(e,null)))},i:W,o:W,d(i){i&&w(e),o.d()}}}function se(l){return l.split(`
`)}function Il(l,e,n){const t=typeof document<"u";let{value:o=[]}=e,{show_legend:i=!1}=e,{show_inline_category:s=!0}=e,{color_map:a={}}=e,{selectable:r=!1}=e,c,f={},m="";const y=Je();let b;function u(k){n(4,m=k)}function d(){n(4,m="")}const S=k=>u(k),V=k=>u(k),O=()=>d(),g=()=>d(),C=(k,L)=>{y("select",{index:k,value:[L.token,L.class_or_confidence]})};return l.$$set=k=>{"value"in k&&n(0,o=k.value),"show_legend"in k&&n(1,i=k.show_legend),"show_inline_category"in k&&n(2,s=k.show_inline_category),"color_map"in k&&n(10,a=k.color_map),"selectable"in k&&n(3,r=k.selectable)},l.$$.update=()=>{if(l.$$.dirty&1025){if(a||n(10,a={}),o.length>0){for(let k of o)if(k.class_or_confidence!==null)if(typeof k.class_or_confidence=="string"){if(n(5,b="categories"),!(k.class_or_confidence in a)){let L=el(Object.keys(a).length);n(10,a[k.class_or_confidence]=L,a)}}else n(5,b="scores")}tl(a,f,t,c)}},[o,i,s,r,m,b,f,y,u,d,a,S,V,O,g,C]}class Rl extends x{constructor(e){super(),ee(this,e,Il,Bl,le,{value:0,show_legend:1,show_inline_category:2,color_map:10,selectable:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),E()}get show_inline_category(){return this.$$.ctx[2]}set show_inline_category(e){this.$$set({show_inline_category:e}),E()}get color_map(){return this.$$.ctx[10]}set color_map(e){this.$$set({color_map:e}),E()}get selectable(){return this.$$.ctx[3]}set selectable(e){this.$$set({selectable:e}),E()}}const Al=Rl;function Dl(l){let e,n,t,o;return{c(){e=N("input"),_(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,_(e,"type","number"),_(e,"step","0.1"),_(e,"style",n="background-color: rgba("+(typeof l[1]=="number"&&l[1]<0?"128, 90, 213,"+-l[1]:"239, 68, 60,"+l[1])+")"),e.value=l[1],D(e,"width","7ch")},m(i,s){v(i,e,s),e.focus(),t||(o=[T(e,"input",l[8]),T(e,"blur",l[14]),T(e,"keydown",l[15])],t=!0)},p(i,s){s&2&&n!==(n="background-color: rgba("+(typeof i[1]=="number"&&i[1]<0?"128, 90, 213,"+-i[1]:"239, 68, 60,"+i[1])+")")&&_(e,"style",n),s&2&&e.value!==i[1]&&(e.value=i[1]);const a=s&2;(s&2||a)&&D(e,"width","7ch")},d(i){i&&w(e),t=!1,U(o)}}}function ql(l){let e,n,t,o;return{c(){e=N("input"),_(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,_(e,"id",n=`label-input-${l[3]}`),_(e,"type","text"),_(e,"placeholder","label"),e.value=l[1],D(e,"background-color",l[1]===null||l[2]&&l[2]!==l[1]?"":l[6][l[1]].primary),D(e,"width",l[7]?l[7].toString()?.length+4+"ch":"8ch")},m(i,s){v(i,e,s),e.focus(),t||(o=[T(e,"input",l[8]),T(e,"blur",l[12]),T(e,"keydown",l[13]),T(e,"focus",Yl)],t=!0)},p(i,s){s&8&&n!==(n=`label-input-${i[3]}`)&&_(e,"id",n),s&2&&e.value!==i[1]&&(e.value=i[1]),s&70&&D(e,"background-color",i[1]===null||i[2]&&i[2]!==i[1]?"":i[6][i[1]].primary),s&128&&D(e,"width",i[7]?i[7].toString()?.length+4+"ch":"8ch")},d(i){i&&w(e),t=!1,U(o)}}}function Fl(l){let e;function n(i,s){return i[5]?Dl:ql}let t=n(l),o=t(l);return{c(){o.c(),e=G()},m(i,s){o.m(i,s),v(i,e,s)},p(i,[s]){t===(t=n(i))&&o?o.p(i,s):(o.d(1),o=t(i),o&&(o.c(),o.m(e.parentNode,e)))},i:W,o:W,d(i){i&&w(e),o.d(i)}}}function Yl(l){let e=l.target;e&&e.placeholder&&(e.placeholder="")}function Zl(l,e,n){let{value:t}=e,{category:o}=e,{active:i}=e,{labelToEdit:s}=e,{indexOfLabel:a}=e,{text:r}=e,{handleValueChange:c}=e,{isScoresMode:f=!1}=e,{_color_map:m}=e,y=o;function b(g){let C=g.target;C&&n(7,y=C.value)}function u(g,C,k){let L=g.target;n(10,t=[...t.slice(0,C),{token:k,class_or_confidence:L.value===""?null:f?Number(L.value):L.value},...t.slice(C+1)]),c()}const d=g=>u(g,a,r),S=g=>{g.key==="Enter"&&(u(g,a,r),n(0,s=-1))},V=g=>u(g,a,r),O=g=>{g.key==="Enter"&&(u(g,a,r),n(0,s=-1))};return l.$$set=g=>{"value"in g&&n(10,t=g.value),"category"in g&&n(1,o=g.category),"active"in g&&n(2,i=g.active),"labelToEdit"in g&&n(0,s=g.labelToEdit),"indexOfLabel"in g&&n(3,a=g.indexOfLabel),"text"in g&&n(4,r=g.text),"handleValueChange"in g&&n(11,c=g.handleValueChange),"isScoresMode"in g&&n(5,f=g.isScoresMode),"_color_map"in g&&n(6,m=g._color_map)},[s,o,i,a,r,f,m,y,b,u,t,c,d,S,V,O]}class ol extends x{constructor(e){super(),ee(this,e,Zl,Fl,le,{value:10,category:1,active:2,labelToEdit:0,indexOfLabel:3,text:4,handleValueChange:11,isScoresMode:5,_color_map:6})}get value(){return this.$$.ctx[10]}set value(e){this.$$set({value:e}),E()}get category(){return this.$$.ctx[1]}set category(e){this.$$set({category:e}),E()}get active(){return this.$$.ctx[2]}set active(e){this.$$set({active:e}),E()}get labelToEdit(){return this.$$.ctx[0]}set labelToEdit(e){this.$$set({labelToEdit:e}),E()}get indexOfLabel(){return this.$$.ctx[3]}set indexOfLabel(e){this.$$set({indexOfLabel:e}),E()}get text(){return this.$$.ctx[4]}set text(e){this.$$set({text:e}),E()}get handleValueChange(){return this.$$.ctx[11]}set handleValueChange(e){this.$$set({handleValueChange:e}),E()}get isScoresMode(){return this.$$.ctx[5]}set isScoresMode(e){this.$$set({isScoresMode:e}),E()}get _color_map(){return this.$$.ctx[6]}set _color_map(e){this.$$set({_color_map:e}),E()}}function Ne(l,e,n){const t=l.slice();t[45]=e[n].token,t[46]=e[n].class_or_confidence,t[48]=n;const o=typeof t[46]=="string"?parseInt(t[46]):t[46];return t[54]=o,t}function Oe(l,e,n){const t=l.slice();return t[45]=e[n].token,t[46]=e[n].class_or_confidence,t[48]=n,t}function Ce(l,e,n){const t=l.slice();return t[49]=e[n],t[51]=n,t}function Me(l,e,n){const t=l.slice();return t[46]=e[n][0],t[52]=e[n][1],t[48]=n,t}function Kl(l){let e,n,t,o=l[1]&&Ve(),i=A(l[0]),s=[];for(let r=0;r<i.length;r+=1)s[r]=Be(Ne(l,i,r));const a=r=>z(s[r],1,1,()=>{s[r]=null});return{c(){o&&o.c(),e=B(),n=N("div");for(let r=0;r<s.length;r+=1)s[r].c();_(n,"class","textfield svelte-1ozsnjl"),_(n,"data-testid","highlighted-text:textfield")},m(r,c){o&&o.m(r,c),v(r,e,c),v(r,n,c);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(n,null);t=!0},p(r,c){if(r[1]?o||(o=Ve(),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null),c[0]&889){i=A(r[0]);let f;for(f=0;f<i.length;f+=1){const m=Ne(r,i,f);s[f]?(s[f].p(m,c),p(s[f],1)):(s[f]=Be(m),s[f].c(),p(s[f],1),s[f].m(n,null))}for(Z(),f=i.length;f<s.length;f+=1)a(f);K()}},i(r){if(!t){for(let c=0;c<i.length;c+=1)p(s[c]);t=!0}},o(r){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)z(s[c]);t=!1},d(r){r&&(w(e),w(n)),o&&o.d(r),Q(s,r)}}}function Pl(l){let e,n,t,o=l[1]&&Ie(l),i=A(l[0]),s=[];for(let r=0;r<i.length;r+=1)s[r]=Pe(Oe(l,i,r));const a=r=>z(s[r],1,1,()=>{s[r]=null});return{c(){o&&o.c(),e=B(),n=N("div");for(let r=0;r<s.length;r+=1)s[r].c();_(n,"class","textfield svelte-1ozsnjl")},m(r,c){o&&o.m(r,c),v(r,e,c),v(r,n,c);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(n,null);t=!0},p(r,c){if(r[1]?o?o.p(r,c):(o=Ie(r),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null),c[0]&13183){i=A(r[0]);let f;for(f=0;f<i.length;f+=1){const m=Oe(r,i,f);s[f]?(s[f].p(m,c),p(s[f],1)):(s[f]=Pe(m),s[f].c(),p(s[f],1),s[f].m(n,null))}for(Z(),f=i.length;f<s.length;f+=1)a(f);K()}},i(r){if(!t){for(let c=0;c<i.length;c+=1)p(s[c]);t=!0}},o(r){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)z(s[c]);t=!1},d(r){r&&(w(e),w(n)),o&&o.d(r),Q(s,r)}}}function Ve(l){let e;return{c(){e=N("div"),e.innerHTML="<span>-1</span> <span>0</span> <span>+1</span>",_(e,"class","color-legend svelte-1ozsnjl"),_(e,"data-testid","highlighted-text:color-legend")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function Le(l){let e,n,t;function o(s){l[32](s)}let i={labelToEdit:l[6],_color_map:l[3],category:l[46],active:l[5],indexOfLabel:l[48],text:l[45],handleValueChange:l[9],isScoresMode:!0};return l[0]!==void 0&&(i.value=l[0]),e=new ol({props:i}),ce.push(()=>ue(e,"value",o)),{c(){q(e.$$.fragment)},m(s,a){F(e,s,a),t=!0},p(s,a){const r={};a[0]&64&&(r.labelToEdit=s[6]),a[0]&8&&(r._color_map=s[3]),a[0]&1&&(r.category=s[46]),a[0]&32&&(r.active=s[5]),a[0]&1&&(r.text=s[45]),!n&&a[0]&1&&(n=!0,r.value=s[0],_e(()=>n=!1)),e.$set(r)},i(s){t||(p(e.$$.fragment,s),t=!0)},o(s){z(e.$$.fragment,s),t=!1},d(s){Y(e,s)}}}function He(l){let e,n,t;function o(){return l[37](l[48])}function i(...s){return l[38](l[48],...s)}return{c(){e=N("span"),e.textContent="×",_(e,"class","label-clear-button svelte-1ozsnjl"),_(e,"role","button"),_(e,"aria-roledescription","Remove label from text"),_(e,"tabindex","0")},m(s,a){v(s,e,a),n||(t=[T(e,"click",o),T(e,"keydown",i)],n=!0)},p(s,a){l=s},d(s){s&&w(e),n=!1,U(t)}}}function Be(l){let e,n,t,o=l[45]+"",i,s,a,r,c,f,m,y,b=l[46]&&l[6]===l[48]&&Le(l);function u(){return l[33](l[48])}function d(){return l[34](l[48])}function S(){return l[35](l[48])}function V(...g){return l[36](l[48],...g)}let O=l[46]&&l[4]===l[48]&&He(l);return{c(){e=N("span"),n=N("span"),t=N("span"),i=P(o),s=B(),b&&b.c(),r=B(),O&&O.c(),c=B(),_(t,"class","text svelte-1ozsnjl"),_(n,"class","textspan score-text svelte-1ozsnjl"),_(n,"role","button"),_(n,"tabindex","0"),_(n,"style",a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"),H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),H(n,"hl",l[46]!==null),_(e,"class","score-text-container svelte-1ozsnjl")},m(g,C){v(g,e,C),M(e,n),M(n,t),M(t,i),M(n,s),b&&b.m(n,null),M(e,r),O&&O.m(e,null),M(e,c),f=!0,m||(y=[T(n,"mouseover",u),T(n,"focus",d),T(n,"click",S),T(n,"keydown",V)],m=!0)},p(g,C){l=g,(!f||C[0]&1)&&o!==(o=l[45]+"")&&X(i,o),l[46]&&l[6]===l[48]?b?(b.p(l,C),C[0]&65&&p(b,1)):(b=Le(l),b.c(),p(b,1),b.m(n,null)):b&&(Z(),z(b,1,1,()=>{b=null}),K()),(!f||C[0]&1&&a!==(a="background-color: rgba("+(l[54]&&l[54]<0?"128, 90, 213,"+-l[54]:"239, 68, 60,"+l[54])+")"))&&_(n,"style",a),(!f||C[0]&33)&&H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!f||C[0]&1)&&H(n,"hl",l[46]!==null),l[46]&&l[4]===l[48]?O?O.p(l,C):(O=He(l),O.c(),O.m(e,c)):O&&(O.d(1),O=null)},i(g){f||(p(b),f=!0)},o(g){z(b),f=!1},d(g){g&&w(e),b&&b.d(),O&&O.d(),m=!1,U(y)}}}function Ie(l){let e,n=l[3]&&Re(l);return{c(){e=N("div"),n&&n.c(),_(e,"class","class_or_confidence-legend svelte-1ozsnjl"),_(e,"data-testid","highlighted-text:class_or_confidence-legend")},m(t,o){v(t,e,o),n&&n.m(e,null)},p(t,o){t[3]?n?n.p(t,o):(n=Re(t),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},d(t){t&&w(e),n&&n.d()}}}function Re(l){let e,n=A(Object.entries(l[3])),t=[];for(let o=0;o<n.length;o+=1)t[o]=Ae(Me(l,n,o));return{c(){for(let o=0;o<t.length;o+=1)t[o].c();e=G()},m(o,i){for(let s=0;s<t.length;s+=1)t[s]&&t[s].m(o,i);v(o,e,i)},p(o,i){if(i[0]&3080){n=A(Object.entries(o[3]));let s;for(s=0;s<n.length;s+=1){const a=Me(o,n,s);t[s]?t[s].p(a,i):(t[s]=Ae(a),t[s].c(),t[s].m(e.parentNode,e))}for(;s<t.length;s+=1)t[s].d(1);t.length=n.length}},d(o){o&&w(e),Q(t,o)}}}function Ae(l){let e,n=l[46]+"",t,o,i,s,a;function r(){return l[15](l[46])}function c(){return l[16](l[46])}return{c(){e=N("div"),t=P(n),o=B(),_(e,"role","button"),_(e,"aria-roledescription","Categories of highlighted text. Hover to see text with this class_or_confidence highlighted."),_(e,"tabindex","0"),_(e,"class","class_or_confidence-label svelte-1ozsnjl"),_(e,"style",i="background-color:"+l[52].secondary)},m(f,m){v(f,e,m),M(e,t),M(e,o),s||(a=[T(e,"mouseover",r),T(e,"focus",c),T(e,"mouseout",l[17]),T(e,"blur",l[18])],s=!0)},p(f,m){l=f,m[0]&8&&n!==(n=l[46]+"")&&X(t,n),m[0]&8&&i!==(i="background-color:"+l[52].secondary)&&_(e,"style",i)},d(f){f&&w(e),s=!1,U(a)}}}function De(l){let e,n,t,o=l[49]+"",i,s,a,r,c,f,m;function y(){return l[20](l[48])}function b(){return l[21](l[48])}function u(){return l[22](l[48])}let d=!l[1]&&l[46]!==null&&l[6]!==l[48]&&qe(l),S=l[6]===l[48]&&l[46]!==null&&Fe(l);function V(){return l[26](l[46],l[48],l[45])}function O(...L){return l[27](l[46],l[48],l[45],...L)}function g(){return l[28](l[48])}function C(){return l[29](l[48])}let k=l[46]!==null&&Ye(l);return{c(){e=N("span"),n=N("span"),t=N("span"),i=P(o),s=B(),d&&d.c(),a=B(),S&&S.c(),r=B(),k&&k.c(),_(t,"class","text svelte-1ozsnjl"),_(t,"role","button"),_(t,"tabindex","0"),H(t,"no-label",l[46]===null),_(n,"role","button"),_(n,"tabindex","0"),_(n,"class","textspan svelte-1ozsnjl"),H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),H(n,"hl",l[46]!==null),H(n,"selectable",l[2]),D(n,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),_(e,"class","text-class_or_confidence-container svelte-1ozsnjl")},m(L,R){v(L,e,R),M(e,n),M(n,t),M(t,i),M(n,s),d&&d.m(n,null),M(n,a),S&&S.m(n,null),M(e,r),k&&k.m(e,null),c=!0,f||(m=[T(t,"keydown",l[19]),T(t,"focus",y),T(t,"mouseover",b),T(t,"click",u),T(n,"click",V),T(n,"keydown",O),T(n,"focus",g),T(n,"mouseover",C)],f=!0)},p(L,R){l=L,(!c||R[0]&1)&&o!==(o=l[49]+"")&&X(i,o),(!c||R[0]&1)&&H(t,"no-label",l[46]===null),!l[1]&&l[46]!==null&&l[6]!==l[48]?d?d.p(l,R):(d=qe(l),d.c(),d.m(n,a)):d&&(d.d(1),d=null),l[6]===l[48]&&l[46]!==null?S?(S.p(l,R),R[0]&65&&p(S,1)):(S=Fe(l),S.c(),p(S,1),S.m(n,null)):S&&(Z(),z(S,1,1,()=>{S=null}),K()),(!c||R[0]&33)&&H(n,"no-cat",l[46]===null||l[5]&&l[5]!==l[46]),(!c||R[0]&1)&&H(n,"hl",l[46]!==null),(!c||R[0]&4)&&H(n,"selectable",l[2]),R[0]&41&&D(n,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[46]&&l[3][l[46]]?l[3][l[46]].secondary:""),l[46]!==null?k?k.p(l,R):(k=Ye(l),k.c(),k.m(e,null)):k&&(k.d(1),k=null)},i(L){c||(p(S),c=!0)},o(L){z(S),c=!1},d(L){L&&w(e),d&&d.d(),S&&S.d(),k&&k.d(),f=!1,U(m)}}}function qe(l){let e,n=l[46]+"",t,o,i;function s(){return l[23](l[48])}function a(){return l[24](l[48])}return{c(){e=N("span"),t=P(n),_(e,"id",`label-tag-${l[48]}`),_(e,"class","label svelte-1ozsnjl"),_(e,"role","button"),_(e,"tabindex","0"),D(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},m(r,c){v(r,e,c),M(e,t),o||(i=[T(e,"click",s),T(e,"keydown",a)],o=!0)},p(r,c){l=r,c[0]&1&&n!==(n=l[46]+"")&&X(t,n),c[0]&41&&D(e,"background-color",l[46]===null||l[5]&&l[5]!==l[46]?"":l[3][l[46]].primary)},d(r){r&&w(e),o=!1,U(i)}}}function Fe(l){let e,n,t,o;function i(a){l[25](a)}let s={labelToEdit:l[6],category:l[46],active:l[5],_color_map:l[3],indexOfLabel:l[48],text:l[45],handleValueChange:l[9]};return l[0]!==void 0&&(s.value=l[0]),n=new ol({props:s}),ce.push(()=>ue(n,"value",i)),{c(){e=P(` 
									`),q(n.$$.fragment)},m(a,r){v(a,e,r),F(n,a,r),o=!0},p(a,r){const c={};r[0]&64&&(c.labelToEdit=a[6]),r[0]&1&&(c.category=a[46]),r[0]&32&&(c.active=a[5]),r[0]&8&&(c._color_map=a[3]),r[0]&1&&(c.text=a[45]),!t&&r[0]&1&&(t=!0,c.value=a[0],_e(()=>t=!1)),n.$set(c)},i(a){o||(p(n.$$.fragment,a),o=!0)},o(a){z(n.$$.fragment,a),o=!1},d(a){a&&w(e),Y(n,a)}}}function Ye(l){let e,n,t;function o(){return l[30](l[48])}function i(...s){return l[31](l[48],...s)}return{c(){e=N("span"),e.textContent="×",_(e,"class","label-clear-button svelte-1ozsnjl"),_(e,"role","button"),_(e,"aria-roledescription","Remove label from text"),_(e,"tabindex","0")},m(s,a){v(s,e,a),n||(t=[T(e,"click",o),T(e,"keydown",i)],n=!0)},p(s,a){l=s},d(s){s&&w(e),n=!1,U(t)}}}function Ze(l){let e;return{c(){e=N("br")},m(n,t){v(n,e,t)},d(n){n&&w(e)}}}function Ke(l){let e=l[49].trim()!=="",n,t=l[51]<oe(l[45]).length-1,o,i,s=e&&De(l),a=t&&Ze();return{c(){s&&s.c(),n=B(),a&&a.c(),o=G()},m(r,c){s&&s.m(r,c),v(r,n,c),a&&a.m(r,c),v(r,o,c),i=!0},p(r,c){c[0]&1&&(e=r[49].trim()!==""),e?s?(s.p(r,c),c[0]&1&&p(s,1)):(s=De(r),s.c(),p(s,1),s.m(n.parentNode,n)):s&&(Z(),z(s,1,1,()=>{s=null}),K()),c[0]&1&&(t=r[51]<oe(r[45]).length-1),t?a||(a=Ze(),a.c(),a.m(o.parentNode,o)):a&&(a.d(1),a=null)},i(r){i||(p(s),i=!0)},o(r){z(s),i=!1},d(r){r&&(w(n),w(o)),s&&s.d(r),a&&a.d(r)}}}function Pe(l){let e,n,t=A(oe(l[45])),o=[];for(let s=0;s<t.length;s+=1)o[s]=Ke(Ce(l,t,s));const i=s=>z(o[s],1,1,()=>{o[s]=null});return{c(){for(let s=0;s<o.length;s+=1)o[s].c();e=G()},m(s,a){for(let r=0;r<o.length;r+=1)o[r]&&o[r].m(s,a);v(s,e,a),n=!0},p(s,a){if(a[0]&13183){t=A(oe(s[45]));let r;for(r=0;r<t.length;r+=1){const c=Ce(s,t,r);o[r]?(o[r].p(c,a),p(o[r],1)):(o[r]=Ke(c),o[r].c(),p(o[r],1),o[r].m(e.parentNode,e))}for(Z(),r=t.length;r<o.length;r+=1)i(r);K()}},i(s){if(!n){for(let a=0;a<t.length;a+=1)p(o[a]);n=!0}},o(s){o=o.filter(Boolean);for(let a=0;a<o.length;a+=1)z(o[a]);n=!1},d(s){s&&w(e),Q(o,s)}}}function Ul(l){let e,n,t,o;const i=[Pl,Kl],s=[];function a(r,c){return r[7]==="categories"?0:1}return n=a(l),t=s[n]=i[n](l),{c(){e=N("div"),t.c(),_(e,"class","container svelte-1ozsnjl")},m(r,c){v(r,e,c),s[n].m(e,null),o=!0},p(r,c){let f=n;n=a(r),n===f?s[n].p(r,c):(Z(),z(s[f],1,1,()=>{s[f]=null}),K(),t=s[n],t?t.p(r,c):(t=s[n]=i[n](r),t.c()),p(t,1),t.m(e,null))},i(r){o||(p(t),o=!0)},o(r){z(t),o=!1},d(r){r&&w(e),s[n].d()}}}function oe(l){return l.split(`
`)}function Gl(l,e,n){const t=typeof document<"u";let{value:o=[]}=e,{show_legend:i=!1}=e,{color_map:s={}}=e,{selectable:a=!1}=e,r=-1,c,f={},m="",y,b=-1;Ml(()=>{const h=()=>{y=window.getSelection(),L(),window.removeEventListener("mouseup",h)};window.addEventListener("mousedown",()=>{window.addEventListener("mouseup",h)})});async function u(h,I){if(y?.toString()&&r!==-1&&o[r].token.toString().includes(y.toString())){const J=Symbol(),$=o[r].token,[Tl,Nl,Ol]=[$.substring(0,h),$.substring(h,I),$.substring(I)];let ne=[...o.slice(0,r),{token:Tl,class_or_confidence:null},{token:Nl,class_or_confidence:O==="scores"?1:"label",flag:J},{token:Ol,class_or_confidence:null},...o.slice(r+1)];n(6,b=ne.findIndex(({flag:te})=>te===J)),ne=ne.filter(te=>te.token.trim()!==""),n(0,o=ne.map(({flag:te,...Cl})=>Cl)),V(),document.getElementById(`label-input-${b}`)?.focus()}}const d=Je();function S(h){!o||h<0||h>=o.length||(n(0,o[h].class_or_confidence=null,o),n(0,o=sl(o)),V(),window.getSelection()?.empty())}function V(){d("change",o),n(6,b=-1),i&&(n(14,s={}),n(3,f={}))}let O;function g(h){n(5,m=h)}function C(){n(5,m="")}async function k(h){y=window.getSelection(),h.key==="Enter"&&L()}function L(){if(y&&y?.toString().trim()!==""){const h=y.getRangeAt(0).startOffset,I=y.getRangeAt(0).endOffset;u(h,I)}}function R(h,I,J){d("select",{index:h,value:[I,J]})}const re=h=>g(h),ae=h=>g(h),j=()=>C(),il=()=>C(),rl=h=>k(h),al=h=>n(4,r=h),fl=h=>n(4,r=h),cl=h=>n(6,b=h),ul=h=>n(6,b=h),_l=h=>n(6,b=h);function hl(h){o=h,n(0,o)}const dl=(h,I,J)=>{h!==null&&R(I,J,h)},gl=(h,I,J,$)=>{h!==null?(n(6,b=I),R(I,J,h)):k($)},ml=h=>n(4,r=h),bl=h=>n(4,r=h),kl=h=>S(h),pl=(h,I)=>{I.key==="Enter"&&S(h)};function vl(h){o=h,n(0,o)}const wl=h=>n(4,r=h),yl=h=>n(4,r=h),jl=h=>n(6,b=h),zl=(h,I)=>{I.key==="Enter"&&n(6,b=h)},Sl=h=>S(h),El=(h,I)=>{I.key==="Enter"&&S(h)};return l.$$set=h=>{"value"in h&&n(0,o=h.value),"show_legend"in h&&n(1,i=h.show_legend),"color_map"in h&&n(14,s=h.color_map),"selectable"in h&&n(2,a=h.selectable)},l.$$.update=()=>{if(l.$$.dirty[0]&16393){if(s||n(14,s={}),o.length>0){for(let h of o)if(h.class_or_confidence!==null)if(typeof h.class_or_confidence=="string"){if(n(7,O="categories"),!(h.class_or_confidence in s)){let I=el(Object.keys(s).length);n(14,s[h.class_or_confidence]=I,s)}}else n(7,O="scores")}tl(s,f,t,c)}},[o,i,a,f,r,m,b,O,S,V,g,C,k,R,s,re,ae,j,il,rl,al,fl,cl,ul,_l,hl,dl,gl,ml,bl,kl,pl,vl,wl,yl,jl,zl,Sl,El]}class Jl extends x{constructor(e){super(),ee(this,e,Gl,Ul,le,{value:0,show_legend:1,color_map:14,selectable:2},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get show_legend(){return this.$$.ctx[1]}set show_legend(e){this.$$set({show_legend:e}),E()}get color_map(){return this.$$.ctx[14]}set color_map(e){this.$$set({color_map:e}),E()}get selectable(){return this.$$.ctx[2]}set selectable(e){this.$$set({selectable:e}),E()}}const Ql=Jl;function Wl(l){let e,n;return e=new Qe({props:{variant:l[13]?"dashed":"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[ln]},$$scope:{ctx:l}}}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&8192&&(i.variant=t[13]?"dashed":"solid"),o&32&&(i.visible=t[5]),o&8&&(i.elem_id=t[3]),o&16&&(i.elem_classes=t[4]),o&512&&(i.container=t[9]),o&1024&&(i.scale=t[10]),o&2048&&(i.min_width=t[11]),o&8442695&&(i.$$scope={dirty:o,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function Xl(l){let e,n;return e=new Qe({props:{variant:"solid",test_id:"highlighted-text",visible:l[5],elem_id:l[3],elem_classes:l[4],padding:!1,container:l[9],scale:l[10],min_width:l[11],$$slots:{default:[on]},$$scope:{ctx:l}}}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&32&&(i.visible=t[5]),o&8&&(i.elem_id=t[3]),o&16&&(i.elem_classes=t[4]),o&512&&(i.container=t[9]),o&1024&&(i.scale=t[10]),o&2048&&(i.min_width=t[11]),o&8442823&&(i.$$scope={dirty:o,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function Ue(l){let e,n;return e=new ll({props:{Icon:ie,label:l[8],float:!1,disable:l[9]===!1,show_label:l[14]}}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&256&&(i.label=t[8]),o&512&&(i.disable=t[9]===!1),o&16384&&(i.show_label=t[14]),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function $l(l){let e,n;return e=new nl({props:{size:"small",unpadded_box:!0,$$slots:{default:[en]},$$scope:{ctx:l}}}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&8388608&&(i.$$scope={dirty:o,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function xl(l){let e,n,t;function o(s){l[21](s)}let i={selectable:l[12],show_legend:l[6],color_map:l[1]};return l[0]!==void 0&&(i.value=l[0]),e=new Ql({props:i}),ce.push(()=>ue(e,"value",o)),e.$on("change",l[22]),{c(){q(e.$$.fragment)},m(s,a){F(e,s,a),t=!0},p(s,a){const r={};a&4096&&(r.selectable=s[12]),a&64&&(r.show_legend=s[6]),a&2&&(r.color_map=s[1]),!n&&a&1&&(n=!0,r.value=s[0],_e(()=>n=!1)),e.$set(r)},i(s){t||(p(e.$$.fragment,s),t=!0)},o(s){z(e.$$.fragment,s),t=!1},d(s){Y(e,s)}}}function en(l){let e,n;return e=new ie({}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function ln(l){let e,n,t,o,i,s,a;const r=[{autoscroll:l[2].autoscroll},l[15],{i18n:l[2].i18n}];let c={};for(let u=0;u<r.length;u+=1)c=We(c,r[u]);e=new Xe({props:c}),e.$on("clear_status",l[20]);let f=l[8]&&l[14]&&Ue(l);const m=[xl,$l],y=[];function b(u,d){return u[0]?0:1}return o=b(l),i=y[o]=m[o](l),{c(){q(e.$$.fragment),n=B(),f&&f.c(),t=B(),i.c(),s=G()},m(u,d){F(e,u,d),v(u,n,d),f&&f.m(u,d),v(u,t,d),y[o].m(u,d),v(u,s,d),a=!0},p(u,d){const S=d&32772?$e(r,[d&4&&{autoscroll:u[2].autoscroll},d&32768&&xe(u[15]),d&4&&{i18n:u[2].i18n}]):{};e.$set(S),u[8]&&u[14]?f?(f.p(u,d),d&16640&&p(f,1)):(f=Ue(u),f.c(),p(f,1),f.m(t.parentNode,t)):f&&(Z(),z(f,1,1,()=>{f=null}),K());let V=o;o=b(u),o===V?y[o].p(u,d):(Z(),z(y[V],1,1,()=>{y[V]=null}),K(),i=y[o],i?i.p(u,d):(i=y[o]=m[o](u),i.c()),p(i,1),i.m(s.parentNode,s))},i(u){a||(p(e.$$.fragment,u),p(f),p(i),a=!0)},o(u){z(e.$$.fragment,u),z(f),z(i),a=!1},d(u){u&&(w(n),w(t),w(s)),Y(e,u),f&&f.d(u),y[o].d(u)}}}function Ge(l){let e,n;return e=new ll({props:{Icon:ie,label:l[8],float:!1,disable:l[9]===!1,show_label:l[14]}}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&256&&(i.label=t[8]),o&512&&(i.disable=t[9]===!1),o&16384&&(i.show_label=t[14]),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function nn(l){let e,n;return e=new nl({props:{$$slots:{default:[sn]},$$scope:{ctx:l}}}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&8388608&&(i.$$scope={dirty:o,ctx:t}),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function tn(l){let e,n;return e=new Al({props:{selectable:l[12],value:l[0],show_legend:l[6],show_inline_category:l[7],color_map:l[1]}}),e.$on("select",l[19]),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},p(t,o){const i={};o&4096&&(i.selectable=t[12]),o&1&&(i.value=t[0]),o&64&&(i.show_legend=t[6]),o&128&&(i.show_inline_category=t[7]),o&2&&(i.color_map=t[1]),e.$set(i)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function sn(l){let e,n;return e=new ie({}),{c(){q(e.$$.fragment)},m(t,o){F(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){Y(e,t)}}}function on(l){let e,n,t,o,i,s,a;const r=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[15]];let c={};for(let u=0;u<r.length;u+=1)c=We(c,r[u]);e=new Xe({props:c}),e.$on("clear_status",l[18]);let f=l[8]&&l[14]&&Ge(l);const m=[tn,nn],y=[];function b(u,d){return u[0]?0:1}return o=b(l),i=y[o]=m[o](l),{c(){q(e.$$.fragment),n=B(),f&&f.c(),t=B(),i.c(),s=G()},m(u,d){F(e,u,d),v(u,n,d),f&&f.m(u,d),v(u,t,d),y[o].m(u,d),v(u,s,d),a=!0},p(u,d){const S=d&32772?$e(r,[d&4&&{autoscroll:u[2].autoscroll},d&4&&{i18n:u[2].i18n},d&32768&&xe(u[15])]):{};e.$set(S),u[8]&&u[14]?f?(f.p(u,d),d&16640&&p(f,1)):(f=Ge(u),f.c(),p(f,1),f.m(t.parentNode,t)):f&&(Z(),z(f,1,1,()=>{f=null}),K());let V=o;o=b(u),o===V?y[o].p(u,d):(Z(),z(y[V],1,1,()=>{y[V]=null}),K(),i=y[o],i?i.p(u,d):(i=y[o]=m[o](u),i.c()),p(i,1),i.m(s.parentNode,s))},i(u){a||(p(e.$$.fragment,u),p(f),p(i),a=!0)},o(u){z(e.$$.fragment,u),z(f),z(i),a=!1},d(u){u&&(w(n),w(t),w(s)),Y(e,u),f&&f.d(u),y[o].d(u)}}}function rn(l){let e,n,t,o;const i=[Xl,Wl],s=[];function a(r,c){return r[13]?1:0}return e=a(l),n=s[e]=i[e](l),{c(){n.c(),t=G()},m(r,c){s[e].m(r,c),v(r,t,c),o=!0},p(r,[c]){let f=e;e=a(r),e===f?s[e].p(r,c):(Z(),z(s[f],1,1,()=>{s[f]=null}),K(),n=s[e],n?n.p(r,c):(n=s[e]=i[e](r),n.c()),p(n,1),n.m(t.parentNode,t))},i(r){o||(p(n),o=!0)},o(r){z(n),o=!1},d(r){r&&w(t),s[e].d(r)}}}function an(l,e,n){let{gradio:t}=e,{elem_id:o=""}=e,{elem_classes:i=[]}=e,{visible:s=!0}=e,{value:a}=e,r,{show_legend:c}=e,{show_inline_category:f}=e,{color_map:m={}}=e,{label:y=t.i18n("highlighted_text.highlighted_text")}=e,{container:b=!0}=e,{scale:u=null}=e,{min_width:d=void 0}=e,{_selectable:S=!1}=e,{combine_adjacent:V=!1}=e,{interactive:O}=e,{show_label:g=!0}=e,{loading_status:C}=e;const k=()=>t.dispatch("clear_status",C),L=({detail:j})=>t.dispatch("select",j),R=()=>t.dispatch("clear_status",C);function re(j){a=j,n(0,a),n(16,V)}const ae=()=>t.dispatch("change");return l.$$set=j=>{"gradio"in j&&n(2,t=j.gradio),"elem_id"in j&&n(3,o=j.elem_id),"elem_classes"in j&&n(4,i=j.elem_classes),"visible"in j&&n(5,s=j.visible),"value"in j&&n(0,a=j.value),"show_legend"in j&&n(6,c=j.show_legend),"show_inline_category"in j&&n(7,f=j.show_inline_category),"color_map"in j&&n(1,m=j.color_map),"label"in j&&n(8,y=j.label),"container"in j&&n(9,b=j.container),"scale"in j&&n(10,u=j.scale),"min_width"in j&&n(11,d=j.min_width),"_selectable"in j&&n(12,S=j._selectable),"combine_adjacent"in j&&n(16,V=j.combine_adjacent),"interactive"in j&&n(13,O=j.interactive),"show_label"in j&&n(14,g=j.show_label),"loading_status"in j&&n(15,C=j.loading_status)},l.$$.update=()=>{l.$$.dirty&2&&!m&&Object.keys(m).length&&n(1,m),l.$$.dirty&65537&&a&&V&&n(0,a=sl(a)),l.$$.dirty&131077&&a!==r&&(n(17,r=a),t.dispatch("change"))},[a,m,t,o,i,s,c,f,y,b,u,d,S,O,g,C,V,r,k,L,R,re,ae]}class hn extends x{constructor(e){super(),ee(this,e,an,rn,le,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,show_legend:6,show_inline_category:7,color_map:1,label:8,container:9,scale:10,min_width:11,_selectable:12,combine_adjacent:16,interactive:13,show_label:14,loading_status:15})}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),E()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),E()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),E()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),E()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),E()}get show_inline_category(){return this.$$.ctx[7]}set show_inline_category(e){this.$$set({show_inline_category:e}),E()}get color_map(){return this.$$.ctx[1]}set color_map(e){this.$$set({color_map:e}),E()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),E()}get container(){return this.$$.ctx[9]}set container(e){this.$$set({container:e}),E()}get scale(){return this.$$.ctx[10]}set scale(e){this.$$set({scale:e}),E()}get min_width(){return this.$$.ctx[11]}set min_width(e){this.$$set({min_width:e}),E()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),E()}get combine_adjacent(){return this.$$.ctx[16]}set combine_adjacent(e){this.$$set({combine_adjacent:e}),E()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),E()}get show_label(){return this.$$.ctx[14]}set show_label(e){this.$$set({show_label:e}),E()}get loading_status(){return this.$$.ctx[15]}set loading_status(e){this.$$set({loading_status:e}),E()}}export{Ql as BaseInteractiveHighlightedText,Al as BaseStaticHighlightedText,hn as default};
//# sourceMappingURL=Index-B7GdVeCP.js.map
