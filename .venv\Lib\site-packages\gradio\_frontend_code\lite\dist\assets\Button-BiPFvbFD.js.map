{"version": 3, "file": "Button-BiPFvbFD.js", "sources": ["../../../button/shared/Button.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { type FileData } from \"@gradio/client\";\n\timport { Image } from \"@gradio/image/shared\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" | \"huggingface\" =\n\t\t\"secondary\";\n\texport let size: \"sm\" | \"md\" | \"lg\" = \"lg\";\n\texport let value: string | null = null;\n\texport let link: string | null = null;\n\texport let icon: FileData | null = null;\n\texport let disabled = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n</script>\n\n{#if link && link.length > 0}\n\t<a\n\t\thref={link}\n\t\trel=\"noopener noreferrer\"\n\t\tclass:hidden={!visible}\n\t\tclass:disabled\n\t\taria-disabled={disabled}\n\t\tclass=\"{size} {variant} {elem_classes.join(' ')}\"\n\t\tstyle:flex-grow={scale}\n\t\tstyle:pointer-events={disabled ? \"none\" : null}\n\t\tstyle:width={scale === 0 ? \"fit-content\" : null}\n\t\tstyle:min-width={typeof min_width === \"number\"\n\t\t\t? `calc(min(${min_width}px, 100%))`\n\t\t\t: null}\n\t\tid={elem_id}\n\t>\n\t\t{#if icon}\n\t\t\t<Image class=\"button-icon\" src={icon.url} alt={`${value} icon`} />\n\t\t{/if}\n\t\t<slot />\n\t</a>\n{:else}\n\t<button\n\t\ton:click\n\t\tclass:hidden={!visible}\n\t\tclass=\"{size} {variant} {elem_classes.join(' ')}\"\n\t\tstyle:flex-grow={scale}\n\t\tstyle:width={scale === 0 ? \"fit-content\" : null}\n\t\tstyle:min-width={typeof min_width === \"number\"\n\t\t\t? `calc(min(${min_width}px, 100%))`\n\t\t\t: null}\n\t\tid={elem_id}\n\t\t{disabled}\n\t>\n\t\t{#if icon}\n\t\t\t<Image\n\t\t\t\tclass={`button-icon ${value ? \"right-padded\" : \"\"}`}\n\t\t\t\tsrc={icon.url}\n\t\t\t\talt={`${value} icon`}\n\t\t\t/>\n\t\t{/if}\n\t\t<slot />\n\t</button>\n{/if}\n\n<style>\n\tbutton,\n\ta {\n\t\tdisplay: inline-flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tpadding: var(--size-0-5) var(--size-2);\n\t\ttext-align: center;\n\t}\n\n\tbutton:hover {\n\t\ttransform: var(--button-transform-hover);\n\t}\n\n\tbutton:active,\n\ta:active {\n\t\ttransform: var(--button-transform-active);\n\t}\n\n\tbutton[disabled],\n\ta.disabled {\n\t\topacity: 0.5;\n\t\tfilter: grayscale(30%);\n\t\tcursor: not-allowed;\n\t\ttransform: none;\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.primary {\n\t\tborder: var(--button-border-width) solid var(--button-primary-border-color);\n\t\tbackground: var(--button-primary-background-fill);\n\t\tcolor: var(--button-primary-text-color);\n\t\tbox-shadow: var(--button-primary-shadow);\n\t}\n\t.primary:hover,\n\t.primary[disabled] {\n\t\tbackground: var(--button-primary-background-fill-hover);\n\t\tcolor: var(--button-primary-text-color-hover);\n\t}\n\n\t.primary:hover {\n\t\tborder-color: var(--button-primary-border-color-hover);\n\t\tbox-shadow: var(--button-primary-shadow-hover);\n\t}\n\t.primary:active {\n\t\tbox-shadow: var(--button-primary-shadow-active);\n\t}\n\n\t.primary[disabled] {\n\t\tborder-color: var(--button-primary-border-color);\n\t}\n\n\t.secondary {\n\t\tborder: var(--button-border-width) solid\n\t\t\tvar(--button-secondary-border-color);\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tcolor: var(--button-secondary-text-color);\n\t\tbox-shadow: var(--button-secondary-shadow);\n\t}\n\n\t.secondary:hover,\n\t.secondary[disabled] {\n\t\tbackground: var(--button-secondary-background-fill-hover);\n\t\tcolor: var(--button-secondary-text-color-hover);\n\t}\n\n\t.secondary:hover {\n\t\tborder-color: var(--button-secondary-border-color-hover);\n\t\tbox-shadow: var(--button-secondary-shadow-hover);\n\t}\n\t.secondary:active {\n\t\tbox-shadow: var(--button-secondary-shadow-active);\n\t}\n\n\t.secondary[disabled] {\n\t\tborder-color: var(--button-secondary-border-color);\n\t}\n\n\t.stop {\n\t\tbackground: var(--button-cancel-background-fill);\n\t\tcolor: var(--button-cancel-text-color);\n\t\tborder: var(--button-border-width) solid var(--button-cancel-border-color);\n\t\tbox-shadow: var(--button-secondary-shadow);\n\t}\n\n\t.stop:hover,\n\t.stop[disabled] {\n\t\tbackground: var(--button-cancel-background-fill-hover);\n\t}\n\n\t.stop:hover {\n\t\tborder-color: var(--button-cancel-border-color-hover);\n\t\tbox-shadow: var(--button-secondary-shadow-hover);\n\t}\n\t.stop:active {\n\t\tbox-shadow: var(--button-secondary-shadow-active);\n\t}\n\n\t.stop[disabled] {\n\t\tborder-color: var(--button-cancel-border-color);\n\t}\n\n\t.sm {\n\t\tborder-radius: var(--button-small-radius);\n\t\tpadding: var(--button-small-padding);\n\t\tfont-weight: var(--button-small-text-weight);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n\n\t.md {\n\t\tborder-radius: var(--button-medium-radius);\n\t\tpadding: var(--button-medium-padding);\n\t\tfont-weight: var(--button-medium-text-weight);\n\t\tfont-size: var(--button-medium-text-size);\n\t}\n\n\t.lg {\n\t\tborder-radius: var(--button-large-radius);\n\t\tpadding: var(--button-large-padding);\n\t\tfont-weight: var(--button-large-text-weight);\n\t\tfont-size: var(--button-large-text-size);\n\t}\n\n\t:global(.button-icon) {\n\t\twidth: var(--text-xl);\n\t\theight: var(--text-xl);\n\t}\n\t:global(.button-icon.right-padded) {\n\t\tmargin-right: var(--spacing-md);\n\t}\n\n\t.huggingface {\n\t\tbackground: rgb(20, 28, 46);\n\t\tcolor: white;\n\t}\n\n\t.huggingface:hover {\n\t\tbackground: rgb(40, 48, 66);\n\t\tcolor: white;\n\t}\n</style>\n"], "names": ["ctx", "create_if_block_2", "attr", "button", "button_class_value", "set_style", "insert", "target", "anchor", "current", "dirty", "create_if_block_1", "a", "a_class_value", "image_changes", "elem_id", "$$props", "elem_classes", "visible", "variant", "size", "value", "link", "icon", "disabled", "scale", "min_width"], "mappings": "uVAoDOA,EAAI,CAAA,GAAAC,EAAAD,CAAA,6FATDE,EAAAC,EAAA,QAAAC,EAAAJ,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,iBAAA,WAM1CA,EAAO,CAAA,CAAA,gCAPIA,EAAO,CAAA,CAAA,kBAELA,EAAK,CAAA,CAAA,EACTK,EAAAF,EAAA,QAAAH,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,EACvBK,EAAAF,EAAA,YAAA,OAAAH,OAAc,qBACvBA,EAAS,EAAA,CAAA,aACrB,IAAI,UARRM,EAoBQC,EAAAJ,EAAAK,CAAA,mFARFR,EAAI,CAAA,uKATD,CAAAS,GAAAC,EAAA,IAAAN,KAAAA,EAAAJ,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,wDAM1CA,EAAO,CAAA,CAAA,2DAPIA,EAAO,CAAA,CAAA,yBAELA,EAAK,CAAA,CAAA,SACTK,EAAAF,EAAA,QAAAH,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,UACvBK,EAAAF,EAAA,YAAA,OAAAH,OAAc,qBACvBA,EAAS,EAAA,CAAA,aACrB,IAAI,6HAdFA,EAAI,CAAA,GAAAW,EAAAX,CAAA,mGAdHA,EAAI,CAAA,CAAA,uDAIKA,EAAQ,CAAA,CAAA,EACfE,EAAAU,EAAA,QAAAC,EAAAb,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,iBAAA,WAO1CA,EAAO,CAAA,CAAA,gBAVIA,EAAO,CAAA,CAAA,uCAILA,EAAK,CAAA,CAAA,uBACAA,EAAQ,CAAA,EAAG,OAAS,IAAI,EACjCK,EAAAO,EAAA,QAAAZ,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,EACvBK,EAAAO,EAAA,YAAA,OAAAZ,OAAc,qBACvBA,EAAS,EAAA,CAAA,aACrB,IAAI,UAZRM,EAmBGC,EAAAK,EAAAJ,CAAA,oDAJGR,EAAI,CAAA,6LAdHA,EAAI,CAAA,CAAA,mCAIKA,EAAQ,CAAA,CAAA,GACf,CAAAS,GAAAC,EAAA,IAAAG,KAAAA,EAAAb,SAAOA,EAAO,CAAA,EAAA,IAAGA,EAAa,CAAA,EAAA,KAAK,GAAG,EAAA,wDAO1CA,EAAO,CAAA,CAAA,4BAVIA,EAAO,CAAA,CAAA,2DAILA,EAAK,CAAA,CAAA,8BACAA,EAAQ,CAAA,EAAG,OAAS,IAAI,SACjCK,EAAAO,EAAA,QAAAZ,EAAU,CAAA,IAAA,EAAI,cAAgB,IAAI,UACvBK,EAAAO,EAAA,YAAA,OAAAZ,OAAc,qBACvBA,EAAS,EAAA,CAAA,aACrB,IAAI,0JAuBiBA,EAAK,CAAA,EAAG,eAAiB,EAAE,GAC5C,IAAAA,KAAK,WACFA,EAAK,CAAA,CAAA,wGAFSA,EAAK,CAAA,EAAG,eAAiB,EAAE,IAC5CU,EAAA,MAAAI,EAAA,IAAAd,KAAK,qBACFA,EAAK,CAAA,CAAA,oKArBkB,IAAAA,KAAK,WAAaA,EAAK,CAAA,CAAA,0EAAvBU,EAAA,MAAAI,EAAA,IAAAd,KAAK,qBAAaA,EAAK,CAAA,CAAA,gKAjBrD,OAAAA,EAAQ,CAAA,GAAAA,EAAK,CAAA,EAAA,OAAS,EAAC,yVAdhB,QAAAe,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,GACV,QAAAG,EACV,WAAA,EAAAH,GACU,KAAAI,EAA2B,IAAA,EAAAJ,GAC3B,MAAAK,EAAuB,IAAA,EAAAL,GACvB,KAAAM,EAAsB,IAAA,EAAAN,GACtB,KAAAO,EAAwB,IAAA,EAAAP,GACxB,SAAAQ,EAAW,EAAA,EAAAR,GACX,MAAAS,EAAuB,IAAA,EAAAT,GACvB,UAAAU,EAAgC,MAAA,EAAAV"}