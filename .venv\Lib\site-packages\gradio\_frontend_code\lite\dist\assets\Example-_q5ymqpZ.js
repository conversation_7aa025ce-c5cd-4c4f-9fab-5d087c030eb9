import{a as m,i as g,s as _,f,y as d,z as h,A as r,d as p,k as u,h as v,t as o,j as b,l as y,c as k,m as $,n as z}from"../lite.js";import{I}from"./Image-BPQ6A_U-.js";/* empty css                                              */import"./file-url-CoOyVRgq.js";function c(n){let e,s;return e=new I({props:{src:n[0].url,alt:""}}),{c(){k(e.$$.fragment)},m(t,l){$(e,t,l),s=!0},p(t,l){const a={};l&1&&(a.src=t[0].url),e.$set(a)},i(t){s||(u(e.$$.fragment,t),s=!0)},o(t){o(e.$$.fragment,t),s=!1},d(t){z(e,t)}}}function j(n){let e,s,t=n[0]&&c(n);return{c(){e=d("div"),t&&t.c(),h(e,"class","container svelte-a9zvka"),r(e,"table",n[1]==="table"),r(e,"gallery",n[1]==="gallery"),r(e,"selected",n[2]),r(e,"border",n[0])},m(l,a){p(l,e,a),t&&t.m(e,null),s=!0},p(l,[a]){l[0]?t?(t.p(l,a),a&1&&u(t,1)):(t=c(l),t.c(),u(t,1),t.m(e,null)):t&&(v(),o(t,1,1,()=>{t=null}),b()),(!s||a&2)&&r(e,"table",l[1]==="table"),(!s||a&2)&&r(e,"gallery",l[1]==="gallery"),(!s||a&4)&&r(e,"selected",l[2]),(!s||a&1)&&r(e,"border",l[0])},i(l){s||(u(t),s=!0)},o(l){o(t),s=!1},d(l){l&&y(e),t&&t.d()}}}function q(n,e,s){let{value:t}=e,{type:l}=e,{selected:a=!1}=e;return n.$$set=i=>{"value"in i&&s(0,t=i.value),"type"in i&&s(1,l=i.type),"selected"in i&&s(2,a=i.selected)},[t,l,a]}class S extends m{constructor(e){super(),g(this,e,q,j,_,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{S as default};
//# sourceMappingURL=Example-_q5ymqpZ.js.map
