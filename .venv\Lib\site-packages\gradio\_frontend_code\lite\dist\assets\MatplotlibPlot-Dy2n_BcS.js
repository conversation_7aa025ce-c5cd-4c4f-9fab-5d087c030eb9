import{a as v,i as f,s as m,f as _,y as c,ar as d,z as u,d as h,C as g,M as b,D as p,l as j,p as q}from"../lite.js";function y(e){let a,t,s,l,o,i;return{c(){a=c("div"),t=c("img"),d(t.src,s=e[1])||u(t,"src",s),u(t,"alt",l=`${e[0].chart} plot visualising provided data`),u(t,"class","svelte-j1jcu3"),u(a,"data-testid","matplotlib"),u(a,"class","matplotlib layout svelte-j1jcu3")},m(r,n){h(r,a,n),g(a,t),o||(i=b(t,"load",e[2]),o=!0)},p(r,[n]){n&2&&!d(t.src,s=r[1])&&u(t,"src",s),n&1&&l!==(l=`${r[0].chart} plot visualising provided data`)&&u(t,"alt",l)},i:p,o:p,d(r){r&&j(a),o=!1,i()}}}function C(e,a,t){let s,{value:l}=a;function o(i){q.call(this,e,i)}return e.$$set=i=>{"value"in i&&t(0,l=i.value)},e.$$.update=()=>{e.$$.dirty&1&&t(1,s=l?.plot)},[l,s,o]}class $ extends v{constructor(a){super(),f(this,a,C,y,m,{value:0})}get value(){return this.$$.ctx[0]}set value(a){this.$$set({value:a}),_()}}export{$ as default};
//# sourceMappingURL=MatplotlibPlot-Dy2n_BcS.js.map
