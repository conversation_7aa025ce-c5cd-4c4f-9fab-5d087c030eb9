{"version": 3, "file": "Index-C_z0Oekd.js", "sources": ["../../../icons/src/Paperclip.svelte", "../../../multimodaltextbox/shared/utils.ts", "../../../multimodaltextbox/shared/MultimodalTextbox.svelte", "../../../multimodaltextbox/Index.svelte"], "sourcesContent": ["<svg\n\tfill=\"currentColor\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 1920 1920\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\t><g id=\"SVGRepo_bgCarrier\" stroke-width=\"0\"></g><g\n\t\tid=\"SVGRepo_tracerCarrier\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></g><g id=\"SVGRepo_iconCarrier\">\n\t\t<path\n\t\t\td=\"M1752.768 221.109C1532.646.986 1174.283.986 954.161 221.109l-838.588 838.588c-154.052 154.165-154.052 404.894 0 558.946 149.534 149.421 409.976 149.308 559.059 0l758.738-758.626c87.982-88.094 87.982-231.417 0-319.51-88.32-88.208-231.642-87.982-319.51 0l-638.796 638.908 79.85 79.849 638.795-638.908c43.934-43.821 115.539-43.934 159.812 0 43.934 44.047 43.934 115.877 0 159.812l-758.739 758.625c-110.23 110.118-289.355 110.005-399.36 0-110.118-110.117-110.005-289.242 0-399.247l838.588-838.588c175.963-175.962 462.382-176.188 638.909 0 176.075 176.188 176.075 462.833 0 638.908l-798.607 798.72 79.849 79.85 798.607-798.72c220.01-220.123 220.01-578.485 0-798.607\"\n\t\t\tfill-rule=\"evenodd\"\n\t\t></path>\n\t</g></svg\n>\n", "import { tick } from \"svelte\";\n\ninterface Value {\n\tlines: number;\n\tmax_lines: number;\n\ttext: string;\n}\n\nexport async function resize(\n\ttarget: HTMLTextAreaElement | HTMLInputElement,\n\tlines: number,\n\tmax_lines: number\n): Promise<void> {\n\tawait tick();\n\tif (lines === max_lines) return;\n\n\tconst computed_styles = window.getComputedStyle(target);\n\tconst padding_top = parseFloat(computed_styles.paddingTop);\n\tconst padding_bottom = parseFloat(computed_styles.paddingBottom);\n\tconst line_height = parseFloat(computed_styles.lineHeight);\n\n\tlet max =\n\t\tmax_lines === undefined\n\t\t\t? false\n\t\t\t: padding_top + padding_bottom + line_height * max_lines;\n\tlet min = padding_top + padding_bottom + lines * line_height;\n\n\ttarget.style.height = \"1px\";\n\n\tlet scroll_height;\n\tif (max && target.scrollHeight > max) {\n\t\tscroll_height = max;\n\t} else if (target.scrollHeight < min) {\n\t\tscroll_height = min;\n\t} else {\n\t\tscroll_height = target.scrollHeight;\n\t}\n\n\ttarget.style.height = `${scroll_height}px`;\n}\n\nexport function text_area_resize(\n\t_el: HTMLTextAreaElement,\n\t_value: Value\n): any | undefined {\n\tif (_value.lines === _value.max_lines) return;\n\t_el.style.overflowY = \"scroll\";\n\n\tfunction handle_input(event: Event): void {\n\t\tresize(event.target as HTMLTextAreaElement, _value.lines, _value.max_lines);\n\t}\n\t_el.addEventListener(\"input\", handle_input);\n\n\tif (!_value.text.trim()) return;\n\tresize(_el, _value.lines, _value.max_lines);\n\n\treturn {\n\t\tdestroy: () => _el.removeEventListener(\"input\", handle_input)\n\t};\n}\n", "<script lang=\"ts\">\n\timport {\n\t\tonMount,\n\t\tbeforeUpdate,\n\t\tafterUpdate,\n\t\tcreateEventDispatcher,\n\t\ttick\n\t} from \"svelte\";\n\timport { text_area_resize, resize } from \"../shared/utils\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Upload } from \"@gradio/upload\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { WaveformOptions } from \"../../audio/shared/types\";\n\timport {\n\t\tClear,\n\t\tFile,\n\t\tMusic,\n\t\tPaperclip,\n\t\tVideo,\n\t\tSend,\n\t\tSquare,\n\t\tMicrophone\n\t} from \"@gradio/icons\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport InteractiveAudio from \"../../audio/interactive/InteractiveAudio.svelte\";\n\n\texport let value: { text: string; files: FileData[] } = {\n\t\ttext: \"\",\n\t\tfiles: []\n\t};\n\n\texport let value_is_output = false;\n\texport let lines = 1;\n\texport let i18n: I18nFormatter;\n\texport let placeholder = \"Type here...\";\n\texport let disabled = false;\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let show_label = true;\n\texport let max_lines: number;\n\texport let submit_btn: string | boolean | null = null;\n\texport let stop_btn: string | boolean | null = null;\n\texport let rtl = false;\n\texport let autofocus = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\texport let autoscroll = true;\n\texport let root: string;\n\texport let file_types: string[] | null = null;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let file_count: \"single\" | \"multiple\" | \"directory\" = \"multiple\";\n\texport let max_plain_text_length = 1000;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let sources: [\"microphone\" | \"upload\"] = [\"upload\"];\n\texport let active_source: \"microphone\" | null = null;\n\tlet upload_component: Upload;\n\tlet hidden_upload: HTMLInputElement;\n\tlet el: HTMLTextAreaElement | HTMLInputElement;\n\tlet can_scroll: boolean;\n\tlet previous_scroll_top = 0;\n\tlet user_has_scrolled_up = false;\n\texport let dragging = false;\n\tlet uploading = false;\n\tlet oldValue = value.text;\n\tlet recording = false;\n\t$: dispatch(\"drag\", dragging);\n\tlet mic_audio: FileData | null = null;\n\n\tlet full_container: HTMLDivElement;\n\n\t$: if (oldValue !== value.text) {\n\t\tdispatch(\"change\", value);\n\t\toldValue = value.text;\n\t}\n\n\t$: if (value === null) value = { text: \"\", files: [] };\n\t$: value, el && lines !== max_lines && resize(el, lines, max_lines);\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: typeof value;\n\t\tsubmit: undefined;\n\t\tstop: undefined;\n\t\tblur: undefined;\n\t\tselect: SelectData;\n\t\tinput: undefined;\n\t\tfocus: undefined;\n\t\tdrag: boolean;\n\t\tupload: FileData[] | FileData;\n\t\tclear: undefined;\n\t\tload: FileData[] | FileData;\n\t\terror: string;\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tcan_scroll = el && el.offsetHeight + el.scrollTop > el.scrollHeight - 100;\n\t});\n\n\tconst scroll = (): void => {\n\t\tif (can_scroll && autoscroll && !user_has_scrolled_up) {\n\t\t\tel.scrollTo(0, el.scrollHeight);\n\t\t}\n\t};\n\n\tasync function handle_change(): Promise<void> {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\n\tonMount(() => {\n\t\tif (autofocus && el !== null) {\n\t\t\tel.focus();\n\t\t}\n\t});\n\n\tafterUpdate(() => {\n\t\tif (can_scroll && autoscroll) {\n\t\t\tscroll();\n\t\t}\n\t\tvalue_is_output = false;\n\t});\n\n\tfunction handle_select(event: Event): void {\n\t\tconst target: HTMLTextAreaElement | HTMLInputElement = event.target as\n\t\t\t| HTMLTextAreaElement\n\t\t\t| HTMLInputElement;\n\t\tconst text = target.value;\n\t\tconst index: [number, number] = [\n\t\t\ttarget.selectionStart as number,\n\t\t\ttarget.selectionEnd as number\n\t\t];\n\t\tdispatch(\"select\", { value: text.substring(...index), index: index });\n\t}\n\n\tasync function handle_keypress(e: KeyboardEvent): Promise<void> {\n\t\tawait tick();\n\t\tif (e.key === \"Enter\" && e.shiftKey && lines > 1) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t} else if (\n\t\t\te.key === \"Enter\" &&\n\t\t\t!e.shiftKey &&\n\t\t\tlines === 1 &&\n\t\t\tmax_lines >= 1\n\t\t) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t\tactive_source = null;\n\t\t\tif (mic_audio) {\n\t\t\t\tvalue.files.push(mic_audio);\n\t\t\t\tvalue = value;\n\t\t\t\tmic_audio = null;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction handle_scroll(event: Event): void {\n\t\tconst target = event.target as HTMLElement;\n\t\tconst current_scroll_top = target.scrollTop;\n\t\tif (current_scroll_top < previous_scroll_top) {\n\t\t\tuser_has_scrolled_up = true;\n\t\t}\n\t\tprevious_scroll_top = current_scroll_top;\n\n\t\tconst max_scroll_top = target.scrollHeight - target.clientHeight;\n\t\tconst user_has_scrolled_to_bottom = current_scroll_top >= max_scroll_top;\n\t\tif (user_has_scrolled_to_bottom) {\n\t\t\tuser_has_scrolled_up = false;\n\t\t}\n\t}\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData>): Promise<void> {\n\t\thandle_change();\n\t\tif (Array.isArray(detail)) {\n\t\t\tfor (let file of detail) {\n\t\t\t\tvalue.files.push(file);\n\t\t\t}\n\t\t\tvalue = value;\n\t\t} else {\n\t\t\tvalue.files.push(detail);\n\t\t\tvalue = value;\n\t\t}\n\t\tawait tick();\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction remove_thumbnail(event: MouseEvent, index: number): void {\n\t\thandle_change();\n\t\tevent.stopPropagation();\n\t\tvalue.files.splice(index, 1);\n\t\tvalue = value;\n\t}\n\n\tfunction handle_upload_click(): void {\n\t\tif (hidden_upload) {\n\t\t\thidden_upload.value = \"\";\n\t\t\thidden_upload.click();\n\t\t}\n\t}\n\n\tfunction handle_stop(): void {\n\t\tdispatch(\"stop\");\n\t}\n\n\tfunction handle_submit(): void {\n\t\tdispatch(\"submit\");\n\t\tactive_source = null;\n\t\tif (mic_audio) {\n\t\t\tvalue.files.push(mic_audio);\n\t\t\tvalue = value;\n\t\t\tmic_audio = null;\n\t\t}\n\t}\n\n\tasync function handle_paste(event: ClipboardEvent): Promise<void> {\n\t\tif (!event.clipboardData) return;\n\t\tconst items = event.clipboardData.items;\n\t\tconst text = event.clipboardData.getData(\"text\");\n\n\t\tif (text && text.length > max_plain_text_length) {\n\t\t\tevent.preventDefault();\n\t\t\tconst file = new window.File([text], \"pasted_text.txt\", {\n\t\t\t\ttype: \"text/plain\",\n\t\t\t\tlastModified: Date.now()\n\t\t\t});\n\t\t\tif (upload_component) {\n\t\t\t\tupload_component.load_files([file]);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tfor (let index in items) {\n\t\t\tconst item = items[index];\n\t\t\tif (item.kind === \"file\" && item.type.includes(\"image\")) {\n\t\t\t\tconst blob = item.getAsFile();\n\t\t\t\tif (blob) upload_component.load_files([blob]);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction handle_dragenter(event: DragEvent): void {\n\t\tevent.preventDefault();\n\t\tdragging = true;\n\t}\n\n\tfunction handle_dragleave(event: DragEvent): void {\n\t\tevent.preventDefault();\n\t\tconst rect = full_container.getBoundingClientRect();\n\t\tconst { clientX, clientY } = event;\n\t\tif (\n\t\t\tclientX <= rect.left ||\n\t\t\tclientX >= rect.right ||\n\t\t\tclientY <= rect.top ||\n\t\t\tclientY >= rect.bottom\n\t\t) {\n\t\t\tdragging = false;\n\t\t}\n\t}\n\n\tfunction handle_drop(event: DragEvent): void {\n\t\tevent.preventDefault();\n\t\tdragging = false;\n\t\tif (event.dataTransfer && event.dataTransfer.files) {\n\t\t\tconst files = Array.from(event.dataTransfer.files);\n\n\t\t\tif (file_types) {\n\t\t\t\tconst valid_files = files.filter((file) => {\n\t\t\t\t\treturn file_types.some((type) => {\n\t\t\t\t\t\tif (type.startsWith(\".\")) {\n\t\t\t\t\t\t\treturn file.name.toLowerCase().endsWith(type.toLowerCase());\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn file.type.match(new RegExp(type.replace(\"*\", \".*\")));\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\tconst invalid_files = files.length - valid_files.length;\n\t\t\t\tif (invalid_files > 0) {\n\t\t\t\t\tdispatch(\n\t\t\t\t\t\t\"error\",\n\t\t\t\t\t\t`${invalid_files} file(s) were rejected. Accepted formats: ${file_types.join(\", \")}`\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif (valid_files.length > 0) {\n\t\t\t\t\tupload_component.load_files(valid_files);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupload_component.load_files(files);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<div\n\tclass=\"full-container\"\n\tclass:dragging\n\tbind:this={full_container}\n\ton:dragenter={handle_dragenter}\n\ton:dragleave={handle_dragleave}\n\ton:dragover|preventDefault\n\ton:drop={handle_drop}\n\trole=\"group\"\n\taria-label=\"Multimedia input field\"\n>\n\t<BlockTitle {root} {show_label} {info}>{label}</BlockTitle>\n\t{#if value.files.length > 0 || uploading}\n\t\t<div\n\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\taria-label=\"Uploaded files\"\n\t\t\tdata-testid=\"container_el\"\n\t\t\tstyle=\"display: {value.files.length > 0 || uploading ? 'flex' : 'none'};\"\n\t\t>\n\t\t\t{#each value.files as file, index}\n\t\t\t\t<span role=\"listitem\" aria-label=\"File thumbnail\">\n\t\t\t\t\t<button class=\"thumbnail-item thumbnail-small\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass:disabled\n\t\t\t\t\t\t\tclass=\"delete-button\"\n\t\t\t\t\t\t\ton:click={(event) => remove_thumbnail(event, index)}\n\t\t\t\t\t\t\t><Clear /></button\n\t\t\t\t\t\t>\n\t\t\t\t\t\t{#if file.mime_type && file.mime_type.includes(\"image\")}\n\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\ttitle={null}\n\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\tclass={\"thumbnail-image\"}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{:else if file.mime_type && file.mime_type.includes(\"audio\")}\n\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t{:else if file.mime_type && file.mime_type.includes(\"video\")}\n\t\t\t\t\t\t\t<Video />\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t</span>\n\t\t\t{/each}\n\t\t\t{#if uploading}\n\t\t\t\t<div class=\"loader\" role=\"status\" aria-label=\"Uploading\"></div>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n\t{#if sources && sources.includes(\"microphone\") && active_source === \"microphone\"}\n\t\t<InteractiveAudio\n\t\t\ton:change={({ detail }) => {\n\t\t\t\tif (detail !== null) {\n\t\t\t\t\tmic_audio = detail;\n\t\t\t\t}\n\t\t\t}}\n\t\t\ton:clear={() => {\n\t\t\t\tactive_source = null;\n\t\t\t}}\n\t\t\ton:start_recording={() => dispatch(\"start_recording\")}\n\t\t\ton:pause_recording={() => dispatch(\"pause_recording\")}\n\t\t\ton:stop_recording={() => dispatch(\"stop_recording\")}\n\t\t\tsources={[\"microphone\"]}\n\t\t\tclass_name=\"compact-audio\"\n\t\t\t{recording}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{i18n}\n\t\t\t{active_source}\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\tstream_every={1}\n\t\t\teditable={true}\n\t\t\t{label}\n\t\t\t{root}\n\t\t\tloop={false}\n\t\t\tshow_label={false}\n\t\t\tshow_download_button={false}\n\t\t\tdragging={false}\n\t\t/>\n\t{/if}\n\t<div class=\"input-container\">\n\t\t{#if sources && sources.includes(\"upload\") && !disabled && !(file_count === \"single\" && value.files.length > 0)}\n\t\t\t<Upload\n\t\t\t\tbind:this={upload_component}\n\t\t\t\ton:load={handle_upload}\n\t\t\t\t{file_count}\n\t\t\t\tfiletype={file_types}\n\t\t\t\t{root}\n\t\t\t\t{max_file_size}\n\t\t\t\tbind:dragging\n\t\t\t\tbind:uploading\n\t\t\t\tshow_progress={false}\n\t\t\t\tdisable_click={true}\n\t\t\t\tbind:hidden_upload\n\t\t\t\ton:error\n\t\t\t\thidden={true}\n\t\t\t\t{upload}\n\t\t\t\t{stream_handler}\n\t\t\t/>\n\t\t\t<button\n\t\t\t\tdata-testid=\"upload-button\"\n\t\t\t\tclass=\"upload-button\"\n\t\t\t\ton:click={handle_upload_click}><Paperclip /></button\n\t\t\t>\n\t\t{/if}\n\t\t{#if sources && sources.includes(\"microphone\")}\n\t\t\t<button\n\t\t\t\tdata-testid=\"microphone-button\"\n\t\t\t\tclass=\"microphone-button\"\n\t\t\t\tclass:recording\n\t\t\t\ton:click={() => {\n\t\t\t\t\tactive_source = active_source !== \"microphone\" ? \"microphone\" : null;\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<Microphone />\n\t\t\t</button>\n\t\t{/if}\n\t\t<!-- svelte-ignore a11y-autofocus -->\n\t\t<textarea\n\t\t\tdata-testid=\"textbox\"\n\t\t\tuse:text_area_resize={{\n\t\t\t\ttext: value.text,\n\t\t\t\tlines: lines,\n\t\t\t\tmax_lines: max_lines\n\t\t\t}}\n\t\t\tclass=\"scroll-hide\"\n\t\t\tclass:no-label={!show_label}\n\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\tbind:value={value.text}\n\t\t\tbind:this={el}\n\t\t\t{placeholder}\n\t\t\trows={lines}\n\t\t\t{disabled}\n\t\t\t{autofocus}\n\t\t\ton:keypress={handle_keypress}\n\t\t\ton:blur\n\t\t\ton:select={handle_select}\n\t\t\ton:focus\n\t\t\ton:scroll={handle_scroll}\n\t\t\ton:paste={handle_paste}\n\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t/>\n\t\t{#if submit_btn}\n\t\t\t<button\n\t\t\t\tclass=\"submit-button\"\n\t\t\t\tclass:padded-button={submit_btn !== true}\n\t\t\t\ton:click={handle_submit}\n\t\t\t>\n\t\t\t\t{#if submit_btn === true}\n\t\t\t\t\t<Send />\n\t\t\t\t{:else}\n\t\t\t\t\t{submit_btn}\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t\t{#if stop_btn}\n\t\t\t<button\n\t\t\t\tclass=\"stop-button\"\n\t\t\t\tclass:padded-button={stop_btn !== true}\n\t\t\t\ton:click={handle_stop}\n\t\t\t>\n\t\t\t\t{#if stop_btn === true}\n\t\t\t\t\t<Square fill={\"none\"} stroke_width={2.5} />\n\t\t\t\t{:else}\n\t\t\t\t\t{stop_btn}\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.full-container {\n\t\twidth: 100%;\n\t\tposition: relative;\n\t\tpadding: var(--block-padding);\n\t\tborder: 1px solid transparent;\n\t}\n\n\t.full-container.dragging {\n\t\tborder: 1px solid var(--color-accent);\n\t\tborder-radius: calc(var(--radius-sm) - 1px);\n\t}\n\n\t.full-container.dragging::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.input-container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\talign-items: flex-end;\n\t}\n\n\ttextarea {\n\t\tflex-grow: 1;\n\t\toutline: none !important;\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: var(--input-padding);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--input-text-weight);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\tborder: none;\n\t\tmargin-top: 0px;\n\t\tmargin-bottom: 0px;\n\t\tresize: none;\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\ttextarea.no-label {\n\t\tpadding-top: 5px;\n\t\tpadding-bottom: 5px;\n\t}\n\n\ttextarea:disabled {\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\ttextarea::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\t.microphone-button,\n\t.upload-button,\n\t.submit-button,\n\t.stop-button {\n\t\tborder: none;\n\t\ttext-align: center;\n\t\ttext-decoration: none;\n\t\tfont-size: 14px;\n\t\tcursor: pointer;\n\t\tborder-radius: 15px;\n\t\tmin-width: 30px;\n\t\theight: 30px;\n\t\tflex-shrink: 0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: var(--layer-1);\n\t\tmargin-left: var(--spacing-sm);\n\t}\n\t.padded-button {\n\t\tpadding: 0 10px;\n\t}\n\n\t.microphone-button,\n\t.stop-button,\n\t.upload-button,\n\t.submit-button {\n\t\tbackground: var(--button-secondary-background-fill);\n\t}\n\n\t.microphone-button:hover,\n\t.stop-button:hover,\n\t.upload-button:hover,\n\t.submit-button:hover {\n\t\tbackground: var(--button-secondary-background-fill-hover);\n\t}\n\n\t.microphone-button:disabled,\n\t.stop-button:disabled,\n\t.upload-button:disabled,\n\t.submit-button:disabled {\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tcursor: initial;\n\t}\n\t.microphone-button:active,\n\t.stop-button:active,\n\t.upload-button:active,\n\t.submit-button:active {\n\t\tbox-shadow: var(--button-shadow-active);\n\t}\n\n\t.submit-button :global(svg) {\n\t\theight: 22px;\n\t\twidth: 22px;\n\t}\n\t.microphone-button :global(svg),\n\t.upload-button :global(svg) {\n\t\theight: 17px;\n\t\twidth: 17px;\n\t}\n\n\t.stop-button :global(svg) {\n\t\theight: 16px;\n\t\twidth: 16px;\n\t}\n\n\t.loader {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tborder: 5px solid #f3f3f3;\n\t\tborder-top: 5px solid var(--color-accent);\n\t\tborder-radius: 50%;\n\t\twidth: 25px;\n\t\theight: 25px;\n\t\tanimation: spin 2s linear infinite;\n\t}\n\n\t@keyframes spin {\n\t\t0% {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\t100% {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t.thumbnails :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: cover;\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\toverflow-x: scroll;\n\t\tpadding-top: var(--spacing-sm);\n\t\tmargin-bottom: 6px;\n\t}\n\n\t.thumbnail-item {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\t0 0 0 2px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tcursor: default;\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-12);\n\t\theight: var(--size-12);\n\t}\n\n\t.thumbnail-item :global(svg) {\n\t\twidth: 30px;\n\t\theight: 30px;\n\t}\n\n\t.delete-button {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tposition: absolute;\n\t\tright: -7px;\n\t\ttop: -7px;\n\t\tcolor: var(--button-secondary-text-color);\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tborder: none;\n\t\ttext-align: center;\n\t\ttext-decoration: none;\n\t\tfont-size: 10px;\n\t\tcursor: pointer;\n\t\tborder-radius: 50%;\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\n\t.disabled {\n\t\tdisplay: none;\n\t}\n\n\t.delete-button :global(svg) {\n\t\twidth: 12px;\n\t\theight: 12px;\n\t}\n\n\t.delete-button:hover {\n\t\tfilter: brightness(1.2);\n\t\tborder: 0.8px solid var(--color-grey-500);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseMultimodalTextbox } from \"./shared/MultimodalTextbox.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport MultimodalTextbox from \"./shared/MultimodalTextbox.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { onMount } from \"svelte\";\n\timport type { WaveformOptions } from \"../audio/shared/types\";\n\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tsubmit: never;\n\t\tstop: never;\n\t\tblur: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tfocus: never;\n\t\terror: string;\n\t\tclear_status: LoadingStatus;\n\t\tstart_recording: never;\n\t\tpause_recording: never;\n\t\tstop_recording: never;\n\t\tupload: FileData[] | FileData;\n\t\tclear: undefined;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { text: string; files: FileData[] } = {\n\t\ttext: \"\",\n\t\tfiles: []\n\t};\n\texport let file_types: string[] | null = null;\n\texport let lines: number;\n\texport let placeholder = \"\";\n\texport let label = \"MultimodalTextbox\";\n\texport let info: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let max_lines: number;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let submit_btn: string | boolean | null = null;\n\texport let stop_btn: string | boolean | null = null;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let value_is_output = false;\n\texport let rtl = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\texport let autofocus = false;\n\texport let autoscroll = true;\n\texport let interactive: boolean;\n\texport let root: string;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\";\n\texport let max_plain_text_length: number;\n\texport let sources: [\"microphone\" | \"upload\"] = [\"upload\"];\n\texport let waveform_options: WaveformOptions = {};\n\n\tlet dragging: boolean;\n\tlet active_source: \"microphone\" | null = null;\n\tlet waveform_settings: Record<string, any>;\n\tlet color_accent = \"darkorange\";\n\n\tonMount(() => {\n\t\tcolor_accent = getComputedStyle(document?.documentElement).getPropertyValue(\n\t\t\t\"--color-accent\"\n\t\t);\n\t\tset_trim_region_colour();\n\t\twaveform_settings.waveColor = waveform_options.waveform_color || \"#9ca3af\";\n\t\twaveform_settings.progressColor =\n\t\t\twaveform_options.waveform_progress_color || color_accent;\n\t\twaveform_settings.mediaControls = waveform_options.show_controls;\n\t\twaveform_settings.sampleRate = waveform_options.sample_rate || 44100;\n\t});\n\n\t$: waveform_settings = {\n\t\theight: 50,\n\n\t\tbarWidth: 2,\n\t\tbarGap: 3,\n\t\tcursorWidth: 2,\n\t\tcursorColor: \"#ddd5e9\",\n\t\tautoplay: false,\n\t\tbarRadius: 10,\n\t\tdragToSeek: true,\n\t\tnormalize: true,\n\t\tminPxPerSec: 20\n\t};\n\n\tconst trim_region_settings = {\n\t\tcolor: waveform_options.trim_region_color,\n\t\tdrag: true,\n\t\tresize: true\n\t};\n\n\tfunction set_trim_region_colour(): void {\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--trim-region-color\",\n\t\t\ttrim_region_settings.color || color_accent\n\t\t);\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\telem_classes={[...elem_classes, \"multimodal-textbox\"]}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tpadding={false}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\n\t<MultimodalTextbox\n\t\tbind:value\n\t\tbind:value_is_output\n\t\tbind:dragging\n\t\tbind:active_source\n\t\t{file_types}\n\t\t{root}\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\t{lines}\n\t\t{rtl}\n\t\t{text_align}\n\t\t{waveform_settings}\n\t\ti18n={gradio.i18n}\n\t\tmax_lines={!max_lines ? lines + 1 : max_lines}\n\t\t{placeholder}\n\t\t{submit_btn}\n\t\t{stop_btn}\n\t\t{autofocus}\n\t\t{autoscroll}\n\t\t{file_count}\n\t\t{sources}\n\t\tmax_file_size={gradio.max_file_size}\n\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:submit={() => gradio.dispatch(\"submit\")}\n\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\ton:error={({ detail }) => {\n\t\t\tgradio.dispatch(\"error\", detail);\n\t\t}}\n\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\ton:pause_recording={() => gradio.dispatch(\"pause_recording\")}\n\t\ton:stop_recording={() => gradio.dispatch(\"stop_recording\")}\n\t\ton:upload={(e) => gradio.dispatch(\"upload\", e.detail)}\n\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\tdisabled={!interactive}\n\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\t{max_plain_text_length}\n\t/>\n</Block>\n"], "names": ["insert", "target", "svg", "anchor", "append", "g0", "g1", "g2", "path", "resize", "lines", "max_lines", "tick", "computed_styles", "padding_top", "padding_bottom", "line_height", "max", "min", "scroll_height", "text_area_resize", "_el", "_value", "handle_input", "event", "ctx", "each_value", "ensure_array_like", "i", "create_if_block_8", "set_style", "div", "each_blocks", "dirty", "image_changes", "span", "button1", "button0", "button", "toggle_class", "show_if_1", "show_if", "if_block0", "create_if_block_7", "create_if_block_2", "create_if_block", "div1", "div0", "textarea", "set_input_value", "recording", "value", "$$props", "value_is_output", "i18n", "placeholder", "disabled", "label", "info", "show_label", "submit_btn", "stop_btn", "rtl", "autofocus", "text_align", "autoscroll", "root", "file_types", "max_file_size", "upload", "stream_handler", "file_count", "max_plain_text_length", "waveform_settings", "waveform_options", "sources", "active_source", "upload_component", "hidden_upload", "el", "can_scroll", "previous_scroll_top", "user_has_scrolled_up", "dragging", "uploading", "oldValue", "mic_audio", "full_container", "dispatch", "createEventDispatcher", "beforeUpdate", "scroll", "handle_change", "onMount", "afterUpdate", "handle_select", "text", "index", "handle_keypress", "e", "handle_scroll", "current_scroll_top", "max_scroll_top", "handle_upload", "detail", "file", "remove_thumbnail", "handle_upload_click", "$$invalidate", "handle_stop", "handle_submit", "handle_paste", "items", "item", "blob", "handle_dragenter", "handle_dragleave", "rect", "clientX", "clientY", "handle_drop", "files", "valid_files", "type", "invalid_files", "click_handler", "start_recording_handler", "pause_recording_handler", "stop_recording_handler", "$$value", "multimodaltextbox_changes", "block_changes", "gradio", "elem_id", "elem_classes", "visible", "scale", "min_width", "loading_status", "interactive", "color_accent", "set_trim_region_colour", "trim_region_settings", "clear_status_handler", "func", "args", "func_1", "change_handler"], "mappings": "uoFAAAA,EAgBAC,EAAAC,EAAAC,CAAA,EAVEC,EAA+CF,EAAAG,CAAA,EAAAD,EAI3CF,EAAAI,CAAA,EAAAF,EAKDF,EAAAK,CAAA,EAJHH,EAGOG,EAAAC,CAAA,mGCNa,eAAAC,GACrBR,EACAS,EACAC,EACgB,CAEhB,GADA,MAAMC,GAAK,EACPF,IAAUC,EAAW,OAEnB,MAAAE,EAAkB,OAAO,iBAAiBZ,CAAM,EAChDa,EAAc,WAAWD,EAAgB,UAAU,EACnDE,EAAiB,WAAWF,EAAgB,aAAa,EACzDG,EAAc,WAAWH,EAAgB,UAAU,EAEzD,IAAII,EACHN,IAAc,OACX,GACAG,EAAcC,EAAiBC,EAAcL,EAC7CO,EAAMJ,EAAcC,EAAiBL,EAAQM,EAEjDf,EAAO,MAAM,OAAS,MAElB,IAAAkB,EACAF,GAAOhB,EAAO,aAAegB,EAChBE,EAAAF,EACNhB,EAAO,aAAeiB,EAChBC,EAAAD,EAEhBC,EAAgBlB,EAAO,aAGjBA,EAAA,MAAM,OAAS,GAAGkB,CAAa,IACvC,CAEgB,SAAAC,GACfC,EACAC,EACkB,CACd,GAAAA,EAAO,QAAUA,EAAO,UAAW,OACvCD,EAAI,MAAM,UAAY,SAEtB,SAASE,EAAaC,EAAoB,CACzCf,GAAOe,EAAM,OAA+BF,EAAO,MAAOA,EAAO,SAAS,CAC3E,CAGI,GAFAD,EAAA,iBAAiB,QAASE,CAAY,EAEtC,EAACD,EAAO,KAAK,KAAK,EACtB,OAAAb,GAAOY,EAAKC,EAAO,MAAOA,EAAO,SAAS,EAEnC,CACN,QAAS,IAAMD,EAAI,oBAAoB,QAASE,CAAY,CAAA,CAE9D,uGCkQyCE,EAAK,CAAA,CAAA,0CAALA,EAAK,CAAA,CAAA,2CAQpCC,EAAAC,GAAAF,KAAM,KAAK,uBAAhB,OAAIG,GAAA,kEA2BDH,EAAS,EAAA,GAAAI,GAAA,qMA7BGC,GAAAC,EAAA,UAAAN,KAAM,MAAM,OAAS,GAAKA,EAAS,EAAA,EAAG,OAAS,MAAM,UAJvEzB,EAoCKC,EAAA8B,EAAA5B,CAAA,0GA9BGuB,EAAAC,GAAAF,KAAM,KAAK,oBAAhB,OAAIG,GAAA,EAAA,yGAAJ,OAAIA,EAAAI,EAAA,OAAAJ,GAAA,YA2BDH,EAAS,EAAA,yEA7BGK,GAAAC,EAAA,UAAAN,KAAM,MAAM,OAAS,GAAKA,EAAS,EAAA,EAAG,OAAS,MAAM,+BAEpE,OAAIG,GAAA,oqBAWI,IAAAH,MAAK,UACH,iCAGA,oFAJFQ,EAAA,CAAA,EAAA,IAAAC,EAAA,IAAAT,MAAK,uTAFPA,EAAI,EAAA,EAAC,WAAaA,MAAK,UAAU,SAAS,OAAO,wBAQ5CA,EAAI,EAAA,EAAC,WAAaA,MAAK,UAAU,SAAS,OAAO,wBAEjDA,EAAI,EAAA,EAAC,WAAaA,MAAK,UAAU,SAAS,OAAO,6TAlB7DzB,EAwBMC,EAAAkC,EAAAhC,CAAA,EAvBLC,EAsBQ+B,EAAAC,CAAA,EArBPhC,EAKAgC,EAAAC,CAAA,wfAoBFrC,EAA8DC,EAAA8B,EAAA5B,CAAA,0EAiBrD,YAAY,qKASR,WACJ,8BAGJ,cACM,wBACU,YACZ,qrBASCsB,EAAU,EAAA,+CAKL,iBACA,UAGP,0SAXCA,EAAa,EAAA,CAAA,uMAevBzB,EAIAC,EAAAqC,EAAAnC,CAAA,qCADWsB,EAAmB,EAAA,CAAA,uFAhBnBA,EAAU,EAAA,iqBAoBrBzB,EASQC,EAAAqC,EAAAnC,CAAA,yNAiCF,OAAAsB,QAAe,GAAI,qGAHHc,GAAAD,EAAA,gBAAAb,QAAe,EAAI,UAFzCzB,EAUQC,EAAAqC,EAAAnC,CAAA,wCAPGsB,EAAa,EAAA,CAAA,sKADFc,GAAAD,EAAA,gBAAAb,QAAe,EAAI,8GAMtCA,EAAU,EAAA,CAAA,2CAAVA,EAAU,EAAA,CAAA,yQAUP,OAAAA,QAAa,GAAI,mGAHDc,GAAAD,EAAA,gBAAAb,QAAa,EAAI,UAFvCzB,EAUQC,EAAAqC,EAAAnC,CAAA,wCAPGsB,EAAW,EAAA,CAAA,sKADAc,GAAAD,EAAA,gBAAAb,QAAa,EAAI,8GAMpCA,EAAQ,EAAA,CAAA,2CAARA,EAAQ,EAAA,CAAA,kEAFK,MAAA,CAAA,KAAA,oBAAsB,GAAG,iKAlHtCA,EAAO,EAAA,GAAIA,EAAQ,EAAA,EAAA,SAAS,YAAY,GAAKA,EAAa,CAAA,IAAK,iBAiC9De,EAAAf,OAAWA,EAAO,EAAA,EAAC,SAAS,QAAQ,IAAMA,EAAQ,CAAA,GAAA,EAAMA,EAAe,EAAA,IAAA,UAAYA,EAAK,CAAA,EAAC,MAAM,OAAS,KAwBxGgB,EAAAhB,EAAW,EAAA,GAAAA,EAAQ,EAAA,EAAA,SAAS,YAAY,sHAhGzC,IAAAiB,GAAAjB,KAAM,MAAM,OAAS,GAAKA,EAAS,EAAA,IAAAkB,GAAAlB,CAAA,qCAqIlCA,EAAU,EAAA,GAAAmB,GAAAnB,CAAA,IAaVA,EAAQ,EAAA,GAAAoB,GAAApB,CAAA,sPA5BPA,EAAG,EAAA,EAAG,MAAQ,KAAK,qCAIlBA,EAAK,CAAA,CAAA,kDASJA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,mBAdnCA,EAAU,CAAA,CAAA,wLAjI9BzB,EA4KKC,EAAA6C,EAAA3C,CAAA,iEAzFJC,EAwFK0C,EAAAC,CAAA,8CAlDJ3C,EAuBC2C,EAAAC,CAAA,EAbYC,GAAAD,EAAAvB,KAAM,IAAI,kHAPrB,KAAMA,EAAK,CAAA,EAAC,KACZ,MAAOA,EAAK,CAAA,EACZ,UAAWA,EAAA,EAAA,uCAWCA,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,kCAEbA,EAAa,EAAA,CAAA,cACdA,EAAY,EAAA,CAAA,kBA1IVA,EAAgB,EAAA,CAAA,kBAChBA,EAAgB,EAAA,CAAA,uCAErBA,EAAW,EAAA,CAAA,uKAKfA,KAAM,MAAM,OAAS,GAAKA,EAAS,EAAA,6HAuCnCA,EAAO,EAAA,GAAIA,EAAQ,EAAA,EAAA,SAAS,YAAY,GAAKA,EAAa,CAAA,IAAK,wHAiC9DQ,EAAA,CAAA,EAAA,WAAAO,EAAAf,OAAWA,EAAO,EAAA,EAAC,SAAS,QAAQ,IAAMA,EAAQ,CAAA,GAAA,EAAMA,EAAe,EAAA,IAAA,UAAYA,EAAK,CAAA,EAAC,MAAM,OAAS,8GAwBxGQ,EAAA,CAAA,EAAA,WAAAQ,EAAAhB,EAAW,EAAA,GAAAA,EAAQ,EAAA,EAAA,SAAS,YAAY,oIAsBvCA,EAAG,EAAA,EAAG,MAAQ,sFAIbA,EAAK,CAAA,CAAA,iGASJA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,qEAnBjD,KAAMA,EAAK,CAAA,EAAC,KACZ,MAAOA,EAAK,CAAA,EACZ,UAAWA,EAAA,EAAA,YAKAwB,GAAAD,EAAAvB,KAAM,IAAI,mCAFLA,EAAU,CAAA,CAAA,EAgBvBA,EAAU,EAAA,sGAaVA,EAAQ,EAAA,4XA1YVyB,GAAY,sBA1CL,GAAA,CAAA,MAAAC,EAAA,CACV,KAAM,GACN,MAAA,CAAA,CAAA,CAAA,EAAAC,GAGU,gBAAAC,EAAkB,EAAA,EAAAD,GAClB,MAAA1C,EAAQ,CAAA,EAAA0C,EACR,CAAA,KAAAE,CAAA,EAAAF,GACA,YAAAG,EAAc,cAAA,EAAAH,GACd,SAAAI,EAAW,EAAA,EAAAJ,EACX,CAAA,MAAAK,CAAA,EAAAL,GACA,KAAAM,EAA2B,MAAA,EAAAN,GAC3B,WAAAO,EAAa,EAAA,EAAAP,EACb,CAAA,UAAAzC,CAAA,EAAAyC,GACA,WAAAQ,EAAsC,IAAA,EAAAR,GACtC,SAAAS,EAAoC,IAAA,EAAAT,GACpC,IAAAU,EAAM,EAAA,EAAAV,GACN,UAAAW,EAAY,EAAA,EAAAX,GACZ,WAAAY,EAA2C,MAAA,EAAAZ,GAC3C,WAAAa,EAAa,EAAA,EAAAb,EACb,CAAA,KAAAc,CAAA,EAAAd,GACA,WAAAe,EAA8B,IAAA,EAAAf,GAC9B,cAAAgB,EAA+B,IAAA,EAAAhB,EAC/B,CAAA,OAAAiB,CAAA,EAAAjB,EACA,CAAA,eAAAkB,CAAA,EAAAlB,GACA,WAAAmB,EAAkD,UAAA,EAAAnB,GAClD,sBAAAoB,EAAwB,GAAA,EAAApB,EACxB,CAAA,kBAAAqB,CAAA,EAAArB,EACA,CAAA,iBAAAsB,EAAA,CACV,wBAAyB,EAAA,CAAA,EAAAtB,EAEf,CAAA,QAAAuB,EAAA,CAAsC,QAAQ,CAAA,EAAAvB,GAC9C,cAAAwB,EAAqC,IAAA,EAAAxB,EAC5CyB,EACAC,EACAC,EACAC,EACAC,GAAsB,EACtBC,GAAuB,IAChB,SAAAC,GAAW,EAAA,EAAA/B,EAClBgC,GAAY,GACZC,GAAWlC,EAAM,KAGjBmC,GAA6B,KAE7BC,SAUEC,EAAWC,KAkBjBC,GAAA,IAAA,CACCV,EAAaD,GAAMA,EAAG,aAAeA,EAAG,UAAYA,EAAG,aAAe,MAGjE,MAAAY,GAAA,IAAA,CACDX,GAAcf,GAAe,CAAAiB,IAChCH,EAAG,SAAS,EAAGA,EAAG,YAAY,GAIjB,eAAAa,IAAA,CACdJ,EAAS,SAAUrC,CAAK,EACnBE,GACJmC,EAAS,OAAO,EAIlBK,GAAA,IAAA,CACK9B,GAAagB,IAAO,MACvBA,EAAG,MAAA,IAILe,GAAA,IAAA,CACKd,GAAcf,GACjB0B,UAEDtC,EAAkB,EAAA,aAGV0C,GAAcvE,EAAA,CAChB,MAAAvB,EAAiDuB,EAAM,OAGvDwE,EAAO/F,EAAO,MACdgG,EACL,CAAAhG,EAAO,eACPA,EAAO,YAAA,EAERuF,EAAS,SAAY,CAAA,MAAOQ,EAAK,UAAA,GAAaC,CAAK,EAAG,MAAAA,CAAA,CAAA,iBAGxCC,GAAgBC,EAAA,CACxB,MAAAvF,GAAA,EACFuF,EAAE,MAAQ,SAAWA,EAAE,UAAYzF,EAAQ,GAC9CyF,EAAE,eAAA,EACFX,EAAS,QAAQ,GAEjBW,EAAE,MAAQ,SACT,CAAAA,EAAE,UACHzF,IAAU,GACVC,GAAa,IAEbwF,EAAE,eAAA,EACFX,EAAS,QAAQ,MACjBZ,EAAgB,IAAA,EACZU,KACHnC,EAAM,MAAM,KAAKmC,EAAS,cAE1BA,GAAY,IAAA,aAKNc,GAAc5E,EAAA,CAChB,MAAAvB,EAASuB,EAAM,OACf6E,EAAqBpG,EAAO,UAC9BoG,EAAqBpB,KACxBC,GAAuB,IAExBD,GAAsBoB,QAEhBC,EAAiBrG,EAAO,aAAeA,EAAO,aAChBoG,GAAsBC,IAEzDpB,GAAuB,mBAIVqB,GACd,CAAA,OAAAC,GAAA,CAGI,GADJZ,KACI,MAAM,QAAQY,CAAM,EAAA,SACdC,KAAQD,EAChBrD,EAAM,MAAM,KAAKsD,CAAI,cAItBtD,EAAM,MAAM,KAAKqD,CAAM,SAGlB,MAAA5F,GAAA,EACN4E,EAAS,SAAUrC,CAAK,EACxBqC,EAAS,SAAUgB,CAAM,EAGjB,SAAAE,GAAiBlF,EAAmByE,EAAA,CAC5CL,KACApE,EAAM,gBAAA,EACN2B,EAAM,MAAM,OAAO8C,EAAO,CAAC,SAInB,SAAAU,IAAA,CACJ7B,IACH8B,EAAA,GAAA9B,EAAc,MAAQ,GAAAA,CAAA,EACtBA,EAAc,MAAA,GAIP,SAAA+B,IAAA,CACRrB,EAAS,MAAM,EAGP,SAAAsB,IAAA,CACRtB,EAAS,QAAQ,MACjBZ,EAAgB,IAAA,EACZU,KACHnC,EAAM,MAAM,KAAKmC,EAAS,cAE1BA,GAAY,IAAA,kBAICyB,GAAavF,EAAA,KACtBA,EAAM,cAAA,OACL,MAAAwF,EAAQxF,EAAM,cAAc,MAC5BwE,EAAOxE,EAAM,cAAc,QAAQ,MAAM,EAE3C,GAAAwE,GAAQA,EAAK,OAASxB,EAAA,CACzBhD,EAAM,eAAA,QACAiF,EAAW,IAAA,OAAO,MAAMT,CAAI,EAAG,mBACpC,KAAM,aACN,aAAc,KAAK,IAAA,IAEhBnB,GACHA,EAAiB,YAAY4B,CAAI,CAAA,iBAK1BR,KAASe,EAAA,CACX,MAAAC,GAAOD,EAAMf,CAAK,EACpB,GAAAgB,GAAK,OAAS,QAAUA,GAAK,KAAK,SAAS,OAAO,EAAA,CAC/C,MAAAC,GAAOD,GAAK,YACdC,IAAMrC,EAAiB,YAAYqC,EAAI,CAAA,aAKrCC,GAAiB3F,EAAA,CACzBA,EAAM,eAAA,MACN2D,GAAW,EAAA,WAGHiC,GAAiB5F,EAAA,CACzBA,EAAM,eAAA,EACA,MAAA6F,EAAO9B,GAAe,wBACpB,CAAA,QAAA+B,EAAS,QAAAC,CAAY,EAAA/F,GAE5B8F,GAAWD,EAAK,MAChBC,GAAWD,EAAK,OAChBE,GAAWF,EAAK,KAChBE,GAAWF,EAAK,aAEhBlC,GAAW,EAAA,WAIJqC,GAAYhG,EAAA,IACpBA,EAAM,eAAA,MACN2D,GAAW,EAAA,EACP3D,EAAM,cAAgBA,EAAM,aAAa,MAAA,CACtC,MAAAiG,EAAQ,MAAM,KAAKjG,EAAM,aAAa,KAAK,EAE7C,GAAA2C,EAAA,CACG,MAAAuD,EAAcD,EAAM,OAAQhB,IAC1BtC,EAAW,KAAMwD,IACnBA,GAAK,WAAW,GAAG,EACflB,GAAK,KAAK,YAAc,EAAA,SAASkB,GAAK,YAAA,CAAA,EAEvClB,GAAK,KAAK,MAAU,IAAA,OAAOkB,GAAK,QAAQ,IAAK,IAAI,CAAA,CAAA,IAIpDC,EAAgBH,EAAM,OAASC,EAAY,OAC7CE,EAAgB,GACnBpC,EACC,QACG,GAAAoC,CAAa,6CAA6CzD,EAAW,KAAK,IAAI,CAAA,EAAA,EAI/EuD,EAAY,OAAS,GACxB7C,EAAiB,WAAW6C,CAAW,OAGxC7C,EAAiB,WAAW4C,CAAK,qGA+BnB,MAAAI,GAAA,CAAA5B,EAAAzE,IAAUkF,GAAiBlF,EAAOyE,CAAK,OA4BxC,OAAAO,KAAM,CACfA,IAAW,MACdI,EAAA,GAAAtB,GAAYkB,CAAM,WAInBI,EAAA,EAAAhC,EAAgB,IAAI,GAEKkD,GAAA,IAAAtC,EAAS,iBAAiB,EAC1BuC,GAAA,IAAAvC,EAAS,iBAAiB,EAC3BwC,GAAA,IAAAxC,EAAS,gBAAgB,6CAuBtCX,EAAgBoD,+IA4B1BrB,EAAA,EAAAhC,EAAgBA,IAAkB,aAAe,aAAe,IAAI,iBAiB1DzB,EAAM,KAAI,KAAA,wDACX4B,EAAEkD,uDAjIJ1C,GAAc0C,gmCA9OtBzC,EAAS,OAAQL,EAAQ,mBAUrBhC,IAAU,MAAAyD,EAAA,EAAMzD,EAAU,CAAA,KAAM,GAAI,MAAA,CAAA,CAAA,CAAA,uCALpCkC,KAAalC,EAAM,OACzBqC,EAAS,SAAUrC,CAAK,EACxByD,EAAA,GAAAvB,GAAWlC,EAAM,IAAA,2BAIR4B,GAAMrE,IAAUC,GAAaF,GAAOsE,EAAIrE,EAAOC,CAAS,6hGCuCpD,CAAA,WAAAc,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,0LAFNQ,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAR,KAAO,UAAU,EACvBQ,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAR,KAAO,IAAI,kBACbA,EAAc,EAAA,CAAA,oIAJfA,EAAc,EAAA,GAAAoB,GAAApB,CAAA,qOAuBZ,KAAAA,KAAO,eACDA,EAAS,EAAA,EAAeA,EAAS,EAAA,EAArBA,KAAQ,mHAQjB,cAAAA,KAAO,wBAgBXA,EAAW,EAAA,8vBAhDlBA,EAAc,EAAA,6YAuBZQ,EAAA,CAAA,EAAA,IAAAiG,EAAA,KAAAzG,KAAO,8BACDA,EAAS,EAAA,EAAeA,EAAS,EAAA,EAArBA,KAAQ,4OAQjBQ,EAAA,CAAA,EAAA,IAAAiG,EAAA,cAAAzG,KAAO,0CAgBXA,EAAW,EAAA,sgBAvDL,aAAA,CAAA,GAAAA,KAAc,oBAAoB,6CAGpC,WACP,eACIA,EAAQ,EAAA,EAAG,QAAU,mKALhBQ,EAAA,CAAA,EAAA,KAAAkG,EAAA,aAAA,CAAA,GAAA1G,KAAc,oBAAoB,6FAKvCA,EAAQ,EAAA,EAAG,QAAU,+KApGvB,GAAA,CAAA,OAAA2G,CAAA,EAAAhF,GAgBA,QAAAiF,EAAU,EAAA,EAAAjF,EACV,CAAA,aAAAkF,EAAA,EAAA,EAAAlF,GACA,QAAAmF,EAAU,EAAA,EAAAnF,EACV,CAAA,MAAAD,EAAA,CACV,KAAM,GACN,MAAA,CAAA,CAAA,CAAA,EAAAC,GAEU,WAAAe,EAA8B,IAAA,EAAAf,EAC9B,CAAA,MAAA1C,CAAA,EAAA0C,GACA,YAAAG,EAAc,EAAA,EAAAH,GACd,MAAAK,EAAQ,mBAAA,EAAAL,GACR,KAAAM,EAA2B,MAAA,EAAAN,EAC3B,CAAA,WAAAO,CAAA,EAAAP,EACA,CAAA,UAAAzC,CAAA,EAAAyC,GACA,MAAAoF,EAAuB,IAAA,EAAApF,GACvB,UAAAqF,EAAgC,MAAA,EAAArF,GAChC,WAAAQ,EAAsC,IAAA,EAAAR,GACtC,SAAAS,EAAoC,IAAA,EAAAT,GACpC,eAAAsF,EAA4C,MAAA,EAAAtF,GAC5C,gBAAAC,EAAkB,EAAA,EAAAD,GAClB,IAAAU,EAAM,EAAA,EAAAV,GACN,WAAAY,EAA2C,MAAA,EAAAZ,GAC3C,UAAAW,EAAY,EAAA,EAAAX,GACZ,WAAAa,EAAa,EAAA,EAAAb,EACb,CAAA,YAAAuF,CAAA,EAAAvF,EACA,CAAA,KAAAc,CAAA,EAAAd,EACA,CAAA,WAAAmB,CAAA,EAAAnB,EACA,CAAA,sBAAAoB,CAAA,EAAApB,EACA,CAAA,QAAAuB,EAAA,CAAsC,QAAQ,CAAA,EAAAvB,EAC9C,CAAA,iBAAAsB,EAAA,EAAA,EAAAtB,EAEP+B,EACAP,EAAqC,KACrCH,EACAmE,GAAe,aAEnB/C,GAAA,IAAA,CACC+C,GAAe,iBAAiB,UAAU,eAAe,EAAE,iBAC1D,gBAAA,EAEDC,UACApE,EAAkB,UAAYC,EAAiB,gBAAkB,UAAAD,CAAA,OACjEA,EAAkB,cACjBC,EAAiB,yBAA2BkE,GAAAnE,CAAA,EAC7CmC,EAAA,GAAAnC,EAAkB,cAAgBC,EAAiB,cAAAD,CAAA,OACnDA,EAAkB,WAAaC,EAAiB,aAAe,MAAAD,CAAA,IAiB1D,MAAAqE,GAAA,CACL,MAAOpE,EAAiB,kBACxB,KAAM,GACN,OAAQ,IAGA,SAAAmE,IAAA,CACR,SAAS,gBAAgB,MAAM,YAC9B,sBACAC,GAAqB,OAASF,EAAA,EAoBP,MAAAG,GAAA,IAAAX,EAAO,SAAS,eAAgBM,CAAc,EA4C1DM,GAAA,IAAAC,IAASb,EAAO,OAAO,UAAUa,CAAI,EAC7BC,GAAA,IAAAD,IAASb,EAAO,OAAO,UAAUa,CAAI,2GAjBxC,MAAAE,GAAA,IAAAf,EAAO,SAAS,SAAUjF,CAAK,SAChCiF,EAAO,SAAS,OAAO,SACtBA,EAAO,SAAS,QAAQ,SAC1BA,EAAO,SAAS,MAAM,SACtBA,EAAO,SAAS,MAAM,KACzBjC,GAAMiC,EAAO,SAAS,SAAUjC,EAAE,MAAM,SACpCiC,EAAO,SAAS,OAAO,OAC1B,OAAA5B,KAAM,CAClB4B,EAAO,SAAS,QAAS5B,CAAM,UAEN4B,EAAO,SAAS,iBAAiB,SACjCA,EAAO,SAAS,iBAAiB,SAClCA,EAAO,SAAS,gBAAgB,KAC7CjC,GAAMiC,EAAO,SAAS,SAAUjC,EAAE,MAAM,SACpCiC,EAAO,SAAS,OAAO,0hCArFrCxB,EAAA,GAAAnC,EAAA,CACF,OAAQ,GAER,SAAU,EACV,OAAQ,EACR,YAAa,EACb,YAAa,UACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,UAAW,GACX,YAAa"}