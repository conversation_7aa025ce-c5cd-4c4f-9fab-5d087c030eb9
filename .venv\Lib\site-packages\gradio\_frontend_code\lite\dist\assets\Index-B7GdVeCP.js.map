{"version": 3, "file": "Index-B7GdVeCP.js", "sources": ["../../../icons/src/TextHighlight.svelte", "../../../highlightedtext/shared/utils.ts", "../../../highlightedtext/shared/StaticHighlightedtext.svelte", "../../../highlightedtext/shared/LabelInput.svelte", "../../../highlightedtext/shared/InteractiveHighlightedtext.svelte", "../../../highlightedtext/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z\"\n\t/>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z\"\n\t/>\n</svg>\n", "import { colors } from \"@gradio/theme\";\n\nexport function name_to_rgba(\n\tname: string,\n\ta: number,\n\tctx: CanvasRenderingContext2D | null\n): string {\n\tif (!ctx) {\n\t\tvar canvas = document.createElement(\"canvas\");\n\t\tctx = canvas.getContext(\"2d\")!;\n\t}\n\tctx.fillStyle = name;\n\tctx.fillRect(0, 0, 1, 1);\n\tconst [r, g, b] = ctx.getImageData(0, 0, 1, 1).data;\n\tctx.clearRect(0, 0, 1, 1);\n\treturn `rgba(${r}, ${g}, ${b}, ${255 / a})`;\n}\n\nexport function correct_color_map(\n\tcolor_map: Record<string, string>,\n\t_color_map: Record<string, { primary: string; secondary: string }>,\n\tbrowser: any,\n\tctx: CanvasRenderingContext2D | null\n): void {\n\tfor (const col in color_map) {\n\t\tconst _c = color_map[col].trim();\n\n\t\tif (_c in colors) {\n\t\t\t_color_map[col] = colors[_c as keyof typeof colors];\n\t\t} else {\n\t\t\t_color_map[col] = {\n\t\t\t\tprimary: browser\n\t\t\t\t\t? name_to_rgba(color_map[col], 1, ctx)\n\t\t\t\t\t: color_map[col],\n\t\t\t\tsecondary: browser\n\t\t\t\t\t? name_to_rgba(color_map[col], 0.5, ctx)\n\t\t\t\t\t: color_map[col]\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport function merge_elements(\n\tvalue: { token: string; class_or_confidence: string | number | null }[],\n\tmergeMode: \"empty\" | \"equal\"\n): { token: string; class_or_confidence: string | number | null }[] {\n\tlet result: typeof value = [];\n\tlet tempStr: string | null = null;\n\tlet tempVal: string | number | null = null;\n\n\tfor (const val of value) {\n\t\tif (\n\t\t\t(mergeMode === \"empty\" && val.class_or_confidence === null) ||\n\t\t\t(mergeMode === \"equal\" && tempVal === val.class_or_confidence)\n\t\t) {\n\t\t\ttempStr = tempStr ? tempStr + val.token : val.token;\n\t\t} else {\n\t\t\tif (tempStr !== null) {\n\t\t\t\tresult.push({\n\t\t\t\t\ttoken: tempStr,\n\t\t\t\t\tclass_or_confidence: tempVal\n\t\t\t\t});\n\t\t\t}\n\t\t\ttempStr = val.token;\n\t\t\ttempVal = val.class_or_confidence;\n\t\t}\n\t}\n\n\tif (tempStr !== null) {\n\t\tresult.push({\n\t\t\ttoken: tempStr,\n\t\t\tclass_or_confidence: tempVal\n\t\t});\n\t}\n\n\treturn result;\n}\n", "<script lang=\"ts\">\n\tconst browser = typeof document !== \"undefined\";\n\timport { get_next_color } from \"@gradio/utils\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { correct_color_map } from \"./utils\";\n\n\texport let value: {\n\t\ttoken: string;\n\t\tclass_or_confidence: string | number | null;\n\t}[] = [];\n\texport let show_legend = false;\n\texport let show_inline_category = true;\n\texport let color_map: Record<string, string> = {};\n\texport let selectable = false;\n\n\tlet ctx: CanvasRenderingContext2D;\n\tlet _color_map: Record<string, { primary: string; secondary: string }> = {};\n\tlet active = \"\";\n\n\tfunction splitTextByNewline(text: string): string[] {\n\t\treturn text.split(\"\\n\");\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t}>();\n\n\tlet mode: \"categories\" | \"scores\";\n\n\t$: {\n\t\tif (!color_map) {\n\t\t\tcolor_map = {};\n\t\t}\n\t\tif (value.length > 0) {\n\t\t\tfor (let entry of value) {\n\t\t\t\tif (entry.class_or_confidence !== null) {\n\t\t\t\t\tif (typeof entry.class_or_confidence === \"string\") {\n\t\t\t\t\t\tmode = \"categories\";\n\t\t\t\t\t\tif (!(entry.class_or_confidence in color_map)) {\n\t\t\t\t\t\t\tlet color = get_next_color(Object.keys(color_map).length);\n\t\t\t\t\t\t\tcolor_map[entry.class_or_confidence] = color;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmode = \"scores\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcorrect_color_map(color_map, _color_map, browser, ctx);\n\t}\n\n\tfunction handle_mouseover(label: string): void {\n\t\tactive = label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = \"\";\n\t}\n</script>\n\n<!-- \n\t@todo victor: try reimplementing without flex (negative margins on container to avoid left margin on linebreak). \n\tIf not possible hijack the copy execution like this:\n\n<svelte:window\n\ton:copy|preventDefault={() => {\n\t\tconst selection =.getSelection()?.toString();\n\t\tconsole.log(selection?.replaceAll(\"\\n\", \" \"));\n\t}}\n/>\n-->\n\n<div class=\"container\">\n\t{#if mode === \"categories\"}\n\t\t{#if show_legend}\n\t\t\t<div\n\t\t\t\tclass=\"category-legend\"\n\t\t\t\tdata-testid=\"highlighted-text:category-legend\"\n\t\t\t>\n\t\t\t\t{#each Object.entries(_color_map) as [category, color], i}\n\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t<div\n\t\t\t\t\t\ton:mouseover={() => handle_mouseover(category)}\n\t\t\t\t\t\ton:focus={() => handle_mouseover(category)}\n\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\tclass=\"category-label\"\n\t\t\t\t\t\tstyle={\"background-color:\" + color.secondary}\n\t\t\t\t\t>\n\t\t\t\t\t\t{category}\n\t\t\t\t\t</div>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t{/if}\n\t\t<div class=\"textfield\">\n\t\t\t{#each value as v, i}\n\t\t\t\t{#each splitTextByNewline(v.token) as line, j}\n\t\t\t\t\t{#if line.trim() !== \"\"}\n\t\t\t\t\t\t<!-- TODO: fix -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t\t\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\tclass=\"textspan\"\n\t\t\t\t\t\t\tstyle:background-color={v.class_or_confidence === null ||\n\t\t\t\t\t\t\t(active && active !== v.class_or_confidence)\n\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t: _color_map[v.class_or_confidence].secondary}\n\t\t\t\t\t\t\tclass:no-cat={v.class_or_confidence === null ||\n\t\t\t\t\t\t\t\t(active && active !== v.class_or_confidence)}\n\t\t\t\t\t\t\tclass:hl={v.class_or_confidence !== null}\n\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\t\tvalue: [v.token, v.class_or_confidence]\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\tclass:no-label={v.class_or_confidence === null ||\n\t\t\t\t\t\t\t\t\t!_color_map[v.class_or_confidence]}\n\t\t\t\t\t\t\t\tclass=\"text\">{line}</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if !show_legend && show_inline_category && v.class_or_confidence !== null}\n\t\t\t\t\t\t\t\t&nbsp;\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"label\"\n\t\t\t\t\t\t\t\t\tstyle:background-color={v.class_or_confidence === null ||\n\t\t\t\t\t\t\t\t\t(active && active !== v.class_or_confidence)\n\t\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t\t: _color_map[v.class_or_confidence].primary}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{v.class_or_confidence}\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if j < splitTextByNewline(v.token).length - 1}\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t{#if show_legend}\n\t\t\t<div class=\"color-legend\" data-testid=\"highlighted-text:color-legend\">\n\t\t\t\t<span>-1</span>\n\t\t\t\t<span>0</span>\n\t\t\t\t<span>+1</span>\n\t\t\t</div>\n\t\t{/if}\n\t\t<div class=\"textfield\" data-testid=\"highlighted-text:textfield\">\n\t\t\t{#each value as v}\n\t\t\t\t{@const score =\n\t\t\t\t\ttypeof v.class_or_confidence === \"string\"\n\t\t\t\t\t\t? parseInt(v.class_or_confidence)\n\t\t\t\t\t\t: v.class_or_confidence}\n\t\t\t\t<span\n\t\t\t\t\tclass=\"textspan score-text\"\n\t\t\t\t\tstyle={\"background-color: rgba(\" +\n\t\t\t\t\t\t(score && score < 0\n\t\t\t\t\t\t\t? \"128, 90, 213,\" + -score\n\t\t\t\t\t\t\t: \"239, 68, 60,\" + score) +\n\t\t\t\t\t\t\")\"}\n\t\t\t\t>\n\t\t\t\t\t<span class=\"text\">{v.token}</span>\n\t\t\t\t</span>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--block-padding);\n\t}\n\t.hl + .hl {\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.textspan:last-child > .label {\n\t\tmargin-right: 0;\n\t}\n\n\t.category-legend {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-sm);\n\t\tcolor: black;\n\t}\n\n\t.category-label {\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.color-legend {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\tvar(--color-purple),\n\t\t\trgba(255, 255, 255, 0),\n\t\t\tvar(--color-red)\n\t\t);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.textfield {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: var(--background-fill-primary);\n\t\tbackground-color: transparent;\n\t\tmax-width: var(--size-full);\n\t\tline-height: var(--scale-4);\n\t\tword-break: break-all;\n\t}\n\n\t.textspan {\n\t\ttransition: 150ms;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t}\n\n\t.label {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: var(--body-text-color);\n\t\tcolor: white;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t}\n\n\t.text {\n\t\tcolor: black;\n\t\twhite-space: pre-wrap;\n\t}\n\n\t.score-text .text {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.score-text {\n\t\tmargin-right: var(--size-1);\n\t\tpadding: var(--size-1);\n\t}\n\n\t.no-cat {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-label {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n</style>\n", "<script lang=\"ts\">\n\ttype HighlightedTextType = {\n\t\ttoken: string;\n\t\tclass_or_confidence: string | number | null;\n\t};\n\n\texport let value: HighlightedTextType[];\n\texport let category: string | number | null;\n\texport let active: string;\n\texport let labelToEdit: number;\n\texport let indexOfLabel: number;\n\texport let text: string;\n\texport let handleValueChange: () => void;\n\texport let isScoresMode = false;\n\texport let _color_map: Record<string, { primary: string; secondary: string }>;\n\n\tlet _input_value = category;\n\n\tfunction handleInput(e: Event): void {\n\t\tlet target = e.target as HTMLInputElement;\n\t\tif (target) {\n\t\t\t_input_value = target.value;\n\t\t}\n\t}\n\n\tfunction updateLabelValue(\n\t\te: Event,\n\t\telementIndex: number,\n\t\ttext: string\n\t): void {\n\t\tlet target = e.target as HTMLInputElement;\n\t\tvalue = [\n\t\t\t...value.slice(0, elementIndex),\n\t\t\t{\n\t\t\t\ttoken: text,\n\t\t\t\tclass_or_confidence:\n\t\t\t\t\ttarget.value === \"\"\n\t\t\t\t\t\t? null\n\t\t\t\t\t\t: isScoresMode\n\t\t\t\t\t\t\t? Number(target.value)\n\t\t\t\t\t\t\t: target.value\n\t\t\t},\n\t\t\t...value.slice(elementIndex + 1)\n\t\t];\n\n\t\thandleValueChange();\n\t}\n\n\tfunction clearPlaceHolderOnFocus(e: FocusEvent): void {\n\t\tlet target = e.target as HTMLInputElement;\n\t\tif (target && target.placeholder) target.placeholder = \"\";\n\t}\n</script>\n\n<!-- svelte-ignore a11y-autofocus -->\n<!-- autofocus should not be disorienting for a screen reader users\nas input is only rendered once a new label is created -->\n{#if !isScoresMode}\n\t<input\n\t\tclass=\"label-input\"\n\t\tautofocus\n\t\tid={`label-input-${indexOfLabel}`}\n\t\ttype=\"text\"\n\t\tplaceholder=\"label\"\n\t\tvalue={category}\n\t\tstyle:background-color={category === null || (active && active !== category)\n\t\t\t? \"\"\n\t\t\t: _color_map[category].primary}\n\t\tstyle:width={_input_value\n\t\t\t? _input_value.toString()?.length + 4 + \"ch\"\n\t\t\t: \"8ch\"}\n\t\ton:input={handleInput}\n\t\ton:blur={(e) => updateLabelValue(e, indexOfLabel, text)}\n\t\ton:keydown={(e) => {\n\t\t\tif (e.key === \"Enter\") {\n\t\t\t\tupdateLabelValue(e, indexOfLabel, text);\n\t\t\t\tlabelToEdit = -1;\n\t\t\t}\n\t\t}}\n\t\ton:focus={clearPlaceHolderOnFocus}\n\t/>\n{:else}\n\t<input\n\t\tclass=\"label-input\"\n\t\tautofocus\n\t\ttype=\"number\"\n\t\tstep=\"0.1\"\n\t\tstyle={\"background-color: rgba(\" +\n\t\t\t(typeof category === \"number\" && category < 0\n\t\t\t\t? \"128, 90, 213,\" + -category\n\t\t\t\t: \"239, 68, 60,\" + category) +\n\t\t\t\")\"}\n\t\tvalue={category}\n\t\tstyle:width=\"7ch\"\n\t\ton:input={handleInput}\n\t\ton:blur={(e) => updateLabelValue(e, indexOfLabel, text)}\n\t\ton:keydown={(e) => {\n\t\t\tif (e.key === \"Enter\") {\n\t\t\t\tupdateLabelValue(e, indexOfLabel, text);\n\t\t\t\tlabelToEdit = -1;\n\t\t\t}\n\t\t}}\n\t/>\n{/if}\n\n<style>\n\t.label-input {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tmargin-right: calc(var(--size-1));\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: black;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t\tline-height: 1;\n\t\tcolor: white;\n\t}\n\n\t.label-input::placeholder {\n\t\tcolor: rgba(1, 1, 1, 0.5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\tconst browser = typeof document !== \"undefined\";\n\timport { get_next_color } from \"@gradio/utils\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { correct_color_map, merge_elements } from \"./utils\";\n\timport LabelInput from \"./LabelInput.svelte\";\n\n\texport let value: {\n\t\ttoken: string;\n\t\tclass_or_confidence: string | number | null;\n\t}[] = [];\n\texport let show_legend = false;\n\texport let color_map: Record<string, string> = {};\n\texport let selectable = false;\n\n\tlet activeElementIndex = -1;\n\tlet ctx: CanvasRenderingContext2D;\n\tlet _color_map: Record<string, { primary: string; secondary: string }> = {};\n\tlet active = \"\";\n\tlet selection: Selection | null;\n\tlet labelToEdit = -1;\n\n\tonMount(() => {\n\t\tconst mouseUpHandler = (): void => {\n\t\t\tselection = window.getSelection();\n\t\t\thandleSelectionComplete();\n\t\t\twindow.removeEventListener(\"mouseup\", mouseUpHandler);\n\t\t};\n\n\t\twindow.addEventListener(\"mousedown\", () => {\n\t\t\twindow.addEventListener(\"mouseup\", mouseUpHandler);\n\t\t});\n\t});\n\n\tasync function handleTextSelected(\n\t\tstartIndex: number,\n\t\tendIndex: number\n\t): Promise<void> {\n\t\tif (\n\t\t\tselection?.toString() &&\n\t\t\tactiveElementIndex !== -1 &&\n\t\t\tvalue[activeElementIndex].token.toString().includes(selection.toString())\n\t\t) {\n\t\t\tconst tempFlag = Symbol();\n\n\t\t\tconst str = value[activeElementIndex].token;\n\t\t\tconst [before, selected, after] = [\n\t\t\t\tstr.substring(0, startIndex),\n\t\t\t\tstr.substring(startIndex, endIndex),\n\t\t\t\tstr.substring(endIndex)\n\t\t\t];\n\n\t\t\tlet tempValue: {\n\t\t\t\ttoken: string;\n\t\t\t\tclass_or_confidence: string | number | null;\n\t\t\t\tflag?: symbol;\n\t\t\t}[] = [\n\t\t\t\t...value.slice(0, activeElementIndex),\n\t\t\t\t{ token: before, class_or_confidence: null },\n\t\t\t\t{\n\t\t\t\t\ttoken: selected,\n\t\t\t\t\tclass_or_confidence: mode === \"scores\" ? 1 : \"label\",\n\t\t\t\t\tflag: tempFlag\n\t\t\t\t}, // add a temp flag to the new highlighted text element\n\t\t\t\t{ token: after, class_or_confidence: null },\n\t\t\t\t...value.slice(activeElementIndex + 1)\n\t\t\t];\n\n\t\t\t// store the index of the new highlighted text element and remove the flag\n\t\t\tlabelToEdit = tempValue.findIndex(({ flag }) => flag === tempFlag);\n\t\t\t// tempValue[labelToEdit].pop();\n\n\t\t\t// remove elements with empty labels\n\t\t\ttempValue = tempValue.filter((item) => item.token.trim() !== \"\");\n\t\t\tvalue = tempValue.map(({ flag, ...rest }) => rest);\n\n\t\t\thandleValueChange();\n\t\t\tdocument.getElementById(`label-input-${labelToEdit}`)?.focus();\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t\tchange: typeof value;\n\t\tinput: never;\n\t}>();\n\n\tfunction splitTextByNewline(text: string): string[] {\n\t\treturn text.split(\"\\n\");\n\t}\n\n\tfunction removeHighlightedText(index: number): void {\n\t\tif (!value || index < 0 || index >= value.length) return;\n\t\tvalue[index].class_or_confidence = null;\n\t\tvalue = merge_elements(value, \"equal\");\n\t\thandleValueChange();\n\t\twindow.getSelection()?.empty();\n\t}\n\n\tfunction handleValueChange(): void {\n\t\tdispatch(\"change\", value);\n\t\tlabelToEdit = -1;\n\n\t\t// reset legend color maps\n\t\tif (show_legend) {\n\t\t\tcolor_map = {};\n\t\t\t_color_map = {};\n\t\t}\n\t}\n\n\tlet mode: \"categories\" | \"scores\";\n\n\t$: {\n\t\tif (!color_map) {\n\t\t\tcolor_map = {};\n\t\t}\n\t\tif (value.length > 0) {\n\t\t\tfor (let entry of value) {\n\t\t\t\tif (entry.class_or_confidence !== null) {\n\t\t\t\t\tif (typeof entry.class_or_confidence === \"string\") {\n\t\t\t\t\t\tmode = \"categories\";\n\t\t\t\t\t\tif (!(entry.class_or_confidence in color_map)) {\n\t\t\t\t\t\t\tlet color = get_next_color(Object.keys(color_map).length);\n\t\t\t\t\t\t\tcolor_map[entry.class_or_confidence] = color;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmode = \"scores\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcorrect_color_map(color_map, _color_map, browser, ctx);\n\t}\n\n\tfunction handle_mouseover(label: string): void {\n\t\tactive = label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = \"\";\n\t}\n\n\tasync function handleKeydownSelection(event: KeyboardEvent): Promise<void> {\n\t\tselection = window.getSelection();\n\n\t\tif (event.key === \"Enter\") {\n\t\t\thandleSelectionComplete();\n\t\t}\n\t}\n\n\tfunction handleSelectionComplete(): void {\n\t\tif (selection && selection?.toString().trim() !== \"\") {\n\t\t\tconst textBeginningIndex = selection.getRangeAt(0).startOffset;\n\t\t\tconst textEndIndex = selection.getRangeAt(0).endOffset;\n\t\t\thandleTextSelected(textBeginningIndex, textEndIndex);\n\t\t}\n\t}\n\n\tfunction handleSelect(\n\t\ti: number,\n\t\ttext: string,\n\t\tclass_or_confidence: string | number | null\n\t): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: i,\n\t\t\tvalue: [text, class_or_confidence]\n\t\t});\n\t}\n</script>\n\n<div class=\"container\">\n\t{#if mode === \"categories\"}\n\t\t{#if show_legend}\n\t\t\t<div\n\t\t\t\tclass=\"class_or_confidence-legend\"\n\t\t\t\tdata-testid=\"highlighted-text:class_or_confidence-legend\"\n\t\t\t>\n\t\t\t\t{#if _color_map}\n\t\t\t\t\t{#each Object.entries(_color_map) as [class_or_confidence, color], i}\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\taria-roledescription=\"Categories of highlighted text. Hover to see text with this class_or_confidence highlighted.\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:mouseover={() => handle_mouseover(class_or_confidence)}\n\t\t\t\t\t\t\ton:focus={() => handle_mouseover(class_or_confidence)}\n\t\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\t\tclass=\"class_or_confidence-label\"\n\t\t\t\t\t\t\tstyle={\"background-color:\" + color.secondary}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{class_or_confidence}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/if}\n\n\t\t<div class=\"textfield\">\n\t\t\t{#each value as { token, class_or_confidence }, i}\n\t\t\t\t{#each splitTextByNewline(token) as line, j}\n\t\t\t\t\t{#if line.trim() !== \"\"}\n\t\t\t\t\t\t<span class=\"text-class_or_confidence-container\">\n\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\tclass=\"textspan\"\n\t\t\t\t\t\t\t\tstyle:background-color={class_or_confidence === null ||\n\t\t\t\t\t\t\t\t(active && active !== class_or_confidence)\n\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t: class_or_confidence && _color_map[class_or_confidence]\n\t\t\t\t\t\t\t\t\t\t? _color_map[class_or_confidence].secondary\n\t\t\t\t\t\t\t\t\t\t: \"\"}\n\t\t\t\t\t\t\t\tclass:no-cat={class_or_confidence === null ||\n\t\t\t\t\t\t\t\t\t(active && active !== class_or_confidence)}\n\t\t\t\t\t\t\t\tclass:hl={class_or_confidence !== null}\n\t\t\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\t\tif (class_or_confidence !== null) {\n\t\t\t\t\t\t\t\t\t\thandleSelect(i, token, class_or_confidence);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\t\t\tif (class_or_confidence !== null) {\n\t\t\t\t\t\t\t\t\t\tlabelToEdit = i;\n\t\t\t\t\t\t\t\t\t\thandleSelect(i, token, class_or_confidence);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\thandleKeydownSelection(e);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:focus={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t\ton:mouseover={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass:no-label={class_or_confidence === null}\n\t\t\t\t\t\t\t\t\tclass=\"text\"\n\t\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\t\ton:keydown={(e) => handleKeydownSelection(e)}\n\t\t\t\t\t\t\t\t\ton:focus={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t\t\ton:mouseover={() => (activeElementIndex = i)}\n\t\t\t\t\t\t\t\t\ton:click={() => (labelToEdit = i)}\n\t\t\t\t\t\t\t\t\ttabindex=\"0\">{line}</span\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{#if !show_legend && class_or_confidence !== null && labelToEdit !== i}\n\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\tid={`label-tag-${i}`}\n\t\t\t\t\t\t\t\t\t\tclass=\"label\"\n\t\t\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\t\t\tstyle:background-color={class_or_confidence === null ||\n\t\t\t\t\t\t\t\t\t\t(active && active !== class_or_confidence)\n\t\t\t\t\t\t\t\t\t\t\t? \"\"\n\t\t\t\t\t\t\t\t\t\t\t: _color_map[class_or_confidence].primary}\n\t\t\t\t\t\t\t\t\t\ton:click={() => (labelToEdit = i)}\n\t\t\t\t\t\t\t\t\t\ton:keydown={() => (labelToEdit = i)}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t{class_or_confidence}\n\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t{#if labelToEdit === i && class_or_confidence !== null}\n\t\t\t\t\t\t\t\t\t&nbsp;\n\t\t\t\t\t\t\t\t\t<LabelInput\n\t\t\t\t\t\t\t\t\t\tbind:value\n\t\t\t\t\t\t\t\t\t\t{labelToEdit}\n\t\t\t\t\t\t\t\t\t\tcategory={class_or_confidence}\n\t\t\t\t\t\t\t\t\t\t{active}\n\t\t\t\t\t\t\t\t\t\t{_color_map}\n\t\t\t\t\t\t\t\t\t\tindexOfLabel={i}\n\t\t\t\t\t\t\t\t\t\ttext={token}\n\t\t\t\t\t\t\t\t\t\t{handleValueChange}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{#if class_or_confidence !== null}\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"label-clear-button\"\n\t\t\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\t\t\taria-roledescription=\"Remove label from text\"\n\t\t\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\t\t\ton:click={() => removeHighlightedText(i)}\n\t\t\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\t\t\tremoveHighlightedText(i);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t>×\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if j < splitTextByNewline(token).length - 1}\n\t\t\t\t\t\t<br />\n\t\t\t\t\t{/if}\n\t\t\t\t{/each}\n\t\t\t{/each}\n\t\t</div>\n\t{:else}\n\t\t{#if show_legend}\n\t\t\t<div class=\"color-legend\" data-testid=\"highlighted-text:color-legend\">\n\t\t\t\t<span>-1</span>\n\t\t\t\t<span>0</span>\n\t\t\t\t<span>+1</span>\n\t\t\t</div>\n\t\t{/if}\n\n\t\t<div class=\"textfield\" data-testid=\"highlighted-text:textfield\">\n\t\t\t{#each value as { token, class_or_confidence }, i}\n\t\t\t\t{@const score =\n\t\t\t\t\ttypeof class_or_confidence === \"string\"\n\t\t\t\t\t\t? parseInt(class_or_confidence)\n\t\t\t\t\t\t: class_or_confidence}\n\t\t\t\t<span class=\"score-text-container\">\n\t\t\t\t\t<span\n\t\t\t\t\t\tclass=\"textspan score-text\"\n\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\tclass:no-cat={class_or_confidence === null ||\n\t\t\t\t\t\t\t(active && active !== class_or_confidence)}\n\t\t\t\t\t\tclass:hl={class_or_confidence !== null}\n\t\t\t\t\t\ton:mouseover={() => (activeElementIndex = i)}\n\t\t\t\t\t\ton:focus={() => (activeElementIndex = i)}\n\t\t\t\t\t\ton:click={() => (labelToEdit = i)}\n\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\tlabelToEdit = i;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tstyle={\"background-color: rgba(\" +\n\t\t\t\t\t\t\t(score && score < 0\n\t\t\t\t\t\t\t\t? \"128, 90, 213,\" + -score\n\t\t\t\t\t\t\t\t: \"239, 68, 60,\" + score) +\n\t\t\t\t\t\t\t\")\"}\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"text\">{token}</span>\n\t\t\t\t\t\t{#if class_or_confidence && labelToEdit === i}\n\t\t\t\t\t\t\t<LabelInput\n\t\t\t\t\t\t\t\tbind:value\n\t\t\t\t\t\t\t\t{labelToEdit}\n\t\t\t\t\t\t\t\t{_color_map}\n\t\t\t\t\t\t\t\tcategory={class_or_confidence}\n\t\t\t\t\t\t\t\t{active}\n\t\t\t\t\t\t\t\tindexOfLabel={i}\n\t\t\t\t\t\t\t\ttext={token}\n\t\t\t\t\t\t\t\t{handleValueChange}\n\t\t\t\t\t\t\t\tisScoresMode\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</span>\n\t\t\t\t\t{#if class_or_confidence && activeElementIndex === i}\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\tclass=\"label-clear-button\"\n\t\t\t\t\t\t\trole=\"button\"\n\t\t\t\t\t\t\taria-roledescription=\"Remove label from text\"\n\t\t\t\t\t\t\ttabindex=\"0\"\n\t\t\t\t\t\t\ton:click={() => removeHighlightedText(i)}\n\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\tremoveHighlightedText(i);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t>×\n\t\t\t\t\t\t</span>\n\t\t\t\t\t{/if}\n\t\t\t\t</span>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.label-clear-button {\n\t\tdisplay: none;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tuser-select: none;\n\t\tposition: relative;\n\t\tleft: -3px;\n\t\tborder-radius: 0 var(--radius-xs) var(--radius-xs) 0;\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\t.text-class_or_confidence-container:hover .label-clear-button,\n\t.text-class_or_confidence-container:focus-within .label-clear-button,\n\t.score-text-container:hover .label-clear-button,\n\t.score-text-container:focus-within .label-clear-button {\n\t\tdisplay: inline;\n\t}\n\n\t.text-class_or_confidence-container:hover .textspan.hl,\n\t.text-class_or_confidence-container:focus-within .textspan.hl,\n\t.score-text:hover {\n\t\tborder-radius: var(--radius-xs) 0 0 var(--radius-xs);\n\t}\n\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--block-padding);\n\t}\n\n\t.hl {\n\t\tmargin-left: var(--size-1);\n\t\ttransition: background-color 0.3s;\n\t\tuser-select: none;\n\t}\n\n\t.textspan:last-child > .label {\n\t\tmargin-right: 0;\n\t}\n\n\t.class_or_confidence-legend {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-sm);\n\t\tcolor: black;\n\t}\n\n\t.class_or_confidence-label {\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-right: var(--size-2);\n\t\tpadding-left: var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.color-legend {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\tvar(--color-purple),\n\t\t\trgba(255, 255, 255, 0),\n\t\t\tvar(--color-red)\n\t\t);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n\n\t.textfield {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: var(--radius-xs);\n\t\tbackground: var(--background-fill-primary);\n\t\tbackground-color: transparent;\n\t\tmax-width: var(--size-full);\n\t\tline-height: var(--scale-4);\n\t\tword-break: break-all;\n\t}\n\n\t.textspan {\n\t\ttransition: 150ms;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding-top: 2.5px;\n\t\tpadding-right: var(--size-1);\n\t\tpadding-bottom: 3.5px;\n\t\tpadding-left: var(--size-1);\n\t\tcolor: black;\n\t\tcursor: text;\n\t}\n\n\t.label {\n\t\ttransition: 150ms;\n\t\tmargin-top: 1px;\n\t\tborder-radius: var(--radius-xs);\n\t\tpadding: 1px 5px;\n\t\tcolor: var(--body-text-color);\n\t\tcolor: white;\n\t\tfont-weight: var(--weight-bold);\n\t\tfont-size: var(--text-sm);\n\t\ttext-transform: uppercase;\n\t\tuser-select: none;\n\t}\n\n\t.text {\n\t\tcolor: black;\n\t\twhite-space: pre-wrap;\n\t}\n\n\t.textspan.hl {\n\t\tuser-select: none;\n\t}\n\n\t.score-text-container {\n\t\tmargin-right: var(--size-1);\n\t}\n\n\t.score-text .text {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-cat {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.no-label {\n\t\tcolor: var(--body-text-color);\n\t\tuser-select: text;\n\t}\n\n\t.selectable {\n\t\tcursor: text;\n\t\tuser-select: text;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseStaticHighlightedText } from \"./shared/StaticHighlightedtext.svelte\";\n\texport { default as BaseInteractiveHighlightedText } from \"./shared/InteractiveHighlightedtext.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, I18nFormatter } from \"@gradio/utils\";\n\timport StaticHighlightedText from \"./shared/StaticHighlightedtext.svelte\";\n\timport InteractiveHighlightedText from \"./shared/InteractiveHighlightedtext.svelte\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { TextHighlight } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { merge_elements } from \"./shared/utils\";\n\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t\tchange: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: {\n\t\ttoken: string;\n\t\tclass_or_confidence: string | number | null;\n\t}[];\n\tlet old_value: typeof value;\n\texport let show_legend: boolean;\n\texport let show_inline_category: boolean;\n\texport let color_map: Record<string, string> = {};\n\texport let label = gradio.i18n(\"highlighted_text.highlighted_text\");\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let _selectable = false;\n\texport let combine_adjacent = false;\n\texport let interactive: boolean;\n\texport let show_label = true;\n\n\t$: if (!color_map && Object.keys(color_map).length) {\n\t\tcolor_map = color_map;\n\t}\n\n\texport let loading_status: LoadingStatus;\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\t$: if (value && combine_adjacent) {\n\t\tvalue = merge_elements(value, \"equal\");\n\t}\n</script>\n\n{#if !interactive}\n\t<Block\n\t\tvariant={\"solid\"}\n\t\ttest_id=\"highlighted-text\"\n\t\t{visible}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\tpadding={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t\t{#if label && show_label}\n\t\t\t<BlockLabel\n\t\t\t\tIcon={TextHighlight}\n\t\t\t\t{label}\n\t\t\t\tfloat={false}\n\t\t\t\tdisable={container === false}\n\t\t\t\t{show_label}\n\t\t\t/>\n\t\t{/if}\n\n\t\t{#if value}\n\t\t\t<StaticHighlightedText\n\t\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\t\tselectable={_selectable}\n\t\t\t\t{value}\n\t\t\t\t{show_legend}\n\t\t\t\t{show_inline_category}\n\t\t\t\t{color_map}\n\t\t\t/>\n\t\t{:else}\n\t\t\t<Empty>\n\t\t\t\t<TextHighlight />\n\t\t\t</Empty>\n\t\t{/if}\n\t</Block>\n{:else}\n\t<Block\n\t\tvariant={interactive ? \"dashed\" : \"solid\"}\n\t\ttest_id=\"highlighted-text\"\n\t\t{visible}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\tpadding={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\t{...loading_status}\n\t\t\ti18n={gradio.i18n}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t\t{#if label && show_label}\n\t\t\t<BlockLabel\n\t\t\t\tIcon={TextHighlight}\n\t\t\t\t{label}\n\t\t\t\tfloat={false}\n\t\t\t\tdisable={container === false}\n\t\t\t\t{show_label}\n\t\t\t/>\n\t\t{/if}\n\n\t\t{#if value}\n\t\t\t<InteractiveHighlightedText\n\t\t\t\tbind:value\n\t\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\t\tselectable={_selectable}\n\t\t\t\t{show_legend}\n\t\t\t\t{color_map}\n\t\t\t/>\n\t\t{:else}\n\t\t\t<Empty size=\"small\" unpadded_box={true}>\n\t\t\t\t<TextHighlight />\n\t\t\t</Empty>\n\t\t{/if}\n\t</Block>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "name_to_rgba", "name", "a", "ctx", "canvas", "r", "g", "b", "correct_color_map", "color_map", "_color_map", "browser", "col", "_c", "colors", "merge_elements", "value", "mergeMode", "result", "tempStr", "tempVal", "val", "child_ctx", "create_if_block_5", "i", "div", "create_if_block_4", "t0_value", "attr", "span1", "span1_style_value", "span0", "dirty", "set_data", "t0", "if_block", "create_if_block_3", "t1_value", "span", "t1", "br", "splitTextByNewline", "create_if_block", "text", "$$props", "show_legend", "show_inline_category", "selectable", "active", "dispatch", "createEventDispatcher", "mode", "handle_mouseover", "label", "handle_mouseout", "mouseover_handler", "category", "focus_handler", "v", "$$invalidate", "entry", "color", "get_next_color", "input", "input_style_value", "set_style", "clearPlaceHolderOnFocus", "e", "labelToEdit", "indexOfLabel", "handleValueChange", "isScoresMode", "_input_value", "handleInput", "updateLabelValue", "elementIndex", "blur_handler", "blur_handler_1", "constants_0", "create_if_block_10", "each_blocks", "create_if_block_6", "create_if_block_9", "create_if_block_8", "toggle_class", "span2", "current", "create_if_block_7", "if_block0", "if_block1", "if_block2", "t_value", "show_if", "each_value_1", "ensure_array_like", "activeElementIndex", "selection", "onMount", "mouseUpHandler", "handleSelectionComplete", "handleTextSelected", "startIndex", "endIndex", "tempFlag", "str", "before", "selected", "after", "tempValue", "flag", "item", "rest", "removeHighlightedText", "index", "handleKeydownSelection", "event", "textBeginningIndex", "textEndIndex", "handleSelect", "class_or_confidence", "focus_handler_1", "mouseover_handler_1", "click_handler", "click_handler_1", "keydown_handler_1", "token", "focus_handler_2", "mouseover_handler_2", "click_handler_3", "mouseover_handler_3", "focus_handler_3", "click_handler_4", "click_handler_5", "TextHighlight", "blocklabel_changes", "create_if_block_2", "gradio", "elem_id", "elem_classes", "visible", "old_value", "container", "scale", "min_width", "_selectable", "combine_adjacent", "interactive", "show_label", "loading_status", "clear_status_handler", "select_handler", "detail", "clear_status_handler_1"], "mappings": "2nCAAAA,EAmBKC,EAAAC,EAAAC,CAAA,EARJC,EAGCF,EAAAG,CAAA,EACDD,EAGCF,EAAAI,CAAA,kGChBc,SAAAC,GACfC,EACAC,EACAC,EACS,CACT,GAAI,CAACA,EAAK,CACL,IAAAC,EAAS,SAAS,cAAc,QAAQ,EACtCD,EAAAC,EAAO,WAAW,IAAI,CAC7B,CACAD,EAAI,UAAYF,EAChBE,EAAI,SAAS,EAAG,EAAG,EAAG,CAAC,EACjB,KAAA,CAACE,EAAGC,EAAGC,CAAC,EAAIJ,EAAI,aAAa,EAAG,EAAG,EAAG,CAAC,EAAE,KAC/C,OAAAA,EAAI,UAAU,EAAG,EAAG,EAAG,CAAC,EACjB,QAAQE,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAK,IAAML,CAAC,GACzC,CAEO,SAASM,GACfC,EACAC,EACAC,EACAR,EACO,CACP,UAAWS,KAAOH,EAAW,CAC5B,MAAMI,EAAKJ,EAAUG,CAAG,EAAE,KAAK,EAE3BC,KAAMC,GACEJ,EAAAE,CAAG,EAAIE,GAAOD,CAAyB,EAElDH,EAAWE,CAAG,EAAI,CACjB,QAASD,EACNX,GAAaS,EAAUG,CAAG,EAAG,EAAGT,CAAG,EACnCM,EAAUG,CAAG,EAChB,UAAWD,EACRX,GAAaS,EAAUG,CAAG,EAAG,GAAKT,CAAG,EACrCM,EAAUG,CAAG,CAAA,CAGnB,CACD,CAEgB,SAAAG,GACfC,EACAC,EACmE,CACnE,IAAIC,EAAuB,CAAA,EACvBC,EAAyB,KACzBC,EAAkC,KAEtC,UAAWC,KAAOL,EAGUI,IAAYC,EAAI,oBAE1CF,EAAUA,EAAUA,EAAUE,EAAI,MAAQA,EAAI,OAE1CF,IAAY,MACfD,EAAO,KAAK,CACX,MAAOC,EACP,oBAAqBC,CAAA,CACrB,EAEFD,EAAUE,EAAI,MACdD,EAAUC,EAAI,qBAIhB,OAAIF,IAAY,MACfD,EAAO,KAAK,CACX,MAAOC,EACP,oBAAqBC,CAAA,CACrB,EAGKF,CACR,gECgFYI,EAAC,EAAA,EAAC,qBAAwB,SAC9B,SAASA,EAAC,EAAA,EAAC,mBAAmB,EAC9BA,MAAE,kRAZHnB,EAAW,CAAA,GAAAoB,GAAA,MAQRpB,EAAK,CAAA,CAAA,uBAAV,OAAIqB,GAAA,mNADP/B,EAiBKC,EAAA+B,EAAA7B,CAAA,8DAxBAO,EAAW,CAAA,oEAQRA,EAAK,CAAA,CAAA,oBAAV,OAAIqB,GAAA,EAAA,mHAAJ,yEA/EErB,EAAW,CAAA,GAAAuB,GAAAvB,CAAA,MAsBRA,EAAK,CAAA,CAAA,uBAAV,OAAIqB,GAAA,mKADP/B,EAgDKC,EAAA+B,EAAA7B,CAAA,8DArEAO,EAAW,CAAA,+EAsBRA,EAAK,CAAA,CAAA,oBAAV,OAAIqB,GAAA,EAAA,mHAAJ,6PAkDF/B,EAIKC,EAAA+B,EAAA7B,CAAA,yCAgBiB+B,EAAAxB,MAAE,MAAK,4IANpByB,EAAAC,EAAA,QAAAC,EAAA,2BACL3B,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,GAAG,UANLV,EASMC,EAAAmC,EAAAjC,CAAA,EADLC,EAAkCgC,EAAAE,CAAA,wBAAdC,EAAA,GAAAL,KAAAA,EAAAxB,MAAE,MAAK,KAAA8B,EAAAC,EAAAP,CAAA,EANpBK,EAAA,GAAAF,KAAAA,EAAA,2BACL3B,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,8DArFK,OAAO,QAAQA,EAAU,CAAA,CAAA,CAAA,uBAA9B,OAAIqB,GAAA,2LAJP/B,EAkBKC,EAAA+B,EAAA7B,CAAA,yEAdG,OAAO,QAAQO,EAAU,CAAA,CAAA,CAAA,oBAA9B,OAAIqB,GAAA,EAAA,mHAAJ,sDAWCrB,EAAQ,EAAA,EAAA,wKAFF,oBAAsBA,EAAK,EAAA,EAAC,SAAS,UAN7CV,EASKC,EAAA+B,EAAA7B,CAAA,uKA+BYO,EAAI,EAAA,EAAA,WAEbgC,EAAA,CAAAhC,MAAeA,EAAoB,CAAA,GAAIA,EAAE,EAAA,EAAA,sBAAwB,MAAIiC,GAAAjC,CAAA,mJAJ1DA,EAAC,EAAA,EAAC,sBAAwB,OACxCA,EAAU,CAAA,EAACA,EAAC,EAAA,EAAC,mBAAmB,CAAA,qDAbrBA,EAAC,EAAA,EAAC,sBAAwB,MACtCA,EAAM,CAAA,GAAIA,EAAM,CAAA,IAAKA,EAAC,EAAA,EAAC,mBAAmB,WAClCA,EAAC,EAAA,EAAC,sBAAwB,IAAI,gDANhBA,EAAC,EAAA,EAAC,sBAAwB,MACjDA,EAAM,CAAA,GAAIA,EAAM,CAAA,IAAKA,EAAC,EAAA,EAAC,oBACrB,GACAA,EAAW,CAAA,EAAAA,EAAE,EAAA,EAAA,mBAAmB,EAAE,SAAS,UAL/CV,EAkCMC,EAAAmC,EAAAjC,CAAA,EAjBLC,EAIAgC,EAAAE,CAAA,kFADe5B,EAAI,EAAA,EAAA,KAAA8B,EAAAC,EAAAP,CAAA,uBAFFxB,EAAC,EAAA,EAAC,sBAAwB,OACxCA,EAAU,CAAA,EAACA,EAAC,EAAA,EAAC,mBAAmB,CAAA,EAG7B,CAAAA,MAAeA,EAAoB,CAAA,GAAIA,EAAE,EAAA,EAAA,sBAAwB,kFAhBzDA,EAAC,EAAA,EAAC,sBAAwB,MACtCA,EAAM,CAAA,GAAIA,EAAM,CAAA,IAAKA,EAAC,EAAA,EAAC,mBAAmB,gBAClCA,EAAC,EAAA,EAAC,sBAAwB,IAAI,2DANhBA,EAAC,EAAA,EAAC,sBAAwB,MACjDA,EAAM,CAAA,GAAIA,EAAM,CAAA,IAAKA,EAAC,EAAA,EAAC,oBACrB,GACAA,EAAW,CAAA,EAAAA,EAAE,EAAA,EAAA,mBAAmB,EAAE,SAAS,2DA0B3CkC,EAAAlC,MAAE,oBAAmB,oBATmD;AAAA,SAE1E,+EAEyBA,EAAC,EAAA,EAAC,sBAAwB,MACjDA,EAAM,CAAA,GAAIA,EAAM,CAAA,IAAKA,EAAC,EAAA,EAAC,oBACrB,GACAA,EAAW,CAAA,EAAAA,EAAE,EAAA,EAAA,mBAAmB,EAAE,OAAO,mBAL7CV,EAQMC,EAAA4C,EAAA1C,CAAA,iBADJoC,EAAA,GAAAK,KAAAA,EAAAlC,MAAE,oBAAmB,KAAA8B,EAAAM,EAAAF,CAAA,+BALElC,EAAC,EAAA,EAAC,sBAAwB,MACjDA,EAAM,CAAA,GAAIA,EAAM,CAAA,IAAKA,EAAC,EAAA,EAAC,oBACrB,GACAA,EAAW,CAAA,EAAAA,EAAE,EAAA,EAAA,mBAAmB,EAAE,OAAO,2EAQ/CV,EAAKC,EAAA8C,EAAA5C,CAAA,uCAzCDO,EAAI,EAAA,EAAC,KAAI,IAAO,OAwChBA,EAAC,EAAA,EAAGsC,GAAmBtC,MAAE,KAAK,EAAE,OAAS,oIAxCzCA,EAAI,EAAA,EAAC,KAAI,IAAO,gFAwChBA,EAAC,EAAA,EAAGsC,GAAmBtC,MAAE,KAAK,EAAE,OAAS,kIAzCxCsC,GAAmBtC,EAAC,EAAA,EAAC,KAAK,CAAA,uBAA/B,OAAIqB,GAAA,gKAACiB,GAAmBtC,EAAC,EAAA,EAAC,KAAK,CAAA,oBAA/B,OAAIqB,GAAA,EAAA,2HAAJ,oEAxBA,OAAArB,OAAS,aAAYuC,mGAD3BjD,EAmGKC,EAAA+B,EAAA7B,CAAA,+HAxJK6C,GAAmBE,EAAA,CACpB,OAAAA,EAAK,MAAM;AAAA,CAAI,qBApBjB,MAAAhC,EAAA,OAAiB,SAAa,IAMzB,GAAA,CAAA,MAAAK,EAAA,EAAA,EAAA4B,GAIA,YAAAC,EAAc,EAAA,EAAAD,GACd,qBAAAE,EAAuB,EAAA,EAAAF,EACvB,CAAA,UAAAnC,EAAA,EAAA,EAAAmC,GACA,WAAAG,EAAa,EAAA,EAAAH,EAEpBzC,EACAO,EAAA,CAAA,EACAsC,EAAS,SAMPC,EAAWC,KAIb,IAAAC,WAyBKC,EAAiBC,EAAA,KACzBL,EAASK,CAAA,EAED,SAAAC,GAAA,KACRN,EAAS,EAAA,EA2Be,MAAAO,EAAAC,GAAAJ,EAAiBI,CAAQ,EAC7BC,EAAAD,GAAAJ,EAAiBI,CAAQ,QACtBF,UACJA,cA2BbL,EAAS,SAAQ,CAChB,MAAOzB,EACP,OAAQkC,EAAE,MAAOA,EAAE,mBAAmB,sQAtF9C,CAIK,GAHCjD,GACJkD,EAAA,GAAAlD,EAAA,CAAA,CAAA,EAEGO,EAAM,OAAS,WACT4C,KAAS5C,EACb,GAAA4C,EAAM,sBAAwB,KACtB,GAAA,OAAAA,EAAM,qBAAwB,UAElC,OADNT,EAAO,YAAA,EACD,EAAAS,EAAM,uBAAuBnD,GAAA,CAC9B,IAAAoD,EAAQC,GAAe,OAAO,KAAKrD,CAAS,EAAE,MAAM,OACxDA,EAAUmD,EAAM,mBAAmB,EAAIC,EAAApD,CAAA,YAGxC0C,EAAO,QAAA,EAMX3C,GAAkBC,EAAWC,EAAYC,EAASR,CAAG,qxBCqC9CyB,EAAAmC,EAAA,QAAAC,EAAA,kCACE7D,EAAQ,CAAA,GAAK,UAAYA,EAAW,CAAA,EAAA,EACzC,gBAAmB,CAAAA,EAAA,CAAA,EACnB,eAAiBA,EAAQ,CAAA,GAC5B,GAAG,UACGA,EAAQ,CAAA,6BAVhBV,EAoBCC,EAAAqE,EAAAnE,CAAA,+BARUO,EAAW,CAAA,CAAA,wDAPd6B,EAAA,GAAAgC,KAAAA,EAAA,kCACE7D,EAAQ,CAAA,GAAK,UAAYA,EAAW,CAAA,EAAA,EACzC,gBAAmB,CAAAA,EAAA,CAAA,EACnB,eAAiBA,EAAQ,CAAA,GAC5B,oCACMA,EAAQ,CAAA,YAARA,EAAQ,CAAA,4MA/BIA,EAAY,CAAA,CAAA,EAAA,wDAGxBA,EAAQ,CAAA,EACS8D,EAAAF,EAAA,mBAAA5D,OAAa,MAASA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,CAAA,EACxE,GACAA,EAAU,CAAA,EAACA,EAAQ,CAAA,CAAA,EAAE,OAAO,EAClB8D,EAAAF,EAAA,QAAA5D,EAAA,CAAA,EACVA,KAAa,SAAQ,GAAI,OAAS,EAAI,KACtC,KAAK,UAZTV,EAsBCC,EAAAqE,EAAAnE,CAAA,+BATUO,EAAW,CAAA,CAAA,qDAQX+D,EAAuB,4CAlBd/D,EAAY,CAAA,CAAA,gCAGxBA,EAAQ,CAAA,YAARA,EAAQ,CAAA,SACS8D,EAAAF,EAAA,mBAAA5D,OAAa,MAASA,EAAU,CAAA,GAAAA,OAAWA,EAAQ,CAAA,EACxE,GACAA,EAAU,CAAA,EAACA,EAAQ,CAAA,CAAA,EAAE,OAAO,SAClB8D,EAAAF,EAAA,QAAA5D,EAAA,CAAA,EACVA,KAAa,SAAQ,GAAI,OAAS,EAAI,KACtC,KAAK,wEAbJA,EAAY,CAAA,KAAAuC,gMATRwB,GAAwBC,EAAA,CAC5B,IAAAzE,EAASyE,EAAE,OACXzE,GAAUA,EAAO,cAAaA,EAAO,YAAc,uBA5C7C,GAAA,CAAA,MAAAsB,CAAA,EAAA4B,EACA,CAAA,SAAAY,CAAA,EAAAZ,EACA,CAAA,OAAAI,CAAA,EAAAJ,EACA,CAAA,YAAAwB,CAAA,EAAAxB,EACA,CAAA,aAAAyB,CAAA,EAAAzB,EACA,CAAA,KAAAD,CAAA,EAAAC,EACA,CAAA,kBAAA0B,CAAA,EAAA1B,GACA,aAAA2B,EAAe,EAAA,EAAA3B,EACf,CAAA,WAAAlC,CAAA,EAAAkC,EAEP4B,EAAehB,WAEViB,EAAYN,EAAA,CAChB,IAAAzE,EAASyE,EAAE,OACXzE,GACHiE,EAAA,EAAAa,EAAe9E,EAAO,KAAA,EAIf,SAAAgF,EACRP,EACAQ,EACAhC,EAAAA,CAEI,IAAAjD,EAASyE,EAAE,OACfR,EAAA,GAAA3C,EAAA,IACIA,EAAM,MAAM,EAAG2D,CAAY,GAE7B,MAAOhC,EACP,oBACCjD,EAAO,QAAU,GACd,KACA6E,EACC,OAAO7E,EAAO,KAAK,EACnBA,EAAO,UAEVsB,EAAM,MAAM2D,EAAe,CAAC,IAGhCL,IA2BU,MAAAM,EAAAT,GAAMO,EAAiBP,EAAGE,EAAc1B,CAAI,IACzCwB,GAAC,CACTA,EAAE,MAAQ,UACbO,EAAiBP,EAAGE,EAAc1B,CAAI,EACtCgB,EAAA,EAAAS,IAAgB,IAmBRS,EAAAV,GAAMO,EAAiBP,EAAGE,EAAc1B,CAAI,IACzCwB,GAAC,CACTA,EAAE,MAAQ,UACbO,EAAiBP,EAAGE,EAAc1B,CAAI,EACtCgB,EAAA,EAAAS,IAAgB,m6CCiNR,MAAAU,EAAA,OAAAxD,OAAwB,SAC5B,SAASA,EAAmB,EAAA,CAAA,EAC5BA,EAAmB,EAAA,uSAbpBnB,EAAW,CAAA,GAAA4E,GAAA,MASR5E,EAAK,CAAA,CAAA,uBAAV,OAAIqB,GAAA,2PADP/B,EA4DKC,EAAA+B,EAAA7B,CAAA,mEApEAO,EAAW,CAAA,yEASRA,EAAK,CAAA,CAAA,oBAAV,OAAIqB,GAAA,EAAA,2GAAJ,OAAIA,EAAAwD,EAAA,OAAAxD,GAAA,yCAAJ,OAAIA,GAAA,sJArIFrB,EAAW,CAAA,GAAA8E,GAAA9E,CAAA,MA0BRA,EAAK,CAAA,CAAA,uBAAV,OAAIqB,GAAA,2MADP/B,EAiGKC,EAAA+B,EAAA7B,CAAA,mEA1HAO,EAAW,CAAA,oFA0BRA,EAAK,CAAA,CAAA,oBAAV,OAAIqB,GAAA,EAAA,2GAAJ,OAAIA,EAAAwD,EAAA,OAAAxD,GAAA,yCAAJ,OAAIA,GAAA,yUAmGN/B,EAIKC,EAAA+B,EAAA7B,CAAA,mHAqCUO,EAAmB,EAAA,2BAEfA,EAAC,EAAA,OACTA,EAAK,EAAA,6QAHDA,EAAmB,EAAA,4CAGvBA,EAAK,EAAA,8aAObV,EAYMC,EAAA4C,EAAA1C,CAAA,iHA5BcO,EAAK,EAAA,EAAA,qBACpBA,EAAmB,EAAA,GAAIA,EAAW,CAAA,IAAKA,EAAC,EAAA,GAAA+E,GAAA/E,CAAA,qJAczCA,EAAmB,EAAA,GAAIA,EAAkB,CAAA,IAAKA,EAAC,EAAA,GAAAgF,GAAAhF,CAAA,0NArB5CyB,EAAAC,EAAA,QAAAC,EAAA,2BACL3B,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,GAAG,EAfUiF,EAAAvD,EAAA,SAAA1B,QAAwB,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,CAAA,EAChCiF,EAAAvD,EAAA,KAAA1B,QAAwB,IAAI,6DAPxCV,EAoDMC,EAAA2F,EAAAzF,CAAA,EAnDLC,EAmCMwF,EAAAxD,CAAA,EAdLhC,EAAgCgC,EAAAE,CAAA,kLAAZ5B,EAAK,EAAA,EAAA,KAAA8B,EAAAC,EAAAP,CAAA,EACpBxB,EAAmB,EAAA,GAAIA,EAAW,CAAA,IAAKA,EAAC,EAAA,sGAPtC,CAAAmF,GAAAtD,EAAA,CAAA,EAAA,GAAAF,KAAAA,EAAA,2BACL3B,EAAS,EAAA,GAAAA,EAAQ,EAAA,EAAA,EACf,gBAAmB,CAAAA,EAAA,EAAA,EACnB,eAAiBA,EAAK,EAAA,GACzB,qCAfaiF,EAAAvD,EAAA,SAAA1B,QAAwB,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,CAAA,gBAChCiF,EAAAvD,EAAA,KAAA1B,QAAwB,IAAI,EA8BlCA,EAAmB,EAAA,GAAIA,EAAkB,CAAA,IAAKA,EAAC,EAAA,iKA1KhDA,EAAU,CAAA,GAAAoF,GAAApF,CAAA,mKAJhBV,EAqBKC,EAAA+B,EAAA7B,CAAA,yBAjBCO,EAAU,CAAA,8GACP,OAAO,QAAQA,EAAU,CAAA,CAAA,CAAA,uBAA9B,OAAIqB,GAAA,oKAAC,OAAO,QAAQrB,EAAU,CAAA,CAAA,CAAA,oBAA9B,OAAIqB,GAAA,EAAA,2HAAJ,sDAYCrB,EAAmB,EAAA,EAAA,4VAFb,oBAAsBA,EAAK,EAAA,EAAC,SAAS,UAT7CV,EAYKC,EAAA+B,EAAA7B,CAAA,mIADHO,EAAmB,EAAA,EAAA,KAAA8B,EAAAC,EAAAP,CAAA,iBAFb,oBAAsBxB,EAAK,EAAA,EAAC,gFAoDnBA,EAAI,EAAA,EAAA,oHAEb,IAAAqF,EAAA,CAAArF,MAAeA,EAAmB,EAAA,IAAK,MAAQA,OAAgBA,EAAC,EAAA,GAAAoB,GAAApB,CAAA,EAgBjEsF,EAAAtF,EAAgB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAwB,MAAIuB,GAAAvB,CAAA,uKAclD,IAAAuF,EAAAvF,QAAwB,MAAIiC,GAAAjC,CAAA,iLAvCfiF,EAAArD,EAAA,WAAA5B,QAAwB,IAAI,kFArB/BiF,EAAAvD,EAAA,SAAA1B,QAAwB,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,CAAA,EAChCiF,EAAAvD,EAAA,KAAA1B,QAAwB,IAAI,yBARd8D,EAAApC,EAAA,mBAAA1B,QAAwB,MAC/CA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,EACtC,GACAA,EAAmB,EAAA,GAAIA,EAAU,CAAA,EAACA,EAAmB,EAAA,CAAA,EACpDA,EAAU,CAAA,EAACA,EAAqB,EAAA,CAAA,EAAA,UAChC,EAAE,2EAVRV,EAsFMC,EAAA2F,EAAAzF,CAAA,EArFLC,EAqEMwF,EAAAxD,CAAA,EAvCLhC,EASAgC,EAAAE,CAAA,uQADe5B,EAAI,EAAA,EAAA,KAAA8B,EAAAC,EAAAP,CAAA,gBAPFyD,EAAArD,EAAA,WAAA5B,QAAwB,IAAI,EASvC,CAAAA,MAAeA,EAAmB,EAAA,IAAK,MAAQA,OAAgBA,EAAC,EAAA,yDAgBjEA,EAAgB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAwB,uHA9CpCiF,EAAAvD,EAAA,SAAA1B,QAAwB,MACpCA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,CAAA,gBAChCiF,EAAAvD,EAAA,KAAA1B,QAAwB,IAAI,gDARd8D,EAAApC,EAAA,mBAAA1B,QAAwB,MAC/CA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,EACtC,GACAA,EAAmB,EAAA,GAAIA,EAAU,CAAA,EAACA,EAAmB,EAAA,CAAA,EACpDA,EAAU,CAAA,EAACA,EAAqB,EAAA,CAAA,EAAA,UAChC,EAAE,EA6DFA,QAAwB,gLAjBzBA,EAAmB,EAAA,EAAA,+HAXHA,EAAC,EAAA,CAAA,EAAA,+EAIM8D,EAAA3B,EAAA,mBAAAnC,QAAwB,MAC/CA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,EACtC,GACAA,EAAU,CAAA,EAACA,EAAmB,EAAA,CAAA,EAAE,OAAO,UAR3CV,EAaMC,EAAA4C,EAAA1C,CAAA,kFADJO,EAAmB,EAAA,EAAA,KAAA8B,EAAA,EAAA0D,CAAA,WAPI1B,EAAA3B,EAAA,mBAAAnC,QAAwB,MAC/CA,EAAU,CAAA,GAAAA,OAAWA,EAAmB,EAAA,EACtC,GACAA,EAAU,CAAA,EAACA,EAAmB,EAAA,CAAA,EAAE,OAAO,+GAYhCA,EAAmB,EAAA,2CAGfA,EAAC,EAAA,OACTA,EAAK,EAAA,yHATyC;AAAA,UAErD,uHAGWA,EAAmB,EAAA,wEAIvBA,EAAK,EAAA,sbAMbV,EAYMC,EAAA4C,EAAA1C,CAAA,wIAKRH,EAAKC,EAAA8C,EAAA5C,CAAA,uCA1FDO,EAAI,EAAA,EAAC,KAAI,IAAO,KAyFhByF,EAAAzF,MAAIsC,GAAmBtC,EAAO,EAAA,CAAA,EAAA,OAAS,8IAzFvCA,EAAI,EAAA,EAAC,KAAI,IAAO,gHAyFhB6B,EAAA,CAAA,EAAA,IAAA4D,EAAAzF,MAAIsC,GAAmBtC,EAAO,EAAA,CAAA,EAAA,OAAS,qKA1FtC0F,EAAAC,EAAArD,GAAmBtC,EAAK,EAAA,CAAA,CAAA,uBAA7B,OAAIqB,GAAA,6MAACqE,EAAAC,EAAArD,GAAmBtC,EAAK,EAAA,CAAA,CAAA,oBAA7B,OAAIqB,GAAA,EAAA,mHAAJ,OAAIA,EAAAwD,EAAA,OAAAxD,GAAA,yCAAJ,OAAIA,GAAA,0KA5BJ,OAAArB,OAAS,aAAY,uGAD3BV,EAoMKC,EAAA+B,EAAA7B,CAAA,wOAvRK6C,GAAmBE,EAAA,CACpB,OAAAA,EAAK,MAAM;AAAA,CAAI,qBAxFjB,MAAAhC,EAAA,OAAiB,SAAa,IAOzB,GAAA,CAAA,MAAAK,EAAA,EAAA,EAAA4B,GAIA,YAAAC,EAAc,EAAA,EAAAD,EACd,CAAA,UAAAnC,EAAA,EAAA,EAAAmC,GACA,WAAAG,EAAa,EAAA,EAAAH,EAEpBmD,EAAqB,GACrB5F,EACAO,EAAA,CAAA,EACAsC,EAAS,GACTgD,EACA5B,EAAc,GAElB6B,GAAA,IAAA,CACO,MAAAC,EAAA,IAAA,CACLF,EAAY,OAAO,eACnBG,IACA,OAAO,oBAAoB,UAAWD,CAAc,GAGrD,OAAO,iBAAiB,YAAA,IAAA,CACvB,OAAO,iBAAiB,UAAWA,CAAc,MAIpC,eAAAE,EACdC,EACAC,EAAA,IAGCN,GAAW,SAAA,GACXD,QACA/E,EAAM+E,CAAkB,EAAE,MAAM,SAAW,EAAA,SAASC,EAAU,SAAA,CAAA,EAAA,OAExDO,EAAW,SAEXC,EAAMxF,EAAM+E,CAAkB,EAAE,MAC/B,CAAAU,GAAQC,GAAUC,EAAK,EAAA,CAC7BH,EAAI,UAAU,EAAGH,CAAU,EAC3BG,EAAI,UAAUH,EAAYC,CAAQ,EAClCE,EAAI,UAAUF,CAAQ,GAGnB,IAAAM,GAAA,IAKA5F,EAAM,MAAM,EAAG+E,CAAkB,EAClC,CAAA,MAAOU,GAAQ,oBAAqB,IAAA,GAErC,MAAOC,GACP,oBAAqBvD,IAAS,SAAW,EAAI,QAC7C,KAAMoD,GAEL,CAAA,MAAOI,GAAO,oBAAqB,IAAA,KAClC3F,EAAM,MAAM+E,EAAqB,CAAC,GAItCpC,EAAA,EAAAS,EAAcwC,GAAU,UAAA,CAAA,CAAa,KAAAC,MAAWA,KAASN,CAAQ,CAAA,EAIjEK,GAAYA,GAAU,OAAQE,IAASA,GAAK,MAAM,SAAW,EAAE,EAC/DnD,EAAA,EAAA3C,EAAQ4F,GAAU,IAAA,CAAA,CAAO,KAAAC,GAAS,GAAAE,MAAWA,EAAI,CAAA,EAEjDzC,IACA,SAAS,eAAA,eAA8BF,CAAW,EAAK,GAAA,eAInDnB,EAAWC,cAUR8D,EAAsBC,EAAA,EACzBjG,GAASiG,EAAQ,GAAKA,GAASjG,EAAM,aAC1CA,EAAMiG,CAAK,EAAE,oBAAsB,KAAAjG,CAAA,MACnCA,EAAQD,GAAeC,CAAc,CAAA,EACrCsD,IACA,OAAO,gBAAgB,SAGf,SAAAA,GAAA,CACRrB,EAAS,SAAUjC,CAAK,MACxBoD,EAAc,EAAA,EAGVvB,IACHc,EAAA,GAAAlD,EAAA,CAAA,CAAA,EACAkD,EAAA,EAAAjD,EAAA,CAAA,CAAA,GAIE,IAAAyC,WAyBKC,EAAiBC,EAAA,KACzBL,EAASK,CAAA,EAED,SAAAC,GAAA,KACRN,EAAS,EAAA,iBAGKkE,EAAuBC,EAAA,CACrCnB,EAAY,OAAO,eAEfmB,EAAM,MAAQ,SACjBhB,IAIO,SAAAA,GAAA,IACJH,GAAaA,GAAW,WAAW,KAAW,IAAA,GAAA,OAC3CoB,EAAqBpB,EAAU,WAAW,CAAC,EAAE,YAC7CqB,EAAerB,EAAU,WAAW,CAAC,EAAE,UAC7CI,EAAmBgB,EAAoBC,CAAY,GAI5C,SAAAC,EACR9F,EACAmB,EACA4E,EAAA,CAEAtE,EAAS,SAAA,CACR,MAAOzB,EACP,MAAA,CAAQmB,EAAM4E,CAAmB,IAkBT,MAAAhE,GAAAgE,GAAAnE,EAAiBmE,CAAmB,EACxC9D,GAAA8D,GAAAnE,EAAiBmE,CAAmB,QACjCjE,WACJA,OAkDAa,GAAM+C,EAAuB/C,CAAC,EAC1BqD,GAAAhG,GAAAmC,EAAA,EAAAoC,EAAqBvE,CAAC,EAClBiG,GAAAjG,GAAAmC,EAAA,EAAAoC,EAAqBvE,CAAC,EAC1BkG,GAAAlG,GAAAmC,EAAA,EAAAS,EAAc5C,CAAC,EAadmG,GAAAnG,GAAAmC,EAAA,EAAAS,EAAc5C,CAAC,EACboG,GAAApG,GAAAmC,EAAA,EAAAS,EAAc5C,CAAC,+CApC/B+F,IAAwB,MAC3BD,EAAa9F,EAAGqG,EAAON,CAAmB,aAG/BpD,IAAC,CACToD,IAAwB,MAC3B5D,EAAA,EAAAS,EAAc5C,CAAC,EACf8F,EAAa9F,EAAGqG,EAAON,CAAmB,GAE1CL,EAAuB/C,CAAC,GAGT2D,GAAAtG,GAAAmC,EAAA,EAAAoC,EAAqBvE,CAAC,EAClBuG,GAAAvG,GAAAmC,EAAA,EAAAoC,EAAqBvE,CAAC,EAgD1BwG,GAAAxG,GAAAwF,EAAsBxF,CAAC,QAC1B2F,IAAK,CACbA,EAAM,MAAQ,SACjBH,EAAsBxF,CAAC,6BAqCP,MAAAyG,GAAAzG,GAAAmC,EAAA,EAAAoC,EAAqBvE,CAAC,EAC1B0G,GAAA1G,GAAAmC,EAAA,EAAAoC,EAAqBvE,CAAC,EACtB2G,GAAA3G,GAAAmC,EAAA,EAAAS,EAAc5C,CAAC,QACnB2C,IAAC,CACTA,EAAE,MAAQ,SACbR,EAAA,EAAAS,EAAc5C,CAAC,GA8BA4G,GAAA5G,GAAAwF,EAAsBxF,CAAC,QAC1B2F,IAAK,CACbA,EAAM,MAAQ,SACjBH,EAAsBxF,CAAC,8MApP/B,CAIK,GAHCf,GACJkD,EAAA,GAAAlD,EAAA,CAAA,CAAA,EAEGO,EAAM,OAAS,WACT4C,KAAS5C,EACb,GAAA4C,EAAM,sBAAwB,KACtB,GAAA,OAAAA,EAAM,qBAAwB,UAElC,OADNT,EAAO,YAAA,EACD,EAAAS,EAAM,uBAAuBnD,GAAA,CAC9B,IAAAoD,EAAQC,GAAe,OAAO,KAAKrD,CAAS,EAAE,MAAM,OACxDA,EAAUmD,EAAM,mBAAmB,EAAIC,EAAApD,CAAA,YAGxC0C,EAAO,QAAA,EAMX3C,GAAkBC,EAAWC,EAAYC,EAASR,CAAG,8nBC9B5CA,EAAW,EAAA,EAAG,SAAW,uFAKzB,0KALAA,EAAW,EAAA,EAAG,SAAW,wVA3CzB,uFAKA,meAwDDkI,oBAEC,GACE,QAAAlI,OAAc,4GAAd6B,EAAA,MAAAsG,EAAA,QAAAnI,OAAc,qMAcU,+SALrBA,EAAW,EAAA,oOAAXA,EAAW,EAAA,2YAnBZ,CAAA,WAAAA,KAAO,UAAU,EACzBA,EAAc,EAAA,EACZ,CAAA,KAAAA,KAAO,IAAI,qGAGb,IAAAqF,EAAArF,MAASA,EAAU,EAAA,GAAAuB,GAAAvB,CAAA,8CAUnBA,EAAK,CAAA,EAAA,+LAfG6B,EAAA,GAAA,CAAA,WAAA7B,KAAO,UAAU,cACzBA,EAAc,EAAA,CAAA,EACZ6B,EAAA,GAAA,CAAA,KAAA7B,KAAO,IAAI,iBAGbA,MAASA,EAAU,EAAA,wbAzChBkI,oBAEC,GACE,QAAAlI,OAAc,4GAAd6B,EAAA,MAAAsG,EAAA,QAAAnI,OAAc,+cAQXA,EAAW,EAAA,qLAAXA,EAAW,EAAA,0ZAlBZ,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,qGAGd,IAAAqF,EAAArF,MAASA,EAAU,EAAA,GAAAoI,GAAApI,CAAA,8CAUnBA,EAAK,CAAA,EAAA,+LAfG6B,EAAA,GAAA,CAAA,WAAA7B,KAAO,UAAU,EACvB6B,EAAA,GAAA,CAAA,KAAA7B,KAAO,IAAI,cACbA,EAAc,EAAA,CAAA,iBAGdA,MAASA,EAAU,EAAA,2cAlBpBA,EAAW,EAAA,IAAA,yTA3CL,GAAA,CAAA,OAAAqI,CAAA,EAAA5F,GAKA,QAAA6F,EAAU,EAAA,EAAA7F,EACV,CAAA,aAAA8F,EAAA,EAAA,EAAA9F,GACA,QAAA+F,EAAU,EAAA,EAAA/F,EACV,CAAA,MAAA5B,CAAA,EAAA4B,EAIPgG,EACO,CAAA,YAAA/F,CAAA,EAAAD,EACA,CAAA,qBAAAE,CAAA,EAAAF,EACA,CAAA,UAAAnC,EAAA,EAAA,EAAAmC,GACA,MAAAS,EAAQmF,EAAO,KAAK,mCAAmC,CAAA,EAAA5F,GACvD,UAAAiG,EAAY,EAAA,EAAAjG,GACZ,MAAAkG,EAAuB,IAAA,EAAAlG,GACvB,UAAAmG,EAAgC,MAAA,EAAAnG,GAChC,YAAAoG,EAAc,EAAA,EAAApG,GACd,iBAAAqG,EAAmB,EAAA,EAAArG,EACnB,CAAA,YAAAsG,CAAA,EAAAtG,GACA,WAAAuG,EAAa,EAAA,EAAAvG,EAMb,CAAA,eAAAwG,CAAA,EAAAxG,EA8Bc,MAAAyG,EAAA,IAAAb,EAAO,SAAS,eAAgBY,CAAc,EActDE,EAAA,CAAA,CAAA,OAAAC,KAAaf,EAAO,SAAS,SAAUe,CAAM,EA6BrCC,EAAA,IAAAhB,EAAO,SAAS,eAAgBY,CAAc,iDAenDZ,EAAO,SAAS,QAAQ,4rBA5F5C,CAAQ/H,GAAa,OAAO,KAAKA,CAAS,EAAE,iCAarCO,GAASiI,OACfjI,EAAQD,GAAeC,CAAc,CAAA,qBAPjCA,IAAU4H,SACbA,EAAY5H,CAAA,EACZwH,EAAO,SAAS,QAAQ"}