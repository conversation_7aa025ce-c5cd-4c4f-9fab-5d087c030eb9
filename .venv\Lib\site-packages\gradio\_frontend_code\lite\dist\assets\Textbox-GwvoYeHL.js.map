{"version": 3, "file": "Textbox-GwvoYeHL.js", "sources": ["../../../textbox/shared/Textbox.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport {\n\t\tbeforeUpdate,\n\t\tafterUpdate,\n\t\tcreateEventDispatcher,\n\t\ttick\n\t} from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Copy, Check, Send, Square } from \"@gradio/icons\";\n\timport { fade } from \"svelte/transition\";\n\timport type { SelectData, CopyData } from \"@gradio/utils\";\n\n\texport let value = \"\";\n\texport let value_is_output = false;\n\texport let lines = 1;\n\texport let placeholder = \"Type here...\";\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let disabled = false;\n\texport let show_label = true;\n\texport let container = true;\n\texport let max_lines: number;\n\texport let type: \"text\" | \"password\" | \"email\" = \"text\";\n\texport let show_copy_button = false;\n\texport let submit_btn: string | boolean | null = null;\n\texport let stop_btn: string | boolean | null = null;\n\texport let rtl = false;\n\texport let autofocus = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\texport let autoscroll = true;\n\texport let max_length: number | undefined = undefined;\n\texport let root: string;\n\n\tlet el: HTMLTextAreaElement | HTMLInputElement;\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\tlet can_scroll: boolean;\n\tlet previous_scroll_top = 0;\n\tlet user_has_scrolled_up = false;\n\n\tconst show_textbox_border = !submit_btn;\n\n\t$: value, el && lines !== max_lines && resize({ target: el });\n\n\t$: if (value === null) value = \"\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tsubmit: undefined;\n\t\tstop: undefined;\n\t\tblur: undefined;\n\t\tselect: SelectData;\n\t\tinput: undefined;\n\t\tfocus: undefined;\n\t\tcopy: CopyData;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tcan_scroll = el && el.offsetHeight + el.scrollTop > el.scrollHeight - 100;\n\t});\n\n\tconst scroll = (): void => {\n\t\tif (can_scroll && autoscroll && !user_has_scrolled_up) {\n\t\t\tel.scrollTo(0, el.scrollHeight);\n\t\t}\n\t};\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tif (autofocus) {\n\t\t\tel.focus();\n\t\t}\n\t\tif (can_scroll && autoscroll) {\n\t\t\tscroll();\n\t\t}\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tdispatch(\"copy\", { value: value });\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tfunction handle_select(event: Event): void {\n\t\tconst target: HTMLTextAreaElement | HTMLInputElement = event.target as\n\t\t\t| HTMLTextAreaElement\n\t\t\t| HTMLInputElement;\n\t\tconst text = target.value;\n\t\tconst index: [number, number] = [\n\t\t\ttarget.selectionStart as number,\n\t\t\ttarget.selectionEnd as number\n\t\t];\n\t\tdispatch(\"select\", { value: text.substring(...index), index: index });\n\t}\n\n\tasync function handle_keypress(e: KeyboardEvent): Promise<void> {\n\t\tawait tick();\n\t\tif (e.key === \"Enter\" && e.shiftKey && lines > 1) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t} else if (\n\t\t\te.key === \"Enter\" &&\n\t\t\t!e.shiftKey &&\n\t\t\tlines === 1 &&\n\t\t\tmax_lines >= 1\n\t\t) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t}\n\t}\n\n\tfunction handle_scroll(event: Event): void {\n\t\tconst target = event.target as HTMLElement;\n\t\tconst current_scroll_top = target.scrollTop;\n\t\tif (current_scroll_top < previous_scroll_top) {\n\t\t\tuser_has_scrolled_up = true;\n\t\t}\n\t\tprevious_scroll_top = current_scroll_top;\n\n\t\tconst max_scroll_top = target.scrollHeight - target.clientHeight;\n\t\tconst user_has_scrolled_to_bottom = current_scroll_top >= max_scroll_top;\n\t\tif (user_has_scrolled_to_bottom) {\n\t\t\tuser_has_scrolled_up = false;\n\t\t}\n\t}\n\n\tfunction handle_stop(): void {\n\t\tdispatch(\"stop\");\n\t}\n\n\tfunction handle_submit(): void {\n\t\tdispatch(\"submit\");\n\t}\n\n\tasync function resize(\n\t\tevent: Event | { target: HTMLTextAreaElement | HTMLInputElement }\n\t): Promise<void> {\n\t\tawait tick();\n\t\tif (lines === max_lines) return;\n\n\t\tconst target = event.target as HTMLTextAreaElement;\n\t\tconst computed_styles = window.getComputedStyle(target);\n\t\tconst padding_top = parseFloat(computed_styles.paddingTop);\n\t\tconst padding_bottom = parseFloat(computed_styles.paddingBottom);\n\t\tconst line_height = parseFloat(computed_styles.lineHeight);\n\n\t\tlet max =\n\t\t\tmax_lines === undefined\n\t\t\t\t? false\n\t\t\t\t: padding_top + padding_bottom + line_height * max_lines;\n\t\tlet min = padding_top + padding_bottom + lines * line_height;\n\n\t\ttarget.style.height = \"1px\";\n\n\t\tlet scroll_height;\n\t\tif (max && target.scrollHeight > max) {\n\t\t\tscroll_height = max;\n\t\t} else if (target.scrollHeight < min) {\n\t\t\tscroll_height = min;\n\t\t} else {\n\t\t\tscroll_height = target.scrollHeight;\n\t\t}\n\n\t\ttarget.style.height = `${scroll_height}px`;\n\t}\n\n\tfunction text_area_resize(\n\t\t_el: HTMLTextAreaElement,\n\t\t_value: string\n\t): any | undefined {\n\t\tif (lines === max_lines) return;\n\t\t_el.style.overflowY = \"scroll\";\n\t\t_el.addEventListener(\"input\", resize);\n\n\t\tif (!_value.trim()) return;\n\t\tresize({ target: _el });\n\n\t\treturn {\n\t\t\tdestroy: () => _el.removeEventListener(\"input\", resize)\n\t\t};\n\t}\n</script>\n\n<!-- svelte-ignore a11y-autofocus -->\n<label class:container class:show_textbox_border>\n\t{#if show_label && show_copy_button}\n\t\t{#if copied}\n\t\t\t<button\n\t\t\t\tin:fade={{ duration: 300 }}\n\t\t\t\tclass=\"copy-button\"\n\t\t\t\taria-label=\"Copied\"\n\t\t\t\taria-roledescription=\"Text copied\"><Check /></button\n\t\t\t>\n\t\t{:else}\n\t\t\t<button\n\t\t\t\ton:click={handle_copy}\n\t\t\t\tclass=\"copy-button\"\n\t\t\t\taria-label=\"Copy\"\n\t\t\t\taria-roledescription=\"Copy text\"><Copy /></button\n\t\t\t>\n\t\t{/if}\n\t{/if}\n\t<BlockTitle {root} {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"input-container\">\n\t\t{#if lines === 1 && max_lines === 1}\n\t\t\t{#if type === \"text\"}\n\t\t\t\t<input\n\t\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\tbind:value\n\t\t\t\t\tbind:this={el}\n\t\t\t\t\t{placeholder}\n\t\t\t\t\t{disabled}\n\t\t\t\t\t{autofocus}\n\t\t\t\t\tmaxlength={max_length}\n\t\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\t\ton:blur\n\t\t\t\t\ton:select={handle_select}\n\t\t\t\t\ton:focus\n\t\t\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t\t\t/>\n\t\t\t{:else if type === \"password\"}\n\t\t\t\t<input\n\t\t\t\t\tdata-testid=\"password\"\n\t\t\t\t\ttype=\"password\"\n\t\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\t\tbind:value\n\t\t\t\t\tbind:this={el}\n\t\t\t\t\t{placeholder}\n\t\t\t\t\t{disabled}\n\t\t\t\t\t{autofocus}\n\t\t\t\t\tmaxlength={max_length}\n\t\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\t\ton:blur\n\t\t\t\t\ton:select={handle_select}\n\t\t\t\t\ton:focus\n\t\t\t\t\tautocomplete=\"\"\n\t\t\t\t/>\n\t\t\t{:else if type === \"email\"}\n\t\t\t\t<input\n\t\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\t\ttype=\"email\"\n\t\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\t\tbind:value\n\t\t\t\t\tbind:this={el}\n\t\t\t\t\t{placeholder}\n\t\t\t\t\t{disabled}\n\t\t\t\t\t{autofocus}\n\t\t\t\t\tmaxlength={max_length}\n\t\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\t\ton:blur\n\t\t\t\t\ton:select={handle_select}\n\t\t\t\t\ton:focus\n\t\t\t\t\tautocomplete=\"email\"\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t{:else}\n\t\t\t<textarea\n\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\tuse:text_area_resize={value}\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\tclass:no-label={!show_label && (submit_btn || stop_btn)}\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\trows={lines}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\tmaxlength={max_length}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\ton:scroll={handle_scroll}\n\t\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t\t/>\n\t\t{/if}\n\t\t{#if submit_btn}\n\t\t\t<button\n\t\t\t\tclass=\"submit-button\"\n\t\t\t\tclass:padded-button={submit_btn !== true}\n\t\t\t\ton:click={handle_submit}\n\t\t\t>\n\t\t\t\t{#if submit_btn === true}\n\t\t\t\t\t<Send />\n\t\t\t\t{:else}\n\t\t\t\t\t{submit_btn}\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t\t{#if stop_btn}\n\t\t\t<button\n\t\t\t\tclass=\"stop-button\"\n\t\t\t\tclass:padded-button={stop_btn !== true}\n\t\t\t\ton:click={handle_stop}\n\t\t\t>\n\t\t\t\t{#if stop_btn === true}\n\t\t\t\t\t<Square fill=\"none\" stroke_width={2.5} />\n\t\t\t\t{:else}\n\t\t\t\t\t{stop_btn}\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t</div>\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t}\n\n\tinput,\n\ttextarea {\n\t\tflex-grow: 1;\n\t\toutline: none !important;\n\t\tmargin-top: 0px;\n\t\tmargin-bottom: 0px;\n\t\tresize: none;\n\t\tz-index: 1;\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--input-padding);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--input-text-weight);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\tborder: none;\n\t}\n\ttextarea.no-label {\n\t\tpadding-top: 5px;\n\t\tpadding-bottom: 5px;\n\t}\n\tlabel.show_textbox_border input,\n\tlabel.show_textbox_border textarea {\n\t\tbox-shadow: var(--input-shadow);\n\t}\n\tlabel:not(.container),\n\tlabel:not(.container) input,\n\tlabel:not(.container) textarea {\n\t\theight: 100%;\n\t}\n\tlabel.container.show_textbox_border input,\n\tlabel.container.show_textbox_border textarea {\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t}\n\tinput:disabled,\n\ttextarea:disabled {\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tlabel.container.show_textbox_border input:focus,\n\tlabel.container.show_textbox_border textarea:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t\tbackground: var(--input-background-fill-focus);\n\t}\n\n\tinput::placeholder,\n\ttextarea::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\t.copy-button {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\talign-items: center;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: 5px;\n\t\twidth: 22px;\n\t\theight: 22px;\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-color);\n\t\tfont: var(--font-sans);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n\n\t/* Same submit button style as MultimodalTextbox for the consistent UI */\n\t.input-container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\talign-items: flex-end;\n\t}\n\t.submit-button,\n\t.stop-button {\n\t\tborder: none;\n\t\ttext-align: center;\n\t\ttext-decoration: none;\n\t\tfont-size: 14px;\n\t\tcursor: pointer;\n\t\tborder-radius: 15px;\n\t\tmin-width: 30px;\n\t\theight: 30px;\n\t\tflex-shrink: 0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: var(--layer-1);\n\t}\n\t.stop-button,\n\t.submit-button {\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\t.stop-button:hover,\n\t.submit-button:hover {\n\t\tbackground: var(--button-secondary-background-fill-hover);\n\t}\n\t.stop-button:disabled,\n\t.submit-button:disabled {\n\t\tbackground: var(--button-secondary-background-fill);\n\t\tcursor: pointer;\n\t}\n\t.stop-button:active,\n\t.submit-button:active {\n\t\tbox-shadow: var(--button-shadow-active);\n\t}\n\t.submit-button :global(svg) {\n\t\theight: 22px;\n\t\twidth: 22px;\n\t}\n\n\t.stop-button :global(svg) {\n\t\theight: 16px;\n\t\twidth: 16px;\n\t}\n\t.padded-button {\n\t\tpadding: 0 10px;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "button", "anchor", "button_intro", "create_in_transition", "fade", "textarea", "create_if_block_5", "create_if_block_6", "create_if_block_7", "input", "toggle_class", "if_block0", "create_if_block_8", "create_if_block_4", "create_if_block_2", "create_if_block", "label_1", "append", "div", "value", "$$props", "value_is_output", "lines", "placeholder", "label", "info", "disabled", "show_label", "container", "max_lines", "type", "show_copy_button", "submit_btn", "stop_btn", "rtl", "autofocus", "text_align", "autoscroll", "max_length", "root", "el", "copied", "timer", "can_scroll", "previous_scroll_top", "user_has_scrolled_up", "show_textbox_border", "dispatch", "createEventDispatcher", "beforeUpdate", "scroll", "handle_change", "afterUpdate", "handle_copy", "copy_feedback", "handle_select", "event", "text", "index", "handle_keypress", "e", "tick", "handle_scroll", "current_scroll_top", "max_scroll_top", "handle_stop", "handle_submit", "resize", "computed_styles", "padding_top", "padding_bottom", "line_height", "max", "min", "scroll_height", "text_area_resize", "_el", "_value", "$$value"], "mappings": "mpBA2MOA,EAAM,EAAA,EAAA,+eAQVC,EAKAC,EAAAC,EAAAC,CAAA,qCAJWJ,EAAW,EAAA,CAAA,qTARtBC,EAKAC,EAAAC,EAAAC,CAAA,mEAJYC,EAAAC,GAAAH,EAAAI,GAAA,CAAA,SAAU,GAAG,CAAA,6GAcaP,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,+JA8DrCA,EAAG,EAAA,EAAG,MAAQ,KAAK,qCAKlBA,EAAK,CAAA,CAAA,oDAGAA,EAAU,EAAA,CAAA,gBAMdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,kBAbnCA,EAAU,CAAA,IAAKA,EAAU,EAAA,GAAIA,EAAQ,EAAA,EAAA,UALvDC,EAmBCC,EAAAM,EAAAJ,CAAA,oEAjBsBJ,EAAK,CAAA,CAAA,CAAA,oCAWdA,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,kCAEbA,EAAa,EAAA,CAAA,mCAbnBA,EAAG,EAAA,EAAG,MAAQ,wEAKbA,EAAK,CAAA,CAAA,yFAGAA,EAAU,EAAA,CAAA,qBAMdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,gEAhB5BA,EAAK,CAAA,CAAA,+CAGVA,EAAU,CAAA,IAAKA,EAAU,EAAA,GAAIA,EAAQ,EAAA,EAAA,6EA3DlD,GAAAA,OAAS,OAAM,OAAAS,GAkBV,GAAAT,OAAS,WAAU,OAAAU,GAiBnB,GAAAV,OAAS,QAAO,OAAAW,sZAUbX,EAAU,EAAA,CAAA,sCATtBC,EAeCC,EAAAU,EAAAR,CAAA,+EALaJ,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,2JAHbA,EAAU,EAAA,CAAA,oSAjBVA,EAAU,EAAA,CAAA,iCATtBC,EAeCC,EAAAU,EAAAR,CAAA,+EALaJ,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,2JAHbA,EAAU,EAAA,CAAA,qOAvBhBA,EAAG,EAAA,EAAG,MAAQ,KAAK,4EAMbA,EAAU,EAAA,CAAA,gBAKdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,UAfrDC,EAgBCC,EAAAU,EAAAR,CAAA,+EALaJ,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,sDATnBA,EAAG,EAAA,EAAG,MAAQ,4IAMRA,EAAU,EAAA,CAAA,qBAKdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,gKAiE9C,OAAAA,QAAe,GAAI,sGAHHa,EAAAV,EAAA,gBAAAH,QAAe,EAAI,UAFzCC,EAUQC,EAAAC,EAAAC,CAAA,wCAPGJ,EAAa,EAAA,CAAA,oKADFa,EAAAV,EAAA,gBAAAH,QAAe,EAAI,8GAMtCA,EAAU,EAAA,CAAA,2CAAVA,EAAU,EAAA,CAAA,yQAUP,OAAAA,QAAa,GAAI,oGAHDa,EAAAV,EAAA,gBAAAH,QAAa,EAAI,UAFvCC,EAUQC,EAAAC,EAAAC,CAAA,wCAPGJ,EAAW,EAAA,CAAA,oKADAa,EAAAV,EAAA,gBAAAH,QAAa,EAAI,8GAMpCA,EAAQ,EAAA,CAAA,2CAARA,EAAQ,EAAA,CAAA,kGAFyB,GAAG,uKApHpCc,EAAAd,MAAcA,EAAgB,EAAA,GAAAe,GAAAf,CAAA,kHAoB7B,OAAAA,EAAU,CAAA,IAAA,GAAKA,OAAc,EAACgB,0BA4E9BhB,EAAU,EAAA,GAAAiB,GAAAjB,CAAA,IAaVA,EAAQ,EAAA,GAAAkB,GAAAlB,CAAA,0PA9GfC,EA4HOC,EAAAiB,EAAAf,CAAA,2CAxGNgB,EAuGKD,EAAAE,CAAA,uEA1HArB,MAAcA,EAAgB,EAAA,wTAgG7BA,EAAU,EAAA,oGAaVA,EAAQ,EAAA,yTA3SH,MAAAsB,EAAQ,EAAA,EAAAC,GACR,gBAAAC,EAAkB,EAAA,EAAAD,GAClB,MAAAE,EAAQ,CAAA,EAAAF,GACR,YAAAG,EAAc,cAAA,EAAAH,EACd,CAAA,MAAAI,CAAA,EAAAJ,GACA,KAAAK,EAA2B,MAAA,EAAAL,GAC3B,SAAAM,EAAW,EAAA,EAAAN,GACX,WAAAO,EAAa,EAAA,EAAAP,GACb,UAAAQ,EAAY,EAAA,EAAAR,EACZ,CAAA,UAAAS,CAAA,EAAAT,GACA,KAAAU,EAAsC,MAAA,EAAAV,GACtC,iBAAAW,EAAmB,EAAA,EAAAX,GACnB,WAAAY,EAAsC,IAAA,EAAAZ,GACtC,SAAAa,EAAoC,IAAA,EAAAb,GACpC,IAAAc,EAAM,EAAA,EAAAd,GACN,UAAAe,EAAY,EAAA,EAAAf,GACZ,WAAAgB,GAA2C,MAAA,EAAAhB,GAC3C,WAAAiB,EAAa,EAAA,EAAAjB,GACb,WAAAkB,GAAiC,MAAA,EAAAlB,EACjC,CAAA,KAAAmB,EAAA,EAAAnB,EAEPoB,EACAC,EAAS,GACTC,EACAC,EACAC,GAAsB,EACtBC,EAAuB,SAErBC,GAAuB,CAAAd,EAMvBe,EAAWC,KAWjBC,GAAA,IAAA,CACCN,EAAaH,GAAMA,EAAG,aAAeA,EAAG,UAAYA,EAAG,aAAe,MAGjE,MAAAU,GAAA,IAAA,CACDP,GAAcN,GAAe,CAAAQ,GAChCL,EAAG,SAAS,EAAGA,EAAG,YAAY,GAIvB,SAAAW,IAAA,CACRJ,EAAS,SAAU5B,CAAK,EACnBE,GACJ0B,EAAS,OAAO,EAGlBK,GAAA,IAAA,CACKjB,GACHK,EAAG,MAAA,EAEAG,GAAcN,GACjBa,UAED7B,EAAkB,EAAA,IAIJ,eAAAgC,IAAA,CACV,cAAe,kBACZ,UAAU,UAAU,UAAUlC,CAAK,EACzC4B,EAAS,OAAU,CAAA,MAAA5B,CAAA,CAAA,EACnBmC,MAIO,SAAAA,IAAA,MACRb,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,qBACPD,EAAS,EAAA,GACP,cAGKc,GAAcC,EAAA,CAChB,MAAAzD,EAAiDyD,EAAM,OAGvDC,EAAO1D,EAAO,MACd2D,EACL,CAAA3D,EAAO,eACPA,EAAO,YAAA,EAERgD,EAAS,SAAY,CAAA,MAAOU,EAAK,UAAA,GAAaC,CAAK,EAAG,MAAAA,CAAA,CAAA,iBAGxCC,GAAgBC,EAAA,CACxB,MAAAC,GAAA,GACFD,EAAE,MAAQ,SAAWA,EAAE,UAAYtC,EAAQ,GAI9CsC,EAAE,MAAQ,SACT,CAAAA,EAAE,UACHtC,IAAU,GACVO,GAAa,KAEb+B,EAAE,eAAA,EACFb,EAAS,QAAQ,YAIVe,GAAcN,EAAA,CAChB,MAAAzD,EAASyD,EAAM,OACfO,EAAqBhE,EAAO,UAC9BgE,EAAqBnB,KACxBC,EAAuB,IAExBD,GAAsBmB,QAEhBC,EAAiBjE,EAAO,aAAeA,EAAO,aAChBgE,GAAsBC,IAEzDnB,EAAuB,IAIhB,SAAAoB,IAAA,CACRlB,EAAS,MAAM,EAGP,SAAAmB,IAAA,CACRnB,EAAS,QAAQ,iBAGHoB,EACdX,EAAA,IAEM,MAAAK,GAAA,EACFvC,IAAUO,EAAA,OAER,MAAA9B,EAASyD,EAAM,OACfY,EAAkB,OAAO,iBAAiBrE,CAAM,EAChDsE,EAAc,WAAWD,EAAgB,UAAU,EACnDE,EAAiB,WAAWF,EAAgB,aAAa,EACzDG,GAAc,WAAWH,EAAgB,UAAU,EAErD,IAAAI,GACH3C,IAAc,OACX,GACAwC,EAAcC,EAAiBC,GAAc1C,EAC7C4C,GAAMJ,EAAcC,EAAiBhD,EAAQiD,GAEjDxE,EAAO,MAAM,OAAS,MAElB,IAAA2E,EACAF,IAAOzE,EAAO,aAAeyE,GAChCE,EAAgBF,GACNzE,EAAO,aAAe0E,GAChCC,EAAgBD,GAEhBC,EAAgB3E,EAAO,aAGxBA,EAAO,MAAM,UAAY2E,CAAa,KAG9B,SAAAC,GACRC,EACAC,EAAA,IAEIvD,IAAUO,IACd+C,EAAI,MAAM,UAAY,SACtBA,EAAI,iBAAiB,QAAST,CAAM,IAE/BU,EAAO,KAAA,GACZ,OAAAV,EAAA,CAAS,OAAQS,CAAA,CAAA,GAGhB,QAAe,IAAAA,EAAI,oBAAoB,QAAST,CAAM,+UAmCzC3B,EAAEsC,wFAiBFtC,EAAEsC,wFAiBFtC,EAAEsC,wFAoBHtC,EAAEsC,iwBAhPT3D,IAAU,UAAMA,EAAQ,EAAA,wBAFrBqB,GAAMlB,IAAUO,GAAasC,GAAS,OAAQ3B,CAAA,CAAA,mBAwC9CW,GAAA"}