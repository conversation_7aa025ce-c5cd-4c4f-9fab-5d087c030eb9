import{a as ne,i as re,s as se,f as k,y as F,w as I,b as J,z as c,A as y,d as M,C as z,x as R,D as te,l as S,o as ae,a5 as Te,aa as Ue,$ as U,e as oe,h as ue,t as L,j as fe,k as D,ax as Le,p as E,O as Ne,q as de,M as W,ay as O,a1 as T,u as _e,r as he,v as ge,V as Ie,c as Me,m as Se,n as je,N as qe}from"../lite.js";/* empty css                                             */function le(t){let e,l,n,r,d=j(t[2])+"",o,_,u,s,a=t[2].orig_name+"",h;return{c(){e=F("div"),l=F("span"),n=F("div"),r=F("progress"),o=I(d),u=J(),s=F("span"),h=I(a),U(r,"visibility","hidden"),U(r,"height","0"),U(r,"width","0"),r.value=_=j(t[2]),c(r,"max","100"),c(r,"class","svelte-1vsfomn"),c(n,"class","progress-bar svelte-1vsfomn"),c(s,"class","file-name svelte-1vsfomn"),c(e,"class","file svelte-1vsfomn")},m(m,w){M(m,e,w),z(e,l),z(l,n),z(n,r),z(r,o),z(e,u),z(e,s),z(s,h)},p(m,w){w&4&&d!==(d=j(m[2])+"")&&R(o,d),w&4&&_!==(_=j(m[2]))&&(r.value=_),w&4&&a!==(a=m[2].orig_name+"")&&R(h,a)},d(m){m&&S(e)}}}function Je(t){let e,l,n,r=t[0].length+"",d,o,_=t[0].length>1?"files":"file",u,s,a,h=t[2]&&le(t);return{c(){e=F("div"),l=F("span"),n=I("Uploading "),d=I(r),o=J(),u=I(_),s=I("..."),a=J(),h&&h.c(),c(l,"class","uploading svelte-1vsfomn"),c(e,"class","wrap svelte-1vsfomn"),y(e,"progress",t[1])},m(m,w){M(m,e,w),z(e,l),z(l,n),z(l,d),z(l,o),z(l,u),z(l,s),z(e,a),h&&h.m(e,null)},p(m,[w]){w&1&&r!==(r=m[0].length+"")&&R(d,r),w&1&&_!==(_=m[0].length>1?"files":"file")&&R(u,_),m[2]?h?h.p(m,w):(h=le(m),h.c(),h.m(e,null)):h&&(h.d(1),h=null),w&2&&y(e,"progress",m[1])},i:te,o:te,d(m){m&&S(e),h&&h.d()}}}function j(t){return t.progress*100/(t.size||0)||0}function Re(t){let e=0;return t.forEach(l=>{e+=j(l)}),document.documentElement.style.setProperty("--upload-progress-width",(e/t.length).toFixed(2)+"%"),e/t.length}function Ve(t,e,l){let{upload_id:n}=e,{root:r}=e,{files:d}=e,{stream_handler:o}=e,_,u=!1,s,a,h=d.map(g=>({...g,progress:0}));const m=ae();function w(g,f){l(0,h=h.map(b=>(b.orig_name===g&&(b.progress+=f),b)))}return Te(async()=>{if(_=await o(new URL(`${r}/gradio_api/upload_progress?upload_id=${n}`)),_==null)throw new Error("Event source is not defined");_.onmessage=async function(g){const f=JSON.parse(g.data);u||l(1,u=!0),f.msg==="done"?(_?.close(),m("done")):(l(7,s=f),w(f.orig_name,f.chunk_size))}}),Ue(()=>{(_!=null||_!=null)&&_.close()}),t.$$set=g=>{"upload_id"in g&&l(3,n=g.upload_id),"root"in g&&l(4,r=g.root),"files"in g&&l(5,d=g.files),"stream_handler"in g&&l(6,o=g.stream_handler)},t.$$.update=()=>{t.$$.dirty&1&&Re(h),t.$$.dirty&129&&l(2,a=s||h[0])},[h,u,a,n,r,d,o,s]}class Be extends ne{constructor(e){super(),re(this,e,Ve,Je,se,{upload_id:3,root:4,files:5,stream_handler:6})}get upload_id(){return this.$$.ctx[3]}set upload_id(e){this.$$set({upload_id:e}),k()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),k()}get files(){return this.$$.ctx[5]}set files(e){this.$$set({files:e}),k()}get stream_handler(){return this.$$.ctx[6]}set stream_handler(e){this.$$set({stream_handler:e}),k()}}function Ge(t){let e,l,n,r,d,o,_,u,s,a,h,m;const w=t[30].default,g=de(w,t,t[29],null);return{c(){e=F("button"),g&&g.c(),l=J(),n=F("input"),c(n,"aria-label","File upload"),c(n,"data-testid","file-upload"),c(n,"type","file"),c(n,"accept",r=t[19]||void 0),n.multiple=d=t[6]==="multiple"||void 0,c(n,"webkitdirectory",o=t[6]==="directory"||void 0),c(n,"mozdirectory",_=t[6]==="directory"||void 0),c(n,"class","svelte-1b742ao"),c(e,"tabindex",u=t[9]?-1:0),c(e,"aria-label",s=t[14]||"Click to upload or drop files"),c(e,"aria-dropeffect","copy"),c(e,"class","svelte-1b742ao"),y(e,"hidden",t[9]),y(e,"center",t[4]),y(e,"boundedheight",t[3]),y(e,"flex",t[5]),y(e,"disable_click",t[7]),y(e,"icon-mode",t[12]),U(e,"height",t[12]?"":t[13]?typeof t[13]=="number"?t[13]+"px":t[13]:"100%")},m(f,b){M(f,e,b),g&&g.m(e,null),z(e,l),z(e,n),t[38](n),a=!0,h||(m=[W(n,"change",t[21]),W(e,"drag",O(T(t[31]))),W(e,"dragstart",O(T(t[32]))),W(e,"dragend",O(T(t[33]))),W(e,"dragover",O(T(t[34]))),W(e,"dragenter",O(T(t[35]))),W(e,"dragleave",O(T(t[36]))),W(e,"drop",O(T(t[37]))),W(e,"click",t[16]),W(e,"drop",t[22]),W(e,"dragenter",t[20]),W(e,"dragleave",t[20])],h=!0)},p(f,b){g&&g.p&&(!a||b[0]&536870912)&&_e(g,w,f,f[29],a?ge(w,f[29],b,null):he(f[29]),null),(!a||b[0]&524288&&r!==(r=f[19]||void 0))&&c(n,"accept",r),(!a||b[0]&64&&d!==(d=f[6]==="multiple"||void 0))&&(n.multiple=d),(!a||b[0]&64&&o!==(o=f[6]==="directory"||void 0))&&c(n,"webkitdirectory",o),(!a||b[0]&64&&_!==(_=f[6]==="directory"||void 0))&&c(n,"mozdirectory",_),(!a||b[0]&512&&u!==(u=f[9]?-1:0))&&c(e,"tabindex",u),(!a||b[0]&16384&&s!==(s=f[14]||"Click to upload or drop files"))&&c(e,"aria-label",s),(!a||b[0]&512)&&y(e,"hidden",f[9]),(!a||b[0]&16)&&y(e,"center",f[4]),(!a||b[0]&8)&&y(e,"boundedheight",f[3]),(!a||b[0]&32)&&y(e,"flex",f[5]),(!a||b[0]&128)&&y(e,"disable_click",f[7]),(!a||b[0]&4096)&&y(e,"icon-mode",f[12]),b[0]&12288&&U(e,"height",f[12]?"":f[13]?typeof f[13]=="number"?f[13]+"px":f[13]:"100%")},i(f){a||(D(g,f),a=!0)},o(f){L(g,f),a=!1},d(f){f&&S(e),g&&g.d(f),t[38](null),h=!1,Ie(m)}}}function He(t){let e,l,n=!t[9]&&ie(t);return{c(){n&&n.c(),e=oe()},m(r,d){n&&n.m(r,d),M(r,e,d),l=!0},p(r,d){r[9]?n&&(ue(),L(n,1,1,()=>{n=null}),fe()):n?(n.p(r,d),d[0]&512&&D(n,1)):(n=ie(r),n.c(),D(n,1),n.m(e.parentNode,e))},i(r){l||(D(n),l=!0)},o(r){L(n),l=!1},d(r){r&&S(e),n&&n.d(r)}}}function Ke(t){let e,l,n,r,d,o;const _=t[30].default,u=de(_,t,t[29],null);return{c(){e=F("button"),u&&u.c(),c(e,"tabindex",l=t[9]?-1:0),c(e,"aria-label",n=t[14]||"Paste from clipboard"),c(e,"class","svelte-1b742ao"),y(e,"hidden",t[9]),y(e,"center",t[4]),y(e,"boundedheight",t[3]),y(e,"flex",t[5]),y(e,"icon-mode",t[12]),U(e,"height",t[12]?"":t[13]?typeof t[13]=="number"?t[13]+"px":t[13]:"100%")},m(s,a){M(s,e,a),u&&u.m(e,null),r=!0,d||(o=W(e,"click",t[15]),d=!0)},p(s,a){u&&u.p&&(!r||a[0]&536870912)&&_e(u,_,s,s[29],r?ge(_,s[29],a,null):he(s[29]),null),(!r||a[0]&512&&l!==(l=s[9]?-1:0))&&c(e,"tabindex",l),(!r||a[0]&16384&&n!==(n=s[14]||"Paste from clipboard"))&&c(e,"aria-label",n),(!r||a[0]&512)&&y(e,"hidden",s[9]),(!r||a[0]&16)&&y(e,"center",s[4]),(!r||a[0]&8)&&y(e,"boundedheight",s[3]),(!r||a[0]&32)&&y(e,"flex",s[5]),(!r||a[0]&4096)&&y(e,"icon-mode",s[12]),a[0]&12288&&U(e,"height",s[12]?"":s[13]?typeof s[13]=="number"?s[13]+"px":s[13]:"100%")},i(s){r||(D(u,s),r=!0)},o(s){L(u,s),r=!1},d(s){s&&S(e),u&&u.d(s),d=!1,o()}}}function ie(t){let e,l;return e=new Be({props:{root:t[8],upload_id:t[17],files:t[18],stream_handler:t[11]}}),{c(){Me(e.$$.fragment)},m(n,r){Se(e,n,r),l=!0},p(n,r){const d={};r[0]&256&&(d.root=n[8]),r[0]&131072&&(d.upload_id=n[17]),r[0]&262144&&(d.files=n[18]),r[0]&2048&&(d.stream_handler=n[11]),e.$set(d)},i(n){l||(D(e.$$.fragment,n),l=!0)},o(n){L(e.$$.fragment,n),l=!1},d(n){je(e,n)}}}function Qe(t){let e,l,n,r;const d=[Ke,He,Ge],o=[];function _(u,s){return u[0]==="clipboard"?0:u[1]&&u[10]?1:2}return e=_(t),l=o[e]=d[e](t),{c(){l.c(),n=oe()},m(u,s){o[e].m(u,s),M(u,n,s),r=!0},p(u,s){let a=e;e=_(u),e===a?o[e].p(u,s):(ue(),L(o[a],1,1,()=>{o[a]=null}),fe(),l=o[e],l?l.p(u,s):(l=o[e]=d[e](u),l.c()),D(l,1),l.m(n.parentNode,n))},i(u){r||(D(l),r=!0)},o(u){L(l),r=!1},d(u){u&&S(n),o[e].d(u)}}}function Xe(t,e,l){if(!t||t==="*"||t==="file/*"||Array.isArray(t)&&t.some(r=>r==="*"||r==="file/*"))return!0;let n;if(typeof t=="string")n=t.split(",").map(r=>r.trim());else if(Array.isArray(t))n=t;else return!1;return n.includes(e)||n.some(r=>{const[d]=r.split("/").map(o=>o.trim());return r.endsWith("/*")&&l.startsWith(d+"/")})}function Ye(t,e,l){let n,{$$slots:r={},$$scope:d}=e,{filetype:o=null}=e,{dragging:_=!1}=e,{boundedheight:u=!0}=e,{center:s=!0}=e,{flex:a=!0}=e,{file_count:h="single"}=e,{disable_click:m=!1}=e,{root:w}=e,{hidden:g=!1}=e,{format:f="file"}=e,{uploading:b=!1}=e,{hidden_upload:P=null}=e,{show_progress:X=!0}=e,{max_file_size:V=null}=e,{upload:B}=e,{stream_handler:Y}=e,{icon_upload:Z=!1}=e,{height:x=void 0}=e,{aria_label:$=void 0}=e,G,H,N,ee=null;const ce=()=>{if(typeof navigator<"u"){const i=navigator.userAgent.toLowerCase();return i.indexOf("iphone")>-1||i.indexOf("ipad")>-1}return!1},C=ae(),me=["image","video","audio","text","file"],K=i=>n&&i.startsWith(".")?(ee=!0,i):n&&i.includes("file/*")?"*":i.startsWith(".")||i.endsWith("/*")?i:me.includes(i)?i+"/*":"."+i;function be(){l(23,_=!_)}function pe(){navigator.clipboard.read().then(async i=>{for(let p=0;p<i.length;p++){const v=i[p].types.find(A=>A.startsWith("image/"));if(v){i[p].getType(v).then(async A=>{const Q=new File([A],`clipboard.${v.replace("image/","")}`);await q([Q])});break}}})}function ye(){m||P&&(l(2,P.value="",P),P.click())}async function ke(i){await qe(),l(17,G=Math.random().toString(36).substring(2,15)),l(1,b=!0);try{const p=await B(i,w,G,V??1/0);return C("load",h==="single"?p?.[0]:p),l(1,b=!1),p||[]}catch(p){return C("error",p.message),l(1,b=!1),[]}}async function q(i){if(!i.length)return;let p=i.map(v=>new File([v],v instanceof File?v.name:"file",{type:v.type}));return n&&ee&&(p=p.filter(v=>we(v)?!0:(C("error",`Invalid file type: ${v.name}. Only ${o} allowed.`),!1)),p.length===0)?[]:(l(18,H=await Le(p)),await ke(H))}function we(i){return o?(Array.isArray(o)?o:[o]).some(v=>{const A=K(v);if(A.startsWith("."))return i.name.toLowerCase().endsWith(A.toLowerCase());if(A==="*")return!0;if(A.endsWith("/*")){const[Q]=A.split("/");return i.type.startsWith(Q+"/")}return i.type===A}):!0}async function ve(i){const p=i.target;if(p.files)if(f!="blob")await q(Array.from(p.files));else{if(h==="single"){C("load",p.files[0]);return}C("load",p.files)}}async function Ae(i){if(l(23,_=!1),!i.dataTransfer?.files)return;const p=Array.from(i.dataTransfer.files).filter(v=>{const A="."+v.name.split(".").pop();return A&&Xe(N,A,v.type)||(A&&Array.isArray(o)?o.includes(A):A===o)?!0:(C("error",`Invalid file type only ${o} allowed.`),!1)});if(f!="blob")await q(p);else{if(h==="single"){C("load",p[0]);return}C("load",p)}}function ze(i){E.call(this,t,i)}function We(i){E.call(this,t,i)}function Fe(i){E.call(this,t,i)}function Pe(i){E.call(this,t,i)}function Ce(i){E.call(this,t,i)}function De(i){E.call(this,t,i)}function Ee(i){E.call(this,t,i)}function Oe(i){Ne[i?"unshift":"push"](()=>{P=i,l(2,P)})}return t.$$set=i=>{"filetype"in i&&l(0,o=i.filetype),"dragging"in i&&l(23,_=i.dragging),"boundedheight"in i&&l(3,u=i.boundedheight),"center"in i&&l(4,s=i.center),"flex"in i&&l(5,a=i.flex),"file_count"in i&&l(6,h=i.file_count),"disable_click"in i&&l(7,m=i.disable_click),"root"in i&&l(8,w=i.root),"hidden"in i&&l(9,g=i.hidden),"format"in i&&l(24,f=i.format),"uploading"in i&&l(1,b=i.uploading),"hidden_upload"in i&&l(2,P=i.hidden_upload),"show_progress"in i&&l(10,X=i.show_progress),"max_file_size"in i&&l(25,V=i.max_file_size),"upload"in i&&l(26,B=i.upload),"stream_handler"in i&&l(11,Y=i.stream_handler),"icon_upload"in i&&l(12,Z=i.icon_upload),"height"in i&&l(13,x=i.height),"aria_label"in i&&l(14,$=i.aria_label),"$$scope"in i&&l(29,d=i.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&268435457&&(o==null?l(19,N=null):typeof o=="string"?l(19,N=K(o)):n&&o.includes("file/*")?l(19,N="*"):(l(0,o=o.map(K)),l(19,N=o.join(", "))))},l(28,n=ce()),[o,b,P,u,s,a,h,m,w,g,X,Y,Z,x,$,pe,ye,G,H,N,be,ve,Ae,_,f,V,B,q,n,d,r,ze,We,Fe,Pe,Ce,De,Ee,Oe]}class $e extends ne{constructor(e){super(),re(this,e,Ye,Qe,se,{filetype:0,dragging:23,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:24,uploading:1,hidden_upload:2,show_progress:10,max_file_size:25,upload:26,stream_handler:11,icon_upload:12,height:13,aria_label:14,paste_clipboard:15,open_file_upload:16,load_files:27},null,[-1,-1])}get filetype(){return this.$$.ctx[0]}set filetype(e){this.$$set({filetype:e}),k()}get dragging(){return this.$$.ctx[23]}set dragging(e){this.$$set({dragging:e}),k()}get boundedheight(){return this.$$.ctx[3]}set boundedheight(e){this.$$set({boundedheight:e}),k()}get center(){return this.$$.ctx[4]}set center(e){this.$$set({center:e}),k()}get flex(){return this.$$.ctx[5]}set flex(e){this.$$set({flex:e}),k()}get file_count(){return this.$$.ctx[6]}set file_count(e){this.$$set({file_count:e}),k()}get disable_click(){return this.$$.ctx[7]}set disable_click(e){this.$$set({disable_click:e}),k()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),k()}get hidden(){return this.$$.ctx[9]}set hidden(e){this.$$set({hidden:e}),k()}get format(){return this.$$.ctx[24]}set format(e){this.$$set({format:e}),k()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),k()}get hidden_upload(){return this.$$.ctx[2]}set hidden_upload(e){this.$$set({hidden_upload:e}),k()}get show_progress(){return this.$$.ctx[10]}set show_progress(e){this.$$set({show_progress:e}),k()}get max_file_size(){return this.$$.ctx[25]}set max_file_size(e){this.$$set({max_file_size:e}),k()}get upload(){return this.$$.ctx[26]}set upload(e){this.$$set({upload:e}),k()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),k()}get icon_upload(){return this.$$.ctx[12]}set icon_upload(e){this.$$set({icon_upload:e}),k()}get height(){return this.$$.ctx[13]}set height(e){this.$$set({height:e}),k()}get aria_label(){return this.$$.ctx[14]}set aria_label(e){this.$$set({aria_label:e}),k()}get paste_clipboard(){return this.$$.ctx[15]}get open_file_upload(){return this.$$.ctx[16]}get load_files(){return this.$$.ctx[27]}}export{$e as U};
//# sourceMappingURL=Upload-Do_omv-N.js.map
