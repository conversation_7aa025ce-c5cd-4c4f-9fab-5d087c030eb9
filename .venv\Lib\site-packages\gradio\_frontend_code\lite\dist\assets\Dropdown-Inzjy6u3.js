import{a as ue,i as fe,s as re,f as O,P as _e,y as R,b as M,e as ye,z as g,d as j,M as U,k as H,h as ce,t as K,j as he,l as I,V as v,o as ae,aq as x,$ as B,a1 as Oe,bd as $,as as Ae,w as de,A as T,C as z,x as me,O as W,c as X,m as Y,R as ee,n as Z,K as De}from"../lite.js";import{a as te}from"./index-B9I6rkKj.js";import{B as Te}from"./BlockTitle-DvFB_De3.js";import{D as Ce}from"./DropdownArrow-DIboSv6l.js";function le(l,t,e){const r=l.slice();return r[28]=t[e],r}function se(l){let t,e,r,h,f,u=x(l[1]),s=[];for(let n=0;n<u.length;n+=1)s[n]=ne(le(l,u,n));return{c(){t=R("ul");for(let n=0;n<s.length;n+=1)s[n].c();g(t,"class","options svelte-y6qw75"),g(t,"role","listbox"),B(t,"top",l[9]),B(t,"bottom",l[10]),B(t,"max-height",`calc(${l[11]}px - var(--window-padding))`),B(t,"width",l[8]+"px")},m(n,p){j(n,t,p);for(let o=0;o<s.length;o+=1)s[o]&&s[o].m(t,null);l[24](t),r=!0,h||(f=[U(t,"mousedown",Oe(l[22])),U(t,"scroll",l[23])],h=!0)},p(n,p){if(p&307){u=x(n[1]);let o;for(o=0;o<u.length;o+=1){const a=le(n,u,o);s[o]?s[o].p(a,p):(s[o]=ne(a),s[o].c(),s[o].m(t,null))}for(;o<s.length;o+=1)s[o].d(1);s.length=u.length}p&512&&B(t,"top",n[9]),p&1024&&B(t,"bottom",n[10]),p&2048&&B(t,"max-height",`calc(${n[11]}px - var(--window-padding))`),p&256&&B(t,"width",n[8]+"px")},i(n){r||(n&&_e(()=>{r&&(e||(e=$(t,te,{duration:200,y:5},!0)),e.run(1))}),r=!0)},o(n){n&&(e||(e=$(t,te,{duration:200,y:5},!1)),e.run(0)),r=!1},d(n){n&&I(t),Ae(s,n),l[24](null),n&&e&&e.end(),h=!1,v(f)}}}function ne(l){let t,e,r,h=l[0][l[28]][0]+"",f,u,s,n,p;return{c(){t=R("li"),e=R("span"),e.textContent="✓",r=M(),f=de(h),u=M(),g(e,"class","inner-item svelte-y6qw75"),T(e,"hide",!l[4].includes(l[28])),g(t,"class","item svelte-y6qw75"),g(t,"data-index",s=l[28]),g(t,"aria-label",n=l[0][l[28]][0]),g(t,"data-testid","dropdown-option"),g(t,"role","option"),g(t,"aria-selected",p=l[4].includes(l[28])),T(t,"selected",l[4].includes(l[28])),T(t,"active",l[28]===l[5]),T(t,"bg-gray-100",l[28]===l[5]),T(t,"dark:bg-gray-600",l[28]===l[5]),B(t,"width",l[8]+"px")},m(o,a){j(o,t,a),z(t,e),z(t,r),z(t,f),z(t,u)},p(o,a){a&18&&T(e,"hide",!o[4].includes(o[28])),a&3&&h!==(h=o[0][o[28]][0]+"")&&me(f,h),a&2&&s!==(s=o[28])&&g(t,"data-index",s),a&3&&n!==(n=o[0][o[28]][0])&&g(t,"aria-label",n),a&18&&p!==(p=o[4].includes(o[28]))&&g(t,"aria-selected",p),a&18&&T(t,"selected",o[4].includes(o[28])),a&34&&T(t,"active",o[28]===o[5]),a&34&&T(t,"bg-gray-100",o[28]===o[5]),a&34&&T(t,"dark:bg-gray-600",o[28]===o[5]),a&256&&B(t,"width",o[8]+"px")},d(o){o&&I(t)}}}function Ee(l){let t,e,r,h,f;_e(l[20]);let u=l[2]&&!l[3]&&se(l);return{c(){t=R("div"),e=M(),u&&u.c(),r=ye(),g(t,"class","reference")},m(s,n){j(s,t,n),l[21](t),j(s,e,n),u&&u.m(s,n),j(s,r,n),h||(f=[U(window,"scroll",l[14]),U(window,"resize",l[20])],h=!0)},p(s,[n]){s[2]&&!s[3]?u?(u.p(s,n),n&12&&H(u,1)):(u=se(s),u.c(),H(u,1),u.m(r.parentNode,r)):u&&(ce(),K(u,1,1,()=>{u=null}),he())},i(s){H(u)},o(s){K(u)},d(s){s&&(I(t),I(e),I(r)),l[21](null),u&&u.d(s),h=!1,v(f)}}}function qe(l,t,e){let{choices:r}=t,{filtered_indices:h}=t,{show_options:f=!1}=t,{disabled:u=!1}=t,{selected_indices:s=[]}=t,{active_index:n=null}=t,{remember_scroll:p=!1}=t,o,a,y,A,E,m,i,c,D,k,w=0;function J(){const{top:d,bottom:V}=E.getBoundingClientRect();e(17,o=d),e(18,a=k-V)}let N=null;function q(){f&&(N!==null&&clearTimeout(N),N=setTimeout(()=>{J(),N=null},10))}function C(){m?.scrollTo?.(0,w)}const b=ae();function S(){e(12,k=window.innerHeight)}function L(d){W[d?"unshift":"push"](()=>{E=d,e(6,E)})}const F=d=>b("change",d),Q=d=>e(13,w=d.currentTarget.scrollTop);function P(d){W[d?"unshift":"push"](()=>{m=d,e(7,m)})}return l.$$set=d=>{"choices"in d&&e(0,r=d.choices),"filtered_indices"in d&&e(1,h=d.filtered_indices),"show_options"in d&&e(2,f=d.show_options),"disabled"in d&&e(3,u=d.disabled),"selected_indices"in d&&e(4,s=d.selected_indices),"active_index"in d&&e(5,n=d.active_index),"remember_scroll"in d&&e(16,p=d.remember_scroll)},l.$$.update=()=>{if(l.$$.dirty&983252){if(f&&E){if(p)C();else if(m&&s.length>0){let V=m.querySelectorAll("li");for(const G of Array.from(V))if(G.getAttribute("data-index")===s[0].toString()){m?.scrollTo?.(0,G.offsetTop);break}}J();const d=E.parentElement?.getBoundingClientRect();e(19,y=d?.height||0),e(8,A=d?.width||0)}a>o?(e(9,i=`${o}px`),e(11,D=a),e(10,c=null)):(e(10,c=`${a+y}px`),e(11,D=o-y),e(9,i=null))}},[r,h,f,u,s,n,E,m,A,i,c,D,k,w,q,b,p,o,a,y,S,L,F,Q,P]}class Ne extends ue{constructor(t){super(),fe(this,t,qe,Ee,re,{choices:0,filtered_indices:1,show_options:2,disabled:3,selected_indices:4,active_index:5,remember_scroll:16})}get choices(){return this.$$.ctx[0]}set choices(t){this.$$set({choices:t}),O()}get filtered_indices(){return this.$$.ctx[1]}set filtered_indices(t){this.$$set({filtered_indices:t}),O()}get show_options(){return this.$$.ctx[2]}set show_options(t){this.$$set({show_options:t}),O()}get disabled(){return this.$$.ctx[3]}set disabled(t){this.$$set({disabled:t}),O()}get selected_indices(){return this.$$.ctx[4]}set selected_indices(t){this.$$set({selected_indices:t}),O()}get active_index(){return this.$$.ctx[5]}set active_index(t){this.$$set({active_index:t}),O()}get remember_scroll(){return this.$$.ctx[16]}set remember_scroll(t){this.$$set({remember_scroll:t}),O()}}function Se(l,t){return(l%t+t)%t}function oe(l,t){return l.reduce((e,r,h)=>((!t||r[0].toLowerCase().includes(t.toLowerCase()))&&e.push(h),e),[])}function ze(l,t,e){l("change",t),e||l("input")}function Be(l,t,e){if(l.key==="Escape")return[!1,t];if((l.key==="ArrowDown"||l.key==="ArrowUp")&&e.length>=0)if(t===null)t=l.key==="ArrowDown"?e[0]:e[e.length-1];else{const r=e.indexOf(t),h=l.key==="ArrowUp"?-1:1;t=e[Se(r+h,e.length)]}return[!0,t]}function Re(l){let t;return{c(){t=de(l[0])},m(e,r){j(e,t,r)},p(e,r){r[0]&1&&me(t,e[0])},d(e){e&&I(t)}}}function ie(l){let t,e,r;return e=new Ce({}),{c(){t=R("div"),X(e.$$.fragment),g(t,"class","icon-wrap svelte-1hfxrpf")},m(h,f){j(h,t,f),Y(e,t,null),r=!0},i(h){r||(H(e.$$.fragment,h),r=!0)},o(h){K(e.$$.fragment,h),r=!1},d(h){h&&I(t),Z(e)}}}function Ue(l){let t,e,r,h,f,u,s,n,p,o,a,y,A,E;e=new Te({props:{root:l[8],show_label:l[4],info:l[1],$$slots:{default:[Re]},$$scope:{ctx:l}}});let m=!l[3]&&ie();return a=new Ne({props:{show_options:l[13],choices:l[2],filtered_indices:l[11],disabled:l[3],selected_indices:l[12]===null?[]:[l[12]],active_index:l[15]}}),a.$on("change",l[17]),{c(){t=R("div"),X(e.$$.fragment),r=M(),h=R("div"),f=R("div"),u=R("div"),s=R("input"),p=M(),m&&m.c(),o=M(),X(a.$$.fragment),g(s,"role","listbox"),g(s,"aria-controls","dropdown-options"),g(s,"aria-expanded",l[13]),g(s,"aria-label",l[0]),g(s,"class","border-none svelte-1hfxrpf"),s.disabled=l[3],g(s,"autocomplete","off"),s.readOnly=n=!l[7],T(s,"subdued",!l[14].includes(l[10])&&!l[6]),g(u,"class","secondary-wrap svelte-1hfxrpf"),g(f,"class","wrap-inner svelte-1hfxrpf"),T(f,"show_options",l[13]),g(h,"class","wrap svelte-1hfxrpf"),g(t,"class","svelte-1hfxrpf"),T(t,"container",l[5])},m(i,c){j(i,t,c),Y(e,t,null),z(t,r),z(t,h),z(h,f),z(f,u),z(u,s),ee(s,l[10]),l[30](s),z(u,p),m&&m.m(u,null),z(h,o),Y(a,h,null),y=!0,A||(E=[U(s,"input",l[29]),U(s,"keydown",l[20]),U(s,"keyup",l[31]),U(s,"blur",l[19]),U(s,"focus",l[18])],A=!0)},p(i,c){const D={};c[0]&256&&(D.root=i[8]),c[0]&16&&(D.show_label=i[4]),c[0]&2&&(D.info=i[1]),c[0]&1|c[1]&16&&(D.$$scope={dirty:c,ctx:i}),e.$set(D),(!y||c[0]&8192)&&g(s,"aria-expanded",i[13]),(!y||c[0]&1)&&g(s,"aria-label",i[0]),(!y||c[0]&8)&&(s.disabled=i[3]),(!y||c[0]&128&&n!==(n=!i[7]))&&(s.readOnly=n),c[0]&1024&&s.value!==i[10]&&ee(s,i[10]),(!y||c[0]&17472)&&T(s,"subdued",!i[14].includes(i[10])&&!i[6]),i[3]?m&&(ce(),K(m,1,1,()=>{m=null}),he()):m?c[0]&8&&H(m,1):(m=ie(),m.c(),H(m,1),m.m(u,null)),(!y||c[0]&8192)&&T(f,"show_options",i[13]);const k={};c[0]&8192&&(k.show_options=i[13]),c[0]&4&&(k.choices=i[2]),c[0]&2048&&(k.filtered_indices=i[11]),c[0]&8&&(k.disabled=i[3]),c[0]&4096&&(k.selected_indices=i[12]===null?[]:[i[12]]),c[0]&32768&&(k.active_index=i[15]),a.$set(k),(!y||c[0]&32)&&T(t,"container",i[5])},i(i){y||(H(e.$$.fragment,i),H(m),H(a.$$.fragment,i),y=!0)},o(i){K(e.$$.fragment,i),K(m),K(a.$$.fragment,i),y=!1},d(i){i&&I(t),Z(e),l[30](null),m&&m.d(),Z(a),A=!1,v(E)}}}function He(l,t,e){let{label:r}=t,{info:h=void 0}=t,{value:f=void 0}=t,u,{value_is_output:s=!1}=t,{choices:n}=t,p,{disabled:o=!1}=t,{show_label:a}=t,{container:y=!0}=t,{allow_custom_value:A=!1}=t,{filterable:E=!0}=t,{root:m}=t,i,c=!1,D,k,w="",J="",N=!1,q=[],C=null,b=null,S;const L=ae();f&&(S=n.map(_=>_[1]).indexOf(f),b=S,b===-1?(u=f,b=null):([w,u]=n[b],J=w),P());function F(){e(14,D=n.map(_=>_[0])),e(25,k=n.map(_=>_[1]))}const Q=typeof window<"u";function P(){F(),f===void 0||Array.isArray(f)&&f.length===0?(e(10,w=""),e(12,b=null)):k.includes(f)?(e(10,w=D[k.indexOf(f)]),e(12,b=k.indexOf(f))):A?(e(10,w=f),e(12,b=null)):(e(10,w=""),e(12,b=null)),e(28,S=b)}function d(_){if(e(12,b=parseInt(_.detail.target.dataset.index)),isNaN(b)){e(12,b=null);return}e(13,c=!1),e(15,C=null),i.blur()}function V(_){e(11,q=n.map((Je,ke)=>ke)),e(13,c=!0),L("focus")}function G(){A?e(21,f=w):e(10,w=D[k.indexOf(f)]),e(13,c=!1),e(15,C=null),L("blur")}function be(_){e(13,[c,C]=Be(_,C,q),c,(e(15,C),e(2,n),e(24,p),e(6,A),e(10,w),e(11,q),e(9,i),e(26,J),e(12,b),e(28,S),e(27,N),e(25,k))),_.key==="Enter"&&(C!==null?(e(12,b=C),e(13,c=!1),i.blur(),e(15,C=null)):D.includes(w)?(e(12,b=D.indexOf(w)),e(13,c=!1),e(15,C=null),i.blur()):A&&(e(21,f=w),e(12,b=null),e(13,c=!1),e(15,C=null),i.blur()))}De(()=>{e(22,s=!1),e(27,N=!0)});function we(){w=this.value,e(10,w),e(12,b),e(28,S),e(27,N),e(2,n),e(25,k)}function ge(_){W[_?"unshift":"push"](()=>{i=_,e(9,i)})}const pe=_=>L("key_up",{key:_.key,input_value:w});return l.$$set=_=>{"label"in _&&e(0,r=_.label),"info"in _&&e(1,h=_.info),"value"in _&&e(21,f=_.value),"value_is_output"in _&&e(22,s=_.value_is_output),"choices"in _&&e(2,n=_.choices),"disabled"in _&&e(3,o=_.disabled),"show_label"in _&&e(4,a=_.show_label),"container"in _&&e(5,y=_.container),"allow_custom_value"in _&&e(6,A=_.allow_custom_value),"filterable"in _&&e(7,E=_.filterable),"root"in _&&e(8,m=_.root)},l.$$.update=()=>{l.$$.dirty[0]&436211716&&b!==S&&b!==null&&N&&(e(10,[w,f]=n[b],w,(e(21,f),e(12,b),e(28,S),e(27,N),e(2,n),e(25,k))),e(28,S=b),L("select",{index:b,value:k[b],selected:!0})),l.$$.dirty[0]&14680064&&JSON.stringify(u)!==JSON.stringify(f)&&(P(),ze(L,f,s),e(23,u=f)),l.$$.dirty[0]&4&&F(),l.$$.dirty[0]&16780868&&n!==p&&(A||P(),e(24,p=n),e(11,q=oe(n,w)),!A&&q.length>0&&e(15,C=q[0]),Q&&i===document.activeElement&&e(13,c=!0)),l.$$.dirty[0]&67112004&&w!==J&&(e(11,q=oe(n,w)),e(26,J=w),!A&&q.length>0&&e(15,C=q[0]))},[r,h,n,o,a,y,A,E,m,i,w,q,b,c,D,C,L,d,V,G,be,f,s,u,p,k,J,N,S,we,ge,pe]}class Me extends ue{constructor(t){super(),fe(this,t,He,Ue,re,{label:0,info:1,value:21,value_is_output:22,choices:2,disabled:3,show_label:4,container:5,allow_custom_value:6,filterable:7,root:8},null,[-1,-1])}get label(){return this.$$.ctx[0]}set label(t){this.$$set({label:t}),O()}get info(){return this.$$.ctx[1]}set info(t){this.$$set({info:t}),O()}get value(){return this.$$.ctx[21]}set value(t){this.$$set({value:t}),O()}get value_is_output(){return this.$$.ctx[22]}set value_is_output(t){this.$$set({value_is_output:t}),O()}get choices(){return this.$$.ctx[2]}set choices(t){this.$$set({choices:t}),O()}get disabled(){return this.$$.ctx[3]}set disabled(t){this.$$set({disabled:t}),O()}get show_label(){return this.$$.ctx[4]}set show_label(t){this.$$set({show_label:t}),O()}get container(){return this.$$.ctx[5]}set container(t){this.$$set({container:t}),O()}get allow_custom_value(){return this.$$.ctx[6]}set allow_custom_value(t){this.$$set({allow_custom_value:t}),O()}get filterable(){return this.$$.ctx[7]}set filterable(t){this.$$set({filterable:t}),O()}get root(){return this.$$.ctx[8]}set root(t){this.$$set({root:t}),O()}}export{Ne as D,ze as a,Be as b,Me as c,oe as h};
//# sourceMappingURL=Dropdown-Inzjy6u3.js.map
