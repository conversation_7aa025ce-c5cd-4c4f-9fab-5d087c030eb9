import{a as _,i as m,s as d,f as b,y as u,c as g,b as w,w as I,z as c,A as o,d as k,C as h,m as B,x as C,k as q,t as v,l as z,n as A}from"../lite.js";function L(n){let e,s,i,r,f,a;return i=new n[1]({}),{c(){e=u("label"),s=u("span"),g(i.$$.fragment),r=w(),f=I(n[0]),c(s,"class","svelte-i3tvor"),c(e,"for",""),c(e,"data-testid","block-label"),c(e,"class","svelte-i3tvor"),o(e,"hide",!n[2]),o(e,"sr-only",!n[2]),o(e,"float",n[4]),o(e,"hide-label",n[3])},m(l,t){k(l,e,t),h(e,s),B(i,s,null),h(e,r),h(e,f),a=!0},p(l,[t]){(!a||t&1)&&C(f,l[0]),(!a||t&4)&&o(e,"hide",!l[2]),(!a||t&4)&&o(e,"sr-only",!l[2]),(!a||t&16)&&o(e,"float",l[4]),(!a||t&8)&&o(e,"hide-label",l[3])},i(l){a||(q(i.$$.fragment,l),a=!0)},o(l){v(i.$$.fragment,l),a=!1},d(l){l&&z(e),A(i)}}}function S(n,e,s){let{label:i=null}=e,{Icon:r}=e,{show_label:f=!0}=e,{disable:a=!1}=e,{float:l=!0}=e;return n.$$set=t=>{"label"in t&&s(0,i=t.label),"Icon"in t&&s(1,r=t.Icon),"show_label"in t&&s(2,f=t.show_label),"disable"in t&&s(3,a=t.disable),"float"in t&&s(4,l=t.float)},[i,r,f,a,l]}class x extends _{constructor(e){super(),m(this,e,S,L,d,{label:0,Icon:1,show_label:2,disable:3,float:4})}get label(){return this.$$.ctx[0]}set label(e){this.$$set({label:e}),b()}get Icon(){return this.$$.ctx[1]}set Icon(e){this.$$set({Icon:e}),b()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),b()}get disable(){return this.$$.ctx[3]}set disable(e){this.$$set({disable:e}),b()}get float(){return this.$$.ctx[4]}set float(e){this.$$set({float:e}),b()}}export{x as B};
//# sourceMappingURL=BlockLabel-DWW9BWN3.js.map
