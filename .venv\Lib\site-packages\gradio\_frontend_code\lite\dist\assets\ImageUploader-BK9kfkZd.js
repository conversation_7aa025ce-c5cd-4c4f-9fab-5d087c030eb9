import{a as fe,i as _e,s as me,E as ae,z as d,d as B,C as D,D as K,l as C,y as R,c as j,b as A,w as ie,$ as oe,m as q,M as te,k as b,t as k,n as N,o as Me,f as I,A as J,ar as Be,h as X,j as Y,a5 as He,aa as Ve,O as $,e as de,P as Qe,Q as Ge,ay as Je,T as Ke,V as Xe,ax as Ye,aq as Ce,as as Ze,R as We,x as ge,a7 as ue,a8 as ce,N as $e,p as he,I as xe,a2 as et,q as tt,u as it,r as lt,v as nt}from"../lite.js";import{B as rt}from"./BlockLabel-DWW9BWN3.js";import{I as st,F as at}from"./FullscreenButton-DsVuMC2h.js";import{W as ot,a as ut,S as ct}from"./SelectSource-kJI_8u2f.js";import{I as ft}from"./IconButtonWrapper-BqpIgNIH.js";import{g as _t}from"./utils-Gtzs_Zla.js";import{D as Ae}from"./DropdownArrow-DIboSv6l.js";import{S as mt}from"./Square-CkbFMpLj.js";import{f as dt}from"./index-B9I6rkKj.js";import{S as gt}from"./StreamingBar-lVbwTGD1.js";import{U as ht}from"./Upload-Do_omv-N.js";import{I as bt}from"./Image-BPQ6A_U-.js";function wt(n){let e,t,i;return{c(){e=ae("svg"),t=ae("path"),i=ae("circle"),d(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),d(i,"cx","12"),d(i,"cy","13"),d(i,"r","4"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"fill","none"),d(e,"stroke","currentColor"),d(e,"stroke-width","1.5"),d(e,"stroke-linecap","round"),d(e,"stroke-linejoin","round"),d(e,"class","feather feather-camera")},m(l,s){B(l,e,s),D(e,t),D(e,i)},p:K,i:K,o:K,d(l){l&&C(e)}}}class pt extends fe{constructor(e){super(),_e(this,e,null,wt,me,{})}}function vt(n){let e,t;return{c(){e=ae("svg"),t=ae("circle"),d(t,"cx","12"),d(t,"cy","12"),d(t,"r","10"),d(e,"xmlns","http://www.w3.org/2000/svg"),d(e,"width","100%"),d(e,"height","100%"),d(e,"viewBox","0 0 24 24"),d(e,"stroke-width","1.5"),d(e,"stroke-linecap","round"),d(e,"stroke-linejoin","round"),d(e,"class","feather feather-circle")},m(i,l){B(i,e,l),D(e,t)},p:K,i:K,o:K,d(i){i&&C(e)}}}class kt extends fe{constructor(e){super(),_e(this,e,null,vt,me,{})}}function yt(n){let e,t,i,l,s,r="Click to Access Webcam",u,a,c,_;return l=new ot({}),{c(){e=R("button"),t=R("div"),i=R("span"),j(l.$$.fragment),s=A(),u=ie(r),d(i,"class","icon-wrap svelte-qbrfs"),d(t,"class","wrap svelte-qbrfs"),d(e,"class","svelte-qbrfs"),oe(e,"height","100%")},m(f,y){B(f,e,y),D(e,t),D(t,i),q(l,i,null),D(t,s),D(t,u),a=!0,c||(_=te(e,"click",n[1]),c=!0)},p:K,i(f){a||(b(l.$$.fragment,f),a=!0)},o(f){k(l.$$.fragment,f),a=!1},d(f){f&&C(e),N(l),c=!1,_()}}}function It(n){const e=Me();return[e,()=>e("click")]}class Dt extends fe{constructor(e){super(),_e(this,e,It,yt,me,{})}}function Wt(){return navigator.mediaDevices.enumerateDevices()}function Mt(n,e){e.srcObject=n,e.muted=!0,e.play()}async function Re(n,e,t,i){const l={video:i?{deviceId:{exact:i},...t?.video}:t?.video||{width:{ideal:1920},height:{ideal:1440}},audio:n&&(t?.audio??!0)};return navigator.mediaDevices.getUserMedia(l).then(s=>(Mt(s,e),s))}function zt(n){return n.filter(t=>t.kind==="videoinput")}function Se(n,e,t){const i=n.slice();return i[39]=e[t],i}function Bt(n){let e,t,i,l,s,r,u,a,c,_,f;const y=[St,Rt],z=[];function U(v,M){return v[2]==="video"||v[1]?0:1}i=U(n),l=z[i]=y[i](n);let h=!n[11]&&Ee(n),p=n[13]&&n[8]&&Te(n);return{c(){e=R("div"),t=R("button"),l.c(),r=A(),h&&h.c(),u=A(),p&&p.c(),a=de(),d(t,"aria-label",s=n[2]==="image"?"capture photo":"start recording"),d(t,"class","svelte-s8feoe"),d(e,"class","button-wrap svelte-s8feoe")},m(v,M){B(v,e,M),D(e,t),z[i].m(t,null),D(e,r),h&&h.m(e,null),B(v,u,M),p&&p.m(v,M),B(v,a,M),c=!0,_||(f=te(t,"click",n[28]),_=!0)},p(v,M){let F=i;i=U(v),i===F?z[i].p(v,M):(X(),k(z[F],1,1,()=>{z[F]=null}),Y(),l=z[i],l?l.p(v,M):(l=z[i]=y[i](v),l.c()),b(l,1),l.m(t,null)),(!c||M[0]&4&&s!==(s=v[2]==="image"?"capture photo":"start recording"))&&d(t,"aria-label",s),v[11]?h&&(X(),k(h,1,1,()=>{h=null}),Y()):h?(h.p(v,M),M[0]&2048&&b(h,1)):(h=Ee(v),h.c(),b(h,1),h.m(e,null)),v[13]&&v[8]?p?(p.p(v,M),M[0]&8448&&b(p,1)):(p=Te(v),p.c(),b(p,1),p.m(a.parentNode,a)):p&&(X(),k(p,1,1,()=>{p=null}),Y())},i(v){c||(b(l),b(h),b(p),c=!0)},o(v){k(l),k(h),k(p),c=!1},d(v){v&&(C(e),C(u),C(a)),z[i].d(),h&&h.d(),p&&p.d(v),_=!1,f()}}}function Ct(n){let e,t,i,l;return t=new Dt({}),t.$on("click",n[27]),{c(){e=R("div"),j(t.$$.fragment),d(e,"title","grant webcam access"),oe(e,"height","100%")},m(s,r){B(s,e,r),q(t,e,null),l=!0},p:K,i(s){l||(b(t.$$.fragment,s),s&&(i||Qe(()=>{i=Ge(e,dt,{delay:100,duration:200}),i.start()})),l=!0)},o(s){k(t.$$.fragment,s),l=!1},d(s){s&&C(e),N(t)}}}function Rt(n){let e,t,i;return t=new pt({}),{c(){e=R("div"),j(t.$$.fragment),d(e,"class","icon svelte-s8feoe"),d(e,"title","capture photo")},m(l,s){B(l,e,s),q(t,e,null),i=!0},p:K,i(l){i||(b(t.$$.fragment,l),i=!0)},o(l){k(t.$$.fragment,l),i=!1},d(l){l&&C(e),N(t)}}}function St(n){let e,t,i,l;const s=[jt,Tt,Et],r=[];function u(a,c){return a[1]&&a[10]==="waiting"?0:a[1]&&a[10]==="open"||!a[1]&&a[11]?1:2}return e=u(n),t=r[e]=s[e](n),{c(){t.c(),i=de()},m(a,c){r[e].m(a,c),B(a,i,c),l=!0},p(a,c){let _=e;e=u(a),e===_?r[e].p(a,c):(X(),k(r[_],1,1,()=>{r[_]=null}),Y(),t=r[e],t?t.p(a,c):(t=r[e]=s[e](a),t.c()),b(t,1),t.m(i.parentNode,i))},i(a){l||(b(t),l=!0)},o(a){k(t),l=!1},d(a){a&&C(i),r[e].d(a)}}}function Et(n){let e,t,i,l,s=n[4]("audio.record")+"",r,u;return i=new kt({}),{c(){e=R("div"),t=R("div"),j(i.$$.fragment),l=A(),r=ie(s),d(t,"class","icon color-primary svelte-s8feoe"),d(t,"title","start recording"),d(e,"class","icon-with-text svelte-s8feoe")},m(a,c){B(a,e,c),D(e,t),q(i,t,null),D(e,l),D(e,r),u=!0},p(a,c){(!u||c[0]&16)&&s!==(s=a[4]("audio.record")+"")&&ge(r,s)},i(a){u||(b(i.$$.fragment,a),u=!0)},o(a){k(i.$$.fragment,a),u=!1},d(a){a&&C(e),N(i)}}}function Tt(n){let e,t,i,l,s=n[4]("audio.stop")+"",r,u;return i=new mt({}),{c(){e=R("div"),t=R("div"),j(i.$$.fragment),l=A(),r=ie(s),d(t,"class","icon color-primary svelte-s8feoe"),d(t,"title","stop recording"),d(e,"class","icon-with-text svelte-s8feoe")},m(a,c){B(a,e,c),D(e,t),q(i,t,null),D(e,l),D(e,r),u=!0},p(a,c){(!u||c[0]&16)&&s!==(s=a[4]("audio.stop")+"")&&ge(r,s)},i(a){u||(b(i.$$.fragment,a),u=!0)},o(a){k(i.$$.fragment,a),u=!1},d(a){a&&C(e),N(i)}}}function jt(n){let e,t,i,l,s=n[4]("audio.waiting")+"",r,u;return i=new ut({}),{c(){e=R("div"),t=R("div"),j(i.$$.fragment),l=A(),r=ie(s),d(t,"class","icon color-primary svelte-s8feoe"),d(t,"title","spinner"),d(e,"class","icon-with-text svelte-s8feoe"),oe(e,"width","var(--size-24)")},m(a,c){B(a,e,c),D(e,t),q(i,t,null),D(e,l),D(e,r),u=!0},p(a,c){(!u||c[0]&16)&&s!==(s=a[4]("audio.waiting")+"")&&ge(r,s)},i(a){u||(b(i.$$.fragment,a),u=!0)},o(a){k(i.$$.fragment,a),u=!1},d(a){a&&C(e),N(i)}}}function Ee(n){let e,t,i,l,s;return t=new Ae({}),{c(){e=R("button"),j(t.$$.fragment),d(e,"class","icon svelte-s8feoe"),d(e,"aria-label","select input source")},m(r,u){B(r,e,u),q(t,e,null),i=!0,l||(s=te(e,"click",n[29]),l=!0)},p:K,i(r){i||(b(t.$$.fragment,r),i=!0)},o(r){k(t.$$.fragment,r),i=!1},d(r){r&&C(e),N(t),l=!1,s()}}}function Te(n){let e,t,i,l,s,r,u;i=new Ae({});function a(f,y){return f[7].length===0?Nt:qt}let c=a(n),_=c(n);return{c(){e=R("select"),t=R("button"),j(i.$$.fragment),l=A(),_.c(),d(t,"class","inset-icon svelte-s8feoe"),d(e,"class","select-wrap svelte-s8feoe"),d(e,"aria-label","select source")},m(f,y){B(f,e,y),D(e,t),q(i,t,null),D(t,l),_.m(e,null),s=!0,r||(u=[te(t,"click",Je(n[30])),Ke(ze.call(null,e,n[17])),te(e,"change",n[14])],r=!0)},p(f,y){c===(c=a(f))&&_?_.p(f,y):(_.d(1),_=c(f),_&&(_.c(),_.m(e,null)))},i(f){s||(b(i.$$.fragment,f),s=!0)},o(f){k(i.$$.fragment,f),s=!1},d(f){f&&C(e),N(i),_.d(),r=!1,Xe(u)}}}function qt(n){let e,t=Ce(n[7]),i=[];for(let l=0;l<t.length;l+=1)i[l]=je(Se(n,t,l));return{c(){for(let l=0;l<i.length;l+=1)i[l].c();e=de()},m(l,s){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(l,s);B(l,e,s)},p(l,s){if(s[0]&384){t=Ce(l[7]);let r;for(r=0;r<t.length;r+=1){const u=Se(l,t,r);i[r]?i[r].p(u,s):(i[r]=je(u),i[r].c(),i[r].m(e.parentNode,e))}for(;r<i.length;r+=1)i[r].d(1);i.length=t.length}},d(l){l&&C(e),Ze(i,l)}}}function Nt(n){let e,t=n[4]("common.no_devices")+"",i;return{c(){e=R("option"),i=ie(t),e.__value="",We(e,e.__value),d(e,"class","svelte-s8feoe")},m(l,s){B(l,e,s),D(e,i)},p(l,s){s[0]&16&&t!==(t=l[4]("common.no_devices")+"")&&ge(i,t)},d(l){l&&C(e)}}}function je(n){let e,t=n[39].label+"",i,l,s,r;return{c(){e=R("option"),i=ie(t),l=A(),e.__value=s=n[39].deviceId,We(e,e.__value),e.selected=r=n[8].deviceId===n[39].deviceId,d(e,"class","svelte-s8feoe")},m(u,a){B(u,e,a),D(e,i),D(e,l)},p(u,a){a[0]&128&&t!==(t=u[39].label+"")&&ge(i,t),a[0]&128&&s!==(s=u[39].deviceId)&&(e.__value=s,We(e,e.__value)),a[0]&384&&r!==(r=u[8].deviceId===u[39].deviceId)&&(e.selected=r)},d(u){u&&C(e)}}}function Ut(n){let e,t,i,l,s,r,u,a,c,_,f;t=new gt({props:{time_limit:n[9]}});const y=[Ct,Bt],z=[];function U(h,p){return h[12]?1:0}return c=U(n),_=z[c]=y[c](n),{c(){e=R("div"),j(t.$$.fragment),i=A(),l=R("video"),s=A(),r=R("img"),a=A(),_.c(),d(l,"class","svelte-s8feoe"),J(l,"flip",n[3]),J(l,"hide",!n[12]||n[12]&&!!n[0]),Be(r.src,u=n[0]?.url)||d(r,"src",u),d(r,"class","svelte-s8feoe"),J(r,"hide",!n[12]||n[12]&&!n[0]),d(e,"class","wrap svelte-s8feoe")},m(h,p){B(h,e,p),q(t,e,null),D(e,i),D(e,l),n[26](l),D(e,s),D(e,r),D(e,a),z[c].m(e,null),f=!0},p(h,p){const v={};p[0]&512&&(v.time_limit=h[9]),t.$set(v),(!f||p[0]&8)&&J(l,"flip",h[3]),(!f||p[0]&4097)&&J(l,"hide",!h[12]||h[12]&&!!h[0]),(!f||p[0]&1&&!Be(r.src,u=h[0]?.url))&&d(r,"src",u),(!f||p[0]&4097)&&J(r,"hide",!h[12]||h[12]&&!h[0]);let M=c;c=U(h),c===M?z[c].p(h,p):(X(),k(z[M],1,1,()=>{z[M]=null}),Y(),_=z[c],_?_.p(h,p):(_=z[c]=y[c](h),_.c()),b(_,1),_.m(e,null))},i(h){f||(b(t.$$.fragment,h),b(_),f=!0)},o(h){k(t.$$.fragment,h),k(_),f=!1},d(h){h&&C(e),N(t),n[26](null),z[c].d()}}}function ze(n,e){const t=i=>{n&&!n.contains(i.target)&&!i.defaultPrevented&&e(i)};return document.addEventListener("click",t,!0),{destroy(){document.removeEventListener("click",t,!0)}}}function Lt(n,e,t){let i,l=[],s=null,r=null,u="closed";const a=g=>{g==="closed"?(t(9,r=null),t(10,u="closed"),t(0,H=null)):g==="waiting"?t(10,u="waiting"):t(10,u="open")},c=g=>{L&&t(9,r=g)};let _,{streaming:f=!1}=e,{pending:y=!1}=e,{root:z=""}=e,{stream_every:U=1}=e,{mode:h="image"}=e,{mirror_webcam:p}=e,{include_audio:v}=e,{webcam_constraints:M=null}=e,{i18n:F}=e,{upload:T}=e,{value:H=null}=e;const W=Me();He(()=>{_=document.createElement("canvas"),f&&h==="image"&&window.setInterval(()=>{i&&!y&&O()},U*1e3)});const m=async g=>{const G=g.target.value;await Re(v,i,M,G).then(async re=>{V=re,t(8,s=l.find(se=>se.deviceId===G)||null),t(13,Z=!1)})};async function w(){try{Re(v,i,M).then(async g=>{t(12,le=!0),t(7,l=await Wt()),V=g}).then(()=>zt(l)).then(g=>{t(7,l=g);const S=V.getTracks().map(G=>G.getSettings()?.deviceId)[0];t(8,s=S&&g.find(G=>G.deviceId===S)||l[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&W("error",F("image.no_webcam_support"))}catch(g){if(g instanceof DOMException&&g.name=="NotAllowedError")W("error",F("image.allow_webcam_access"));else throw g}}function O(){var g=_.getContext("2d");if((!f||f&&L)&&i.videoWidth&&i.videoHeight){if(_.width=i.videoWidth,_.height=i.videoHeight,g.drawImage(i,0,0,i.videoWidth,i.videoHeight),p&&(g.scale(-1,1),g.drawImage(i,-i.videoWidth,0)),f&&(!L||u==="waiting"))return;if(f){const S=_.toDataURL("image/jpeg");W("stream",S);return}_.toBlob(S=>{W(f?"stream":"capture",S)},`image/${f?"jpeg":"png"}`,.8)}}let L=!1,E=[],V,Q,P;function x(){if(L){P.stop();let g=new Blob(E,{type:Q}),S=new FileReader;S.onload=async function(G){if(G.target){let re=new File([g],"sample."+Q.substring(6));const se=await Ye([re]);let Ie=(await T(se,z))?.filter(Boolean)[0];W("capture",Ie),W("stop_recording")}},S.readAsDataURL(g)}else if(typeof MediaRecorder<"u"){W("start_recording"),E=[];let g=["video/webm","video/mp4"];for(let S of g)if(MediaRecorder.isTypeSupported(S)){Q=S;break}if(Q===null){console.error("No supported MediaRecorder mimeType");return}P=new MediaRecorder(V,{mimeType:Q}),P.addEventListener("dataavailable",function(S){E.push(S.data)}),P.start(200)}t(11,L=!L)}let le=!1;function ne({destroy:g}={}){h==="image"&&f&&t(11,L=!L),g||(h==="image"?O():x()),!L&&V&&(W("close_stream"),V.getTracks().forEach(S=>S.stop()),t(6,i.srcObject=null,i),t(12,le=!1),window.setTimeout(()=>{t(0,H=null)},500),t(0,H=null))}let Z=!1;function be(g){g.preventDefault(),g.stopPropagation(),t(13,Z=!1)}Ve(()=>{typeof window>"u"||(ne({destroy:!0}),V?.getTracks().forEach(g=>g.stop()))});function we(g){$[g?"unshift":"push"](()=>{i=g,t(6,i)})}const pe=async()=>w(),ve=()=>ne(),ke=()=>t(13,Z=!0),ye=()=>t(13,Z=!1);return n.$$set=g=>{"streaming"in g&&t(1,f=g.streaming),"pending"in g&&t(20,y=g.pending),"root"in g&&t(21,z=g.root),"stream_every"in g&&t(22,U=g.stream_every),"mode"in g&&t(2,h=g.mode),"mirror_webcam"in g&&t(3,p=g.mirror_webcam),"include_audio"in g&&t(23,v=g.include_audio),"webcam_constraints"in g&&t(24,M=g.webcam_constraints),"i18n"in g&&t(4,F=g.i18n),"upload"in g&&t(25,T=g.upload),"value"in g&&t(0,H=g.value)},[H,f,h,p,F,ze,i,l,s,r,u,L,le,Z,m,w,ne,be,a,c,y,z,U,v,M,T,we,pe,ve,ke,ye]}class At extends fe{constructor(e){super(),_e(this,e,Lt,Ut,me,{modify_stream:18,set_time_limit:19,streaming:1,pending:20,root:21,stream_every:22,mode:2,mirror_webcam:3,include_audio:23,webcam_constraints:24,i18n:4,upload:25,value:0,click_outside:5},null,[-1,-1])}get modify_stream(){return this.$$.ctx[18]}get set_time_limit(){return this.$$.ctx[19]}get streaming(){return this.$$.ctx[1]}set streaming(e){this.$$set({streaming:e}),I()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),I()}get root(){return this.$$.ctx[21]}set root(e){this.$$set({root:e}),I()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),I()}get mode(){return this.$$.ctx[2]}set mode(e){this.$$set({mode:e}),I()}get mirror_webcam(){return this.$$.ctx[3]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),I()}get include_audio(){return this.$$.ctx[23]}set include_audio(e){this.$$set({include_audio:e}),I()}get webcam_constraints(){return this.$$.ctx[24]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),I()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),I()}get upload(){return this.$$.ctx[25]}set upload(e){this.$$set({upload:e}),I()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),I()}get click_outside(){return ze}}const Ft=At;function qe(n){let e,t,i,l=n[18]&&Ne(n);return t=new xe({props:{Icon:et,label:"Remove Image"}}),t.$on("click",n[31]),{c(){l&&l.c(),e=A(),j(t.$$.fragment)},m(s,r){l&&l.m(s,r),B(s,e,r),q(t,s,r),i=!0},p(s,r){s[18]?l?(l.p(s,r),r[0]&262144&&b(l,1)):(l=Ne(s),l.c(),b(l,1),l.m(e.parentNode,e)):l&&(X(),k(l,1,1,()=>{l=null}),Y())},i(s){i||(b(l),b(t.$$.fragment,s),i=!0)},o(s){k(l),k(t.$$.fragment,s),i=!1},d(s){s&&C(e),l&&l.d(s),N(t,s)}}}function Ne(n){let e,t;return e=new at({props:{container:n[22]}}),{c(){j(e.$$.fragment)},m(i,l){q(e,i,l),t=!0},p(i,l){const s={};l[0]&4194304&&(s.container=i[22]),e.$set(s)},i(i){t||(b(e.$$.fragment,i),t=!0)},o(i){k(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Ot(n){let e,t,i=n[3]?.url&&!n[20]&&qe(n);return{c(){i&&i.c(),e=de()},m(l,s){i&&i.m(l,s),B(l,e,s),t=!0},p(l,s){l[3]?.url&&!l[20]?i?(i.p(l,s),s[0]&1048584&&b(i,1)):(i=qe(l),i.c(),b(i,1),i.m(e.parentNode,e)):i&&(X(),k(i,1,1,()=>{i=null}),Y())},i(l){t||(b(i),t=!0)},o(l){k(i),t=!1},d(l){l&&C(e),i&&i.d(l)}}}function Ue(n){let e;const t=n[30].default,i=tt(t,n,n[46],null);return{c(){i&&i.c()},m(l,s){i&&i.m(l,s),e=!0},p(l,s){i&&i.p&&(!e||s[1]&32768)&&it(i,t,l,l[46],e?nt(t,l[46],s,null):lt(l[46]),null)},i(l){e||(b(i,l),e=!0)},o(l){k(i,l),e=!1},d(l){i&&i.d(l)}}}function Pt(n){let e,t,i=n[3]===null&&Ue(n);return{c(){i&&i.c(),e=de()},m(l,s){i&&i.m(l,s),B(l,e,s),t=!0},p(l,s){l[3]===null?i?(i.p(l,s),s[0]&8&&b(i,1)):(i=Ue(l),i.c(),b(i,1),i.m(e.parentNode,e)):i&&(X(),k(i,1,1,()=>{i=null}),Y())},i(l){t||(b(i),t=!0)},o(l){k(i),t=!1},d(l){l&&C(e),i&&i.d(l)}}}function Ht(n){let e,t,i,l,s;return t=new bt({props:{src:n[3].url,alt:n[3].alt_text}}),{c(){e=R("div"),j(t.$$.fragment),d(e,"class","image-frame svelte-1ti4ehe"),J(e,"selectable",n[11])},m(r,u){B(r,e,u),q(t,e,null),i=!0,l||(s=te(e,"click",n[27]),l=!0)},p(r,u){const a={};u[0]&8&&(a.src=r[3].url),u[0]&8&&(a.alt=r[3].alt_text),t.$set(a),(!i||u[0]&2048)&&J(e,"selectable",r[11])},i(r){i||(b(t.$$.fragment,r),i=!0)},o(r){k(t.$$.fragment,r),i=!1},d(r){r&&C(e),N(t),l=!1,s()}}}function Vt(n){let e,t,i,l;function s(a){n[36](a)}function r(a){n[37](a)}let u={root:n[12],value:n[3],mirror_webcam:n[10],stream_every:n[17],streaming:n[9],mode:"image",include_audio:!1,i18n:n[13],upload:n[15],webcam_constraints:n[19]};return n[4]!==void 0&&(u.modify_stream=n[4]),n[5]!==void 0&&(u.set_time_limit=n[5]),e=new Ft({props:u}),$.push(()=>ue(e,"modify_stream",s)),$.push(()=>ue(e,"set_time_limit",r)),e.$on("capture",n[38]),e.$on("stream",n[39]),e.$on("error",n[40]),e.$on("drag",n[41]),e.$on("upload",n[42]),e.$on("close_stream",n[43]),{c(){j(e.$$.fragment)},m(a,c){q(e,a,c),l=!0},p(a,c){const _={};c[0]&4096&&(_.root=a[12]),c[0]&8&&(_.value=a[3]),c[0]&1024&&(_.mirror_webcam=a[10]),c[0]&131072&&(_.stream_every=a[17]),c[0]&512&&(_.streaming=a[9]),c[0]&8192&&(_.i18n=a[13]),c[0]&32768&&(_.upload=a[15]),c[0]&524288&&(_.webcam_constraints=a[19]),!t&&c[0]&16&&(t=!0,_.modify_stream=a[4],ce(()=>t=!1)),!i&&c[0]&32&&(i=!0,_.set_time_limit=a[5],ce(()=>i=!1)),e.$set(_)},i(a){l||(b(e.$$.fragment,a),l=!0)},o(a){k(e.$$.fragment,a),l=!1},d(a){N(e,a)}}}function Le(n){let e,t,i;function l(r){n[44](r)}let s={sources:n[8],handle_clear:n[24],handle_select:n[28]};return n[1]!==void 0&&(s.active_source=n[1]),e=new ct({props:s}),$.push(()=>ue(e,"active_source",l)),{c(){j(e.$$.fragment)},m(r,u){q(e,r,u),i=!0},p(r,u){const a={};u[0]&256&&(a.sources=r[8]),!t&&u[0]&2&&(t=!0,a.active_source=r[1],ce(()=>t=!1)),e.$set(a)},i(r){i||(b(e.$$.fragment,r),i=!0)},o(r){k(e.$$.fragment,r),i=!1},d(r){N(e,r)}}}function Qt(n){let e,t,i,l,s,r,u,a,c,_,f,y,z,U=n[8].length>1||n[8].includes("clipboard"),h;e=new rt({props:{show_label:n[7],Icon:st,label:n[6]||"Image"}}),l=new ft({props:{$$slots:{default:[Ot]},$$scope:{ctx:n}}});function p(m){n[33](m)}function v(m){n[34](m)}let M={hidden:n[3]!==null||n[1]==="webcam",filetype:n[1]==="clipboard"?"clipboard":"image/*",root:n[12],max_file_size:n[14],disable_click:!n[8].includes("upload")||n[3]!==null,upload:n[15],stream_handler:n[16],aria_label:n[13]("image.drop_to_upload"),$$slots:{default:[Pt]},$$scope:{ctx:n}};n[0]!==void 0&&(M.uploading=n[0]),n[2]!==void 0&&(M.dragging=n[2]),u=new ht({props:M}),n[32](u),$.push(()=>ue(u,"uploading",p)),$.push(()=>ue(u,"dragging",v)),u.$on("load",n[23]),u.$on("error",n[35]);const F=[Vt,Ht],T=[];function H(m,w){return m[1]==="webcam"&&(m[9]||!m[9]&&!m[3])?0:m[3]!==null&&!m[9]?1:-1}~(f=H(n))&&(y=T[f]=F[f](n));let W=U&&Le(n);return{c(){j(e.$$.fragment),t=A(),i=R("div"),j(l.$$.fragment),s=A(),r=R("div"),j(u.$$.fragment),_=A(),y&&y.c(),z=A(),W&&W.c(),d(r,"class","upload-container svelte-1ti4ehe"),J(r,"reduced-height",n[8].length>1),oe(r,"width",n[3]?"auto":"100%"),d(i,"data-testid","image"),d(i,"class","image-container svelte-1ti4ehe")},m(m,w){q(e,m,w),B(m,t,w),B(m,i,w),q(l,i,null),D(i,s),D(i,r),q(u,r,null),D(r,_),~f&&T[f].m(r,null),D(i,z),W&&W.m(i,null),n[45](i),h=!0},p(m,w){const O={};w[0]&128&&(O.show_label=m[7]),w[0]&64&&(O.label=m[6]||"Image"),e.$set(O);const L={};w[0]&5505032|w[1]&32768&&(L.$$scope={dirty:w,ctx:m}),l.$set(L);const E={};w[0]&10&&(E.hidden=m[3]!==null||m[1]==="webcam"),w[0]&2&&(E.filetype=m[1]==="clipboard"?"clipboard":"image/*"),w[0]&4096&&(E.root=m[12]),w[0]&16384&&(E.max_file_size=m[14]),w[0]&264&&(E.disable_click=!m[8].includes("upload")||m[3]!==null),w[0]&32768&&(E.upload=m[15]),w[0]&65536&&(E.stream_handler=m[16]),w[0]&8192&&(E.aria_label=m[13]("image.drop_to_upload")),w[0]&8|w[1]&32768&&(E.$$scope={dirty:w,ctx:m}),!a&&w[0]&1&&(a=!0,E.uploading=m[0],ce(()=>a=!1)),!c&&w[0]&4&&(c=!0,E.dragging=m[2],ce(()=>c=!1)),u.$set(E);let V=f;f=H(m),f===V?~f&&T[f].p(m,w):(y&&(X(),k(T[V],1,1,()=>{T[V]=null}),Y()),~f?(y=T[f],y?y.p(m,w):(y=T[f]=F[f](m),y.c()),b(y,1),y.m(r,null)):y=null),(!h||w[0]&256)&&J(r,"reduced-height",m[8].length>1),w[0]&8&&oe(r,"width",m[3]?"auto":"100%"),w[0]&256&&(U=m[8].length>1||m[8].includes("clipboard")),U?W?(W.p(m,w),w[0]&256&&b(W,1)):(W=Le(m),W.c(),b(W,1),W.m(i,null)):W&&(X(),k(W,1,1,()=>{W=null}),Y())},i(m){h||(b(e.$$.fragment,m),b(l.$$.fragment,m),b(u.$$.fragment,m),b(y),b(W),h=!0)},o(m){k(e.$$.fragment,m),k(l.$$.fragment,m),k(u.$$.fragment,m),k(y),k(W),h=!1},d(m){m&&(C(t),C(i)),N(e,m),N(l),n[32](null),N(u),~f&&T[f].d(),W&&W.d(),n[45](null)}}}function Gt(n,e,t){let i,{$$slots:l={},$$scope:s}=e,{value:r=null}=e,{label:u=void 0}=e,{show_label:a}=e,{sources:c=["upload","clipboard","webcam"]}=e,{streaming:_=!1}=e,{pending:f=!1}=e,{mirror_webcam:y}=e,{selectable:z=!1}=e,{root:U}=e,{i18n:h}=e,{max_file_size:p=null}=e,{upload:v}=e,{stream_handler:M}=e,{stream_every:F}=e,{modify_stream:T}=e,{set_time_limit:H}=e,{show_fullscreen_button:W=!0}=e,m,{uploading:w=!1}=e,{active_source:O=null}=e,{webcam_constraints:L=void 0}=e;async function E({detail:o}){if(!_){if(o.path?.toLowerCase().endsWith(".svg")&&o.url){const De=await(await fetch(o.url)).text();t(3,r={...o,url:`data:image/svg+xml,${encodeURIComponent(De)}`})}else t(3,r=o);P("upload")}}function V(){t(3,r=null),P("clear"),P("change",null)}async function Q(o,ee){if(ee==="stream"){P("stream",{value:{url:o},is_value_data:!0});return}t(29,f=!0);const De=await m.load_files([new File([o],`image/${_?"jpeg":"png"}`)]);(ee==="change"||ee==="upload")&&(t(3,r=De?.[0]||null),await $e(),P("change")),t(29,f=!1)}const P=Me();let{dragging:x=!1}=e;function le(o){let ee=_t(o);ee&&P("select",{index:ee,value:null})}async function ne(o){switch(o){case"clipboard":m.paste_clipboard();break}}let Z;const be=o=>{t(3,r=null),P("clear"),o.stopPropagation()};function we(o){$[o?"unshift":"push"](()=>{m=o,t(21,m)})}function pe(o){w=o,t(0,w)}function ve(o){x=o,t(2,x)}function ke(o){he.call(this,n,o)}function ye(o){T=o,t(4,T)}function g(o){H=o,t(5,H)}const S=o=>Q(o.detail,"change"),G=o=>Q(o.detail,"stream");function re(o){he.call(this,n,o)}function se(o){he.call(this,n,o)}const Ie=o=>Q(o.detail,"upload");function Fe(o){he.call(this,n,o)}function Oe(o){O=o,t(1,O),t(8,c)}function Pe(o){$[o?"unshift":"push"](()=>{Z=o,t(22,Z)})}return n.$$set=o=>{"value"in o&&t(3,r=o.value),"label"in o&&t(6,u=o.label),"show_label"in o&&t(7,a=o.show_label),"sources"in o&&t(8,c=o.sources),"streaming"in o&&t(9,_=o.streaming),"pending"in o&&t(29,f=o.pending),"mirror_webcam"in o&&t(10,y=o.mirror_webcam),"selectable"in o&&t(11,z=o.selectable),"root"in o&&t(12,U=o.root),"i18n"in o&&t(13,h=o.i18n),"max_file_size"in o&&t(14,p=o.max_file_size),"upload"in o&&t(15,v=o.upload),"stream_handler"in o&&t(16,M=o.stream_handler),"stream_every"in o&&t(17,F=o.stream_every),"modify_stream"in o&&t(4,T=o.modify_stream),"set_time_limit"in o&&t(5,H=o.set_time_limit),"show_fullscreen_button"in o&&t(18,W=o.show_fullscreen_button),"uploading"in o&&t(0,w=o.uploading),"active_source"in o&&t(1,O=o.active_source),"webcam_constraints"in o&&t(19,L=o.webcam_constraints),"dragging"in o&&t(2,x=o.dragging),"$$scope"in o&&t(46,s=o.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&258&&!O&&c&&t(1,O=c[0]),n.$$.dirty[0]&514&&t(20,i=_&&O==="webcam"),n.$$.dirty[0]&1048577&&w&&!i&&t(3,r=null),n.$$.dirty[0]&4&&P("drag",x)},[w,O,x,r,T,H,u,a,c,_,y,z,U,h,p,v,M,F,W,L,i,m,Z,E,V,Q,P,le,ne,f,l,be,we,pe,ve,ke,ye,g,S,G,re,se,Ie,Fe,Oe,Pe,s]}class Jt extends fe{constructor(e){super(),_e(this,e,Gt,Qt,me,{value:3,label:6,show_label:7,sources:8,streaming:9,pending:29,mirror_webcam:10,selectable:11,root:12,i18n:13,max_file_size:14,upload:15,stream_handler:16,stream_every:17,modify_stream:4,set_time_limit:5,show_fullscreen_button:18,uploading:0,active_source:1,webcam_constraints:19,dragging:2},null,[-1,-1])}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),I()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),I()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),I()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),I()}get streaming(){return this.$$.ctx[9]}set streaming(e){this.$$set({streaming:e}),I()}get pending(){return this.$$.ctx[29]}set pending(e){this.$$set({pending:e}),I()}get mirror_webcam(){return this.$$.ctx[10]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),I()}get selectable(){return this.$$.ctx[11]}set selectable(e){this.$$set({selectable:e}),I()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),I()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),I()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),I()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),I()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),I()}get stream_every(){return this.$$.ctx[17]}set stream_every(e){this.$$set({stream_every:e}),I()}get modify_stream(){return this.$$.ctx[4]}set modify_stream(e){this.$$set({modify_stream:e}),I()}get set_time_limit(){return this.$$.ctx[5]}set set_time_limit(e){this.$$set({set_time_limit:e}),I()}get show_fullscreen_button(){return this.$$.ctx[18]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),I()}get uploading(){return this.$$.ctx[0]}set uploading(e){this.$$set({uploading:e}),I()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),I()}get webcam_constraints(){return this.$$.ctx[19]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),I()}get dragging(){return this.$$.ctx[2]}set dragging(e){this.$$set({dragging:e}),I()}}const si=Jt;export{si as I,Ft as W};
//# sourceMappingURL=ImageUploader-BK9kfkZd.js.map
