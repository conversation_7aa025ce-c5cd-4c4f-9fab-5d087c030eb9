import{a as c,i as d,s as h,E as n,z as t,d as u,C as l,D as r,l as f}from"../lite.js";function w(i){let e,s,a;return{c(){e=n("svg"),s=n("polyline"),a=n("path"),t(s,"points","1 4 1 10 7 10"),t(a,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),t(e,"aria-label","undo"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-rotate-ccw")},m(o,p){u(o,e,p),l(e,s),l(e,a)},p:r,i:r,o:r,d(o){o&&f(e)}}}class m extends c{constructor(e){super(),d(this,e,null,w,h,{})}}export{m as U};
//# sourceMappingURL=Undo-50qkik3g.js.map
