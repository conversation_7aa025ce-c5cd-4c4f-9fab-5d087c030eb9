import{a as m,i as x,s as y,f as a,e as d,d as c,D as o,l as r,w as u,x as _}from"../lite.js";function b(f){let t,s,l;return{c(){t=u(f[1]),s=u(" x "),l=u(f[2])},m(e,i){c(e,t,i),c(e,s,i),c(e,l,i)},p(e,i){i&2&&_(t,e[1]),i&4&&_(l,e[2])},d(e){e&&(r(t),r(s),r(l))}}}function h(f){let t;return{c(){t=u(f[0])},m(s,l){c(s,t,l)},p(s,l){l&1&&_(t,s[0])},d(s){s&&r(t)}}}function k(f){let t;function s(i,n){return i[0]?h:b}let l=s(f),e=l(f);return{c(){e.c(),t=d()},m(i,n){e.m(i,n),c(i,t,n)},p(i,[n]){l===(l=s(i))&&e?e.p(i,n):(e.d(1),e=l(i),e&&(e.c(),e.m(t.parentNode,t)))},i:o,o,d(i){i&&r(t),e.d(i)}}}function q(f,t,s){let{title:l}=t,{x:e}=t,{y:i}=t;return f.$$set=n=>{"title"in n&&s(0,l=n.title),"x"in n&&s(1,e=n.x),"y"in n&&s(2,i=n.y)},[l,e,i]}class C extends m{constructor(t){super(),x(this,t,q,k,y,{title:0,x:1,y:2})}get title(){return this.$$.ctx[0]}set title(t){this.$$set({title:t}),a()}get x(){return this.$$.ctx[1]}set x(t){this.$$set({x:t}),a()}get y(){return this.$$.ctx[2]}set y(t){this.$$set({y:t}),a()}}export{C as default};
//# sourceMappingURL=Example-DBMqaoUc.js.map
