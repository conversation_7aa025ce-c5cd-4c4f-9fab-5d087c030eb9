import{a as m,i as p,s as c,f,q as b,y as g,z as r,A as o,d as z,C as v,u as C,r as E,v as k,k as q,t as B,l as R,O as y}from"../lite.js";function A(s){let e,l,a;const d=s[5].default,n=b(d,s,s[4],null);return{c(){e=g("div"),l=g("div"),n&&n.c(),r(l,"class","icon svelte-1oiin9d"),r(e,"class","empty svelte-1oiin9d"),r(e,"aria-label","Empty value"),o(e,"small",s[0]==="small"),o(e,"large",s[0]==="large"),o(e,"unpadded_box",s[1]),o(e,"small_parent",s[3])},m(t,i){z(t,e,i),v(e,l),n&&n.m(l,null),s[6](e),a=!0},p(t,[i]){n&&n.p&&(!a||i&16)&&C(n,d,t,t[4],a?k(d,t[4],i,null):E(t[4]),null),(!a||i&1)&&o(e,"small",t[0]==="small"),(!a||i&1)&&o(e,"large",t[0]==="large"),(!a||i&2)&&o(e,"unpadded_box",t[1]),(!a||i&8)&&o(e,"small_parent",t[3])},i(t){a||(q(n,t),a=!0)},o(t){B(n,t),a=!1},d(t){t&&R(e),n&&n.d(t),s[6](null)}}}function O(s){if(!s)return!1;const{height:e}=s.getBoundingClientRect(),{height:l}=s.parentElement?.getBoundingClientRect()||{height:e};return e>l+2}function S(s,e,l){let a,{$$slots:d={},$$scope:n}=e,{size:t="small"}=e,{unpadded_box:i=!1}=e,_;function h(u){y[u?"unshift":"push"](()=>{_=u,l(2,_)})}return s.$$set=u=>{"size"in u&&l(0,t=u.size),"unpadded_box"in u&&l(1,i=u.unpadded_box),"$$scope"in u&&l(4,n=u.$$scope)},s.$$.update=()=>{s.$$.dirty&4&&l(3,a=O(_))},[t,i,_,a,n,d,h]}class w extends m{constructor(e){super(),p(this,e,S,A,c,{size:0,unpadded_box:1})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),f()}get unpadded_box(){return this.$$.ctx[1]}set unpadded_box(e){this.$$set({unpadded_box:e}),f()}}export{w as E};
//# sourceMappingURL=Empty-Bzq0Ew6m.js.map
