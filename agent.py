import asyncio
import os
from dataclasses import dataclass
from typing import List, Optional

import gradio as gr
from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI, ChatOpenAI
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
import json

from browser_use import Agent, <PERSON><PERSON><PERSON>, BrowserConfig,Controller, ActionResult, SystemPrompt
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate

from models import CustomerJourney
from prompt import ENGINEER, GENZ, SENIOR_CITIZEN, UX_PARAMETERS, personas
from bs4 import BeautifulSoup

# from langchain.chat_models import AzureChatOpenAI
from langchain.schema import HumanMessage

from utils import open_pdf
from config import get_config

load_dotenv()
controller = Controller()
config = get_config()

@dataclass
class ActionResult:
	is_done: bool
	extracted_content: Optional[str]
	error: Optional[str]
	include_in_memory: bool


@dataclass
class AgentHistoryList:
	all_results: List[ActionResult]
	all_model_outputs: List[dict]

@controller.action('Ask user for login username and password')
def ask_human(question: str) -> str:
    answer = input(f'\n{question}\nInput: ')
    return ActionResult(extracted_content=answer)

def parse_agent_history(history_str: str) -> None:
	console = Console()

	# Split the content into sections based on ActionResult entries
	sections = history_str.split('ActionResult(')

	for i, section in enumerate(sections[1:], 1):  # Skip first empty section
		# Extract relevant information
		content = ''
		if 'extracted_content=' in section:
			content = section.split('extracted_content=')[1].split(',')[0].strip("'")

		if content:
			header = Text(f'Step {i}', style='bold blue')
			panel = Panel(content, title=header, border_style='blue')
			console.print(panel)
			console.print()

def initialize_agent_object(task: str, **kwargs) -> Agent:
	return Agent(
		task=task,
		llm=AzureChatOpenAI(
			azure_deployment=os.getenv("AZURE_DEPLOYMENT", config.azure_deployment),
			openai_api_version=os.getenv("OPENAI_API_VERSION", config.openai_api_version),
			azure_endpoint=os.getenv("AZURE_ENDPOINT", config.azure_endpoint),
			api_key=os.getenv("AZURE_API_KEY")
		),
		controller= controller,
		**kwargs
	)

async def browser_task_with_persona(task: str, user: dict, headless: bool = False) -> bool:
	try:
		class PersonaPrompt(SystemPrompt):
			def important_rules(self) -> str:
				# Get existing rules from parent class
				existing_rules = super().important_rules()

				# Add your custom rules
				new_rules = """
		9. MOST IMPORTANT RULE THAT GOVERNS HOW YOU TAKE YOUR DECISION:
		- 
		""" + user['behavior']
				return f'{existing_rules}\n{new_rules}'		
		agent = initialize_agent_object(task, system_prompt_class=PersonaPrompt)
		result = await agent.run()	
		task_log = build_log(task,result)
		result = await generate_customer_journey_data(task_log)
		generate_customer_journey_report(result.model_dump(),user)
		return True
	except Exception as e:
		print(f'Error while executing task with persona{user["name"]}: {str(e)}')
		return False

async def browser_task(task: str, headless: bool = False) -> bool:
	try:
		browser_config = BrowserConfig(
			headless=headless,
			disable_security=True,
			chrome_instance_path=config.chrome_instance_path
		)
		browser = Browser(browser_config)
		agent = initialize_agent_object(task, browser=browser)
		result = await agent.run()
		browser.close()
		return result
	except Exception as e:
		print(f'Error while executing task: {str(e)}')
		return False

async def run_browser_task(
	task: str,
    user_type:str,
	headless: bool = False,
	explorer: bool = True,
) -> str:
	try:
		if explorer:
			# if user_type.lower() == 'engineer':
			# 	result = await browser_task_with_persona(task, personas["dev"], headless)
			# elif user_type.lower() == 'genz':
			# 	result = await browser_task_with_persona(task, personas["genz"], headless)
			# elif user_type.lower() == 'senior citizen':
			# 	result = await browser_task_with_persona(task, personas["senior_citizen"], headless)
			task_list = []
			if user_type.lower() == 'all':
				task_list = [browser_task_with_persona(task, user, headless) for user in personas.values()]
			else:
				for user in personas.values():
					if user['user_type'] == user_type:
						task_list.append(browser_task_with_persona(task, user, headless))
			result = await asyncio.gather(*task_list)
			if isinstance(result, list):
				if all(result):
					return f"Customer journey analysis for {', '.join(personas.keys())} completed successfully. Report Generated."
				elif any(result):
					return f"Customer journey analysis for {', '.join([_ for i, _ in enumerate(personas.keys()) if result[i]])} Completed. Report Generated. {len(result) - sum(result)} Persona failed. Please check logs for details."
				return f"All personas failed to complete customer journey analysis. Please check logs for details"
			elif isinstance(result, bool):
				if result is False:
					return "Failed to complete customer journey analysis. Please check logs for details"
				return "Customer journey analysis completed successfully. Report Generated."
			else:
				return f"Unexpected Result Type: {type(result).__name__}. Task may have completed with issues"
		else: 
			result = await browser_task(task, headless)
			if isinstance(result, bool) and result is False:
				return "Failed to execute task. Please check logs for details."
			return result
	except Exception as e:
		return f"Error while executing task: {str(e)}"
		
	# sensitive_data={'username':'<EMAIL>', 'password':'password'}
 

def generate_customer_journey_report(data,user):
	html_content = """<!DOCTYPE html>
	<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>Customer Journey Map</title>
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
		<style>
			.header-section {
				background-color: #FF4949;
				color: white;
				padding: 30px;
			}

			.persona-card,
			.scenario-card,
			.goal-card {
				border: 1px solid white;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.1);
				height: 100%;
			}

			.persona-card img {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				display: block;
				margin: 0 auto;
			}

			.goal-card input {
				width: 100%;
				margin-bottom: 10px;
				padding: 8px;
				border: none;
				border-radius: 5px;
			}

			.stage-bar {
				display: flex;
				gap: 5px;
				margin-top: 20px;
			}

			.stage {
				flex: 1;
				padding: 10px 15px;
				font-weight: bold;
				color: white;
				text-align: center;
				border-radius: 5px;
			}

			.awareness {
				background-color: #5E2590;
			}

			.consideration {
				background-color: #F55050;
			}

			.purchase {
				background-color: #F78D1E;
			}

			.onboarding {
				background-color: #F7C934;
			}

			.advocacy {
				background-color: #8BC34A;
			}
		</style>
	</head>
	<body>
		<div class="main">
			<div class="container-fluid header-section">
				<div class="container">
					<h1 class="text-white fw-bold">Customer Journey Map</h1>
					<div class="row mt-4">
						<!-- Persona Section -->
						<div class="col-md-3">
							<div class="persona-card text-center">
								<img src="../images/boy.png" alt="Persona">
								<h5 class="mt-3">Persona Name</h5>
								<p>Brief Info.</p>
							</div>
						</div>
						<!-- Scenario Section -->
						<div class="col-md-5">
							<div class="scenario-card">
								<h4 class="fw-bold">Scenario</h4>
								<p>Explain the use case you are exploring for this product or service.</p>
							</div>
						</div>
						<!-- Goals Section -->
						<div class="col-md-4">
							<div class="goal-card">
								<input type="text" class="goal" placeholder="Main Goal" value="Goal">
								<input type="text" class="expectation" placeholder="Expectation 1" value="Expectation">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	</html>"""

	soup = BeautifulSoup(html_content, "html.parser")
	persona_card = soup.find("div", class_="persona-card")
	if persona_card:
		name_tag = persona_card.find("h5")
		if name_tag:
			name_tag.string = user['name']
		
		bio_tag = persona_card.find("p")
		if bio_tag:
			bio_tag.string = user['role']
   
		img = persona_card.find("img")
		if img:
			img['src'] = user['image']

	# Locate the Scenario section and update text
	scenario_card = soup.find("div", class_="scenario-card")
	if scenario_card:
		scenario_text_tag = scenario_card.find("p")
		if scenario_text_tag:
			scenario_text_tag.string = data['bio']['scenario']

	# Locate the Goals section and update inputs
	goal = soup.find("input", class_="goal")
	if goal:
		goal['value'] = data['bio']['goal']
	expectation = soup.find("input", class_="expectation")
	if expectation:
		expectation['value'] = data['bio']['expectation']

	header_section = soup.find("div", class_="container-fluid header-section")

	# Define categories and colors
	categories = {
		"Pain Points": [entry["pain_points"] for entry in data["customer_journey"]],
		"Emotions": [entry["emotion"] for entry in data["customer_journey"]],
		"Customer Actions": [entry["customer_action"] for entry in data["customer_journey"]],
		"Stages": [entry["stage"] for entry in data["customer_journey"]],
	}
	colors = ["#5E2590", "#F55050", "#F78D1E", "#F7C934", "#8BC34A"]

	for category, items in categories.items():
		container = soup.new_tag("div", **{"class": "container mt-4 mb-4"})
		title = soup.new_tag("h4", **{"class": "fw-bold"})
		title.string = category
		container.append(title)

		stage_bar = soup.new_tag("div", **{"class": "stage-bar"})
		for i, item in enumerate(items):
			stage_div = soup.new_tag("div", **{"class": "stage"})
			stage_div["style"] = f"background-color: {colors[i % len(colors)]}"  # Rotate colors
			stage_div.string = item
			stage_bar.append(stage_div)

		container.append(stage_bar)
		header_section.insert_after(container)

	os.makedirs('output', exist_ok=True)
	filename = f"output/customer_journey_map_{user['name']}.html"
	with open(filename, "w", encoding="utf-8") as output_file:
		output_file.write(str(soup.prettify()))
	open_pdf(filename)

def build_log(task,result):
    log = " Goal : " + task
    try:
        for idx, message in enumerate(result.model_thoughts()):  # Renamed 'id' to 'idx'
            print('message_id:', getattr(message, "evaluation_previous_goal", "N/A"))  # Debugging print
            log += "Step " + str(idx + 1) + "\n"
            print('before prev')

            # Ensure safe string conversion
            log += "Eval: " + str(getattr(message, "evaluation_previous_goal", "N/A")) + "\n"
            print('before memory')

            log += "Memory: " + str(getattr(message, "memory", "N/A")) + "\n"
            print('before next goal')

            log += "Next Goal: " + str(getattr(message, "next_goal", "N/A")) + "\n"

            # Ensure action_results() exists and is indexed correctly
            action_results = result.action_results()
            if action_results and len(action_results) > idx:
                print('Action Reulst=:', action_results[idx])

                log += "Result: " + str(action_results[idx].extracted_content) + "\n"
            else:
                log += "Result: N/A\n"

    except Exception as e:
        print('Error:', str(e))  # Proper string conversion
    return log

def create_ui():
	print("Initializing UI...")

	with gr.Blocks( title='Crew Agent') as interface:
		gr.Markdown('# Creative Ascender Task Automation')
		with gr.Row():
			with gr.Column():
				task = gr.Textbox(
					label='Task Description',
					placeholder='E.g., Find flights from New York to London for next week',
					lines=3,
				)
				
				# Add dropdown for user type
				user_type = gr.Dropdown(
					choices=list(set([personas[key]['user_type'] for key in personas.keys() if "user_type" in personas[key]])) + ["ALL"],
					label='Persona',
					value=config.default_persona
				)

				# Add conditional text input for 'Other' option
				# other_type = gr.Textbox(
				#     label='Specify Other User Type',
				#     visible=False,  # Initially hidden
				#     placeholder='Please specify your user type'
				# )	
				
				explorer = gr.Checkbox(label='Explorer', value=config.explorer_default)
				headless = gr.Checkbox(label='Run Headless', value=config.headless_default)
				submit_btn = gr.Button('Run Task')

			with gr.Column():
				output = gr.Textbox(label='Output', lines=10, interactive=False)

		# Function to toggle visibility of other_type input
		def toggle_other_input(choice):
			return gr.update(visible=(choice == "Other"))

		# Connect dropdown to visibility toggle
        # user_type.change(
        #     fn=toggle_other_input,
        #     inputs=user_type,
        #     outputs=other_type
        # )

		submit_btn.click(
			fn=lambda task, user_type, headless, explorer: asyncio.run(run_browser_task(task, user_type, headless, explorer)),
			inputs=[task, user_type, headless, explorer],
			outputs=output,
		)

		print("UI setup complete. Launching...")
		return interface

async def generate_customer_journey_data(task_log:str):
	print("Entered this portion")
	# Azure OpenAI Configuration
	azure_openai_config = {
		"openai_api_base": os.getenv("AZURE_ENDPOINT", config.azure_endpoint),
		"openai_api_version": os.getenv("OPENAI_API_VERSION", config.openai_api_version),
		"deployment_name": os.getenv("AZURE_DEPLOYMENT", config.azure_deployment),
		"openai_api_key": os.getenv("AZURE_API_KEY"),
	}

	# Initialize Azure OpenAI Model in LangChain
	llm = AzureChatOpenAI(
		azure_endpoint=azure_openai_config["openai_api_base"],
		openai_api_version=azure_openai_config["openai_api_version"],
		azure_deployment=azure_openai_config["deployment_name"],
		api_key=azure_openai_config["openai_api_key"],
	)
	print("LLM Created")
	parser = PydanticOutputParser(pydantic_object=CustomerJourney)

	# Define Prompt Template
	prompt_template = PromptTemplate(
		template="""
		Given the log of my testcase execution in browser. 
		- Identify the stage of the customer journey
		- Identify customer action in each stages
		- Identify emotions at each stage (if data is available or can be infered)
		- Identify pain points and obstacles.
		and generate a customer journey JSON that follows this Pydantic model:
		{format_instructions}
		The testcase execution log: {task_log}
		""",
		input_variables=['task_log'],
		partial_variables={"format_instructions": parser.get_format_instructions()},
	)

	# Generate Prompt with User Input
	formatted_prompt = prompt_template.format(task_log=task_log)

	print("Creating message")
	# Pass formatted prompt to the LLM
	messages = [HumanMessage(content=formatted_prompt)]
	response = llm(messages)

	print("message: ", response)
	# Parse Response to Pydantic Model
	customer_journey = parser.parse(response.content)

	# Output Parsed Data
	print(customer_journey)
	return customer_journey

if __name__ == '__main__':

	demo = create_ui()
	demo.launch(inbrowser=config.launch_gradio_in_browser)