import{a as Ae,i as Ue,s as Pe,E as de,z as k,d as X,C as I,D as Z,l as Q,N as Re,f as h,y,c as K,b as $,A as le,m as Y,R as qe,T as ht,M as W,a1 as bt,k as w,h as ee,t as C,j as te,U as pt,n as N,V as wt,o as kt,J as vt,a5 as xe,K as zt,w as je,x as Ie,aq as Ge,$ as Le,as as Ct,a2 as St,O as se,a7 as ae,a8 as ue,p as pe,B as Tt,Y as Dt,S as Mt,a0 as Et,a6 as Bt}from"../lite.js";import{B as Ht}from"./BlockTitle-DvFB_De3.js";import{F as Ft}from"./File-C5WPisji.js";import{M as Rt}from"./SelectSource-kJI_8u2f.js";import{M as Vt}from"./Music-BCIGqdvV.js";import{S as At}from"./Send-DPp49sBe.js";import{S as Ut}from"./Square-CkbFMpLj.js";import{V as Pt}from"./Video-4rA7HTQG.js";import{U as jt}from"./Upload-Do_omv-N.js";import{I as It}from"./Image-BPQ6A_U-.js";/* empty css                                                   */import{I as qt}from"./InteractiveAudio-BSRCxffH.js";import{default as Gl}from"./Example-CYkgQj2b.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";import"./Upload-CYshamIj.js";/* empty css                                             */import"./file-url-CoOyVRgq.js";import"./ModifyUpload-b77W1M2_.js";import"./Download-RUpc9r8A.js";import"./Edit-fMGAgLsI.js";import"./Undo-50qkik3g.js";import"./IconButtonWrapper-BqpIgNIH.js";import"./DownloadLink-dHe4pFcz.js";import"./BlockLabel-DWW9BWN3.js";import"./StreamingBar-lVbwTGD1.js";import"./AudioPlayer-Dn45keYP.js";import"./utils-BsGrhMNe.js";import"./Trim-CDsEvQ4G.js";import"./Play-BIkNyEKH.js";import"./Empty-Bzq0Ew6m.js";import"./hls-CnVhpNcu.js";import"./Video-DBVExGTx.js";function Gt(l){let e,t,i,o,s;return{c(){e=de("svg"),t=de("g"),i=de("g"),o=de("g"),s=de("path"),k(t,"id","SVGRepo_bgCarrier"),k(t,"stroke-width","0"),k(i,"id","SVGRepo_tracerCarrier"),k(i,"stroke-linecap","round"),k(i,"stroke-linejoin","round"),k(s,"d","M1752.768 221.109C1532.646.986 1174.283.986 954.161 221.109l-838.588 838.588c-154.052 154.165-154.052 404.894 0 558.946 149.534 149.421 409.976 149.308 559.059 0l758.738-758.626c87.982-88.094 87.982-231.417 0-319.51-88.32-88.208-231.642-87.982-319.51 0l-638.796 638.908 79.85 79.849 638.795-638.908c43.934-43.821 115.539-43.934 159.812 0 43.934 44.047 43.934 115.877 0 159.812l-758.739 758.625c-110.23 110.118-289.355 110.005-399.36 0-110.118-110.117-110.005-289.242 0-399.247l838.588-838.588c175.963-175.962 462.382-176.188 638.909 0 176.075 176.188 176.075 462.833 0 638.908l-798.607 798.72 79.849 79.85 798.607-798.72c220.01-220.123 220.01-578.485 0-798.607"),k(s,"fill-rule","evenodd"),k(o,"id","SVGRepo_iconCarrier"),k(e,"fill","currentColor"),k(e,"width","100%"),k(e,"height","100%"),k(e,"viewBox","0 0 1920 1920"),k(e,"xmlns","http://www.w3.org/2000/svg")},m(d,_){X(d,e,_),I(e,t),I(e,i),I(e,o),I(o,s)},p:Z,i:Z,o:Z,d(d){d&&Q(e)}}}class Lt extends Ae{constructor(e){super(),Ue(this,e,null,Gt,Pe,{})}}async function Ve(l,e,t){if(await Re(),e===t)return;const i=window.getComputedStyle(l),o=parseFloat(i.paddingTop),s=parseFloat(i.paddingBottom),d=parseFloat(i.lineHeight);let _=t===void 0?!1:o+s+d*t,a=o+s+e*d;l.style.height="1px";let p;_&&l.scrollHeight>_?p=_:l.scrollHeight<a?p=a:p=l.scrollHeight,l.style.height=`${p}px`}function Wt(l,e){if(e.lines===e.max_lines)return;l.style.overflowY="scroll";function t(i){Ve(i.target,e.lines,e.max_lines)}if(l.addEventListener("input",t),!!e.text.trim())return Ve(l,e.lines,e.max_lines),{destroy:()=>l.removeEventListener("input",t)}}function We(l,e,t){const i=l.slice();return i[71]=e[t],i[73]=t,i}function Kt(l){let e;return{c(){e=je(l[7])},m(t,i){X(t,e,i)},p(t,i){i[0]&128&&Ie(e,t[7])},d(t){t&&Q(e)}}}function Ke(l){let e,t,i,o=Ge(l[0].files),s=[];for(let a=0;a<o.length;a+=1)s[a]=Ye(We(l,o,a));const d=a=>C(s[a],1,1,()=>{s[a]=null});let _=l[28]&&Ne();return{c(){e=y("div");for(let a=0;a<s.length;a+=1)s[a].c();t=$(),_&&_.c(),k(e,"class","thumbnails scroll-hide svelte-d47mdf"),k(e,"aria-label","Uploaded files"),k(e,"data-testid","container_el"),Le(e,"display",l[0].files.length>0||l[28]?"flex":"none")},m(a,p){X(a,e,p);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(e,null);I(e,t),_&&_.m(e,null),i=!0},p(a,p){if(p[0]&65|p[1]&32){o=Ge(a[0].files);let f;for(f=0;f<o.length;f+=1){const S=We(a,o,f);s[f]?(s[f].p(S,p),w(s[f],1)):(s[f]=Ye(S),s[f].c(),w(s[f],1),s[f].m(e,t))}for(ee(),f=o.length;f<s.length;f+=1)d(f);te()}a[28]?_||(_=Ne(),_.c(),_.m(e,null)):_&&(_.d(1),_=null),(!i||p[0]&268435457)&&Le(e,"display",a[0].files.length>0||a[28]?"flex":"none")},i(a){if(!i){for(let p=0;p<o.length;p+=1)w(s[p]);i=!0}},o(a){s=s.filter(Boolean);for(let p=0;p<s.length;p+=1)C(s[p]);i=!1},d(a){a&&Q(e),Ct(s,a),_&&_.d()}}}function Yt(l){let e,t;return e=new Ft({}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p:Z,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Nt(l){let e,t;return e=new Pt({}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p:Z,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Jt(l){let e,t;return e=new Vt({}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p:Z,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Ot(l){let e,t;return e=new It({props:{src:l[71].url,title:null,alt:"",loading:"lazy",class:"thumbnail-image"}}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p(i,o){const s={};o[0]&1&&(s.src=i[71].url),e.$set(s)},i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Ye(l){let e,t,i,o,s,d,_,a,p,f,S,b,V;o=new St({});function c(...v){return l[51](l[73],...v)}const u=[Ot,Jt,Nt,Yt],m=[];function R(v,A){return A[0]&1&&(d=null),A[0]&1&&(_=null),A[0]&1&&(a=null),d==null&&(d=!!(v[71].mime_type&&v[71].mime_type.includes("image"))),d?0:(_==null&&(_=!!(v[71].mime_type&&v[71].mime_type.includes("audio"))),_?1:(a==null&&(a=!!(v[71].mime_type&&v[71].mime_type.includes("video"))),a?2:3))}return p=R(l,[-1,-1,-1]),f=m[p]=u[p](l),{c(){e=y("span"),t=y("button"),i=y("button"),K(o.$$.fragment),s=$(),f.c(),k(i,"class","delete-button svelte-d47mdf"),le(i,"disabled",l[6]),k(t,"class","thumbnail-item thumbnail-small svelte-d47mdf"),k(e,"role","listitem"),k(e,"aria-label","File thumbnail")},m(v,A){X(v,e,A),I(e,t),I(t,i),Y(o,i,null),I(t,s),m[p].m(t,null),S=!0,b||(V=W(i,"click",c),b=!0)},p(v,A){l=v,(!S||A[0]&64)&&le(i,"disabled",l[6]);let P=p;p=R(l,A),p===P?m[p].p(l,A):(ee(),C(m[P],1,1,()=>{m[P]=null}),te(),f=m[p],f?f.p(l,A):(f=m[p]=u[p](l),f.c()),w(f,1),f.m(t,null))},i(v){S||(w(o.$$.fragment,v),w(f),S=!0)},o(v){C(o.$$.fragment,v),C(f),S=!1},d(v){v&&Q(e),N(o),m[p].d(),b=!1,V()}}}function Ne(l){let e;return{c(){e=y("div"),k(e,"class","loader svelte-d47mdf"),k(e,"role","status"),k(e,"aria-label","Uploading")},m(t,i){X(t,e,i)},d(t){t&&Q(e)}}}function Je(l){let e,t;return e=new qt({props:{sources:["microphone"],class_name:"compact-audio",recording:$e,waveform_settings:l[22],waveform_options:l[23],i18n:l[4],active_source:l[2],upload:l[19],stream_handler:l[20],stream_every:1,editable:!0,label:l[7],root:l[16],loop:!1,show_label:!1,show_download_button:!1,dragging:!1}}),e.$on("change",l[52]),e.$on("clear",l[53]),e.$on("start_recording",l[54]),e.$on("pause_recording",l[55]),e.$on("stop_recording",l[56]),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p(i,o){const s={};o[0]&4194304&&(s.waveform_settings=i[22]),o[0]&8388608&&(s.waveform_options=i[23]),o[0]&16&&(s.i18n=i[4]),o[0]&4&&(s.active_source=i[2]),o[0]&524288&&(s.upload=i[19]),o[0]&1048576&&(s.stream_handler=i[20]),o[0]&128&&(s.label=i[7]),o[0]&65536&&(s.root=i[16]),e.$set(s)},i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Oe(l){let e,t,i,o,s,d,_,a,p,f;function S(u){l[58](u)}function b(u){l[59](u)}function V(u){l[60](u)}let c={file_count:l[21],filetype:l[17],root:l[16],max_file_size:l[18],show_progress:!1,disable_click:!0,hidden:!0,upload:l[19],stream_handler:l[20]};return l[1]!==void 0&&(c.dragging=l[1]),l[28]!==void 0&&(c.uploading=l[28]),l[27]!==void 0&&(c.hidden_upload=l[27]),e=new jt({props:c}),l[57](e),se.push(()=>ae(e,"dragging",S)),se.push(()=>ae(e,"uploading",b)),se.push(()=>ae(e,"hidden_upload",V)),e.$on("load",l[35]),e.$on("error",l[61]),_=new Lt({}),{c(){K(e.$$.fragment),s=$(),d=y("button"),K(_.$$.fragment),k(d,"data-testid","upload-button"),k(d,"class","upload-button svelte-d47mdf")},m(u,m){Y(e,u,m),X(u,s,m),X(u,d,m),Y(_,d,null),a=!0,p||(f=W(d,"click",l[37]),p=!0)},p(u,m){const R={};m[0]&2097152&&(R.file_count=u[21]),m[0]&131072&&(R.filetype=u[17]),m[0]&65536&&(R.root=u[16]),m[0]&262144&&(R.max_file_size=u[18]),m[0]&524288&&(R.upload=u[19]),m[0]&1048576&&(R.stream_handler=u[20]),!t&&m[0]&2&&(t=!0,R.dragging=u[1],ue(()=>t=!1)),!i&&m[0]&268435456&&(i=!0,R.uploading=u[28],ue(()=>i=!1)),!o&&m[0]&134217728&&(o=!0,R.hidden_upload=u[27],ue(()=>o=!1)),e.$set(R)},i(u){a||(w(e.$$.fragment,u),w(_.$$.fragment,u),a=!0)},o(u){C(e.$$.fragment,u),C(_.$$.fragment,u),a=!1},d(u){u&&(Q(s),Q(d)),l[57](null),N(e,u),N(_),p=!1,f()}}}function Xe(l){let e,t,i,o,s;return t=new Rt({}),{c(){e=y("button"),K(t.$$.fragment),k(e,"data-testid","microphone-button"),k(e,"class","microphone-button svelte-d47mdf"),le(e,"recording",$e)},m(d,_){X(d,e,_),Y(t,e,null),i=!0,o||(s=W(e,"click",l[62]),o=!0)},p:Z,i(d){i||(w(t.$$.fragment,d),i=!0)},o(d){C(t.$$.fragment,d),i=!1},d(d){d&&Q(e),N(t),o=!1,s()}}}function Qe(l){let e,t,i,o,s,d;const _=[Qt,Xt],a=[];function p(f,S){return f[11]===!0?0:1}return t=p(l),i=a[t]=_[t](l),{c(){e=y("button"),i.c(),k(e,"class","submit-button svelte-d47mdf"),le(e,"padded-button",l[11]!==!0)},m(f,S){X(f,e,S),a[t].m(e,null),o=!0,s||(d=W(e,"click",l[39]),s=!0)},p(f,S){let b=t;t=p(f),t===b?a[t].p(f,S):(ee(),C(a[b],1,1,()=>{a[b]=null}),te(),i=a[t],i?i.p(f,S):(i=a[t]=_[t](f),i.c()),w(i,1),i.m(e,null)),(!o||S[0]&2048)&&le(e,"padded-button",f[11]!==!0)},i(f){o||(w(i),o=!0)},o(f){C(i),o=!1},d(f){f&&Q(e),a[t].d(),s=!1,d()}}}function Xt(l){let e;return{c(){e=je(l[11])},m(t,i){X(t,e,i)},p(t,i){i[0]&2048&&Ie(e,t[11])},i:Z,o:Z,d(t){t&&Q(e)}}}function Qt(l){let e,t;return e=new At({}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p:Z,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function Ze(l){let e,t,i,o,s,d;const _=[yt,Zt],a=[];function p(f,S){return f[12]===!0?0:1}return t=p(l),i=a[t]=_[t](l),{c(){e=y("button"),i.c(),k(e,"class","stop-button svelte-d47mdf"),le(e,"padded-button",l[12]!==!0)},m(f,S){X(f,e,S),a[t].m(e,null),o=!0,s||(d=W(e,"click",l[38]),s=!0)},p(f,S){let b=t;t=p(f),t===b?a[t].p(f,S):(ee(),C(a[b],1,1,()=>{a[b]=null}),te(),i=a[t],i?i.p(f,S):(i=a[t]=_[t](f),i.c()),w(i,1),i.m(e,null)),(!o||S[0]&4096)&&le(e,"padded-button",f[12]!==!0)},i(f){o||(w(i),o=!0)},o(f){C(i),o=!1},d(f){f&&Q(e),a[t].d(),s=!1,d()}}}function Zt(l){let e;return{c(){e=je(l[12])},m(t,i){X(t,e,i)},p(t,i){i[0]&4096&&Ie(e,t[12])},i:Z,o:Z,d(t){t&&Q(e)}}}function yt(l){let e,t;return e=new Ut({props:{fill:"none",stroke_width:2.5}}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p:Z,i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function xt(l){let e,t,i,o,s=l[24]&&l[24].includes("microphone")&&l[2]==="microphone",d,_,a=l[24]&&l[24].includes("upload")&&!l[6]&&!(l[21]==="single"&&l[0].files.length>0),p,f=l[24]&&l[24].includes("microphone"),S,b,V,c,u,m,R,v,A,P;t=new Ht({props:{root:l[16],show_label:l[9],info:l[8],$$slots:{default:[Kt]},$$scope:{ctx:l}}});let M=(l[0].files.length>0||l[28])&&Ke(l),E=s&&Je(l),B=a&&Oe(l),H=f&&Xe(l),T=l[11]&&Qe(l),F=l[12]&&Ze(l);return{c(){e=y("div"),K(t.$$.fragment),i=$(),M&&M.c(),o=$(),E&&E.c(),d=$(),_=y("div"),B&&B.c(),p=$(),H&&H.c(),S=$(),b=y("textarea"),m=$(),T&&T.c(),R=$(),F&&F.c(),k(b,"data-testid","textbox"),k(b,"class","scroll-hide svelte-d47mdf"),k(b,"dir",V=l[13]?"rtl":"ltr"),k(b,"placeholder",l[5]),k(b,"rows",l[3]),b.disabled=l[6],b.autofocus=l[14],k(b,"style",c=l[15]?"text-align: "+l[15]:""),le(b,"no-label",!l[9]),k(_,"class","input-container svelte-d47mdf"),k(e,"class","full-container svelte-d47mdf"),k(e,"role","group"),k(e,"aria-label","Multimedia input field"),le(e,"dragging",l[1])},m(g,z){X(g,e,z),Y(t,e,null),I(e,i),M&&M.m(e,null),I(e,o),E&&E.m(e,null),I(e,d),I(e,_),B&&B.m(_,null),I(_,p),H&&H.m(_,null),I(_,S),I(_,b),qe(b,l[0].text),l[64](b),I(_,m),T&&T.m(_,null),I(_,R),F&&F.m(_,null),l[65](e),v=!0,l[14]&&b.focus(),A||(P=[ht(u=Wt.call(null,b,{text:l[0].text,lines:l[3],max_lines:l[10]})),W(b,"input",l[63]),W(b,"keypress",l[33]),W(b,"blur",l[49]),W(b,"select",l[32]),W(b,"focus",l[50]),W(b,"scroll",l[34]),W(b,"paste",l[40]),W(e,"dragenter",l[41]),W(e,"dragleave",l[42]),W(e,"dragover",bt(l[48])),W(e,"drop",l[43])],A=!0)},p(g,z){const q={};z[0]&65536&&(q.root=g[16]),z[0]&512&&(q.show_label=g[9]),z[0]&256&&(q.info=g[8]),z[0]&128|z[2]&4096&&(q.$$scope={dirty:z,ctx:g}),t.$set(q),g[0].files.length>0||g[28]?M?(M.p(g,z),z[0]&268435457&&w(M,1)):(M=Ke(g),M.c(),w(M,1),M.m(e,o)):M&&(ee(),C(M,1,1,()=>{M=null}),te()),z[0]&16777220&&(s=g[24]&&g[24].includes("microphone")&&g[2]==="microphone"),s?E?(E.p(g,z),z[0]&16777220&&w(E,1)):(E=Je(g),E.c(),w(E,1),E.m(e,d)):E&&(ee(),C(E,1,1,()=>{E=null}),te()),z[0]&18874433&&(a=g[24]&&g[24].includes("upload")&&!g[6]&&!(g[21]==="single"&&g[0].files.length>0)),a?B?(B.p(g,z),z[0]&18874433&&w(B,1)):(B=Oe(g),B.c(),w(B,1),B.m(_,p)):B&&(ee(),C(B,1,1,()=>{B=null}),te()),z[0]&16777216&&(f=g[24]&&g[24].includes("microphone")),f?H?(H.p(g,z),z[0]&16777216&&w(H,1)):(H=Xe(g),H.c(),w(H,1),H.m(_,S)):H&&(ee(),C(H,1,1,()=>{H=null}),te()),(!v||z[0]&8192&&V!==(V=g[13]?"rtl":"ltr"))&&k(b,"dir",V),(!v||z[0]&32)&&k(b,"placeholder",g[5]),(!v||z[0]&8)&&k(b,"rows",g[3]),(!v||z[0]&64)&&(b.disabled=g[6]),(!v||z[0]&16384)&&(b.autofocus=g[14]),(!v||z[0]&32768&&c!==(c=g[15]?"text-align: "+g[15]:""))&&k(b,"style",c),u&&pt(u.update)&&z[0]&1033&&u.update.call(null,{text:g[0].text,lines:g[3],max_lines:g[10]}),z[0]&1&&qe(b,g[0].text),(!v||z[0]&512)&&le(b,"no-label",!g[9]),g[11]?T?(T.p(g,z),z[0]&2048&&w(T,1)):(T=Qe(g),T.c(),w(T,1),T.m(_,R)):T&&(ee(),C(T,1,1,()=>{T=null}),te()),g[12]?F?(F.p(g,z),z[0]&4096&&w(F,1)):(F=Ze(g),F.c(),w(F,1),F.m(_,null)):F&&(ee(),C(F,1,1,()=>{F=null}),te()),(!v||z[0]&2)&&le(e,"dragging",g[1])},i(g){v||(w(t.$$.fragment,g),w(M),w(E),w(B),w(H),w(T),w(F),v=!0)},o(g){C(t.$$.fragment,g),C(M),C(E),C(B),C(H),C(T),C(F),v=!1},d(g){g&&Q(e),N(t),M&&M.d(),E&&E.d(),B&&B.d(),H&&H.d(),l[64](null),T&&T.d(),F&&F.d(),l[65](null),A=!1,wt(P)}}}let $e=!1;function $t(l,e,t){let{value:i={text:"",files:[]}}=e,{value_is_output:o=!1}=e,{lines:s=1}=e,{i18n:d}=e,{placeholder:_="Type here..."}=e,{disabled:a=!1}=e,{label:p}=e,{info:f=void 0}=e,{show_label:S=!0}=e,{max_lines:b}=e,{submit_btn:V=null}=e,{stop_btn:c=null}=e,{rtl:u=!1}=e,{autofocus:m=!1}=e,{text_align:R=void 0}=e,{autoscroll:v=!0}=e,{root:A}=e,{file_types:P=null}=e,{max_file_size:M=null}=e,{upload:E}=e,{stream_handler:B}=e,{file_count:H="multiple"}=e,{max_plain_text_length:T=1e3}=e,{waveform_settings:F}=e,{waveform_options:g={show_recording_waveform:!0}}=e,{sources:z=["upload"]}=e,{active_source:q=null}=e,G,x,j,J,re=0,ce=!1,{dragging:ie=!1}=e,me=!1,ge=i.text,ne=null,fe;const U=kt();vt(()=>{J=j&&j.offsetHeight+j.scrollTop>j.scrollHeight-100});const we=()=>{J&&v&&!ce&&j.scrollTo(0,j.scrollHeight)};async function he(){U("change",i),o||U("input")}xe(()=>{m&&j!==null&&j.focus()}),zt(()=>{J&&v&&we(),t(44,o=!1)});function ke(n){const D=n.target,L=D.value,O=[D.selectionStart,D.selectionEnd];U("select",{value:L.substring(...O),index:O})}async function ve(n){await Re(),n.key==="Enter"&&n.shiftKey&&s>1?(n.preventDefault(),U("submit")):n.key==="Enter"&&!n.shiftKey&&s===1&&b>=1&&(n.preventDefault(),U("submit"),t(2,q=null),ne&&(i.files.push(ne),t(0,i),t(29,ne=null)))}function ze(n){const D=n.target,L=D.scrollTop;L<re&&(ce=!0),re=L;const O=D.scrollHeight-D.clientHeight;L>=O&&(ce=!1)}async function Ce({detail:n}){if(he(),Array.isArray(n)){for(let D of n)i.files.push(D);t(0,i)}else i.files.push(n),t(0,i);await Re(),U("change",i),U("upload",n)}function be(n,D){he(),n.stopPropagation(),i.files.splice(D,1),t(0,i)}function Se(){x&&(t(27,x.value="",x),x.click())}function Te(){U("stop")}function De(){U("submit"),t(2,q=null),ne&&(i.files.push(ne),t(0,i),t(29,ne=null))}async function Me(n){if(!n.clipboardData)return;const D=n.clipboardData.items,L=n.clipboardData.getData("text");if(L&&L.length>T){n.preventDefault();const O=new window.File([L],"pasted_text.txt",{type:"text/plain",lastModified:Date.now()});G&&G.load_files([O]);return}for(let O in D){const oe=D[O];if(oe.kind==="file"&&oe.type.includes("image")){const _e=oe.getAsFile();_e&&G.load_files([_e])}}}function Ee(n){n.preventDefault(),t(1,ie=!0)}function Be(n){n.preventDefault();const D=fe.getBoundingClientRect(),{clientX:L,clientY:O}=n;(L<=D.left||L>=D.right||O<=D.top||O>=D.bottom)&&t(1,ie=!1)}function He(n){if(n.preventDefault(),t(1,ie=!1),n.dataTransfer&&n.dataTransfer.files){const D=Array.from(n.dataTransfer.files);if(P){const L=D.filter(oe=>P.some(_e=>_e.startsWith(".")?oe.name.toLowerCase().endsWith(_e.toLowerCase()):oe.type.match(new RegExp(_e.replace("*",".*"))))),O=D.length-L.length;O>0&&U("error",`${O} file(s) were rejected. Accepted formats: ${P.join(", ")}`),L.length>0&&G.load_files(L)}else G.load_files(D)}}function Fe(n){pe.call(this,l,n)}function r(n){pe.call(this,l,n)}function et(n){pe.call(this,l,n)}const tt=(n,D)=>be(D,n),lt=({detail:n})=>{n!==null&&t(29,ne=n)},it=()=>{t(2,q=null)},nt=()=>U("start_recording"),st=()=>U("pause_recording"),ot=()=>U("stop_recording");function at(n){se[n?"unshift":"push"](()=>{G=n,t(26,G)})}function ut(n){ie=n,t(1,ie)}function rt(n){me=n,t(28,me)}function ft(n){x=n,t(27,x)}function _t(n){pe.call(this,l,n)}const ct=()=>{t(2,q=q!=="microphone"?"microphone":null)};function mt(){i.text=this.value,t(0,i)}function gt(n){se[n?"unshift":"push"](()=>{j=n,t(25,j)})}function dt(n){se[n?"unshift":"push"](()=>{fe=n,t(30,fe)})}return l.$$set=n=>{"value"in n&&t(0,i=n.value),"value_is_output"in n&&t(44,o=n.value_is_output),"lines"in n&&t(3,s=n.lines),"i18n"in n&&t(4,d=n.i18n),"placeholder"in n&&t(5,_=n.placeholder),"disabled"in n&&t(6,a=n.disabled),"label"in n&&t(7,p=n.label),"info"in n&&t(8,f=n.info),"show_label"in n&&t(9,S=n.show_label),"max_lines"in n&&t(10,b=n.max_lines),"submit_btn"in n&&t(11,V=n.submit_btn),"stop_btn"in n&&t(12,c=n.stop_btn),"rtl"in n&&t(13,u=n.rtl),"autofocus"in n&&t(14,m=n.autofocus),"text_align"in n&&t(15,R=n.text_align),"autoscroll"in n&&t(45,v=n.autoscroll),"root"in n&&t(16,A=n.root),"file_types"in n&&t(17,P=n.file_types),"max_file_size"in n&&t(18,M=n.max_file_size),"upload"in n&&t(19,E=n.upload),"stream_handler"in n&&t(20,B=n.stream_handler),"file_count"in n&&t(21,H=n.file_count),"max_plain_text_length"in n&&t(46,T=n.max_plain_text_length),"waveform_settings"in n&&t(22,F=n.waveform_settings),"waveform_options"in n&&t(23,g=n.waveform_options),"sources"in n&&t(24,z=n.sources),"active_source"in n&&t(2,q=n.active_source),"dragging"in n&&t(1,ie=n.dragging)},l.$$.update=()=>{l.$$.dirty[0]&2&&U("drag",ie),l.$$.dirty[0]&1&&i===null&&t(0,i={text:"",files:[]}),l.$$.dirty[0]&1|l.$$.dirty[1]&65536&&ge!==i.text&&(U("change",i),t(47,ge=i.text)),l.$$.dirty[0]&33555465&&j&&s!==b&&Ve(j,s,b)},[i,ie,q,s,d,_,a,p,f,S,b,V,c,u,m,R,A,P,M,E,B,H,F,g,z,j,G,x,me,ne,fe,U,ke,ve,ze,Ce,be,Se,Te,De,Me,Ee,Be,He,o,v,T,ge,Fe,r,et,tt,lt,it,nt,st,ot,at,ut,rt,ft,_t,ct,mt,gt,dt]}class el extends Ae{constructor(e){super(),Ue(this,e,$t,xt,Pe,{value:0,value_is_output:44,lines:3,i18n:4,placeholder:5,disabled:6,label:7,info:8,show_label:9,max_lines:10,submit_btn:11,stop_btn:12,rtl:13,autofocus:14,text_align:15,autoscroll:45,root:16,file_types:17,max_file_size:18,upload:19,stream_handler:20,file_count:21,max_plain_text_length:46,waveform_settings:22,waveform_options:23,sources:24,active_source:2,dragging:1},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get value_is_output(){return this.$$.ctx[44]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get lines(){return this.$$.ctx[3]}set lines(e){this.$$set({lines:e}),h()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),h()}get placeholder(){return this.$$.ctx[5]}set placeholder(e){this.$$set({placeholder:e}),h()}get disabled(){return this.$$.ctx[6]}set disabled(e){this.$$set({disabled:e}),h()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[8]}set info(e){this.$$set({info:e}),h()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),h()}get max_lines(){return this.$$.ctx[10]}set max_lines(e){this.$$set({max_lines:e}),h()}get submit_btn(){return this.$$.ctx[11]}set submit_btn(e){this.$$set({submit_btn:e}),h()}get stop_btn(){return this.$$.ctx[12]}set stop_btn(e){this.$$set({stop_btn:e}),h()}get rtl(){return this.$$.ctx[13]}set rtl(e){this.$$set({rtl:e}),h()}get autofocus(){return this.$$.ctx[14]}set autofocus(e){this.$$set({autofocus:e}),h()}get text_align(){return this.$$.ctx[15]}set text_align(e){this.$$set({text_align:e}),h()}get autoscroll(){return this.$$.ctx[45]}set autoscroll(e){this.$$set({autoscroll:e}),h()}get root(){return this.$$.ctx[16]}set root(e){this.$$set({root:e}),h()}get file_types(){return this.$$.ctx[17]}set file_types(e){this.$$set({file_types:e}),h()}get max_file_size(){return this.$$.ctx[18]}set max_file_size(e){this.$$set({max_file_size:e}),h()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),h()}get stream_handler(){return this.$$.ctx[20]}set stream_handler(e){this.$$set({stream_handler:e}),h()}get file_count(){return this.$$.ctx[21]}set file_count(e){this.$$set({file_count:e}),h()}get max_plain_text_length(){return this.$$.ctx[46]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),h()}get waveform_settings(){return this.$$.ctx[22]}set waveform_settings(e){this.$$set({waveform_settings:e}),h()}get waveform_options(){return this.$$.ctx[23]}set waveform_options(e){this.$$set({waveform_options:e}),h()}get sources(){return this.$$.ctx[24]}set sources(e){this.$$set({sources:e}),h()}get active_source(){return this.$$.ctx[2]}set active_source(e){this.$$set({active_source:e}),h()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),h()}}const tl=el;function ye(l){let e,t;const i=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[17]];let o={};for(let s=0;s<i.length;s+=1)o=Dt(o,i[s]);return e=new Mt({props:o}),e.$on("clear_status",l[31]),{c(){K(e.$$.fragment)},m(s,d){Y(e,s,d),t=!0},p(s,d){const _=d[0]&131076?Et(i,[d[0]&4&&{autoscroll:s[2].autoscroll},d[0]&4&&{i18n:s[2].i18n},d[0]&131072&&Bt(s[17])]):{};e.$set(_)},i(s){t||(w(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){N(e,s)}}}function ll(l){let e,t,i,o,s,d,_,a=l[17]&&ye(l);function p(c){l[34](c)}function f(c){l[35](c)}function S(c){l[36](c)}function b(c){l[37](c)}let V={file_types:l[6],root:l[23],label:l[9],info:l[10],show_label:l[11],lines:l[7],rtl:l[18],text_align:l[19],waveform_settings:l[29],i18n:l[2].i18n,max_lines:l[12]?l[12]:l[7]+1,placeholder:l[8],submit_btn:l[15],stop_btn:l[16],autofocus:l[20],autoscroll:l[21],file_count:l[24],sources:l[26],max_file_size:l[2].max_file_size,disabled:!l[22],upload:l[32],stream_handler:l[33],max_plain_text_length:l[25]};return l[0]!==void 0&&(V.value=l[0]),l[1]!==void 0&&(V.value_is_output=l[1]),l[27]!==void 0&&(V.dragging=l[27]),l[28]!==void 0&&(V.active_source=l[28]),t=new tl({props:V}),se.push(()=>ae(t,"value",p)),se.push(()=>ae(t,"value_is_output",f)),se.push(()=>ae(t,"dragging",S)),se.push(()=>ae(t,"active_source",b)),t.$on("change",l[38]),t.$on("input",l[39]),t.$on("submit",l[40]),t.$on("stop",l[41]),t.$on("blur",l[42]),t.$on("select",l[43]),t.$on("focus",l[44]),t.$on("error",l[45]),t.$on("start_recording",l[46]),t.$on("pause_recording",l[47]),t.$on("stop_recording",l[48]),t.$on("upload",l[49]),t.$on("clear",l[50]),{c(){a&&a.c(),e=$(),K(t.$$.fragment)},m(c,u){a&&a.m(c,u),X(c,e,u),Y(t,c,u),_=!0},p(c,u){c[17]?a?(a.p(c,u),u[0]&131072&&w(a,1)):(a=ye(c),a.c(),w(a,1),a.m(e.parentNode,e)):a&&(ee(),C(a,1,1,()=>{a=null}),te());const m={};u[0]&64&&(m.file_types=c[6]),u[0]&8388608&&(m.root=c[23]),u[0]&512&&(m.label=c[9]),u[0]&1024&&(m.info=c[10]),u[0]&2048&&(m.show_label=c[11]),u[0]&128&&(m.lines=c[7]),u[0]&262144&&(m.rtl=c[18]),u[0]&524288&&(m.text_align=c[19]),u[0]&536870912&&(m.waveform_settings=c[29]),u[0]&4&&(m.i18n=c[2].i18n),u[0]&4224&&(m.max_lines=c[12]?c[12]:c[7]+1),u[0]&256&&(m.placeholder=c[8]),u[0]&32768&&(m.submit_btn=c[15]),u[0]&65536&&(m.stop_btn=c[16]),u[0]&1048576&&(m.autofocus=c[20]),u[0]&2097152&&(m.autoscroll=c[21]),u[0]&16777216&&(m.file_count=c[24]),u[0]&67108864&&(m.sources=c[26]),u[0]&4&&(m.max_file_size=c[2].max_file_size),u[0]&4194304&&(m.disabled=!c[22]),u[0]&4&&(m.upload=c[32]),u[0]&4&&(m.stream_handler=c[33]),u[0]&33554432&&(m.max_plain_text_length=c[25]),!i&&u[0]&1&&(i=!0,m.value=c[0],ue(()=>i=!1)),!o&&u[0]&2&&(o=!0,m.value_is_output=c[1],ue(()=>o=!1)),!s&&u[0]&134217728&&(s=!0,m.dragging=c[27],ue(()=>s=!1)),!d&&u[0]&268435456&&(d=!0,m.active_source=c[28],ue(()=>d=!1)),t.$set(m)},i(c){_||(w(a),w(t.$$.fragment,c),_=!0)},o(c){C(a),C(t.$$.fragment,c),_=!1},d(c){c&&Q(e),a&&a.d(c),N(t,c)}}}function il(l){let e,t;return e=new Tt({props:{visible:l[5],elem_id:l[3],elem_classes:[...l[4],"multimodal-textbox"],scale:l[13],min_width:l[14],allow_overflow:!1,padding:!1,border_mode:l[27]?"focus":"base",$$slots:{default:[ll]},$$scope:{ctx:l}}}),{c(){K(e.$$.fragment)},m(i,o){Y(e,i,o),t=!0},p(i,o){const s={};o[0]&32&&(s.visible=i[5]),o[0]&8&&(s.elem_id=i[3]),o[0]&16&&(s.elem_classes=[...i[4],"multimodal-textbox"]),o[0]&8192&&(s.scale=i[13]),o[0]&16384&&(s.min_width=i[14]),o[0]&134217728&&(s.border_mode=i[27]?"focus":"base"),o[0]&1073717191|o[1]&8388608&&(s.$$scope={dirty:o,ctx:i}),e.$set(s)},i(i){t||(w(e.$$.fragment,i),t=!0)},o(i){C(e.$$.fragment,i),t=!1},d(i){N(e,i)}}}function nl(l,e,t){let{gradio:i}=e,{elem_id:o=""}=e,{elem_classes:s=[]}=e,{visible:d=!0}=e,{value:_={text:"",files:[]}}=e,{file_types:a=null}=e,{lines:p}=e,{placeholder:f=""}=e,{label:S="MultimodalTextbox"}=e,{info:b=void 0}=e,{show_label:V}=e,{max_lines:c}=e,{scale:u=null}=e,{min_width:m=void 0}=e,{submit_btn:R=null}=e,{stop_btn:v=null}=e,{loading_status:A=void 0}=e,{value_is_output:P=!1}=e,{rtl:M=!1}=e,{text_align:E=void 0}=e,{autofocus:B=!1}=e,{autoscroll:H=!0}=e,{interactive:T}=e,{root:F}=e,{file_count:g}=e,{max_plain_text_length:z}=e,{sources:q=["upload"]}=e,{waveform_options:G={}}=e,x,j=null,J,re="darkorange";xe(()=>{re=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),ie(),t(29,J.waveColor=G.waveform_color||"#9ca3af",J),t(29,J.progressColor=G.waveform_progress_color||re,J),t(29,J.mediaControls=G.show_controls,J),t(29,J.sampleRate=G.sample_rate||44100,J)});const ce={color:G.trim_region_color,drag:!0,resize:!0};function ie(){document.documentElement.style.setProperty("--trim-region-color",ce.color||re)}const me=()=>i.dispatch("clear_status",A),ge=(...r)=>i.client.upload(...r),ne=(...r)=>i.client.stream(...r);function fe(r){_=r,t(0,_)}function U(r){P=r,t(1,P)}function we(r){x=r,t(27,x)}function he(r){j=r,t(28,j)}const ke=()=>i.dispatch("change",_),ve=()=>i.dispatch("input"),ze=()=>i.dispatch("submit"),Ce=()=>i.dispatch("stop"),be=()=>i.dispatch("blur"),Se=r=>i.dispatch("select",r.detail),Te=()=>i.dispatch("focus"),De=({detail:r})=>{i.dispatch("error",r)},Me=()=>i.dispatch("start_recording"),Ee=()=>i.dispatch("pause_recording"),Be=()=>i.dispatch("stop_recording"),He=r=>i.dispatch("upload",r.detail),Fe=()=>i.dispatch("clear");return l.$$set=r=>{"gradio"in r&&t(2,i=r.gradio),"elem_id"in r&&t(3,o=r.elem_id),"elem_classes"in r&&t(4,s=r.elem_classes),"visible"in r&&t(5,d=r.visible),"value"in r&&t(0,_=r.value),"file_types"in r&&t(6,a=r.file_types),"lines"in r&&t(7,p=r.lines),"placeholder"in r&&t(8,f=r.placeholder),"label"in r&&t(9,S=r.label),"info"in r&&t(10,b=r.info),"show_label"in r&&t(11,V=r.show_label),"max_lines"in r&&t(12,c=r.max_lines),"scale"in r&&t(13,u=r.scale),"min_width"in r&&t(14,m=r.min_width),"submit_btn"in r&&t(15,R=r.submit_btn),"stop_btn"in r&&t(16,v=r.stop_btn),"loading_status"in r&&t(17,A=r.loading_status),"value_is_output"in r&&t(1,P=r.value_is_output),"rtl"in r&&t(18,M=r.rtl),"text_align"in r&&t(19,E=r.text_align),"autofocus"in r&&t(20,B=r.autofocus),"autoscroll"in r&&t(21,H=r.autoscroll),"interactive"in r&&t(22,T=r.interactive),"root"in r&&t(23,F=r.root),"file_count"in r&&t(24,g=r.file_count),"max_plain_text_length"in r&&t(25,z=r.max_plain_text_length),"sources"in r&&t(26,q=r.sources),"waveform_options"in r&&t(30,G=r.waveform_options)},t(29,J={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:!1,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20}),[_,P,i,o,s,d,a,p,f,S,b,V,c,u,m,R,v,A,M,E,B,H,T,F,g,z,q,x,j,J,G,me,ge,ne,fe,U,we,he,ke,ve,ze,Ce,be,Se,Te,De,Me,Ee,Be,He,Fe]}class jl extends Ae{constructor(e){super(),Ue(this,e,nl,il,Pe,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,file_types:6,lines:7,placeholder:8,label:9,info:10,show_label:11,max_lines:12,scale:13,min_width:14,submit_btn:15,stop_btn:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21,interactive:22,root:23,file_count:24,max_plain_text_length:25,sources:26,waveform_options:30},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),h()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get file_types(){return this.$$.ctx[6]}set file_types(e){this.$$set({file_types:e}),h()}get lines(){return this.$$.ctx[7]}set lines(e){this.$$set({lines:e}),h()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),h()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),h()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),h()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),h()}get max_lines(){return this.$$.ctx[12]}set max_lines(e){this.$$set({max_lines:e}),h()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),h()}get submit_btn(){return this.$$.ctx[15]}set submit_btn(e){this.$$set({submit_btn:e}),h()}get stop_btn(){return this.$$.ctx[16]}set stop_btn(e){this.$$set({stop_btn:e}),h()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),h()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),h()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),h()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),h()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),h()}get interactive(){return this.$$.ctx[22]}set interactive(e){this.$$set({interactive:e}),h()}get root(){return this.$$.ctx[23]}set root(e){this.$$set({root:e}),h()}get file_count(){return this.$$.ctx[24]}set file_count(e){this.$$set({file_count:e}),h()}get max_plain_text_length(){return this.$$.ctx[25]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),h()}get sources(){return this.$$.ctx[26]}set sources(e){this.$$set({sources:e}),h()}get waveform_options(){return this.$$.ctx[30]}set waveform_options(e){this.$$set({waveform_options:e}),h()}}export{Gl as BaseExample,tl as BaseMultimodalTextbox,jl as default};
//# sourceMappingURL=Index-C_z0Oekd.js.map
