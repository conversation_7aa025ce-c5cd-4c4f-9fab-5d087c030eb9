{"version": 3, "file": "Index-9a2E18Dk.js", "sources": ["../../../code/shared/Copy.svelte", "../../../code/shared/Download.svelte", "../../../code/shared/Widgets.svelte", "../../../code/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onD<PERSON>roy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton Icon={copied ? Check : Copy} on:click={handle_copy} />\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Download, Check } from \"@gradio/icons\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\texport let value: string;\n\texport let language: string;\n\n\t$: ext = get_ext_for_type(language);\n\n\tfunction get_ext_for_type(type: string): string {\n\t\tconst exts: Record<string, string> = {\n\t\t\tpy: \"py\",\n\t\t\tpython: \"py\",\n\t\t\tmd: \"md\",\n\t\t\tmarkdown: \"md\",\n\t\t\tjson: \"json\",\n\t\t\thtml: \"html\",\n\t\t\tcss: \"css\",\n\t\t\tjs: \"js\",\n\t\t\tjavascript: \"js\",\n\t\t\tts: \"ts\",\n\t\t\ttypescript: \"ts\",\n\t\t\tyaml: \"yaml\",\n\t\t\tyml: \"yml\",\n\t\t\tdockerfile: \"dockerfile\",\n\t\t\tsh: \"sh\",\n\t\t\tshell: \"sh\",\n\t\t\tr: \"r\",\n\t\t\tc: \"c\",\n\t\t\tcpp: \"cpp\"\n\t\t};\n\n\t\treturn exts[type] || \"txt\";\n\t}\n\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\t$: download_value = URL.createObjectURL(new Blob([value]));\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<DownloadLink\n\tdownload=\"file.{ext}\"\n\thref={download_value}\n\ton:click={copy_feedback}\n>\n\t<IconButton Icon={copied ? Check : Download} />\n</DownloadLink>\n", "<script lang=\"ts\">\n\timport Copy from \"./Copy.svelte\";\n\timport Download from \"./Download.svelte\";\n\timport { IconButtonWrapper } from \"@gradio/atoms\";\n\n\texport let value: string;\n\texport let language: string;\n</script>\n\n<IconButtonWrapper>\n\t<Download {value} {language} />\n\t<Copy {value} />\n</IconButtonWrapper>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseCode } from \"./shared/Code.svelte\";\n\texport { default as BaseCopy } from \"./shared/Copy.svelte\";\n\texport { default as BaseDownload } from \"./shared/Download.svelte\";\n\texport { default as BaseWidget } from \"./shared/Widgets.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { afterUpdate } from \"svelte\";\n\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\timport Code from \"./shared/Code.svelte\";\n\timport Widget from \"./shared/Widgets.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { Block, BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { Code as CodeIcon } from \"@gradio/icons\";\n\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tinput: never;\n\t\tblur: never;\n\t\tfocus: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let value = \"\";\n\texport let value_is_output = false;\n\texport let language = \"\";\n\texport let lines = 5;\n\texport let max_lines: number | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let label = gradio.i18n(\"code.code\");\n\texport let show_label = true;\n\texport let loading_status: LoadingStatus;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let wrap_lines = false;\n\texport let show_line_numbers = true;\n\n\texport let interactive: boolean;\n\n\tlet dark_mode = gradio.theme === \"dark\";\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n</script>\n\n<Block\n\theight={max_lines && \"fit-content\"}\n\tvariant={\"solid\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t{#if show_label}\n\t\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={false} />\n\t{/if}\n\n\t{#if !value && !interactive}\n\t\t<Empty unpadded_box={true} size=\"large\">\n\t\t\t<CodeIcon />\n\t\t</Empty>\n\t{:else}\n\t\t<Widget {language} {value} />\n\n\t\t<Code\n\t\t\tbind:value\n\t\t\t{language}\n\t\t\t{lines}\n\t\t\t{max_lines}\n\t\t\t{dark_mode}\n\t\t\t{wrap_lines}\n\t\t\t{show_line_numbers}\n\t\t\treadonly={!interactive}\n\t\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["ctx", "Check", "Copy", "copied", "value", "$$props", "timer", "copy_feedback", "handle_copy", "onDestroy", "Download", "get_ext_for_type", "type", "language", "$$invalidate", "ext", "download_value", "CodeIcon", "create_if_block_1", "dirty", "block_changes", "gradio", "value_is_output", "lines", "max_lines", "elem_id", "elem_classes", "visible", "label", "show_label", "loading_status", "scale", "min_width", "wrap_lines", "show_line_numbers", "interactive", "dark_mode", "handle_change", "afterUpdate", "clear_status_handler"], "mappings": "gsBA6BkBA,EAAM,CAAA,EAAGC,EAAQC,CAAI,kBAAYF,EAAW,CAAA,CAAA,gFAA5CA,EAAM,CAAA,EAAGC,EAAQC,oHAxB9BC,EAAS,GACF,CAAA,MAAAC,CAAA,EAAAC,EACPC,EAEK,SAAAC,GAAA,KACRJ,EAAS,EAAA,EACLG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPH,EAAS,EAAA,GACP,KAGW,eAAAK,GAAA,CACV,cAAe,kBACZ,UAAU,UAAU,UAAUJ,CAAK,EACzCG,KAIF,OAAAE,EAAA,IAAA,CACKH,GAAO,aAAaA,CAAK,wPCmCZN,EAAM,CAAA,EAAGC,EAAQS,iFAAjBV,EAAM,CAAA,EAAGC,EAAQS,6JAJnBV,EAAG,CAAA,OACbA,EAAc,CAAA,0DACVA,EAAa,CAAA,CAAA,4FAFPA,EAAG,CAAA,gBACbA,EAAc,CAAA,yIA9CXW,GAAiBC,EAAA,CAuBlB,MAtBD,CACL,GAAI,KACJ,OAAQ,KACR,GAAI,KACJ,SAAU,KACV,KAAM,OACN,KAAM,OACN,IAAK,MACL,GAAI,KACJ,WAAY,KACZ,GAAI,KACJ,WAAY,KACZ,KAAM,OACN,IAAK,MACL,WAAY,aACZ,GAAI,KACJ,MAAO,KACP,EAAG,IACH,EAAG,IACH,IAAK,OAGMA,CAAI,GAAK,iCA5BX,CAAA,MAAAR,CAAA,EAAAC,EACA,CAAA,SAAAQ,CAAA,EAAAR,EA8BPF,EAAS,GACTG,EAEK,SAAAC,GAAA,KACRJ,EAAS,EAAA,EACLG,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPH,EAAS,EAAA,GACP,KAKJ,OAAAM,EAAA,IAAA,CACKH,GAAO,aAAaA,CAAK,+GA1C3BQ,EAAA,EAAAC,EAAMJ,GAAiBE,CAAQ,CAAA,iBAuClCC,EAAA,EAAGE,EAAiB,IAAI,gBAAoB,IAAA,KAAA,CAAMZ,CAAK,CAAA,CAAA,CAAA,+9BC3C5C,GAAA,CAAA,MAAAA,CAAA,EAAAC,EACA,CAAA,SAAAQ,CAAA,EAAAR,0XCuEQY,mCAAsC,maAkB5CjB,EAAW,EAAA,scAAXA,EAAW,EAAA,wPAdF,0cAXT,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,yGAIdA,EAAU,CAAA,GAAAkB,EAAAlB,CAAA,uCAIT,MAAA,CAAAA,OAAUA,EAAW,EAAA,EAAA,6LAVdmB,EAAA,GAAA,CAAA,WAAAnB,KAAO,UAAU,EACvBmB,EAAA,GAAA,CAAA,KAAAnB,KAAO,IAAI,aACbA,EAAc,EAAA,CAAA,iBAIdA,EAAU,CAAA,+aAhBP,OAAAA,MAAa,sBACZ,gBACA,sLAFDmB,EAAA,KAAAC,EAAA,OAAApB,MAAa,kSAxCV,GAAA,CAAA,OAAAqB,CAAA,EAAAhB,GAOA,MAAAD,EAAQ,EAAA,EAAAC,GACR,gBAAAiB,EAAkB,EAAA,EAAAjB,GAClB,SAAAQ,EAAW,EAAA,EAAAR,GACX,MAAAkB,EAAQ,CAAA,EAAAlB,GACR,UAAAmB,EAAgC,MAAA,EAAAnB,GAChC,QAAAoB,EAAU,EAAA,EAAApB,EACV,CAAA,aAAAqB,EAAA,EAAA,EAAArB,GACA,QAAAsB,EAAU,EAAA,EAAAtB,GACV,MAAAuB,EAAQP,EAAO,KAAK,WAAW,CAAA,EAAAhB,GAC/B,WAAAwB,EAAa,EAAA,EAAAxB,EACb,CAAA,eAAAyB,CAAA,EAAAzB,GACA,MAAA0B,EAAuB,IAAA,EAAA1B,GACvB,UAAA2B,EAAgC,MAAA,EAAA3B,GAChC,WAAA4B,EAAa,EAAA,EAAA5B,GACb,kBAAA6B,EAAoB,EAAA,EAAA7B,EAEpB,CAAA,YAAA8B,CAAA,EAAA9B,EAEP+B,EAAYf,EAAO,QAAU,OAExB,SAAAgB,GAAA,CACRhB,EAAO,SAAS,SAAUjB,CAAK,EAC1BkB,GACJD,EAAO,SAAS,OAAO,EAGzBiB,EAAA,IAAA,MACChB,EAAkB,EAAA,IAmBK,MAAAiB,EAAA,IAAAlB,EAAO,SAAS,eAAgBS,CAAc,uCAuBrDT,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO,oqBAzC/BgB,EAAA"}