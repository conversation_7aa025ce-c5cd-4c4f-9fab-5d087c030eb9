import{a as M,i as N,s as O,f as y,aq as q,y as w,w as S,b as W,z,P as F,A as p,d as b,C as v,a4 as G,x as V,k as d,h as j,j as A,t as h,l as k,as as H,a5 as J,e as K,O as L,D as g,ar as C,c as B,m as D,n as E}from"../lite.js";import{I as Q}from"./Image-BPQ6A_U-.js";/* empty css                                                   */import{V as R}from"./Video-DBVExGTx.js";import"./file-url-CoOyVRgq.js";import"./hls-CnVhpNcu.js";function I(i,e,s){const t=i.slice();return t[7]=e[s],t}function T(i){let e=i[7].orig_name+"",s;return{c(){s=S(e)},m(t,n){b(t,s,n)},p(t,n){n&1&&e!==(e=t[7].orig_name+"")&&V(s,e)},i:g,o:g,d(t){t&&k(s)}}}function U(i){let e,s;return{c(){e=w("audio"),C(e.src,s=i[7].url)||z(e,"src",s),e.controls=!0},m(t,n){b(t,e,n)},p(t,n){n&1&&!C(e.src,s=t[7].url)&&z(e,"src",s)},i:g,o:g,d(t){t&&k(e)}}}function X(i){let e,s;return e=new R({props:{src:i[7].url,alt:"",loop:!0,is_stream:!1}}),{c(){B(e.$$.fragment)},m(t,n){D(e,t,n),s=!0},p(t,n){const u={};n&1&&(u.src=t[7].url),e.$set(u)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){h(e.$$.fragment,t),s=!1},d(t){E(e,t)}}}function Y(i){let e,s;return e=new Q({props:{src:i[7].url,alt:""}}),{c(){B(e.$$.fragment)},m(t,n){D(e,t,n),s=!0},p(t,n){const u={};n&1&&(u.src=t[7].url),e.$set(u)},i(t){s||(d(e.$$.fragment,t),s=!0)},o(t){h(e.$$.fragment,t),s=!1},d(t){E(e,t)}}}function P(i){let e,s,t,n,u,_,c;const m=[Y,X,U,T],r=[];function f(l,a){return a&1&&(e=null),a&1&&(s=null),a&1&&(t=null),e==null&&(e=!!(l[7].mime_type&&l[7].mime_type.includes("image"))),e?0:(s==null&&(s=!!(l[7].mime_type&&l[7].mime_type.includes("video"))),s?1:(t==null&&(t=!!(l[7].mime_type&&l[7].mime_type.includes("audio"))),t?2:3))}return n=f(i,-1),u=r[n]=m[n](i),{c(){u.c(),_=K()},m(l,a){r[n].m(l,a),b(l,_,a),c=!0},p(l,a){let o=n;n=f(l,a),n===o?r[n].p(l,a):(j(),h(r[o],1,1,()=>{r[o]=null}),A(),u=r[n],u?u.p(l,a):(u=r[n]=m[n](l),u.c()),d(u,1),u.m(_.parentNode,_))},i(l){c||(d(u),c=!0)},o(l){h(u),c=!1},d(l){l&&k(_),r[n].d(l)}}}function Z(i){let e,s,t=(i[0].text?i[0].text:"")+"",n,u,_,c,m=q(i[0].files),r=[];for(let l=0;l<m.length;l+=1)r[l]=P(I(i,m,l));const f=l=>h(r[l],1,1,()=>{r[l]=null});return{c(){e=w("div"),s=w("p"),n=S(t),u=W();for(let l=0;l<r.length;l+=1)r[l].c();z(e,"class","container svelte-1cl8bqt"),F(()=>i[5].call(e)),p(e,"table",i[1]==="table"),p(e,"gallery",i[1]==="gallery"),p(e,"selected",i[2]),p(e,"border",i[0])},m(l,a){b(l,e,a),v(e,s),v(s,n),v(e,u);for(let o=0;o<r.length;o+=1)r[o]&&r[o].m(e,null);_=G(e,i[5].bind(e)),i[6](e),c=!0},p(l,[a]){if((!c||a&1)&&t!==(t=(l[0].text?l[0].text:"")+"")&&V(n,t),a&1){m=q(l[0].files);let o;for(o=0;o<m.length;o+=1){const $=I(l,m,o);r[o]?(r[o].p($,a),d(r[o],1)):(r[o]=P($),r[o].c(),d(r[o],1),r[o].m(e,null))}for(j(),o=m.length;o<r.length;o+=1)f(o);A()}(!c||a&2)&&p(e,"table",l[1]==="table"),(!c||a&2)&&p(e,"gallery",l[1]==="gallery"),(!c||a&4)&&p(e,"selected",l[2]),(!c||a&1)&&p(e,"border",l[0])},i(l){if(!c){for(let a=0;a<m.length;a+=1)d(r[a]);c=!0}},o(l){r=r.filter(Boolean);for(let a=0;a<r.length;a+=1)h(r[a]);c=!1},d(l){l&&k(e),H(r,l),_(),i[6](null)}}}function x(i,e){i.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),i.style.whiteSpace="unset"}function ee(i,e,s){let{value:t={text:"",files:[]}}=e,{type:n}=e,{selected:u=!1}=e,_,c;J(()=>{x(c,_)});function m(){_=this.clientWidth,s(3,_)}function r(f){L[f?"unshift":"push"](()=>{c=f,s(4,c)})}return i.$$set=f=>{"value"in f&&s(0,t=f.value),"type"in f&&s(1,n=f.type),"selected"in f&&s(2,u=f.selected)},[t,n,u,_,c,m,r]}class ae extends M{constructor(e){super(),N(this,e,ee,Z,O,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),y()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),y()}}export{ae as default};
//# sourceMappingURL=Example-CYkgQj2b.js.map
