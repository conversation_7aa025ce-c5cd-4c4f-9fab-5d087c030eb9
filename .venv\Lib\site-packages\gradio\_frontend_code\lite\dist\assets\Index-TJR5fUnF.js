import{a as p,i as $,s as tt,f as _,B as et,c as v,m as k,k as b,t as m,n as B,O as G,a7 as H,b as st,d as it,h as lt,j as nt,a8 as J,l as at,Y as ut,S as ot,a0 as _t,a6 as ft}from"../lite.js";import{T as ht}from"./Textbox-GwvoYeHL.js";import{default as Nt}from"./Example-z38AELH2.js";import"./BlockTitle-DvFB_De3.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";import"./Check-DbzZ-PD_.js";import"./Copy-DcTA0nce.js";import"./Send-DPp49sBe.js";import"./Square-CkbFMpLj.js";import"./index-B9I6rkKj.js";/* empty css                                              *//* empty css                                              */function K(i){let t,s;const n=[{autoscroll:i[2].autoscroll},{i18n:i[2].i18n},i[19]];let h={};for(let a=0;a<n.length;a+=1)h=ut(h,n[a]);return t=new ot({props:h}),t.$on("clear_status",i[27]),{c(){v(t.$$.fragment)},m(a,o){k(t,a,o),s=!0},p(a,o){const r=o[0]&524292?_t(n,[o[0]&4&&{autoscroll:a[2].autoscroll},o[0]&4&&{i18n:a[2].i18n},o[0]&524288&&ft(a[19])]):{};t.$set(r)},i(a){s||(b(t.$$.fragment,a),s=!0)},o(a){m(t.$$.fragment,a),s=!1},d(a){B(t,a)}}}function ct(i){let t,s,n,h,a,o=i[19]&&K(i);function r(l){i[28](l)}function g(l){i[29](l)}let c={label:i[3],info:i[4],root:i[25],show_label:i[10],lines:i[8],type:i[12],rtl:i[20],text_align:i[21],max_lines:i[11]?i[11]:i[8]+1,placeholder:i[9],submit_btn:i[16],stop_btn:i[17],show_copy_button:i[18],autofocus:i[22],container:i[13],autoscroll:i[23],max_length:i[26],disabled:!i[24]};return i[0]!==void 0&&(c.value=i[0]),i[1]!==void 0&&(c.value_is_output=i[1]),s=new ht({props:c}),G.push(()=>H(s,"value",r)),G.push(()=>H(s,"value_is_output",g)),s.$on("change",i[30]),s.$on("input",i[31]),s.$on("submit",i[32]),s.$on("blur",i[33]),s.$on("select",i[34]),s.$on("focus",i[35]),s.$on("stop",i[36]),s.$on("copy",i[37]),{c(){o&&o.c(),t=st(),v(s.$$.fragment)},m(l,u){o&&o.m(l,u),it(l,t,u),k(s,l,u),a=!0},p(l,u){l[19]?o?(o.p(l,u),u[0]&524288&&b(o,1)):(o=K(l),o.c(),b(o,1),o.m(t.parentNode,t)):o&&(lt(),m(o,1,1,()=>{o=null}),nt());const f={};u[0]&8&&(f.label=l[3]),u[0]&16&&(f.info=l[4]),u[0]&33554432&&(f.root=l[25]),u[0]&1024&&(f.show_label=l[10]),u[0]&256&&(f.lines=l[8]),u[0]&4096&&(f.type=l[12]),u[0]&1048576&&(f.rtl=l[20]),u[0]&2097152&&(f.text_align=l[21]),u[0]&2304&&(f.max_lines=l[11]?l[11]:l[8]+1),u[0]&512&&(f.placeholder=l[9]),u[0]&65536&&(f.submit_btn=l[16]),u[0]&131072&&(f.stop_btn=l[17]),u[0]&262144&&(f.show_copy_button=l[18]),u[0]&4194304&&(f.autofocus=l[22]),u[0]&8192&&(f.container=l[13]),u[0]&8388608&&(f.autoscroll=l[23]),u[0]&67108864&&(f.max_length=l[26]),u[0]&16777216&&(f.disabled=!l[24]),!n&&u[0]&1&&(n=!0,f.value=l[0],J(()=>n=!1)),!h&&u[0]&2&&(h=!0,f.value_is_output=l[1],J(()=>h=!1)),s.$set(f)},i(l){a||(b(o),b(s.$$.fragment,l),a=!0)},o(l){m(o),m(s.$$.fragment,l),a=!1},d(l){l&&at(t),o&&o.d(l),B(s,l)}}}function rt(i){let t,s;return t=new et({props:{visible:i[7],elem_id:i[5],elem_classes:i[6],scale:i[14],min_width:i[15],allow_overflow:!1,padding:i[13],$$slots:{default:[ct]},$$scope:{ctx:i}}}),{c(){v(t.$$.fragment)},m(n,h){k(t,n,h),s=!0},p(n,h){const a={};h[0]&128&&(a.visible=n[7]),h[0]&32&&(a.elem_id=n[5]),h[0]&64&&(a.elem_classes=n[6]),h[0]&16384&&(a.scale=n[14]),h[0]&32768&&(a.min_width=n[15]),h[0]&8192&&(a.padding=n[13]),h[0]&134168351|h[1]&128&&(a.$$scope={dirty:h,ctx:n}),t.$set(a)},i(n){s||(b(t.$$.fragment,n),s=!0)},o(n){m(t.$$.fragment,n),s=!1},d(n){B(t,n)}}}function bt(i,t,s){let{gradio:n}=t,{label:h="Textbox"}=t,{info:a=void 0}=t,{elem_id:o=""}=t,{elem_classes:r=[]}=t,{visible:g=!0}=t,{value:c=""}=t,{lines:l}=t,{placeholder:u=""}=t,{show_label:f}=t,{max_lines:T}=t,{type:S="text"}=t,{container:j=!0}=t,{scale:q=null}=t,{min_width:x=void 0}=t,{submit_btn:C=null}=t,{stop_btn:E=null}=t,{show_copy_button:I=!1}=t,{loading_status:w=void 0}=t,{value_is_output:d=!1}=t,{rtl:N=!1}=t,{text_align:O=void 0}=t,{autofocus:Y=!1}=t,{autoscroll:z=!0}=t,{interactive:A}=t,{root:D}=t,{max_length:F=void 0}=t;const L=()=>n.dispatch("clear_status",w);function M(e){c=e,s(0,c)}function P(e){d=e,s(1,d)}const Q=()=>n.dispatch("change",c),R=()=>n.dispatch("input"),U=()=>n.dispatch("submit"),V=()=>n.dispatch("blur"),W=e=>n.dispatch("select",e.detail),X=()=>n.dispatch("focus"),Z=()=>n.dispatch("stop"),y=e=>n.dispatch("copy",e.detail);return i.$$set=e=>{"gradio"in e&&s(2,n=e.gradio),"label"in e&&s(3,h=e.label),"info"in e&&s(4,a=e.info),"elem_id"in e&&s(5,o=e.elem_id),"elem_classes"in e&&s(6,r=e.elem_classes),"visible"in e&&s(7,g=e.visible),"value"in e&&s(0,c=e.value),"lines"in e&&s(8,l=e.lines),"placeholder"in e&&s(9,u=e.placeholder),"show_label"in e&&s(10,f=e.show_label),"max_lines"in e&&s(11,T=e.max_lines),"type"in e&&s(12,S=e.type),"container"in e&&s(13,j=e.container),"scale"in e&&s(14,q=e.scale),"min_width"in e&&s(15,x=e.min_width),"submit_btn"in e&&s(16,C=e.submit_btn),"stop_btn"in e&&s(17,E=e.stop_btn),"show_copy_button"in e&&s(18,I=e.show_copy_button),"loading_status"in e&&s(19,w=e.loading_status),"value_is_output"in e&&s(1,d=e.value_is_output),"rtl"in e&&s(20,N=e.rtl),"text_align"in e&&s(21,O=e.text_align),"autofocus"in e&&s(22,Y=e.autofocus),"autoscroll"in e&&s(23,z=e.autoscroll),"interactive"in e&&s(24,A=e.interactive),"root"in e&&s(25,D=e.root),"max_length"in e&&s(26,F=e.max_length)},[c,d,n,h,a,o,r,g,l,u,f,T,S,j,q,x,C,E,I,w,N,O,Y,z,A,D,F,L,M,P,Q,R,U,V,W,X,Z,y]}class Ct extends p{constructor(t){super(),$(this,t,bt,rt,tt,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,submit_btn:16,stop_btn:17,show_copy_button:18,loading_status:19,value_is_output:1,rtl:20,text_align:21,autofocus:22,autoscroll:23,interactive:24,root:25,max_length:26},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(t){this.$$set({gradio:t}),_()}get label(){return this.$$.ctx[3]}set label(t){this.$$set({label:t}),_()}get info(){return this.$$.ctx[4]}set info(t){this.$$set({info:t}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(t){this.$$set({elem_id:t}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(t){this.$$set({elem_classes:t}),_()}get visible(){return this.$$.ctx[7]}set visible(t){this.$$set({visible:t}),_()}get value(){return this.$$.ctx[0]}set value(t){this.$$set({value:t}),_()}get lines(){return this.$$.ctx[8]}set lines(t){this.$$set({lines:t}),_()}get placeholder(){return this.$$.ctx[9]}set placeholder(t){this.$$set({placeholder:t}),_()}get show_label(){return this.$$.ctx[10]}set show_label(t){this.$$set({show_label:t}),_()}get max_lines(){return this.$$.ctx[11]}set max_lines(t){this.$$set({max_lines:t}),_()}get type(){return this.$$.ctx[12]}set type(t){this.$$set({type:t}),_()}get container(){return this.$$.ctx[13]}set container(t){this.$$set({container:t}),_()}get scale(){return this.$$.ctx[14]}set scale(t){this.$$set({scale:t}),_()}get min_width(){return this.$$.ctx[15]}set min_width(t){this.$$set({min_width:t}),_()}get submit_btn(){return this.$$.ctx[16]}set submit_btn(t){this.$$set({submit_btn:t}),_()}get stop_btn(){return this.$$.ctx[17]}set stop_btn(t){this.$$set({stop_btn:t}),_()}get show_copy_button(){return this.$$.ctx[18]}set show_copy_button(t){this.$$set({show_copy_button:t}),_()}get loading_status(){return this.$$.ctx[19]}set loading_status(t){this.$$set({loading_status:t}),_()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(t){this.$$set({value_is_output:t}),_()}get rtl(){return this.$$.ctx[20]}set rtl(t){this.$$set({rtl:t}),_()}get text_align(){return this.$$.ctx[21]}set text_align(t){this.$$set({text_align:t}),_()}get autofocus(){return this.$$.ctx[22]}set autofocus(t){this.$$set({autofocus:t}),_()}get autoscroll(){return this.$$.ctx[23]}set autoscroll(t){this.$$set({autoscroll:t}),_()}get interactive(){return this.$$.ctx[24]}set interactive(t){this.$$set({interactive:t}),_()}get root(){return this.$$.ctx[25]}set root(t){this.$$set({root:t}),_()}get max_length(){return this.$$.ctx[26]}set max_length(t){this.$$set({max_length:t}),_()}}export{Nt as BaseExample,ht as BaseTextbox,Ct as default};
//# sourceMappingURL=Index-TJR5fUnF.js.map
