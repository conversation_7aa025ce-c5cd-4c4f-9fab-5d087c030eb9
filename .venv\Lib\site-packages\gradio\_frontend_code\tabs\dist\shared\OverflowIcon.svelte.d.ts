/** @typedef {typeof __propDef.props}  OverflowIconProps */
/** @typedef {typeof __propDef.events}  OverflowIconEvents */
/** @typedef {typeof __propDef.slots}  OverflowIconSlots */
export default class OverflowIcon extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type OverflowIconProps = typeof __propDef.props;
export type OverflowIconEvents = typeof __propDef.events;
export type OverflowIconSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
