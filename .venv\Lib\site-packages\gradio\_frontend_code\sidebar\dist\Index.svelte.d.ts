import { SvelteComponent } from "svelte";
import type { LoadingStatus } from "@gradio/statustracker";
import type { Gradio } from "@gradio/utils";
declare const __propDef: {
    props: {
        open?: boolean | undefined;
        position?: ("left" | "right") | undefined;
        loading_status: LoadingStatus;
        gradio: Gradio<{
            expand: never;
            collapse: never;
        }>;
        width: number | string;
        visible?: boolean | undefined;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {
        default: {};
    };
};
export type IndexProps = typeof __propDef.props;
export type IndexEvents = typeof __propDef.events;
export type IndexSlots = typeof __propDef.slots;
export default class Index extends SvelteComponent<IndexProps, IndexEvents, IndexSlots> {
}
export {};
