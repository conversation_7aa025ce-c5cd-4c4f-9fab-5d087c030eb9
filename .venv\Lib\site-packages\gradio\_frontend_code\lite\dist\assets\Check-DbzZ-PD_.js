import{a as l,i as p,s as c,E as r,z as t,d,C as u,D as o,l as h}from"../lite.js";function g(a){let e,s;return{c(){e=r("svg"),s=r("polyline"),t(s,"points","20 6 9 17 4 12"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","2 0 20 20"),t(e,"width","100%"),t(e,"height","100%"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"aria-hidden","true"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round")},m(n,i){d(n,e,i),u(e,s)},p:o,i:o,o,d(n){n&&h(e)}}}class w extends l{constructor(e){super(),p(this,e,null,g,c,{})}}export{w as C};
//# sourceMappingURL=Check-DbzZ-PD_.js.map
