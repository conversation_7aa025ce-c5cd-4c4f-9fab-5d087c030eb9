import{a as z,i as D,s as E,f as b,q as M,y as $,w as G,b as I,z as B,$ as k,A as j,d as w,C as v,M as H,x as J,u as O,r as Y,v as F,k as p,t as h,l as C,o as K,B as L,c as S,m as q,n as A,Y as N,S as P,O as Q,a7 as R,a0 as T,a6 as U,a8 as V}from"../lite.js";import W from"./Index-BHNCUXBa.js";function X(n){let e,l,t,s,a,c,_,m,r,o;const u=n[4].default,d=M(u,n,n[3],null);return{c(){e=$("button"),l=$("span"),t=G(n[1]),s=I(),a=$("span"),a.textContent="▼",c=I(),_=$("div"),d&&d.c(),B(l,"class","svelte-1w6vloh"),B(a,"class","icon svelte-1w6vloh"),k(a,"transform",n[0]?"rotate(0)":"rotate(90deg)"),B(e,"class","label-wrap svelte-1w6vloh"),j(e,"open",n[0]),k(_,"display",n[0]?"block":"none")},m(i,g){w(i,e,g),v(e,l),v(l,t),v(e,s),v(e,a),w(i,c,g),w(i,_,g),d&&d.m(_,null),m=!0,r||(o=H(e,"click",n[5]),r=!0)},p(i,[g]){(!m||g&2)&&J(t,i[1]),g&1&&k(a,"transform",i[0]?"rotate(0)":"rotate(90deg)"),(!m||g&1)&&j(e,"open",i[0]),d&&d.p&&(!m||g&8)&&O(d,u,i,i[3],m?F(u,i[3],g,null):Y(i[3]),null),g&1&&k(_,"display",i[0]?"block":"none")},i(i){m||(p(d,i),m=!0)},o(i){h(d,i),m=!1},d(i){i&&(C(e),C(c),C(_)),d&&d.d(i),r=!1,o()}}}function Z(n,e,l){let{$$slots:t={},$$scope:s}=e;const a=K();let{open:c=!0}=e,{label:_=""}=e;const m=()=>{l(0,c=!c),a(c?"expand":"collapse")};return n.$$set=r=>{"open"in r&&l(0,c=r.open),"label"in r&&l(1,_=r.label),"$$scope"in r&&l(3,s=r.$$scope)},[c,_,a,s,t,m]}class y extends z{constructor(e){super(),D(this,e,Z,X,E,{open:0,label:1})}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),b()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),b()}}function x(n){let e;const l=n[7].default,t=M(l,n,n[11],null);return{c(){t&&t.c()},m(s,a){t&&t.m(s,a),e=!0},p(s,a){t&&t.p&&(!e||a&2048)&&O(t,l,s,s[11],e?F(l,s[11],a,null):Y(s[11]),null)},i(s){e||(p(t,s),e=!0)},o(s){h(t,s),e=!1},d(s){t&&t.d(s)}}}function ee(n){let e,l;return e=new W({props:{$$slots:{default:[x]},$$scope:{ctx:n}}}),{c(){S(e.$$.fragment)},m(t,s){q(e,t,s),l=!0},p(t,s){const a={};s&2048&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){h(e.$$.fragment,t),l=!1},d(t){A(e,t)}}}function te(n){let e,l,t,s,a;const c=[{autoscroll:n[6].autoscroll},{i18n:n[6].i18n},n[5]];let _={};for(let o=0;o<c.length;o+=1)_=N(_,c[o]);e=new P({props:_});function m(o){n[8](o)}let r={label:n[1],$$slots:{default:[ee]},$$scope:{ctx:n}};return n[0]!==void 0&&(r.open=n[0]),t=new y({props:r}),Q.push(()=>R(t,"open",m)),t.$on("expand",n[9]),t.$on("collapse",n[10]),{c(){S(e.$$.fragment),l=I(),S(t.$$.fragment)},m(o,u){q(e,o,u),w(o,l,u),q(t,o,u),a=!0},p(o,u){const d=u&96?T(c,[u&64&&{autoscroll:o[6].autoscroll},u&64&&{i18n:o[6].i18n},u&32&&U(o[5])]):{};e.$set(d);const i={};u&2&&(i.label=o[1]),u&2048&&(i.$$scope={dirty:u,ctx:o}),!s&&u&1&&(s=!0,i.open=o[0],V(()=>s=!1)),t.$set(i)},i(o){a||(p(e.$$.fragment,o),p(t.$$.fragment,o),a=!0)},o(o){h(e.$$.fragment,o),h(t.$$.fragment,o),a=!1},d(o){o&&C(l),A(e,o),A(t,o)}}}function se(n){let e,l;return e=new L({props:{elem_id:n[2],elem_classes:n[3],visible:n[4],$$slots:{default:[te]},$$scope:{ctx:n}}}),{c(){S(e.$$.fragment)},m(t,s){q(e,t,s),l=!0},p(t,[s]){const a={};s&4&&(a.elem_id=t[2]),s&8&&(a.elem_classes=t[3]),s&16&&(a.visible=t[4]),s&2147&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){l||(p(e.$$.fragment,t),l=!0)},o(t){h(e.$$.fragment,t),l=!1},d(t){A(e,t)}}}function le(n,e,l){let{$$slots:t={},$$scope:s}=e,{label:a}=e,{elem_id:c}=e,{elem_classes:_}=e,{visible:m=!0}=e,{open:r=!0}=e,{loading_status:o}=e,{gradio:u}=e;function d(f){r=f,l(0,r)}const i=()=>u.dispatch("expand"),g=()=>u.dispatch("collapse");return n.$$set=f=>{"label"in f&&l(1,a=f.label),"elem_id"in f&&l(2,c=f.elem_id),"elem_classes"in f&&l(3,_=f.elem_classes),"visible"in f&&l(4,m=f.visible),"open"in f&&l(0,r=f.open),"loading_status"in f&&l(5,o=f.loading_status),"gradio"in f&&l(6,u=f.gradio),"$$scope"in f&&l(11,s=f.$$scope)},[r,a,c,_,m,o,u,t,d,i,g,s]}class oe extends z{constructor(e){super(),D(this,e,le,se,E,{label:1,elem_id:2,elem_classes:3,visible:4,open:0,loading_status:5,gradio:6})}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),b()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),b()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),b()}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),b()}get loading_status(){return this.$$.ctx[5]}set loading_status(e){this.$$set({loading_status:e}),b()}get gradio(){return this.$$.ctx[6]}set gradio(e){this.$$set({gradio:e}),b()}}export{oe as default};
//# sourceMappingURL=Index-B8ScEi4t.js.map
