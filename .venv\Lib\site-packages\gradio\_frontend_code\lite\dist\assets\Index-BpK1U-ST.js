import{a as Fe,i as Je,s as We,f,e as ee,d as Y,h as te,t as b,j as se,k as w,l as A,K as qe,O as N,B as ie,c as v,m as I,n as B,Y as ne,S as ae,a7 as E,b as le,a0 as re,a6 as oe,a8 as O}from"../lite.js";import Ce from"./ImagePreview-DGP9AANb.js";import{I as Ke}from"./ImageUploader-BK9kfkZd.js";import{W as Nt}from"./ImageUploader-BK9kfkZd.js";import{E as Te}from"./Empty-Bzq0Ew6m.js";import{I as Ye}from"./FullscreenButton-DsVuMC2h.js";import{U as _e}from"./UploadText-Chjc4Zy7.js";import{I as Et}from"./Image-BPQ6A_U-.js";import{default as Pt}from"./Example-_q5ymqpZ.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-DWW9BWN3.js";import"./ShareButton-Be-vgu5O.js";import"./Community-BFnPJcwx.js";import"./Download-RUpc9r8A.js";import"./IconButtonWrapper-BqpIgNIH.js";import"./utils-Gtzs_Zla.js";import"./DownloadLink-dHe4pFcz.js";import"./file-url-CoOyVRgq.js";/* empty css                                                   */import"./Minimize-DOBO88I3.js";import"./SelectSource-kJI_8u2f.js";import"./Upload-CYshamIj.js";import"./DropdownArrow-DIboSv6l.js";import"./Square-CkbFMpLj.js";import"./index-B9I6rkKj.js";import"./StreamingBar-lVbwTGD1.js";import"./Upload-Do_omv-N.js";/* empty css                                             *//* empty css                                              */function Ae(s){let e,i;return e=new ie({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[29]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10]||void 0,width:s[11],allow_overflow:!1,container:s[14],scale:s[15],min_width:s[16],$$slots:{default:[Ve]},$$scope:{ctx:s}}}),e.$on("dragenter",s[32]),e.$on("dragleave",s[32]),e.$on("dragover",s[32]),e.$on("drop",s[33]),{c(){v(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&32&&(o.visible=t[5]),a[0]&1&&(o.variant=t[0]===null?"dashed":"solid"),a[0]&536870912&&(o.border_mode=t[29]?"focus":"base"),a[0]&8&&(o.elem_id=t[3]),a[0]&16&&(o.elem_classes=t[4]),a[0]&1024&&(o.height=t[10]||void 0),a[0]&2048&&(o.width=t[11]),a[0]&16384&&(o.container=t[14]),a[0]&32768&&(o.scale=t[15]),a[0]&65536&&(o.min_width=t[16]),a[0]&2129932999|a[1]&1073741825&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){B(e,t)}}}function Ge(s){let e,i;return e=new ie({props:{visible:s[5],variant:"solid",border_mode:s[29]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10]||void 0,width:s[11],allow_overflow:!1,container:s[14],scale:s[15],min_width:s[16],$$slots:{default:[Xe]},$$scope:{ctx:s}}}),{c(){v(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&32&&(o.visible=t[5]),a[0]&536870912&&(o.border_mode=t[29]?"focus":"base"),a[0]&8&&(o.elem_id=t[3]),a[0]&16&&(o.elem_classes=t[4]),a[0]&1024&&(o.height=t[10]||void 0),a[0]&2048&&(o.width=t[11]),a[0]&16384&&(o.container=t[14]),a[0]&32768&&(o.scale=t[15]),a[0]&65536&&(o.min_width=t[16]),a[0]&84025797|a[1]&1073741824&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){B(e,t)}}}function He(s){let e,i;return e=new Te({props:{unpadded_box:!0,size:"large",$$slots:{default:[Qe]},$$scope:{ctx:s}}}),{c(){v(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[1]&1073741824&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){B(e,t)}}}function Le(s){let e,i;return e=new _e({props:{i18n:s[26].i18n,type:"clipboard",mode:"short"}}),{c(){v(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&67108864&&(o.i18n=t[26].i18n),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){B(e,t)}}}function Me(s){let e,i;return e=new _e({props:{i18n:s[26].i18n,type:"image",placeholder:s[23]}}),{c(){v(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},p(t,a){const o={};a[0]&67108864&&(o.i18n=t[26].i18n),a[0]&8388608&&(o.placeholder=t[23]),e.$set(o)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){B(e,t)}}}function Qe(s){let e,i;return e=new Ye({}),{c(){v(e.$$.fragment)},m(t,a){I(e,t,a),i=!0},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){B(e,t)}}}function Re(s){let e,i,t,a;const o=[Me,Le,He],m=[];function _(l,g){return l[30]==="upload"||!l[30]?0:l[30]==="clipboard"?1:2}return e=_(s),i=m[e]=o[e](s),{c(){i.c(),t=ee()},m(l,g){m[e].m(l,g),Y(l,t,g),a=!0},p(l,g){let c=e;e=_(l),e===c?m[e].p(l,g):(te(),b(m[c],1,1,()=>{m[c]=null}),se(),i=m[e],i?i.p(l,g):(i=m[e]=o[e](l),i.c()),w(i,1),i.m(t.parentNode,t))},i(l){a||(w(i),a=!0)},o(l){b(i),a=!1},d(l){l&&A(t),m[e].d(l)}}}function Ve(s){let e,i,t,a,o,m,_,l,g,c;const D=[{autoscroll:s[26].autoscroll},{i18n:s[26].i18n},s[2]];let p={};for(let r=0;r<D.length;r+=1)p=ne(p,D[r]);e=new ae({props:p}),e.$on("clear_status",s[42]);function P(r){s[45](r)}function F(r){s[46](r)}function J(r){s[47](r)}function W(r){s[48](r)}function q(r){s[49](r)}function C(r){s[50](r)}let k={selectable:s[13],root:s[9],sources:s[18],label:s[6],show_label:s[7],pending:s[21],streaming:s[20],mirror_webcam:s[22],stream_every:s[12],webcam_constraints:s[25],max_file_size:s[26].max_file_size,i18n:s[26].i18n,upload:s[43],stream_handler:s[26].client?.stream,$$slots:{default:[Re]},$$scope:{ctx:s}};return s[27]!==void 0&&(k.uploading=s[27]),s[30]!==void 0&&(k.active_source=s[30]),s[0]!==void 0&&(k.value=s[0]),s[29]!==void 0&&(k.dragging=s[29]),s[28]!==void 0&&(k.modify_stream=s[28]),s[1]!==void 0&&(k.set_time_limit=s[1]),t=new Ke({props:k}),s[44](t),N.push(()=>E(t,"uploading",P)),N.push(()=>E(t,"active_source",F)),N.push(()=>E(t,"value",J)),N.push(()=>E(t,"dragging",W)),N.push(()=>E(t,"modify_stream",q)),N.push(()=>E(t,"set_time_limit",C)),t.$on("edit",s[51]),t.$on("clear",s[52]),t.$on("stream",s[53]),t.$on("drag",s[54]),t.$on("upload",s[55]),t.$on("select",s[56]),t.$on("share",s[57]),t.$on("error",s[58]),t.$on("close_stream",s[59]),{c(){v(e.$$.fragment),i=le(),v(t.$$.fragment)},m(r,u){I(e,r,u),Y(r,i,u),I(t,r,u),c=!0},p(r,u){const K=u[0]&67108868?re(D,[u[0]&67108864&&{autoscroll:r[26].autoscroll},u[0]&67108864&&{i18n:r[26].i18n},u[0]&4&&oe(r[2])]):{};e.$set(K);const h={};u[0]&8192&&(h.selectable=r[13]),u[0]&512&&(h.root=r[9]),u[0]&262144&&(h.sources=r[18]),u[0]&64&&(h.label=r[6]),u[0]&128&&(h.show_label=r[7]),u[0]&2097152&&(h.pending=r[21]),u[0]&1048576&&(h.streaming=r[20]),u[0]&4194304&&(h.mirror_webcam=r[22]),u[0]&4096&&(h.stream_every=r[12]),u[0]&33554432&&(h.webcam_constraints=r[25]),u[0]&67108864&&(h.max_file_size=r[26].max_file_size),u[0]&67108864&&(h.i18n=r[26].i18n),u[0]&67108864&&(h.upload=r[43]),u[0]&67108864&&(h.stream_handler=r[26].client?.stream),u[0]&1149239296|u[1]&1073741824&&(h.$$scope={dirty:u,ctx:r}),!a&&u[0]&134217728&&(a=!0,h.uploading=r[27],O(()=>a=!1)),!o&&u[0]&1073741824&&(o=!0,h.active_source=r[30],O(()=>o=!1)),!m&&u[0]&1&&(m=!0,h.value=r[0],O(()=>m=!1)),!_&&u[0]&536870912&&(_=!0,h.dragging=r[29],O(()=>_=!1)),!l&&u[0]&268435456&&(l=!0,h.modify_stream=r[28],O(()=>l=!1)),!g&&u[0]&2&&(g=!0,h.set_time_limit=r[1],O(()=>g=!1)),t.$set(h)},i(r){c||(w(e.$$.fragment,r),w(t.$$.fragment,r),c=!0)},o(r){b(e.$$.fragment,r),b(t.$$.fragment,r),c=!1},d(r){r&&A(i),B(e,r),s[44](null),B(t,r)}}}function Xe(s){let e,i,t,a;const o=[{autoscroll:s[26].autoscroll},{i18n:s[26].i18n},s[2]];let m={};for(let _=0;_<o.length;_+=1)m=ne(m,o[_]);return e=new ae({props:m}),t=new Ce({props:{value:s[0],label:s[6],show_label:s[7],show_download_button:s[8],selectable:s[13],show_share_button:s[17],i18n:s[26].i18n,show_fullscreen_button:s[24]}}),t.$on("select",s[39]),t.$on("share",s[40]),t.$on("error",s[41]),{c(){v(e.$$.fragment),i=le(),v(t.$$.fragment)},m(_,l){I(e,_,l),Y(_,i,l),I(t,_,l),a=!0},p(_,l){const g=l[0]&67108868?re(o,[l[0]&67108864&&{autoscroll:_[26].autoscroll},l[0]&67108864&&{i18n:_[26].i18n},l[0]&4&&oe(_[2])]):{};e.$set(g);const c={};l[0]&1&&(c.value=_[0]),l[0]&64&&(c.label=_[6]),l[0]&128&&(c.show_label=_[7]),l[0]&256&&(c.show_download_button=_[8]),l[0]&8192&&(c.selectable=_[13]),l[0]&131072&&(c.show_share_button=_[17]),l[0]&67108864&&(c.i18n=_[26].i18n),l[0]&16777216&&(c.show_fullscreen_button=_[24]),t.$set(c)},i(_){a||(w(e.$$.fragment,_),w(t.$$.fragment,_),a=!0)},o(_){b(e.$$.fragment,_),b(t.$$.fragment,_),a=!1},d(_){_&&A(i),B(e,_),B(t,_)}}}function Ze(s){let e,i,t,a;const o=[Ge,Ae],m=[];function _(l,g){return l[19]?1:0}return e=_(s),i=m[e]=o[e](s),{c(){i.c(),t=ee()},m(l,g){m[e].m(l,g),Y(l,t,g),a=!0},p(l,g){let c=e;e=_(l),e===c?m[e].p(l,g):(te(),b(m[c],1,1,()=>{m[c]=null}),se(),i=m[e],i?i.p(l,g):(i=m[e]=o[e](l),i.c()),w(i,1),i.m(t.parentNode,t))},i(l){a||(w(i),a=!0)},o(l){b(i),a=!1},d(l){l&&A(t),m[e].d(l)}}}function ye(s,e,i){let t="closed",a=()=>{};function o(n){t=n,a(n)}const m=()=>t;let{set_time_limit:_}=e,{value_is_output:l=!1}=e,{elem_id:g=""}=e,{elem_classes:c=[]}=e,{visible:D=!0}=e,{value:p=null}=e,P=null,{label:F}=e,{show_label:J}=e,{show_download_button:W}=e,{root:q}=e,{height:C}=e,{width:k}=e,{stream_every:r}=e,{_selectable:u=!1}=e,{container:K=!0}=e,{scale:h=null}=e,{min_width:M=void 0}=e,{loading_status:z}=e,{show_share_button:Q=!1}=e,{sources:R=["upload","clipboard","webcam"]}=e,{interactive:G}=e,{streaming:V}=e,{pending:X}=e,{mirror_webcam:Z}=e,{placeholder:y=void 0}=e,{show_fullscreen_button:$}=e,{input_ready:H}=e,{webcam_constraints:x=void 0}=e,T=!1,{gradio:d}=e;qe(()=>{i(34,l=!1)});let U,L=null,j;const ue=n=>{const S=n;S.preventDefault(),S.stopPropagation(),S.type==="dragenter"||S.type==="dragover"?i(29,U=!0):S.type==="dragleave"&&i(29,U=!1)},fe=n=>{if(G){const S=n;S.preventDefault(),S.stopPropagation(),i(29,U=!1),j&&j.loadFilesFromDrop(S)}},me=({detail:n})=>d.dispatch("select",n),ce=({detail:n})=>d.dispatch("share",n),he=({detail:n})=>d.dispatch("error",n),ge=()=>d.dispatch("clear_status",z),de=(...n)=>d.client.upload(...n);function be(n){N[n?"unshift":"push"](()=>{j=n,i(31,j)})}function we(n){T=n,i(27,T)}function pe(n){L=n,i(30,L)}function ke(n){p=n,i(0,p)}function ve(n){U=n,i(29,U)}function Ie(n){a=n,i(28,a)}function Be(n){_=n,i(1,_)}const Se=()=>d.dispatch("edit"),ze=()=>{d.dispatch("clear")},Ue=({detail:n})=>d.dispatch("stream",n),Ne=({detail:n})=>i(29,U=n),De=()=>d.dispatch("upload"),Ee=({detail:n})=>d.dispatch("select",n),Oe=({detail:n})=>d.dispatch("share",n),Pe=({detail:n})=>{i(2,z=z||{}),i(2,z.status="error",z),d.dispatch("error",n)},je=()=>{d.dispatch("close_stream","stream")};return s.$$set=n=>{"set_time_limit"in n&&i(1,_=n.set_time_limit),"value_is_output"in n&&i(34,l=n.value_is_output),"elem_id"in n&&i(3,g=n.elem_id),"elem_classes"in n&&i(4,c=n.elem_classes),"visible"in n&&i(5,D=n.visible),"value"in n&&i(0,p=n.value),"label"in n&&i(6,F=n.label),"show_label"in n&&i(7,J=n.show_label),"show_download_button"in n&&i(8,W=n.show_download_button),"root"in n&&i(9,q=n.root),"height"in n&&i(10,C=n.height),"width"in n&&i(11,k=n.width),"stream_every"in n&&i(12,r=n.stream_every),"_selectable"in n&&i(13,u=n._selectable),"container"in n&&i(14,K=n.container),"scale"in n&&i(15,h=n.scale),"min_width"in n&&i(16,M=n.min_width),"loading_status"in n&&i(2,z=n.loading_status),"show_share_button"in n&&i(17,Q=n.show_share_button),"sources"in n&&i(18,R=n.sources),"interactive"in n&&i(19,G=n.interactive),"streaming"in n&&i(20,V=n.streaming),"pending"in n&&i(21,X=n.pending),"mirror_webcam"in n&&i(22,Z=n.mirror_webcam),"placeholder"in n&&i(23,y=n.placeholder),"show_fullscreen_button"in n&&i(24,$=n.show_fullscreen_button),"input_ready"in n&&i(35,H=n.input_ready),"webcam_constraints"in n&&i(25,x=n.webcam_constraints),"gradio"in n&&i(26,d=n.gradio)},s.$$.update=()=>{s.$$.dirty[0]&134217728&&i(35,H=!T),s.$$.dirty[0]&67108865|s.$$.dirty[1]&136&&JSON.stringify(p)!==JSON.stringify(P)&&(i(38,P=p),d.dispatch("change"),l||d.dispatch("input"))},[p,_,z,g,c,D,F,J,W,q,C,k,r,u,K,h,M,Q,R,G,V,X,Z,y,$,x,d,T,a,U,L,j,ue,fe,l,H,o,m,P,me,ce,he,ge,de,be,we,pe,ke,ve,Ie,Be,Se,ze,Ue,Ne,De,Ee,Oe,Pe,je]}class St extends Fe{constructor(e){super(),Je(this,e,ye,Ze,We,{modify_stream_state:36,get_stream_state:37,set_time_limit:1,value_is_output:34,elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,height:10,width:11,stream_every:12,_selectable:13,container:14,scale:15,min_width:16,loading_status:2,show_share_button:17,sources:18,interactive:19,streaming:20,pending:21,mirror_webcam:22,placeholder:23,show_fullscreen_button:24,input_ready:35,webcam_constraints:25,gradio:26},null,[-1,-1])}get modify_stream_state(){return this.$$.ctx[36]}get get_stream_state(){return this.$$.ctx[37]}get set_time_limit(){return this.$$.ctx[1]}set set_time_limit(e){this.$$set({set_time_limit:e}),f()}get value_is_output(){return this.$$.ctx[34]}set value_is_output(e){this.$$set({value_is_output:e}),f()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),f()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),f()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),f()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),f()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),f()}get stream_every(){return this.$$.ctx[12]}set stream_every(e){this.$$set({stream_every:e}),f()}get _selectable(){return this.$$.ctx[13]}set _selectable(e){this.$$set({_selectable:e}),f()}get container(){return this.$$.ctx[14]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[15]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[16]}set min_width(e){this.$$set({min_width:e}),f()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get sources(){return this.$$.ctx[18]}set sources(e){this.$$set({sources:e}),f()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),f()}get streaming(){return this.$$.ctx[20]}set streaming(e){this.$$set({streaming:e}),f()}get pending(){return this.$$.ctx[21]}set pending(e){this.$$set({pending:e}),f()}get mirror_webcam(){return this.$$.ctx[22]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),f()}get placeholder(){return this.$$.ctx[23]}set placeholder(e){this.$$set({placeholder:e}),f()}get show_fullscreen_button(){return this.$$.ctx[24]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}get input_ready(){return this.$$.ctx[35]}set input_ready(e){this.$$set({input_ready:e}),f()}get webcam_constraints(){return this.$$.ctx[25]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),f()}get gradio(){return this.$$.ctx[26]}set gradio(e){this.$$set({gradio:e}),f()}}export{Pt as BaseExample,Et as BaseImage,Ke as BaseImageUploader,Ce as BaseStaticImage,Nt as Webcam,St as default};
//# sourceMappingURL=Index-BpK1U-ST.js.map
