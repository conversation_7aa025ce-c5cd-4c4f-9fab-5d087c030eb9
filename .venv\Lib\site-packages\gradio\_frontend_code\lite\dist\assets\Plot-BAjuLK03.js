const __vite__fileDeps=["./PlotlyPlot-CQtop1ne.js","../lite.js","../lite.css","./BokehPlot-O00Avp7W.js","./BokehPlot-Cd-I2ErV.css","./AltairPlot-BCUkbvgH.js","./color-DS9FAt0c.js","./vega-tooltip.module-vN725zP3.js","./dsv-DB8NKgIY.js","./AltairPlot-CSe9xcFj.css","./MatplotlibPlot-Dy2n_BcS.js","./MatplotlibPlot-AF_QcUtc.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{a as Z,i as x,s as L,E as d,z as r,d as C,C as g,D as y,l as O,f as h,e as M,h as A,t as m,j as R,k as b,o as W,az as k,p as S,c as v,m as P,n as E,au as H,O as q,a7 as B,a8 as X}from"../lite.js";import{E as $}from"./Empty-Bzq0Ew6m.js";function ee(u){let e,l,o,n,c,i,f;return{c(){e=d("svg"),l=d("circle"),o=d("circle"),n=d("circle"),c=d("circle"),i=d("circle"),f=d("path"),r(l,"cx","20"),r(l,"cy","4"),r(l,"r","2"),r(l,"fill","currentColor"),r(o,"cx","8"),r(o,"cy","16"),r(o,"r","2"),r(o,"fill","currentColor"),r(n,"cx","28"),r(n,"cy","12"),r(n,"r","2"),r(n,"fill","currentColor"),r(c,"cx","11"),r(c,"cy","7"),r(c,"r","2"),r(c,"fill","currentColor"),r(i,"cx","16"),r(i,"cy","24"),r(i,"r","2"),r(i,"fill","currentColor"),r(f,"fill","currentColor"),r(f,"d","M30 3.413L28.586 2L4 26.585V2H2v26a2 2 0 0 0 2 2h26v-2H5.413Z"),r(e,"xmlns","http://www.w3.org/2000/svg"),r(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),r(e,"aria-hidden","true"),r(e,"role","img"),r(e,"class","iconify iconify--carbon"),r(e,"width","100%"),r(e,"height","100%"),r(e,"preserveAspectRatio","xMidYMid meet"),r(e,"viewBox","0 0 32 32")},m(t,_){C(t,e,_),g(e,l),g(e,o),g(e,n),g(e,c),g(e,i),g(e,f)},p:y,i:y,o:y,d(t){t&&O(e)}}}let te=class extends Z{constructor(e){super(),x(this,e,null,ee,L,{})}};function le(u){let e,l;return e=new $({props:{unpadded_box:!0,size:"large",$$slots:{default:[ne]},$$scope:{ctx:u}}}),{c(){v(e.$$.fragment)},m(o,n){P(e,o,n),l=!0},p(o,n){const c={};n&4194304&&(c.$$scope={dirty:n,ctx:o}),e.$set(c)},i(o){l||(b(e.$$.fragment,o),l=!0)},o(o){m(e.$$.fragment,o),l=!1},d(o){E(e,o)}}}function oe(u){let e=u[12],l,o,n=Y(u);return{c(){n.c(),l=M()},m(c,i){n.m(c,i),C(c,l,i),o=!0},p(c,i){i&4096&&L(e,e=c[12])?(A(),m(n,1,1,y),R(),n=Y(c),n.c(),b(n,1),n.m(l.parentNode,l)):n.p(c,i)},i(c){o||(b(n),o=!0)},o(c){m(n),o=!1},d(c){c&&O(l),n.d(c)}}}function ne(u){let e,l;return e=new te({}),{c(){v(e.$$.fragment)},m(o,n){P(e,o,n),l=!0},i(o){l||(b(e.$$.fragment,o),l=!0)},o(o){m(e.$$.fragment,o),l=!1},d(o){E(e,o)}}}function Y(u){let e,l,o,n;function c(t){u[16](t)}var i=u[10];function f(t,_){let a={value:t[0],colors:t[1],theme_mode:t[3],show_label:t[2],caption:t[4],bokeh_version:t[5],show_actions_button:t[6],gradio:t[7],_selectable:t[9],x_lim:t[8]};return t[11]!==void 0&&(a.loaded_plotly_css=t[11]),{props:a}}return i&&(e=H(i,f(u)),q.push(()=>B(e,"loaded_plotly_css",c)),e.$on("load",u[17]),e.$on("select",u[18])),{c(){e&&v(e.$$.fragment),o=M()},m(t,_){e&&P(e,t,_),C(t,o,_),n=!0},p(t,_){if(_&1024&&i!==(i=t[10])){if(e){A();const a=e;m(a.$$.fragment,1,0,()=>{E(a,1)}),R()}i?(e=H(i,f(t)),q.push(()=>B(e,"loaded_plotly_css",c)),e.$on("load",t[17]),e.$on("select",t[18]),v(e.$$.fragment),b(e.$$.fragment,1),P(e,o.parentNode,o)):e=null}else if(i){const a={};_&1&&(a.value=t[0]),_&2&&(a.colors=t[1]),_&8&&(a.theme_mode=t[3]),_&4&&(a.show_label=t[2]),_&16&&(a.caption=t[4]),_&32&&(a.bokeh_version=t[5]),_&64&&(a.show_actions_button=t[6]),_&128&&(a.gradio=t[7]),_&512&&(a._selectable=t[9]),_&256&&(a.x_lim=t[8]),!l&&_&2048&&(l=!0,a.loaded_plotly_css=t[11],X(()=>l=!1)),e.$set(a)}},i(t){n||(e&&b(e.$$.fragment,t),n=!0)},o(t){e&&m(e.$$.fragment,t),n=!1},d(t){t&&O(o),e&&E(e,t)}}}function se(u){let e,l,o,n;const c=[oe,le],i=[];function f(t,_){return t[0]&&t[10]?0:1}return e=f(u),l=i[e]=c[e](u),{c(){l.c(),o=M()},m(t,_){i[e].m(t,_),C(t,o,_),n=!0},p(t,[_]){let a=e;e=f(t),e===a?i[e].p(t,_):(A(),m(i[a],1,1,()=>{i[a]=null}),R(),l=i[e],l?l.p(t,_):(l=i[e]=c[e](t),l.c()),b(l,1),l.m(o.parentNode,o))},i(t){n||(b(l),n=!0)},o(t){m(l),n=!1},d(t){t&&O(o),i[e].d(t)}}}function ie(u,e,l){let{value:o}=e,n,{colors:c=[]}=e,{show_label:i}=e,{theme_mode:f}=e,{caption:t}=e,{bokeh_version:_}=e,{show_actions_button:a}=e,{gradio:V}=e,{x_lim:z=null}=e,{_selectable:I}=e,p=null,T=o?.type,D=!1;const F=W(),j={plotly:()=>k(()=>import("./PlotlyPlot-CQtop1ne.js"),__vite__mapDeps([0,1,2]),import.meta.url),bokeh:()=>k(()=>import("./BokehPlot-O00Avp7W.js"),__vite__mapDeps([3,1,2,4]),import.meta.url),altair:()=>k(()=>import("./AltairPlot-BCUkbvgH.js"),__vite__mapDeps([5,1,2,6,7,8,9]),import.meta.url),matplotlib:()=>k(()=>import("./MatplotlibPlot-Dy2n_BcS.js"),__vite__mapDeps([10,1,2,11]),import.meta.url)};let w={};const G=typeof window<"u";let N=0;function J(s){D=s,l(11,D)}function K(s){S.call(this,u,s)}function Q(s){S.call(this,u,s)}return u.$$set=s=>{"value"in s&&l(0,o=s.value),"colors"in s&&l(1,c=s.colors),"show_label"in s&&l(2,i=s.show_label),"theme_mode"in s&&l(3,f=s.theme_mode),"caption"in s&&l(4,t=s.caption),"bokeh_version"in s&&l(5,_=s.bokeh_version),"show_actions_button"in s&&l(6,a=s.show_actions_button),"gradio"in s&&l(7,V=s.gradio),"x_lim"in s&&l(8,z=s.x_lim),"_selectable"in s&&l(9,I=s._selectable)},u.$$.update=()=>{if(u.$$.dirty&62465&&o!==n){l(12,N+=1);let s=o?.type;s!==T&&l(10,p=null),s&&s in j&&G&&(w[s]?l(10,p=w[s]):j[s]().then(U=>{l(10,p=U.default),l(15,w[s]=p,w)})),l(13,n=o),l(14,T=s),F("change")}},[o,c,i,f,t,_,a,V,z,I,p,D,N,n,T,w,J,K,Q]}class re extends Z{constructor(e){super(),x(this,e,ie,se,L,{value:0,colors:1,show_label:2,theme_mode:3,caption:4,bokeh_version:5,show_actions_button:6,gradio:7,x_lim:8,_selectable:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get colors(){return this.$$.ctx[1]}set colors(e){this.$$set({colors:e}),h()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),h()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),h()}get caption(){return this.$$.ctx[4]}set caption(e){this.$$set({caption:e}),h()}get bokeh_version(){return this.$$.ctx[5]}set bokeh_version(e){this.$$set({bokeh_version:e}),h()}get show_actions_button(){return this.$$.ctx[6]}set show_actions_button(e){this.$$set({show_actions_button:e}),h()}get gradio(){return this.$$.ctx[7]}set gradio(e){this.$$set({gradio:e}),h()}get x_lim(){return this.$$.ctx[8]}set x_lim(e){this.$$set({x_lim:e}),h()}get _selectable(){return this.$$.ctx[9]}set _selectable(e){this.$$set({_selectable:e}),h()}}const ue=Object.freeze(Object.defineProperty({__proto__:null,default:re},Symbol.toStringTag,{value:"Module"}));export{te as P,re as a,ue as b};
//# sourceMappingURL=Plot-BAjuLK03.js.map
