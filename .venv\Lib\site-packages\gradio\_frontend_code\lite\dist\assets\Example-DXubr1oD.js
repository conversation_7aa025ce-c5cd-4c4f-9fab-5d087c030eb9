import{a as g,i as y,s as h,f,y as r,w as v,z as m,A as u,d as o,C as c,x as _,D as d,l as b}from"../lite.js";function p(s){let e,a,i=JSON.stringify(s[0],null,2)+"",n;return{c(){e=r("div"),a=r("pre"),n=v(i),m(e,"class","svelte-1ayixqk"),u(e,"table",s[1]==="table"),u(e,"gallery",s[1]==="gallery"),u(e,"selected",s[2])},m(l,t){o(l,e,t),c(e,a),c(a,n)},p(l,[t]){t&1&&i!==(i=JSON.stringify(l[0],null,2)+"")&&_(n,i),t&2&&u(e,"table",l[1]==="table"),t&2&&u(e,"gallery",l[1]==="gallery"),t&4&&u(e,"selected",l[2])},i:d,o:d,d(l){l&&b(e)}}}function x(s,e,a){let{value:i}=e,{type:n}=e,{selected:l=!1}=e;return s.$$set=t=>{"value"in t&&a(0,i=t.value),"type"in t&&a(1,n=t.type),"selected"in t&&a(2,l=t.selected)},[i,n,l]}class q extends g{constructor(e){super(),y(this,e,x,p,h,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),f()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),f()}}export{q as default};
//# sourceMappingURL=Example-DXubr1oD.js.map
