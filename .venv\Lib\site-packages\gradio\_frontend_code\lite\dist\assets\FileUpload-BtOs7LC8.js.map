{"version": 3, "file": "FileUpload-BtOs7LC8.js", "sources": ["../../../file/shared/utils.ts", "../../../file/shared/FilePreview.svelte", "../../../file/shared/File.svelte", "../../../file/shared/FileUpload.svelte"], "sourcesContent": ["import type { FileData } from \"@gradio/client\";\n\nexport const prettyBytes = (bytes: number): string => {\n\tlet units = [\"B\", \"KB\", \"MB\", \"GB\", \"PB\"];\n\tlet i = 0;\n\twhile (bytes > 1024) {\n\t\tbytes /= 1024;\n\t\ti++;\n\t}\n\tlet unit = units[i];\n\treturn bytes.toFixed(1) + \"&nbsp;\" + unit;\n};\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/client\";\n\timport { prettyBytes } from \"./utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { I18nFormatter, SelectData } from \"@gradio/utils\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tselect: SelectData;\n\t\tchange: FileData[] | FileData;\n\t\tdelete: FileData;\n\t\tdownload: FileData;\n\t}>();\n\texport let value: FileData | FileData[];\n\texport let selectable = false;\n\texport let height: number | string | undefined = undefined;\n\texport let i18n: I18nFormatter;\n\texport let allow_reordering = false;\n\n\tlet dragging_index: number | null = null;\n\tlet drop_target_index: number | null = null;\n\n\tfunction handle_drag_start(event: DragEvent, index: number): void {\n\t\tdragging_index = index;\n\t\tif (event.dataTransfer) {\n\t\t\tevent.dataTransfer.effectAllowed = \"move\";\n\t\t\tevent.dataTransfer.setData(\"text/plain\", index.toString());\n\t\t}\n\t}\n\n\tfunction handle_drag_over(event: DragEvent, index: number): void {\n\t\tevent.preventDefault();\n\t\tif (index === normalized_files.length - 1) {\n\t\t\tconst rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n\t\t\tconst midY = rect.top + rect.height / 2;\n\t\t\tdrop_target_index =\n\t\t\t\tevent.clientY > midY ? normalized_files.length : index;\n\t\t} else {\n\t\t\tdrop_target_index = index;\n\t\t}\n\t\tif (event.dataTransfer) {\n\t\t\tevent.dataTransfer.dropEffect = \"move\";\n\t\t}\n\t}\n\n\tfunction handle_drag_end(event: DragEvent): void {\n\t\tif (\n\t\t\t!event.dataTransfer?.dropEffect ||\n\t\t\tevent.dataTransfer.dropEffect === \"none\"\n\t\t) {\n\t\t\tdragging_index = null;\n\t\t\tdrop_target_index = null;\n\t\t}\n\t}\n\n\tfunction handle_drop(event: DragEvent, index: number): void {\n\t\tevent.preventDefault();\n\t\tif (dragging_index === null || dragging_index === index) return;\n\n\t\tconst files = Array.isArray(value) ? [...value] : [value];\n\t\tconst [removed] = files.splice(dragging_index, 1);\n\t\tfiles.splice(\n\t\t\tdrop_target_index === normalized_files.length\n\t\t\t\t? normalized_files.length\n\t\t\t\t: index,\n\t\t\t0,\n\t\t\tremoved\n\t\t);\n\n\t\tconst new_value = Array.isArray(value) ? files : files[0];\n\t\tdispatch(\"change\", new_value);\n\n\t\tdragging_index = null;\n\t\tdrop_target_index = null;\n\t}\n\n\tfunction split_filename(filename: string): [string, string] {\n\t\tconst last_dot = filename.lastIndexOf(\".\");\n\t\tif (last_dot === -1) {\n\t\t\treturn [filename, \"\"];\n\t\t}\n\t\treturn [filename.slice(0, last_dot), filename.slice(last_dot)];\n\t}\n\n\t$: normalized_files = (Array.isArray(value) ? value : [value]).map((file) => {\n\t\tconst [filename_stem, filename_ext] = split_filename(file.orig_name ?? \"\");\n\t\treturn {\n\t\t\t...file,\n\t\t\tfilename_stem,\n\t\t\tfilename_ext\n\t\t};\n\t});\n\n\tfunction handle_row_click(\n\t\tevent: MouseEvent & { currentTarget: HTMLTableRowElement },\n\t\tindex: number\n\t): void {\n\t\tconst tr = event.currentTarget;\n\t\tconst should_select =\n\t\t\tevent.target === tr || // Only select if the click is on the row itself\n\t\t\t(tr &&\n\t\t\t\ttr.firstElementChild &&\n\t\t\t\tevent.composedPath().includes(tr.firstElementChild)); // Or if the click is on the name column\n\n\t\tif (should_select) {\n\t\t\tdispatch(\"select\", { value: normalized_files[index].orig_name, index });\n\t\t}\n\t}\n\n\tfunction remove_file(index: number): void {\n\t\tconst removed = normalized_files.splice(index, 1);\n\t\tnormalized_files = [...normalized_files];\n\t\tvalue = normalized_files;\n\t\tdispatch(\"delete\", removed[0]);\n\t\tdispatch(\"change\", normalized_files);\n\t}\n\n\tfunction handle_download(file: FileData): void {\n\t\tdispatch(\"download\", file);\n\t}\n\n\tconst is_browser = typeof window !== \"undefined\";\n</script>\n\n<div\n\tclass=\"file-preview-holder\"\n\tstyle:max-height={height\n\t\t? typeof height === \"number\"\n\t\t\t? height + \"px\"\n\t\t\t: height\n\t\t: \"auto\"}\n>\n\t<table class=\"file-preview\">\n\t\t<tbody>\n\t\t\t{#each normalized_files as file, i (file.url)}\n\t\t\t\t<tr\n\t\t\t\t\tclass=\"file\"\n\t\t\t\t\tclass:selectable\n\t\t\t\t\tclass:dragging={dragging_index === i}\n\t\t\t\t\tclass:drop-target={drop_target_index === i ||\n\t\t\t\t\t\t(i === normalized_files.length - 1 &&\n\t\t\t\t\t\t\tdrop_target_index === normalized_files.length)}\n\t\t\t\t\tdata-drop-target={drop_target_index === normalized_files.length &&\n\t\t\t\t\ti === normalized_files.length - 1\n\t\t\t\t\t\t? \"after\"\n\t\t\t\t\t\t: drop_target_index === i + 1\n\t\t\t\t\t\t\t? \"after\"\n\t\t\t\t\t\t\t: \"before\"}\n\t\t\t\t\tdraggable={allow_reordering && normalized_files.length > 1}\n\t\t\t\t\ton:click={(event) => {\n\t\t\t\t\t\thandle_row_click(event, i);\n\t\t\t\t\t}}\n\t\t\t\t\ton:dragstart={(event) => handle_drag_start(event, i)}\n\t\t\t\t\ton:dragenter|preventDefault\n\t\t\t\t\ton:dragover={(event) => handle_drag_over(event, i)}\n\t\t\t\t\ton:drop={(event) => handle_drop(event, i)}\n\t\t\t\t\ton:dragend={handle_drag_end}\n\t\t\t\t>\n\t\t\t\t\t<td class=\"filename\" aria-label={file.orig_name}>\n\t\t\t\t\t\t{#if allow_reordering && normalized_files.length > 1}\n\t\t\t\t\t\t\t<span class=\"drag-handle\">⋮⋮</span>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t<span class=\"stem\">{file.filename_stem}</span>\n\t\t\t\t\t\t<span class=\"ext\">{file.filename_ext}</span>\n\t\t\t\t\t</td>\n\n\t\t\t\t\t<td class=\"download\">\n\t\t\t\t\t\t{#if file.url}\n\t\t\t\t\t\t\t<DownloadLink\n\t\t\t\t\t\t\t\thref={file.url}\n\t\t\t\t\t\t\t\ton:click={() => handle_download(file)}\n\t\t\t\t\t\t\t\tdownload={is_browser && window.__is_colab__\n\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t: file.orig_name}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{@html file.size != null\n\t\t\t\t\t\t\t\t\t? prettyBytes(file.size)\n\t\t\t\t\t\t\t\t\t: \"(size unknown)\"}&nbsp;&#8675;\n\t\t\t\t\t\t\t</DownloadLink>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t{i18n(\"file.uploading\")}\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</td>\n\n\t\t\t\t\t{#if normalized_files.length > 1}\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\tclass=\"label-clear-button\"\n\t\t\t\t\t\t\t\taria-label=\"Remove this file\"\n\t\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\t\tremove_file(i);\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\t\t\t\tremove_file(i);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t>×\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/if}\n\t\t\t\t</tr>\n\t\t\t{/each}\n\t\t</tbody>\n\t</table>\n</div>\n\n<style>\n\t.label-clear-button {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tposition: relative;\n\t\tleft: -3px;\n\t}\n\n\t.label-clear-button:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.file-preview {\n\t\ttable-layout: fixed;\n\t\twidth: var(--size-full);\n\t\tmax-height: var(--size-60);\n\t\toverflow-y: auto;\n\t\tmargin-top: var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.file-preview-holder {\n\t\toverflow: auto;\n\t}\n\n\t.file {\n\t\tdisplay: flex;\n\t\twidth: var(--size-full);\n\t}\n\n\t.file > * {\n\t\tpadding: var(--size-1) var(--size-2-5);\n\t}\n\n\t.filename {\n\t\tflex-grow: 1;\n\t\tdisplay: flex;\n\t\toverflow: hidden;\n\t}\n\t.filename .stem {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\t.filename .ext {\n\t\twhite-space: nowrap;\n\t}\n\n\t.download {\n\t\tmin-width: 8rem;\n\t\twidth: 10%;\n\t\twhite-space: nowrap;\n\t\ttext-align: right;\n\t}\n\t.download:hover {\n\t\ttext-decoration: underline;\n\t}\n\t.download > :global(a) {\n\t\tcolor: var(--link-text-color);\n\t}\n\n\t.download > :global(a:hover) {\n\t\tcolor: var(--link-text-color-hover);\n\t}\n\t.download > :global(a:visited) {\n\t\tcolor: var(--link-text-color-visited);\n\t}\n\t.download > :global(a:active) {\n\t\tcolor: var(--link-text-color-active);\n\t}\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\ttbody > tr:nth-child(even) {\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\ttbody > tr:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.drag-handle {\n\t\tcursor: grab;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tpadding-right: var(--size-2);\n\t\tuser-select: none;\n\t}\n\n\t.dragging {\n\t\topacity: 0.5;\n\t\tcursor: grabbing;\n\t}\n\n\t.drop-target {\n\t\tborder-top: 2px solid var(--color-accent);\n\t}\n\n\ttr:last-child.drop-target[data-drop-target=\"before\"] {\n\t\tborder-top: 2px solid var(--color-accent);\n\t\tborder-bottom: none;\n\t}\n\n\ttr:last-child.drop-target[data-drop-target=\"after\"] {\n\t\tborder-top: none;\n\t\tborder-bottom: 2px solid var(--color-accent);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/client\";\n\timport { BlockLabel, Empty } from \"@gradio/atoms\";\n\timport { File } from \"@gradio/icons\";\n\timport FilePreview from \"./FilePreview.svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let value: FileData | FileData[] | null = null;\n\texport let label: string;\n\texport let show_label = true;\n\texport let selectable = false;\n\texport let height: number | undefined = undefined;\n\texport let i18n: I18nFormatter;\n</script>\n\n<BlockLabel\n\t{show_label}\n\tfloat={value === null}\n\tIcon={File}\n\tlabel={label || \"File\"}\n/>\n\n{#if value && (Array.isArray(value) ? value.length > 0 : true)}\n\t<FilePreview {i18n} {selectable} on:select on:download {value} {height} />\n{:else}\n\t<Empty unpadded_box={true} size=\"large\"><File /></Empty>\n{/if}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport { <PERSON>Label, IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport { File, Clear, Upload as UploadIcon } from \"@gradio/icons\";\n\n\timport FilePreview from \"./FilePreview.svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\texport let value: null | FileData | FileData[];\n\n\texport let label: string;\n\texport let show_label = true;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\" = \"single\";\n\texport let file_types: string[] | null = null;\n\texport let selectable = false;\n\texport let root: string;\n\texport let height: number | undefined = undefined;\n\texport let i18n: I18nFormatter;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let uploading = false;\n\texport let allow_reordering = false;\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData | FileData[]>): Promise<void> {\n\t\tif (Array.isArray(value)) {\n\t\t\tvalue = [...value, ...(Array.isArray(detail) ? detail : [detail])];\n\t\t} else if (value) {\n\t\t\tvalue = [value, ...(Array.isArray(detail) ? detail : [detail])];\n\t\t} else {\n\t\t\tvalue = detail;\n\t\t}\n\t\tawait tick();\n\t\tdispatch(\"change\", value);\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData[] | FileData | null;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t\tupload: FileData[] | FileData;\n\t\tload: FileData[] | FileData;\n\t\terror: string;\n\t}>();\n\n\tlet dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={File} float={!value} label={label || \"File\"} />\n\n{#if value && (Array.isArray(value) ? value.length > 0 : true)}\n\t<IconButtonWrapper>\n\t\t{#if !(file_count === \"single\" && (Array.isArray(value) ? value.length > 0 : value !== null))}\n\t\t\t<IconButton Icon={UploadIcon} label={i18n(\"common.upload\")}>\n\t\t\t\t<Upload\n\t\t\t\t\ticon_upload={true}\n\t\t\t\t\ton:load={handle_upload}\n\t\t\t\t\tfiletype={file_types}\n\t\t\t\t\t{file_count}\n\t\t\t\t\t{max_file_size}\n\t\t\t\t\t{root}\n\t\t\t\t\tbind:dragging\n\t\t\t\t\tbind:uploading\n\t\t\t\t\ton:error\n\t\t\t\t\t{stream_handler}\n\t\t\t\t\t{upload}\n\t\t\t\t/>\n\t\t\t</IconButton>\n\t\t{/if}\n\t\t<IconButton\n\t\t\tIcon={Clear}\n\t\t\tlabel={i18n(\"common.clear\")}\n\t\t\ton:click={(event) => {\n\t\t\t\tdispatch(\"clear\");\n\t\t\t\tevent.stopPropagation();\n\t\t\t\thandle_clear();\n\t\t\t}}\n\t\t/>\n\t</IconButtonWrapper>\n\n\t<FilePreview\n\t\t{i18n}\n\t\ton:select\n\t\t{selectable}\n\t\t{value}\n\t\t{height}\n\t\ton:change\n\t\ton:delete\n\t\t{allow_reordering}\n\t/>\n{:else}\n\t<Upload\n\t\ton:load={handle_upload}\n\t\tfiletype={file_types}\n\t\t{file_count}\n\t\t{max_file_size}\n\t\t{root}\n\t\tbind:dragging\n\t\tbind:uploading\n\t\ton:error\n\t\t{stream_handler}\n\t\t{upload}\n\t\t{height}\n\t>\n\t\t<slot />\n\t</Upload>\n{/if}\n"], "names": ["prettyBytes", "bytes", "units", "i", "unit", "insert", "target", "span", "anchor", "t_value", "ctx", "dirty", "set_data", "t", "downloadlink_changes", "html_tag", "raw_value", "td", "append", "button", "t1_value", "t3_value", "if_block0", "create_if_block_2", "create_if_block", "attr", "td0", "td0_aria_label_value", "tr", "tr_draggable_value", "toggle_class", "span0", "span1", "td1", "current", "t1", "t3", "get_key", "set_style", "div", "table", "tbody", "split_filename", "filename", "last_dot", "dispatch", "createEventDispatcher", "value", "$$props", "selectable", "height", "i18n", "allow_reordering", "dragging_index", "drop_target_index", "handle_drag_start", "event", "index", "handle_drag_over", "normalized_files", "rect", "midY", "$$invalidate", "handle_drag_end", "handle_drop", "files", "removed", "new_value", "handle_row_click", "remove_file", "handle_download", "file", "is_browser", "click_handler", "dragstart_handler", "dragover_handler", "drop_handler", "filename_stem", "filename_ext", "File", "show_if", "blocklabel_changes", "label", "show_label", "UploadIcon", "iconbutton_changes", "Clear", "file_count", "file_types", "root", "max_file_size", "upload", "stream_handler", "uploading", "handle_upload", "detail", "tick", "handle_clear", "dragging"], "mappings": "qpBAEa,MAAAA,GAAeC,GAA0B,CACrD,IAAIC,EAAQ,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EACpCC,EAAI,EACR,KAAOF,EAAQ,MACLA,GAAA,KACTE,IAEG,IAAAC,EAAOF,EAAMC,CAAC,EAClB,OAAOF,EAAM,QAAQ,CAAC,EAAI,SAAWG,CACtC,oLCqJOC,EAAkCC,EAAAC,EAAAC,CAAA,iCAoBjC,IAAAC,EAAAC,KAAK,gBAAgB,EAAA,gDAArBC,EAAA,GAAAF,KAAAA,EAAAC,KAAK,gBAAgB,EAAA,KAAAE,GAAAC,EAAAJ,CAAA,4GAXf,KAAAC,MAAK,aAEDA,EAAU,EAAA,GAAI,OAAO,aAC5B,KACAA,MAAK,wIAJFC,EAAA,KAAAG,EAAA,KAAAJ,MAAK,uBAEDA,EAAU,EAAA,GAAI,OAAO,aAC5B,KACAA,MAAK,sKAEDA,EAAI,EAAA,EAAC,MAAQ,KACjBV,GAAYU,EAAI,EAAA,EAAC,IAAI,EACrB,kBAAgB,iCAAC,IACrB,0DAHQA,EAAI,EAAA,EAAC,MAAQ,KACjBV,GAAYU,EAAI,EAAA,EAAC,IAAI,EACrB,kBAAgB,KAAAK,EAAA,EAAAC,CAAA,iTAQrBX,EAcIC,EAAAW,EAAAT,CAAA,EAbHU,EAYQD,EAAAE,CAAA,oHApCWC,EAAAV,MAAK,cAAa,SACnBW,EAAAX,MAAK,aAAY,6BAJ/BY,EAAAZ,EAAoB,CAAA,GAAAA,EAAiB,CAAA,EAAA,OAAS,GAACa,GAAA,uCAQ/C,OAAAb,MAAK,IAAG,gCAiBTA,EAAgB,CAAA,EAAC,OAAS,GAACc,GAAAd,CAAA,4aA1BCe,EAAAC,EAAA,aAAAC,EAAAjB,MAAK,SAAS,qGAhB7BA,EAAiB,CAAA,IAAKA,EAAgB,CAAA,EAAC,QACzDA,EAAM,EAAA,IAAAA,EAAiB,CAAA,EAAA,OAAS,GAE7BA,EAAiB,CAAA,IAAKA,EAAI,EAAA,EAAA,EAD1B,QAGC,QAAQ,EACDe,EAAAG,EAAA,YAAAC,EAAAnB,EAAoB,CAAA,GAAAA,EAAiB,CAAA,EAAA,OAAS,CAAC,yBAV1CoB,EAAAF,EAAA,WAAAlB,OAAmBA,EAAC,EAAA,CAAA,EACjBoB,EAAAF,EAAA,cAAAlB,EAAsB,CAAA,IAAAA,EACvC,EAAA,GAAAA,QAAMA,EAAgB,CAAA,EAAC,OAAS,GAChCA,EAAsB,CAAA,IAAAA,KAAiB,MAAM,uBANhDL,EAkEIC,EAAAsB,EAAApB,CAAA,EA3CHU,EAMIU,EAAAF,CAAA,wBAFHR,EAA6CQ,EAAAK,CAAA,gBAC7Cb,EAA2CQ,EAAAM,CAAA,gBAG5Cd,EAgBIU,EAAAK,CAAA,qKA1BQvB,EAAe,CAAA,CAAA,qBAGrBA,EAAoB,CAAA,GAAAA,EAAiB,CAAA,EAAA,OAAS,iDAG/B,CAAAwB,GAAAvB,EAAA,KAAAS,KAAAA,EAAAV,MAAK,cAAa,KAAAE,GAAAuB,EAAAf,CAAA,GACnB,CAAAc,GAAAvB,EAAA,KAAAU,KAAAA,EAAAX,MAAK,aAAY,KAAAE,GAAAwB,EAAAf,CAAA,GALJ,CAAAa,GAAAvB,EAAA,IAAAgB,KAAAA,EAAAjB,MAAK,wKA0BjCA,EAAgB,CAAA,EAAC,OAAS,2EA1CbA,EAAiB,CAAA,IAAKA,EAAgB,CAAA,EAAC,QACzDA,EAAM,EAAA,IAAAA,EAAiB,CAAA,EAAA,OAAS,GAE7BA,EAAiB,CAAA,IAAKA,EAAI,EAAA,EAAA,EAD1B,QAGC,uCACO,CAAAwB,GAAAvB,EAAA,IAAAkB,KAAAA,EAAAnB,EAAoB,CAAA,GAAAA,EAAiB,CAAA,EAAA,OAAS,sEAVzCoB,EAAAF,EAAA,WAAAlB,OAAmBA,EAAC,EAAA,CAAA,cACjBoB,EAAAF,EAAA,cAAAlB,EAAsB,CAAA,IAAAA,EACvC,EAAA,GAAAA,QAAMA,EAAgB,CAAA,EAAC,OAAS,GAChCA,EAAsB,CAAA,IAAAA,KAAiB,MAAM,4IAP1CA,EAAgB,CAAA,CAAA,EAAa,MAAA2B,EAAA3B,GAAAA,MAAK,oBAAvC,OAAIP,GAAA,EAAA,8PARUmC,GAAAC,EAAA,aAAA7B,EAAA,CAAA,EACR,OAAAA,MAAW,SACjBA,KAAS,KACTA,EAAA,CAAA,EACD,MAAM,UANVL,EAiFKC,EAAAiC,EAAA/B,CAAA,EAzEJU,EAwEOqB,EAAAC,CAAA,EAvENtB,EAsEOsB,EAAAC,CAAA,iFArEC/B,EAAgB,CAAA,CAAA,oDARP4B,GAAAC,EAAA,aAAA7B,EAAA,CAAA,EACR,OAAAA,MAAW,SACjBA,KAAS,KACTA,EAAA,CAAA,EACD,MAAM,+BAIL,OAAIP,GAAA,6HA1DCuC,GAAeC,EAAA,OACjBC,EAAWD,EAAS,YAAY,GAAG,SACrCC,IAAa,GACR,CAAAD,EAAU,EAAE,EAEb,CAAAA,EAAS,MAAM,EAAGC,CAAQ,EAAGD,EAAS,MAAMC,CAAQ,CAAA,iCA1EvDC,EAAWC,KAMN,GAAA,CAAA,MAAAC,CAAA,EAAAC,GACA,WAAAC,EAAa,EAAA,EAAAD,GACb,OAAAE,EAAsC,MAAA,EAAAF,EACtC,CAAA,KAAAG,CAAA,EAAAH,GACA,iBAAAI,EAAmB,EAAA,EAAAJ,EAE1BK,EAAgC,KAChCC,EAAmC,KAE9B,SAAAC,EAAkBC,EAAkBC,EAAA,KAC5CJ,EAAiBI,CAAA,EACbD,EAAM,eACTA,EAAM,aAAa,cAAgB,OACnCA,EAAM,aAAa,QAAQ,aAAcC,EAAM,SAAA,CAAA,GAIxC,SAAAC,EAAiBF,EAAkBC,EAAA,CAEvC,GADJD,EAAM,eAAA,EACFC,IAAUE,EAAiB,OAAS,EAAA,CACjC,MAAAC,EAAQJ,EAAM,cAA8B,sBAAA,EAC5CK,EAAOD,EAAK,IAAMA,EAAK,OAAS,EACtCE,EAAA,EAAAR,EACCE,EAAM,QAAUK,EAAOF,EAAiB,OAASF,CAAA,WAElDH,EAAoBG,CAAA,EAEjBD,EAAM,eACTA,EAAM,aAAa,WAAa,iBAIzBO,EAAgBP,EAAA,EAEtB,CAAAA,EAAM,cAAc,YACrBA,EAAM,aAAa,aAAe,cAElCH,EAAiB,IAAA,MACjBC,EAAoB,IAAA,GAIb,SAAAU,EAAYR,EAAkBC,EAAA,CAElC,GADJD,EAAM,eAAA,EACFH,IAAmB,MAAQA,IAAmBI,EAAA,OAE5C,MAAAQ,EAAQ,MAAM,QAAQlB,CAAK,EAAQ,CAAA,GAAAA,CAAK,GAAKA,CAAK,EACjD,CAAAmB,CAAO,EAAID,EAAM,OAAOZ,EAAgB,CAAC,EAChDY,EAAM,OACLX,IAAsBK,EAAiB,OACpCA,EAAiB,OACjBF,EACH,EACAS,GAGK,MAAAC,EAAY,MAAM,QAAQpB,CAAK,EAAIkB,EAAQA,EAAM,CAAC,EACxDpB,EAAS,SAAUsB,CAAS,MAE5Bd,EAAiB,IAAA,MACjBC,EAAoB,IAAA,EAoBZ,SAAAc,EACRZ,EACAC,EAAA,CAEM,MAAA7B,EAAK4B,EAAM,eAEhBA,EAAM,SAAW5B,GAChBA,GACAA,EAAG,mBACH4B,EAAM,aAAe,EAAA,SAAS5B,EAAG,iBAAiB,IAGnDiB,EAAS,SAAA,CAAY,MAAOc,EAAiBF,CAAK,EAAE,UAAW,MAAAA,aAIxDY,EAAYZ,EAAA,OACdS,EAAUP,EAAiB,OAAOF,EAAO,CAAC,EAChDK,EAAA,EAAAH,EAAA,CAAA,GAAuBA,CAAgB,CAAA,OACvCZ,EAAQY,CAAA,EACRd,EAAS,SAAUqB,EAAQ,CAAC,CAAA,EAC5BrB,EAAS,SAAUc,CAAgB,WAG3BW,EAAgBC,EAAA,CACxB1B,EAAS,WAAY0B,CAAI,EAGpB,MAAAC,EAAA,OAAoB,OAAW,mCAiDd,MAAAC,EAAAF,GAAAD,EAAgBC,CAAI,QAoBnCF,EAAYlE,CAAC,QAEDqD,IAAK,CACbA,EAAM,MAAQ,SACjBa,EAAYlE,CAAC,QA7CPqD,IAAK,CACfY,EAAiBZ,EAAOrD,CAAC,GAEXuE,EAAA,CAAAvE,EAAAqD,IAAUD,EAAkBC,EAAOrD,CAAC,EAErCwE,EAAA,CAAAxE,EAAAqD,IAAUE,EAAiBF,EAAOrD,CAAC,EACvCyE,EAAA,CAAAzE,EAAAqD,IAAUQ,EAAYR,EAAOrD,CAAC,sOAvEzC2D,EAAA,EAAAH,GAAoB,MAAM,QAAQZ,CAAK,EAAIA,EAAS,CAAAA,CAAK,GAAG,IAAKwB,GAAA,CAC5D,KAAA,CAAAM,EAAeC,CAAY,EAAIpC,GAAe6B,EAAK,WAAa,EAAE,EAErE,MAAA,CAAA,GAAAA,EACH,cAAAM,EACA,aAAAC,CAAA,ypBChEmB,81BARd,MAAApE,OAAU,UACXqE,GACC,MAAArE,MAAS,oEAGZsE,GAAA,OAAAA,EAAA,CAAA,EAAAtE,EAAU,CAAA,IAAA,OAAM,QAAQA,EAAK,CAAA,CAAA,GAAIA,EAAK,CAAA,EAAC,OAAS,sLAL7CC,EAAA,IAAAsE,EAAA,MAAAvE,OAAU,MAEVC,EAAA,IAAAsE,EAAA,MAAAvE,MAAS,ySAZL,MAAAqC,EAAsC,IAAA,EAAAC,EACtC,CAAA,MAAAkC,CAAA,EAAAlC,GACA,WAAAmC,EAAa,EAAA,EAAAnC,GACb,WAAAC,EAAa,EAAA,EAAAD,GACb,OAAAE,EAA6B,MAAA,EAAAF,EAC7B,CAAA,KAAAG,CAAA,EAAAH,u9BC6FAtC,EAAU,CAAA,kTADXA,EAAa,EAAA,CAAA,wGACZA,EAAU,CAAA,s2CAxCD0E,GAAmB,MAAA1E,KAAK,eAAe,2GAApBC,EAAA,MAAA0E,EAAA,MAAA3E,KAAK,eAAe,kOAE1C,YAEHA,EAAU,CAAA,+PADXA,EAAa,EAAA,CAAA,wGACZA,EAAU,CAAA,qVALhB,IAAAsE,EAAA,EAAAtE,OAAe,WAAa,MAAM,QAAQA,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,OAAS,EAAIA,OAAU,qDAkBhF4E,GACC,MAAA5E,KAAK,cAAc,kHAnBpBC,EAAA,KAAAqE,EAAA,EAAAtE,OAAe,WAAa,MAAM,QAAQA,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,OAAS,EAAIA,OAAU,6HAmB/EC,EAAA,MAAA0E,EAAA,MAAA3E,KAAK,cAAc,gMAvBEqE,UAAcrE,EAAK,CAAA,EAAS,MAAAA,MAAS,oEAE/DsE,GAAA,OAAAA,EAAA,CAAA,EAAAtE,EAAU,CAAA,IAAA,OAAM,QAAQA,EAAK,CAAA,CAAA,GAAIA,EAAK,CAAA,EAAC,OAAS,qMAFRA,EAAK,CAAA,GAASC,EAAA,IAAAsE,EAAA,MAAAvE,MAAS,mUAlDxD,CAAA,MAAAqC,CAAA,EAAAC,EAEA,CAAA,MAAAkC,CAAA,EAAAlC,GACA,WAAAmC,EAAa,EAAA,EAAAnC,GACb,WAAAuC,EAAkD,QAAA,EAAAvC,GAClD,WAAAwC,EAA8B,IAAA,EAAAxC,GAC9B,WAAAC,EAAa,EAAA,EAAAD,EACb,CAAA,KAAAyC,CAAA,EAAAzC,GACA,OAAAE,EAA6B,MAAA,EAAAF,EAC7B,CAAA,KAAAG,CAAA,EAAAH,GACA,cAAA0C,EAA+B,IAAA,EAAA1C,EAC/B,CAAA,OAAA2C,CAAA,EAAA3C,EACA,CAAA,eAAA4C,CAAA,EAAA5C,GACA,UAAA6C,EAAY,EAAA,EAAA7C,GACZ,iBAAAI,EAAmB,EAAA,EAAAJ,iBAEf8C,EACd,CAAA,OAAAC,GAAA,CAEI,MAAM,QAAQhD,CAAK,EACtBe,EAAA,EAAAf,EAAA,CAAA,GAAYA,KAAW,MAAM,QAAQgD,CAAM,EAAIA,GAAUA,CAAM,CAAA,CAAA,EACrDhD,EACVe,EAAA,EAAAf,EAAA,CAASA,KAAW,MAAM,QAAQgD,CAAM,EAAIA,GAAUA,CAAM,CAAA,CAAA,MAE5DhD,EAAQgD,CAAA,EAEH,MAAAC,GAAA,EACNnD,EAAS,SAAUE,CAAK,EACxBF,EAAS,SAAUkD,CAAM,EAGjB,SAAAE,GAAA,KACRlD,EAAQ,IAAA,EACRF,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,QAGXA,EAAWC,SASboD,EAAW,6FA4BF1C,GAAK,CACfX,EAAS,OAAO,EAChBW,EAAM,gBAAe,EACrByC,gwBA9BApD,EAAS,OAAQqD,CAAQ"}