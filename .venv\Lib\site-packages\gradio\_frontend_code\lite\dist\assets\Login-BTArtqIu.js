import{a as G,i as J,s as K,f as z,y as L,c as h,z as N,A as O,d as c,m as v,k as w,t as k,l as $,n as T,ao as Q,ap as R,w as C,b as B,C as H,x as D,B as S,O as F,a7 as P,a8 as j}from"../lite.js";import U from"./Index-BkBXyN3a.js";import{T as E}from"./Textbox-GwvoYeHL.js";/* empty css                                              */import{B as V}from"./Button-BiPFvbFD.js";import W from"./Index-BHNCUXBa.js";import"./BlockTitle-DvFB_De3.js";import"./Info-BVYOtGfA.js";import"./MarkdownCode-DVjr71R6.js";import"./Check-DbzZ-PD_.js";import"./Copy-DcTA0nce.js";import"./Send-DPp49sBe.js";import"./Square-CkbFMpLj.js";import"./index-B9I6rkKj.js";/* empty css                                              */import"./Image-BPQ6A_U-.js";import"./file-url-CoOyVRgq.js";/* empty css                                                   */function q(i){let e;return{c(){e=L("p"),N(e,"class","auth svelte-1ogxbi0")},m(t,s){c(t,e,s),e.innerHTML=i[1]},p(t,s){s&2&&(e.innerHTML=t[1])},d(t){t&&$(e)}}}function x(i){let e,t=i[7]("login.enable_cookies")+"",s;return{c(){e=L("p"),s=C(t),N(e,"class","auth svelte-1ogxbi0")},m(l,n){c(l,e,n),H(e,s)},p(l,n){n&128&&t!==(t=l[7]("login.enable_cookies")+"")&&D(s,t)},d(l){l&&$(e)}}}function A(i){let e,t=i[7]("login.incorrect_credentials")+"",s;return{c(){e=L("p"),s=C(t),N(e,"class","creds svelte-1ogxbi0")},m(l,n){c(l,e,n),H(e,s)},p(l,n){n&128&&t!==(t=l[7]("login.incorrect_credentials")+"")&&D(s,t)},d(l){l&&$(e)}}}function X(i){let e,t,s;function l(o){i[9](o)}let n={root:i[0],label:i[7]("login.username"),lines:1,show_label:!0,max_lines:1};return i[4]!==void 0&&(n.value=i[4]),e=new E({props:n}),F.push(()=>P(e,"value",l)),e.$on("submit",i[8]),{c(){h(e.$$.fragment)},m(o,u){v(e,o,u),s=!0},p(o,u){const f={};u&1&&(f.root=o[0]),u&128&&(f.label=o[7]("login.username")),!t&&u&16&&(t=!0,f.value=o[4],j(()=>t=!1)),e.$set(f)},i(o){s||(w(e.$$.fragment,o),s=!0)},o(o){k(e.$$.fragment,o),s=!1},d(o){T(e,o)}}}function Y(i){let e,t,s;function l(o){i[10](o)}let n={root:i[0],label:i[7]("login.password"),lines:1,show_label:!0,max_lines:1,type:"password"};return i[5]!==void 0&&(n.value=i[5]),e=new E({props:n}),F.push(()=>P(e,"value",l)),e.$on("submit",i[8]),{c(){h(e.$$.fragment)},m(o,u){v(e,o,u),s=!0},p(o,u){const f={};u&1&&(f.root=o[0]),u&128&&(f.label=o[7]("login.password")),!t&&u&32&&(t=!0,f.value=o[5],j(()=>t=!1)),e.$set(f)},i(o){s||(w(e.$$.fragment,o),s=!0)},o(o){k(e.$$.fragment,o),s=!1},d(o){T(e,o)}}}function Z(i){let e,t,s,l;return e=new S({props:{$$slots:{default:[X]},$$scope:{ctx:i}}}),s=new S({props:{$$slots:{default:[Y]},$$scope:{ctx:i}}}),{c(){h(e.$$.fragment),t=B(),h(s.$$.fragment)},m(n,o){v(e,n,o),c(n,t,o),v(s,n,o),l=!0},p(n,o){const u={};o&2193&&(u.$$scope={dirty:o,ctx:n}),e.$set(u);const f={};o&2209&&(f.$$scope={dirty:o,ctx:n}),s.$set(f)},i(n){l||(w(e.$$.fragment,n),w(s.$$.fragment,n),l=!0)},o(n){k(e.$$.fragment,n),k(s.$$.fragment,n),l=!1},d(n){n&&$(t),T(e,n),T(s,n)}}}function y(i){let e=i[7]("login.login")+"",t;return{c(){t=C(e)},m(s,l){c(s,t,l)},p(s,l){l&128&&e!==(e=s[7]("login.login")+"")&&D(t,e)},d(s){s&&$(t)}}}function ee(i){let e,t=i[7]("login.login")+"",s,l,n,o,u,f,g,b,d,p=i[1]&&q(i),_=i[3]&&x(i),r=i[6]&&A(i);return f=new U({props:{$$slots:{default:[Z]},$$scope:{ctx:i}}}),b=new V({props:{size:"lg",variant:"primary",$$slots:{default:[y]},$$scope:{ctx:i}}}),b.$on("click",i[8]),{c(){e=L("h2"),s=C(t),l=B(),p&&p.c(),n=B(),_&&_.c(),o=B(),r&&r.c(),u=B(),h(f.$$.fragment),g=B(),h(b.$$.fragment),N(e,"class","svelte-1ogxbi0")},m(a,m){c(a,e,m),H(e,s),c(a,l,m),p&&p.m(a,m),c(a,n,m),_&&_.m(a,m),c(a,o,m),r&&r.m(a,m),c(a,u,m),v(f,a,m),c(a,g,m),v(b,a,m),d=!0},p(a,m){(!d||m&128)&&t!==(t=a[7]("login.login")+"")&&D(s,t),a[1]?p?p.p(a,m):(p=q(a),p.c(),p.m(n.parentNode,n)):p&&(p.d(1),p=null),a[3]?_?_.p(a,m):(_=x(a),_.c(),_.m(o.parentNode,o)):_&&(_.d(1),_=null),a[6]?r?r.p(a,m):(r=A(a),r.c(),r.m(u.parentNode,u)):r&&(r.d(1),r=null);const I={};m&2225&&(I.$$scope={dirty:m,ctx:a}),f.$set(I);const M={};m&2176&&(M.$$scope={dirty:m,ctx:a}),b.$set(M)},i(a){d||(w(f.$$.fragment,a),w(b.$$.fragment,a),d=!0)},o(a){k(f.$$.fragment,a),k(b.$$.fragment,a),d=!1},d(a){a&&($(e),$(l),$(n),$(o),$(u),$(g)),p&&p.d(a),_&&_.d(a),r&&r.d(a),T(f,a),T(b,a)}}}function te(i){let e,t,s;return t=new W({props:{variant:"panel",min_width:480,$$slots:{default:[ee]},$$scope:{ctx:i}}}),{c(){e=L("div"),h(t.$$.fragment),N(e,"class","wrap svelte-1ogxbi0"),O(e,"min-h-screen",i[2])},m(l,n){c(l,e,n),v(t,e,null),s=!0},p(l,[n]){const o={};n&2299&&(o.$$scope={dirty:n,ctx:l}),t.$set(o),(!s||n&4)&&O(e,"min-h-screen",l[2])},i(l){s||(w(t.$$.fragment,l),s=!0)},o(l){k(t.$$.fragment,l),s=!1},d(l){l&&$(e),T(t)}}}function se(i,e,t){let s;Q(i,R,r=>t(7,s=r));let{root:l}=e,{auth_message:n}=e,{app_mode:o}=e,{space_id:u}=e,f="",g="",b=!1;const d=async()=>{const r=new FormData;r.append("username",f),r.append("password",g);let a=await fetch(l+"/login",{method:"POST",body:r});a.status===400?(t(6,b=!0),t(4,f=""),t(5,g="")):a.status==200&&location.reload()};function p(r){f=r,t(4,f)}function _(r){g=r,t(5,g)}return i.$$set=r=>{"root"in r&&t(0,l=r.root),"auth_message"in r&&t(1,n=r.auth_message),"app_mode"in r&&t(2,o=r.app_mode),"space_id"in r&&t(3,u=r.space_id)},[l,n,o,u,f,g,b,s,d,p,_]}class we extends G{constructor(e){super(),J(this,e,se,te,K,{root:0,auth_message:1,app_mode:2,space_id:3})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),z()}get auth_message(){return this.$$.ctx[1]}set auth_message(e){this.$$set({auth_message:e}),z()}get app_mode(){return this.$$.ctx[2]}set app_mode(e){this.$$set({app_mode:e}),z()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),z()}}export{we as default};
//# sourceMappingURL=Login-BTArtqIu.js.map
