import{a as qe,i as Ge,s as Ke,f as m,e as Me,d as F,h as Ve,t as v,j as Ye,k,l as H,a5 as De,K as Fe,B as $,c as z,m as B,n as C,Y as x,S as ee,O as P,a7 as E,b as te,a0 as se,a6 as ie,a8 as N}from"../lite.js";import He from"./StaticAudio-Bs3sQ5pr.js";import{I as Le}from"./InteractiveAudio-BSRCxffH.js";import{U as Qe}from"./UploadText-Chjc4Zy7.js";import{A as Ot}from"./AudioPlayer-Dn45keYP.js";import{default as jt}from"./Example-D1A46ypO.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-DWW9BWN3.js";import"./Empty-Bzq0Ew6m.js";import"./ShareButton-Be-vgu5O.js";import"./Community-BFnPJcwx.js";import"./Download-RUpc9r8A.js";import"./Music-BCIGqdvV.js";import"./IconButtonWrapper-BqpIgNIH.js";import"./DownloadLink-dHe4pFcz.js";import"./file-url-CoOyVRgq.js";import"./Trim-CDsEvQ4G.js";import"./Play-BIkNyEKH.js";import"./Undo-50qkik3g.js";import"./hls-CnVhpNcu.js";import"./Upload-Do_omv-N.js";/* empty css                                             */import"./ModifyUpload-b77W1M2_.js";import"./Edit-fMGAgLsI.js";import"./SelectSource-kJI_8u2f.js";import"./Upload-CYshamIj.js";import"./StreamingBar-lVbwTGD1.js";function Xe(t){let e,n;return e=new $({props:{variant:t[0]===null&&t[25]==="upload"?"dashed":"solid",border_mode:t[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:t[4],elem_classes:t[5],visible:t[6],container:t[12],scale:t[13],min_width:t[14],$$slots:{default:[$e]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},m(s,r){B(e,s,r),n=!0},p(s,r){const _={};r[0]&33554433&&(_.variant=s[0]===null&&s[25]==="upload"?"dashed":"solid"),r[0]&134217728&&(_.border_mode=s[27]?"focus":"base"),r[0]&16&&(_.elem_id=s[4]),r[0]&32&&(_.elem_classes=s[5]),r[0]&64&&(_.visible=s[6]),r[0]&4096&&(_.container=s[12]),r[0]&8192&&(_.scale=s[13]),r[0]&16384&&(_.min_width=s[14]),r[0]&536710927|r[2]&128&&(_.$$scope={dirty:r,ctx:s}),e.$set(_)},i(s){n||(k(e.$$.fragment,s),n=!0)},o(s){v(e.$$.fragment,s),n=!1},d(s){C(e,s)}}}function Ze(t){let e,n;return e=new $({props:{variant:"solid",border_mode:t[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:t[4],elem_classes:t[5],visible:t[6],container:t[12],scale:t[13],min_width:t[14],$$slots:{default:[xe]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},m(s,r){B(e,s,r),n=!0},p(s,r){const _={};r[0]&134217728&&(_.border_mode=s[27]?"focus":"base"),r[0]&16&&(_.elem_id=s[4]),r[0]&32&&(_.elem_classes=s[5]),r[0]&64&&(_.visible=s[6]),r[0]&4096&&(_.container=s[12]),r[0]&8192&&(_.scale=s[13]),r[0]&16384&&(_.min_width=s[14]),r[0]&277842435|r[2]&128&&(_.$$scope={dirty:r,ctx:s}),e.$set(_)},i(s){n||(k(e.$$.fragment,s),n=!0)},o(s){v(e.$$.fragment,s),n=!1},d(s){C(e,s)}}}function ye(t){let e,n;return e=new Qe({props:{i18n:t[23].i18n,type:"audio"}}),{c(){z(e.$$.fragment)},m(s,r){B(e,s,r),n=!0},p(s,r){const _={};r[0]&8388608&&(_.i18n=s[23].i18n),e.$set(_)},i(s){n||(k(e.$$.fragment,s),n=!0)},o(s){v(e.$$.fragment,s),n=!1},d(s){C(e,s)}}}function $e(t){let e,n,s,r,_,c,l,a,h;const g=[{autoscroll:t[23].autoscroll},{i18n:t[23].i18n},t[1]];let I={};for(let o=0;o<g.length;o+=1)I=x(I,g[o]);e=new ee({props:I}),e.$on("clear_status",t[45]);function O(o){t[48](o)}function U(o){t[49](o)}function j(o){t[50](o)}function J(o){t[51](o)}function b(o){t[52](o)}let w={label:t[9],show_label:t[11],show_download_button:t[16],value:t[0],root:t[10],sources:t[8],active_source:t[25],pending:t[20],streaming:t[21],loop:t[15],max_file_size:t[23].max_file_size,handle_reset_value:t[29],editable:t[18],i18n:t[23].i18n,waveform_settings:t[28],waveform_options:t[19],trim_region_settings:t[30],stream_every:t[22],upload:t[46],stream_handler:t[47],$$slots:{default:[ye]},$$scope:{ctx:t}};return t[2]!==void 0&&(w.recording=t[2]),t[27]!==void 0&&(w.dragging=t[27]),t[24]!==void 0&&(w.uploading=t[24]),t[26]!==void 0&&(w.modify_stream=t[26]),t[3]!==void 0&&(w.set_time_limit=t[3]),s=new Le({props:w}),P.push(()=>E(s,"recording",O)),P.push(()=>E(s,"dragging",U)),P.push(()=>E(s,"uploading",j)),P.push(()=>E(s,"modify_stream",J)),P.push(()=>E(s,"set_time_limit",b)),s.$on("change",t[53]),s.$on("stream",t[54]),s.$on("drag",t[55]),s.$on("edit",t[56]),s.$on("play",t[57]),s.$on("pause",t[58]),s.$on("stop",t[59]),s.$on("start_recording",t[60]),s.$on("pause_recording",t[61]),s.$on("stop_recording",t[62]),s.$on("upload",t[63]),s.$on("clear",t[64]),s.$on("error",t[31]),s.$on("close_stream",t[65]),{c(){z(e.$$.fragment),n=te(),z(s.$$.fragment)},m(o,u){B(e,o,u),F(o,n,u),B(s,o,u),h=!0},p(o,u){const R=u[0]&8388610?se(g,[u[0]&8388608&&{autoscroll:o[23].autoscroll},u[0]&8388608&&{i18n:o[23].i18n},u[0]&2&&ie(o[1])]):{};e.$set(R);const f={};u[0]&512&&(f.label=o[9]),u[0]&2048&&(f.show_label=o[11]),u[0]&65536&&(f.show_download_button=o[16]),u[0]&1&&(f.value=o[0]),u[0]&1024&&(f.root=o[10]),u[0]&256&&(f.sources=o[8]),u[0]&33554432&&(f.active_source=o[25]),u[0]&1048576&&(f.pending=o[20]),u[0]&2097152&&(f.streaming=o[21]),u[0]&32768&&(f.loop=o[15]),u[0]&8388608&&(f.max_file_size=o[23].max_file_size),u[0]&262144&&(f.editable=o[18]),u[0]&8388608&&(f.i18n=o[23].i18n),u[0]&268435456&&(f.waveform_settings=o[28]),u[0]&524288&&(f.waveform_options=o[19]),u[0]&4194304&&(f.stream_every=o[22]),u[0]&8388608&&(f.upload=o[46]),u[0]&8388608&&(f.stream_handler=o[47]),u[0]&8388608|u[2]&128&&(f.$$scope={dirty:u,ctx:o}),!r&&u[0]&4&&(r=!0,f.recording=o[2],N(()=>r=!1)),!_&&u[0]&134217728&&(_=!0,f.dragging=o[27],N(()=>_=!1)),!c&&u[0]&16777216&&(c=!0,f.uploading=o[24],N(()=>c=!1)),!l&&u[0]&67108864&&(l=!0,f.modify_stream=o[26],N(()=>l=!1)),!a&&u[0]&8&&(a=!0,f.set_time_limit=o[3],N(()=>a=!1)),s.$set(f)},i(o){h||(k(e.$$.fragment,o),k(s.$$.fragment,o),h=!0)},o(o){v(e.$$.fragment,o),v(s.$$.fragment,o),h=!1},d(o){o&&H(n),C(e,o),C(s,o)}}}function xe(t){let e,n,s,r;const _=[{autoscroll:t[23].autoscroll},{i18n:t[23].i18n},t[1]];let c={};for(let l=0;l<_.length;l+=1)c=x(c,_[l]);return e=new ee({props:c}),e.$on("clear_status",t[39]),s=new He({props:{i18n:t[23].i18n,show_label:t[11],show_download_button:t[16],show_share_button:t[17],value:t[0],label:t[9],loop:t[15],waveform_settings:t[28],waveform_options:t[19],editable:t[18]}}),s.$on("share",t[40]),s.$on("error",t[41]),s.$on("play",t[42]),s.$on("pause",t[43]),s.$on("stop",t[44]),{c(){z(e.$$.fragment),n=te(),z(s.$$.fragment)},m(l,a){B(e,l,a),F(l,n,a),B(s,l,a),r=!0},p(l,a){const h=a[0]&8388610?se(_,[a[0]&8388608&&{autoscroll:l[23].autoscroll},a[0]&8388608&&{i18n:l[23].i18n},a[0]&2&&ie(l[1])]):{};e.$set(h);const g={};a[0]&8388608&&(g.i18n=l[23].i18n),a[0]&2048&&(g.show_label=l[11]),a[0]&65536&&(g.show_download_button=l[16]),a[0]&131072&&(g.show_share_button=l[17]),a[0]&1&&(g.value=l[0]),a[0]&512&&(g.label=l[9]),a[0]&32768&&(g.loop=l[15]),a[0]&268435456&&(g.waveform_settings=l[28]),a[0]&524288&&(g.waveform_options=l[19]),a[0]&262144&&(g.editable=l[18]),s.$set(g)},i(l){r||(k(e.$$.fragment,l),k(s.$$.fragment,l),r=!0)},o(l){v(e.$$.fragment,l),v(s.$$.fragment,l),r=!1},d(l){l&&H(n),C(e,l),C(s,l)}}}function et(t){let e,n,s,r;const _=[Ze,Xe],c=[];function l(a,h){return a[7]?1:0}return e=l(t),n=c[e]=_[e](t),{c(){n.c(),s=Me()},m(a,h){c[e].m(a,h),F(a,s,h),r=!0},p(a,h){let g=e;e=l(a),e===g?c[e].p(a,h):(Ve(),v(c[g],1,1,()=>{c[g]=null}),Ye(),n=c[e],n?n.p(a,h):(n=c[e]=_[e](a),n.c()),k(n,1),n.m(s.parentNode,s))},i(a){r||(k(n),r=!0)},o(a){v(n),r=!1},d(a){a&&H(s),c[e].d(a)}}}function tt(t,e,n){let{value_is_output:s=!1}=e,{elem_id:r=""}=e,{elem_classes:_=[]}=e,{visible:c=!0}=e,{interactive:l}=e,{value:a=null}=e,{sources:h}=e,{label:g}=e,{root:I}=e,{show_label:O}=e,{container:U=!0}=e,{scale:j=null}=e,{min_width:J=void 0}=e,{loading_status:b}=e,{autoplay:w=!1}=e,{loop:o=!1}=e,{show_download_button:u}=e,{show_share_button:R=!1}=e,{editable:f=!0}=e,{waveform_options:S={show_recording_waveform:!0}}=e,{pending:L}=e,{streaming:Q}=e,{stream_every:X}=e,{input_ready:M}=e,{recording:T=!1}=e,W=!1,Z="closed",q;function ne(i){Z=i,q(i)}const oe=()=>Z;let{set_time_limit:G}=e,{gradio:d}=e,V=null,Y,A=a;const ae=()=>{A===null||a===A||n(0,a=A)};let K,p,D="darkorange";De(()=>{D=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),re(),n(28,p.waveColor=S.waveform_color||"#9ca3af",p),n(28,p.progressColor=S.waveform_progress_color||D,p),n(28,p.mediaControls=S.show_controls,p),n(28,p.sampleRate=S.sample_rate||44100,p)});const y={color:S.trim_region_color,drag:!0,resize:!0};function re(){document.documentElement.style.setProperty("--trim-region-color",y.color||D)}function le({detail:i}){const[Te,We]=i.includes("Invalid file type")?["warning","complete"]:["error","error"];n(1,b=b||{}),n(1,b.status=We,b),n(1,b.message=i,b),d.dispatch(Te,i)}Fe(()=>{n(32,s=!1)});const _e=()=>d.dispatch("clear_status",b),ue=i=>d.dispatch("share",i.detail),fe=i=>d.dispatch("error",i.detail),me=()=>d.dispatch("play"),de=()=>d.dispatch("pause"),ge=()=>d.dispatch("stop"),ce=()=>d.dispatch("clear_status",b),he=(...i)=>d.client.upload(...i),be=(...i)=>d.client.stream(...i);function we(i){T=i,n(2,T)}function pe(i){K=i,n(27,K)}function ve(i){W=i,n(24,W)}function ke(i){q=i,n(26,q)}function Se(i){G=i,n(3,G)}const ze=({detail:i})=>n(0,a=i),Be=({detail:i})=>{n(0,a=i),d.dispatch("stream",a)},Ce=({detail:i})=>n(27,K=i),Ie=()=>d.dispatch("edit"),Ae=()=>d.dispatch("play"),Pe=()=>d.dispatch("pause"),Ee=()=>d.dispatch("stop"),Ne=()=>d.dispatch("start_recording"),Oe=()=>d.dispatch("pause_recording"),Ue=i=>d.dispatch("stop_recording"),je=()=>d.dispatch("upload"),Je=()=>d.dispatch("clear"),Re=()=>d.dispatch("close_stream","stream");return t.$$set=i=>{"value_is_output"in i&&n(32,s=i.value_is_output),"elem_id"in i&&n(4,r=i.elem_id),"elem_classes"in i&&n(5,_=i.elem_classes),"visible"in i&&n(6,c=i.visible),"interactive"in i&&n(7,l=i.interactive),"value"in i&&n(0,a=i.value),"sources"in i&&n(8,h=i.sources),"label"in i&&n(9,g=i.label),"root"in i&&n(10,I=i.root),"show_label"in i&&n(11,O=i.show_label),"container"in i&&n(12,U=i.container),"scale"in i&&n(13,j=i.scale),"min_width"in i&&n(14,J=i.min_width),"loading_status"in i&&n(1,b=i.loading_status),"autoplay"in i&&n(34,w=i.autoplay),"loop"in i&&n(15,o=i.loop),"show_download_button"in i&&n(16,u=i.show_download_button),"show_share_button"in i&&n(17,R=i.show_share_button),"editable"in i&&n(18,f=i.editable),"waveform_options"in i&&n(19,S=i.waveform_options),"pending"in i&&n(20,L=i.pending),"streaming"in i&&n(21,Q=i.streaming),"stream_every"in i&&n(22,X=i.stream_every),"input_ready"in i&&n(33,M=i.input_ready),"recording"in i&&n(2,T=i.recording),"set_time_limit"in i&&n(3,G=i.set_time_limit),"gradio"in i&&n(23,d=i.gradio)},t.$$.update=()=>{t.$$.dirty[0]&16777216&&n(33,M=!W),t.$$.dirty[0]&1|t.$$.dirty[1]&128&&a&&A===null&&n(38,A=a),t.$$.dirty[0]&8388609|t.$$.dirty[1]&66&&JSON.stringify(a)!==JSON.stringify(V)&&(n(37,V=a),d.dispatch("change"),s||d.dispatch("input")),t.$$.dirty[0]&33554688&&!Y&&h&&n(25,Y=h[0]),t.$$.dirty[1]&8&&n(28,p={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:w,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20})},[a,b,T,G,r,_,c,l,h,g,I,O,U,j,J,o,u,R,f,S,L,Q,X,d,W,Y,q,K,p,ae,y,le,s,M,w,ne,oe,V,A,_e,ue,fe,me,de,ge,ce,he,be,we,pe,ve,ke,Se,ze,Be,Ce,Ie,Ae,Pe,Ee,Ne,Oe,Ue,je,Je,Re]}class st extends qe{constructor(e){super(),Ge(this,e,tt,et,Ke,{value_is_output:32,elem_id:4,elem_classes:5,visible:6,interactive:7,value:0,sources:8,label:9,root:10,show_label:11,container:12,scale:13,min_width:14,loading_status:1,autoplay:34,loop:15,show_download_button:16,show_share_button:17,editable:18,waveform_options:19,pending:20,streaming:21,stream_every:22,input_ready:33,recording:2,modify_stream_state:35,get_stream_state:36,set_time_limit:3,gradio:23},null,[-1,-1,-1])}get value_is_output(){return this.$$.ctx[32]}set value_is_output(e){this.$$set({value_is_output:e}),m()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),m()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),m()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),m()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),m()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),m()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),m()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),m()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),m()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),m()}get autoplay(){return this.$$.ctx[34]}set autoplay(e){this.$$set({autoplay:e}),m()}get loop(){return this.$$.ctx[15]}set loop(e){this.$$set({loop:e}),m()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),m()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get editable(){return this.$$.ctx[18]}set editable(e){this.$$set({editable:e}),m()}get waveform_options(){return this.$$.ctx[19]}set waveform_options(e){this.$$set({waveform_options:e}),m()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),m()}get streaming(){return this.$$.ctx[21]}set streaming(e){this.$$set({streaming:e}),m()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),m()}get input_ready(){return this.$$.ctx[33]}set input_ready(e){this.$$set({input_ready:e}),m()}get recording(){return this.$$.ctx[2]}set recording(e){this.$$set({recording:e}),m()}get modify_stream_state(){return this.$$.ctx[35]}get get_stream_state(){return this.$$.ctx[36]}get set_time_limit(){return this.$$.ctx[3]}set set_time_limit(e){this.$$set({set_time_limit:e}),m()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),m()}}const Pt=st;export{jt as BaseExample,Le as BaseInteractiveAudio,Ot as BasePlayer,He as BaseStaticAudio,Pt as default};
//# sourceMappingURL=index-D4itVUqM.js.map
