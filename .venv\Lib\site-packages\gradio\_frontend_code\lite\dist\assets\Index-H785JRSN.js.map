{"version": 3, "file": "Index-H785JRSN.js", "sources": ["../../../file/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as FilePreview } from \"./shared/FilePreview.svelte\";\n\texport { default as BaseFileUpload } from \"./shared/FileUpload.svelte\";\n\texport { default as BaseFile } from \"./shared/File.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport File from \"./shared/File.svelte\";\n\timport FileUpload from \"./shared/FileUpload.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData | FileData[];\n\n\texport let interactive: boolean;\n\texport let root: string;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let height: number | undefined = undefined;\n\n\texport let _selectable = false;\n\texport let loading_status: LoadingStatus;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\terror: string;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tclear_status: LoadingStatus;\n\t\tdelete: FileData;\n\t\tdownload: FileData;\n\t}>;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\";\n\texport let file_types: string[] = [\"file\"];\n\texport let input_ready: boolean;\n\texport let allow_reordering = false;\n\tlet uploading = false;\n\t$: input_ready = !uploading;\n\n\tlet old_value = value;\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\tgradio.dispatch(\"change\");\n\t\told_value = value;\n\t}\n\n\tlet dragging = false;\n\tlet pending_upload = false;\n</script>\n\n<Block\n\t{visible}\n\tvariant={value ? \"solid\" : \"dashed\"}\n\tborder_mode={dragging ? \"focus\" : \"base\"}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tstatus={pending_upload\n\t\t\t? \"generating\"\n\t\t\t: loading_status?.status || \"complete\"}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if !interactive}\n\t\t<File\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:download={({ detail }) => gradio.dispatch(\"download\", detail)}\n\t\t\tselectable={_selectable}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{height}\n\t\t\ti18n={gradio.i18n}\n\t\t/>\n\t{:else}\n\t\t<FileUpload\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{value}\n\t\t\t{file_count}\n\t\t\t{file_types}\n\t\t\tselectable={_selectable}\n\t\t\t{root}\n\t\t\t{height}\n\t\t\t{allow_reordering}\n\t\t\tbind:uploading\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\ton:change={({ detail }) => {\n\t\t\t\tvalue = detail;\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\ton:delete={({ detail }) => {\n\t\t\t\tgradio.dispatch(\"delete\", detail);\n\t\t\t}}\n\t\t\ti18n={gradio.i18n}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"file\" />\n\t\t</FileUpload>\n\t{/if}\n</Block>\n"], "names": ["ctx", "dirty", "fileupload_changes", "file_changes", "uploadtext_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "interactive", "root", "label", "show_label", "height", "_selectable", "loading_status", "container", "scale", "min_width", "gradio", "file_count", "file_types", "input_ready", "allow_reordering", "uploading", "old_value", "dragging", "clear_status_handler", "select_handler", "detail", "download_handler", "func", "args", "func_1", "$$invalidate", "select_handler_1"], "mappings": "u6BAuGeA,EAAW,EAAA,+CAKR,cAAAA,MAAO,cAgBhB,KAAAA,MAAO,6kBArBDA,EAAW,EAAA,4FAKRC,EAAA,CAAA,EAAA,QAAAC,EAAA,cAAAF,MAAO,eAgBhBC,EAAA,CAAA,EAAA,QAAAC,EAAA,KAAAF,MAAO,iQArCDA,EAAW,EAAA,oDAKjB,KAAAA,MAAO,8IALDA,EAAW,EAAA,2GAKjBC,EAAA,CAAA,EAAA,QAAAE,EAAA,KAAAH,MAAO,+IAkCK,KAAAA,MAAO,mFAAPC,EAAA,CAAA,EAAA,QAAAG,EAAA,KAAAJ,MAAO,yIAnDd,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,GACV,OAELA,EAAc,CAAA,GAAE,QAAU,yJAGxBA,EAAW,CAAA,IAAA,0KARJ,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,OAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,WACV,OAELA,EAAc,CAAA,GAAE,QAAU,8VAhBrBA,EAAK,CAAA,EAAG,QAAU,qBACdA,EAAQ,EAAA,EAAG,QAAU,eACzB,6FAMO,yJARPA,EAAK,CAAA,EAAG,QAAU,sCACdA,EAAQ,EAAA,EAAG,QAAU,2TA9CvB,QAAAK,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,MAAAG,CAAA,EAAAH,EAEA,CAAA,YAAAI,CAAA,EAAAJ,EACA,CAAA,KAAAK,CAAA,EAAAL,EACA,CAAA,MAAAM,CAAA,EAAAN,EACA,CAAA,WAAAO,CAAA,EAAAP,GACA,OAAAQ,EAA6B,MAAA,EAAAR,GAE7B,YAAAS,EAAc,EAAA,EAAAT,EACd,CAAA,eAAAU,CAAA,EAAAV,GACA,UAAAW,EAAY,EAAA,EAAAX,GACZ,MAAAY,EAAuB,IAAA,EAAAZ,GACvB,UAAAa,EAAgC,MAAA,EAAAb,EAChC,CAAA,OAAAc,CAAA,EAAAd,EAUA,CAAA,WAAAe,CAAA,EAAAf,EACA,CAAA,WAAAgB,EAAA,CAAwB,MAAM,CAAA,EAAAhB,EAC9B,CAAA,YAAAiB,CAAA,EAAAjB,GACA,iBAAAkB,EAAmB,EAAA,EAAAlB,EAC1BmB,EAAY,GAGZC,EAAYjB,EAMZkB,EAAW,GAuBS,MAAAC,EAAA,IAAAR,EAAO,SAAS,eAAgBJ,CAAc,EAItDa,EAAA,CAAA,CAAA,OAAAC,KAAaV,EAAO,SAAS,SAAUU,CAAM,EAC3CC,EAAA,CAAA,CAAA,OAAAD,KAAaV,EAAO,SAAS,WAAYU,CAAM,EAUnDE,EAAA,IAAAC,IAASb,EAAO,OAAO,UAAUa,CAAI,EAC7BC,EAAA,IAAAD,IAASb,EAAO,OAAO,UAAUa,CAAI,sCAY3C,OAAAH,KAAM,CACnBK,EAAA,EAAA1B,EAAQqB,CAAM,OAEH,OAAAA,CAAM,IAAAK,EAAA,GAAQR,EAAWG,CAAM,QAC3BV,EAAO,SAAS,OAAO,EACzBgB,EAAA,CAAA,CAAA,OAAAN,KAAaV,EAAO,SAAS,SAAUU,CAAM,QAC1CV,EAAO,SAAS,QAAQ,MAC5B,OAAAU,KAAM,CAClBK,EAAA,EAAAnB,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BI,EAAO,SAAS,QAASU,CAAM,OAElB,OAAAA,KAAM,CACnBV,EAAO,SAAS,SAAUU,CAAM,6uBAxEnCK,EAAA,GAAGZ,EAAe,CAAAE,CAAA,yBAGX,KAAK,UAAUC,CAAS,IAAM,KAAK,UAAUjB,CAAK,IACxDW,EAAO,SAAS,QAAQ,OACxBM,EAAYjB,CAAA"}