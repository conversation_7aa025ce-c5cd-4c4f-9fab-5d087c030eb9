import{a as m,i as u,s as c,f as o,y as f,c as p,z as g,A as n,d,m as h,k as _,t as y,l as v,n as b}from"../lite.js";import{I as k}from"./Image-BPQ6A_U-.js";/* empty css                                                   */import"./ImageUploader-BK9kfkZd.js";/* empty css                                              */import"./file-url-CoOyVRgq.js";import"./BlockLabel-DWW9BWN3.js";import"./FullscreenButton-DsVuMC2h.js";import"./Minimize-DOBO88I3.js";import"./SelectSource-kJI_8u2f.js";import"./Upload-CYshamIj.js";import"./IconButtonWrapper-BqpIgNIH.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-DIboSv6l.js";import"./Square-CkbFMpLj.js";import"./index-B9I6rkKj.js";import"./StreamingBar-lVbwTGD1.js";import"./Upload-Do_omv-N.js";/* empty css                                             */function $(i){let e,s,r;return s=new k({props:{src:i[0].composite?.url||i[0].background?.url,alt:""}}),{c(){e=f("div"),p(s.$$.fragment),g(e,"class","container svelte-jhlhb0"),n(e,"table",i[1]==="table"),n(e,"gallery",i[1]==="gallery"),n(e,"selected",i[2])},m(t,a){d(t,e,a),h(s,e,null),r=!0},p(t,[a]){const l={};a&1&&(l.src=t[0].composite?.url||t[0].background?.url),s.$set(l),(!r||a&2)&&n(e,"table",t[1]==="table"),(!r||a&2)&&n(e,"gallery",t[1]==="gallery"),(!r||a&4)&&n(e,"selected",t[2])},i(t){r||(_(s.$$.fragment,t),r=!0)},o(t){y(s.$$.fragment,t),r=!1},d(t){t&&v(e),b(s)}}}function I(i,e,s){let{value:r}=e,{type:t}=e,{selected:a=!1}=e;return i.$$set=l=>{"value"in l&&s(0,r=l.value),"type"in l&&s(1,t=l.type),"selected"in l&&s(2,a=l.selected)},[r,t,a]}class P extends m{constructor(e){super(),u(this,e,I,$,c,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),o()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),o()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),o()}}export{P as default};
//# sourceMappingURL=Example-Cu73cAZL.js.map
