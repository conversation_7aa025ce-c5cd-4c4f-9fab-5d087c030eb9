import{a as G,i as J,s as K,f as v,y as k,b as A,z as u,d as w,C as d,D as E,l as F,a5 as $,w as M,M as P,x as Q,aq as H,e as U,b8 as x,bb as ee,O as le,$ as te,A as S,bc as T,ba as W,c as ne,m as ie,k as ae,t as se,n as re}from"../lite.js";(function(a){a.languages.typescript=a.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),a.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete a.languages.typescript.parameter,delete a.languages.typescript["literal-property"];var e=a.languages.extend("typescript",{});delete e["class-name"],a.languages.typescript["class-name"].inside=e,a.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e}}}}),a.languages.ts=a.languages.typescript})(Prism);function O(a,e,l){const t=a.slice();return t[13]=e[l].type,t[14]=e[l].description,t[15]=e[l].default,t[16]=e[l].name,t}function Z(a){let e,l,t,n,i,s,r,o,m;return{c(){e=k("div"),l=k("span"),t=M(a[1]),n=A(),i=k("button"),s=M("▼"),u(l,"class","title svelte-1mlh4di"),u(i,"class","toggle-all svelte-1mlh4di"),u(i,"title",r=a[5]?"Close All":"Open All"),u(e,"class","header svelte-1mlh4di")},m(b,y){w(b,e,y),d(e,l),d(l,t),d(e,n),d(e,i),d(i,s),o||(m=P(i,"click",a[6]),o=!0)},p(b,y){y&2&&Q(t,b[1]),y&32&&r!==(r=b[5]?"Close All":"Open All")&&u(i,"title",r)},d(b){b&&F(e),o=!1,m()}}}function D(a){let e=[],l=new Map,t,n=H(a[4]);const i=s=>s[16];for(let s=0;s<n.length;s+=1){let r=O(a,n,s),o=i(r);l.set(o,e[s]=N(o,r))}return{c(){for(let s=0;s<e.length;s+=1)e[s].c();t=U()},m(s,r){for(let o=0;o<e.length;o+=1)e[o]&&e[o].m(s,r);w(s,t,r)},p(s,r){r&21&&(n=H(s[4]),e=x(e,r,i,1,s,n,l,t.parentNode,ee,N,t,O))},d(s){s&&F(t);for(let r=0;r<e.length;r+=1)e[r].d(s)}}}function I(a){let e,l,t;return{c(){e=k("a"),l=k("span"),l.textContent="🔗",u(l,"class","link-icon svelte-1mlh4di"),u(e,"href",t="#"+q(a[16]||"",a[2])),u(e,"class","param-link svelte-1mlh4di")},m(n,i){w(n,e,i),d(e,l)},p(n,i){i&20&&t!==(t="#"+q(n[16]||"",n[2]))&&u(e,"href",t)},d(n){n&&F(e)}}}function j(a){let e,l,t=a[13]+"",n;return{c(){e=M(": "),l=new W(!1),n=U(),l.a=n},m(i,s){w(i,e,s),l.m(t,i,s),w(i,n,s)},p(i,s){s&16&&t!==(t=i[13]+"")&&l.p(t)},d(i){i&&(F(e),F(n),l.d())}}}function V(a){let e,l,t,n,i,s,r=a[15]+"";return{c(){e=k("div"),l=k("span"),l.textContent="default",t=A(),n=k("code"),i=M("= "),s=new W(!1),u(l,"class","svelte-1mlh4di"),te(l,"padding-right","4px"),s.a=null,u(n,"class","svelte-1mlh4di"),u(e,"class","default svelte-1mlh4di"),S(e,"last",!a[14])},m(o,m){w(o,e,m),d(e,l),d(e,t),d(e,n),d(n,i),s.m(r,n)},p(o,m){m&16&&r!==(r=o[15]+"")&&s.p(r),m&16&&S(e,"last",!o[14])},d(o){o&&F(e)}}}function B(a){let e,l,t=R(a[14])+"";return{c(){e=k("div"),l=k("p"),u(e,"class","description svelte-1mlh4di")},m(n,i){w(n,e,i),d(e,l),l.innerHTML=t},p(n,i){i&16&&t!==(t=R(n[14])+"")&&(l.innerHTML=t)},d(n){n&&F(e)}}}function N(a,e){let l,t,n,i,s,r=e[16]+"",o,m,b,y,L,C,h=e[2]&&I(e),_=e[13]&&j(e),f=e[15]&&V(e),c=e[14]&&B(e);return{key:a,first:null,c(){l=k("details"),t=k("summary"),h&&h.c(),n=A(),i=k("pre"),s=k("code"),o=M(r),_&&_.c(),b=A(),f&&f.c(),y=A(),c&&c.c(),L=A(),u(s,"class","svelte-1mlh4di"),u(i,"class",m="language-"+e[0]+" svelte-1mlh4di"),u(t,"class","type svelte-1mlh4di"),u(l,"class","param md svelte-1mlh4di"),u(l,"id",C=e[2]?q(e[16]||"",e[2]):void 0),this.first=l},m(p,g){w(p,l,g),d(l,t),h&&h.m(t,null),d(t,n),d(t,i),d(i,s),d(s,o),_&&_.m(s,null),d(l,b),f&&f.m(l,null),d(l,y),c&&c.m(l,null),d(l,L)},p(p,g){e=p,e[2]?h?h.p(e,g):(h=I(e),h.c(),h.m(t,n)):h&&(h.d(1),h=null),g&16&&r!==(r=e[16]+"")&&Q(o,r),e[13]?_?_.p(e,g):(_=j(e),_.c(),_.m(s,null)):_&&(_.d(1),_=null),g&1&&m!==(m="language-"+e[0]+" svelte-1mlh4di")&&u(i,"class",m),e[15]?f?f.p(e,g):(f=V(e),f.c(),f.m(l,y)):f&&(f.d(1),f=null),e[14]?c?c.p(e,g):(c=B(e),c.c(),c.m(l,L)):c&&(c.d(1),c=null),g&20&&C!==(C=e[2]?q(e[16]||"",e[2]):void 0)&&u(l,"id",C)},d(p){p&&F(l),h&&h.d(),_&&_.d(),f&&f.d(),c&&c.d()}}}function fe(a){let e,l,t=a[1]!==null&&Z(a),n=a[4]&&D(a);return{c(){e=k("div"),t&&t.c(),l=A(),n&&n.c(),u(e,"class","wrap svelte-1mlh4di")},m(i,s){w(i,e,s),t&&t.m(e,null),d(e,l),n&&n.m(e,null),a[9](e)},p(i,[s]){i[1]!==null?t?t.p(i,s):(t=Z(i),t.c(),t.m(e,l)):t&&(t.d(1),t=null),i[4]?n?n.p(i,s):(n=D(i),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},i:E,o:E,d(i){i&&F(e),t&&t.d(),n&&n.d(),a[9](null)}}}function q(a,e){let l="param-";return typeof e=="string"&&(l+=e+"-"),l+a.toLowerCase().replace(/[^a-z0-9]+/g,"-")}function R(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank">$1</a>')}function oe(a,e,l){let{docs:t}=e,{lang:n="python"}=e,{linkify:i=[]}=e,{header:s}=e,{anchor_links:r=!1}=e,o,m,b=!1;function y(f,c){let p=T.highlight(f,T.languages[c],c);for(const g of i)p=p.replace(new RegExp(g,"g"),`<a href="#h-${g.toLocaleLowerCase()}">${g}</a>`);return p}function L(f,c){return f?Object.entries(f).map(([p,{type:g,description:X,default:z}])=>{let Y=g?y(g,c):null;return{name:p,type:Y,description:X,default:z?y(z,c):null}}):[]}function C(){l(5,b=!b),o.querySelectorAll(".param").forEach(c=>{c instanceof HTMLDetailsElement&&(c.open=b)})}$(()=>{window.location.hash&&h(window.location.hash),window.addEventListener("hashchange",f=>{h(window.location.hash)})});function h(f){if(!o)return;const c=f.slice(1),p=o.querySelector(`#${c}`);p instanceof HTMLDetailsElement&&(p.open=!0,p.scrollIntoView({behavior:"smooth"}))}function _(f){le[f?"unshift":"push"](()=>{o=f,l(3,o)})}return a.$$set=f=>{"docs"in f&&l(7,t=f.docs),"lang"in f&&l(0,n=f.lang),"linkify"in f&&l(8,i=f.linkify),"header"in f&&l(1,s=f.header),"anchor_links"in f&&l(2,r=f.anchor_links)},a.$$.update=()=>{a.$$.dirty&129&&l(4,m=L(t,n))},[n,s,r,o,m,b,C,t,i,_]}class ce extends G{constructor(e){super(),J(this,e,oe,fe,K,{docs:7,lang:0,linkify:8,header:1,anchor_links:2})}get docs(){return this.$$.ctx[7]}set docs(e){this.$$set({docs:e}),v()}get lang(){return this.$$.ctx[0]}set lang(e){this.$$set({lang:e}),v()}get linkify(){return this.$$.ctx[8]}set linkify(e){this.$$set({linkify:e}),v()}get header(){return this.$$.ctx[1]}set header(e){this.$$set({header:e}),v()}get anchor_links(){return this.$$.ctx[2]}set anchor_links(e){this.$$set({anchor_links:e}),v()}}function ue(a){let e,l;return e=new ce({props:{docs:a[0],linkify:a[1],header:a[2],anchor_links:a[3]}}),{c(){ne(e.$$.fragment)},m(t,n){ie(e,t,n),l=!0},p(t,[n]){const i={};n&1&&(i.docs=t[0]),n&2&&(i.linkify=t[1]),n&4&&(i.header=t[2]),n&8&&(i.anchor_links=t[3]),e.$set(i)},i(t){l||(ae(e.$$.fragment,t),l=!0)},o(t){se(e.$$.fragment,t),l=!1},d(t){re(e,t)}}}function de(a,e,l){let{value:t}=e,{linkify:n=[]}=e,{header:i=null}=e,{anchor_links:s=!1}=e;return a.$$set=r=>{"value"in r&&l(0,t=r.value),"linkify"in r&&l(1,n=r.linkify),"header"in r&&l(2,i=r.header),"anchor_links"in r&&l(3,s=r.anchor_links)},[t,n,i,s]}class _e extends G{constructor(e){super(),J(this,e,de,ue,K,{value:0,linkify:1,header:2,anchor_links:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get linkify(){return this.$$.ctx[1]}set linkify(e){this.$$set({linkify:e}),v()}get header(){return this.$$.ctx[2]}set header(e){this.$$set({header:e}),v()}get anchor_links(){return this.$$.ctx[3]}set anchor_links(e){this.$$set({anchor_links:e}),v()}}export{_e as default};
//# sourceMappingURL=Index-BCbKKeBP.js.map
