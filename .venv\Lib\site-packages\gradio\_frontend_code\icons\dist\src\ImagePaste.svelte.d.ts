/** @typedef {typeof __propDef.props}  ImagePasteProps */
/** @typedef {typeof __propDef.events}  ImagePasteEvents */
/** @typedef {typeof __propDef.slots}  ImagePasteSlots */
export default class ImagePaste extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type ImagePasteProps = typeof __propDef.props;
export type ImagePasteEvents = typeof __propDef.events;
export type ImagePasteSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
