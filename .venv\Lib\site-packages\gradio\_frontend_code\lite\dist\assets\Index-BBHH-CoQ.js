import{a as De,i as Te,s as Re,f as q,b as W,y as N,z as f,A as Y,d as H,M as J,a1 as Fn,h as _e,t as R,j as ce,k as L,l as P,V as qe,o as ut,R as it,ay as Qe,T as On,p as Ge,O as be,w as ye,x as Se,D as ve,c as oe,m as ie,n as re,e as Fe,ba as In,q as gt,P as Rt,$ as se,C as B,a4 as Ht,bn as Ml,u as wt,r as kt,v as pt,a5 as rt,aq as Be,b8 as xe,b9 as $e,N as je,bo as zl,E as ae,U as vt,aa as Kn,a7 as ze,a8 as Le,K as Un,aB as Vn,W as Pt,B as Yn,Y as Wn,S as Xn,a0 as Jn,a6 as Gn}from"../lite.js";import{d as Ne}from"./index-CnqicUFC.js";import{U as Qn}from"./Upload-Do_omv-N.js";import{M as Zn}from"./MarkdownCode-DVjr71R6.js";import{C as xn}from"./Copy-DcTA0nce.js";import{M as $n,a as es}from"./Minimize-DOBO88I3.js";import{d as ts}from"./dsv-DB8NKgIY.js";import{default as Mo}from"./Example-B_2alvl-.js";/* empty css                                             */function Nt(n){let e,t,l;return{c(){e=N("input"),f(e,"role","textbox"),f(e,"tabindex","-1"),f(e,"class","svelte-1xjy58k"),Y(e,"header",n[4])},m(s,o){H(s,e,o),n[24](e),it(e,n[11]),t||(l=[J(e,"input",n[25]),J(e,"blur",n[14]),J(e,"mousedown",Qe(n[21])),J(e,"mouseup",Qe(n[22])),J(e,"click",Qe(n[23])),On(n[13].call(null,e)),J(e,"keydown",n[15])],t=!0)},p(s,o){o&2048&&e.value!==s[11]&&it(e,s[11]),o&16&&Y(e,"header",s[4])},d(s){s&&P(e),n[24](null),t=!1,qe(l)}}}function ls(n){let e=(n[8]?n[12]:n[2]||n[12])+"",t;return{c(){t=ye(e)},m(l,s){H(l,t,s)},p(l,s){s&4356&&e!==(e=(l[8]?l[12]:l[2]||l[12])+"")&&Se(t,e)},i:ve,o:ve,d(l){l&&P(t)}}}function ns(n){let e,t;return e=new Zn({props:{message:n[12].toLocaleString(),latex_delimiters:n[6],line_breaks:n[7],chatbot:!1,root:n[9]}}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},p(l,s){const o={};s&4096&&(o.message=l[12].toLocaleString()),s&64&&(o.latex_delimiters=l[6]),s&128&&(o.line_breaks=l[7]),s&512&&(o.root=l[9]),e.$set(o)},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function ss(n){let e,t;return{c(){e=new In(!1),t=Fe(),e.a=t},m(l,s){e.m(n[12],l,s),H(l,t,s)},p(l,s){s&4096&&e.p(l[12])},i:ve,o:ve,d(l){l&&(P(t),e.d())}}}function os(n){let e,t,l,s,o,i,r,_=n[1]&&Nt(n);const c=[ss,ns,ls],u=[];function h(d,g){return d[5]==="html"?0:d[5]==="markdown"?1:2}return l=h(n),s=u[l]=c[l](n),{c(){_&&_.c(),e=W(),t=N("span"),s.c(),f(t,"tabindex","0"),f(t,"role","button"),f(t,"style",n[3]),f(t,"data-editable",n[8]),f(t,"placeholder"," "),f(t,"class","svelte-1xjy58k"),Y(t,"edit",n[1]),Y(t,"expanded",n[10]),Y(t,"multiline",n[4])},m(d,g){_&&_.m(d,g),H(d,e,g),H(d,t,g),u[l].m(t,null),o=!0,i||(r=[J(t,"click",n[16]),J(t,"keydown",n[15]),J(t,"focus",Fn(n[20]))],i=!0)},p(d,[g]){d[1]?_?_.p(d,g):(_=Nt(d),_.c(),_.m(e.parentNode,e)):_&&(_.d(1),_=null);let z=l;l=h(d),l===z?u[l].p(d,g):(_e(),R(u[z],1,1,()=>{u[z]=null}),ce(),s=u[l],s?s.p(d,g):(s=u[l]=c[l](d),s.c()),L(s,1),s.m(t,null)),(!o||g&8)&&f(t,"style",d[3]),(!o||g&256)&&f(t,"data-editable",d[8]),(!o||g&2)&&Y(t,"edit",d[1]),(!o||g&1024)&&Y(t,"expanded",d[10]),(!o||g&16)&&Y(t,"multiline",d[4])},i(d){o||(L(s),o=!0)},o(d){R(s),o=!1},d(d){d&&(P(e),P(t)),_&&_.d(d),u[l].d(),i=!1,qe(r)}}}function is(n,e=null){const t=String(n);return!e||t.length<=e?t:t.slice(0,e)+"..."}function rs(n,e,t){let l,s,{edit:o}=e,{value:i=""}=e,{display_value:r=null}=e,{styling:_=""}=e,{header:c=!1}=e,{datatype:u="str"}=e,{latex_delimiters:h}=e,{clear_on_focus:d=!1}=e,{line_breaks:g=!0}=e,{editable:z=!0}=e,{root:F}=e,{max_chars:O=null}=e;const w=ut();let k=!1,{el:v}=e;function T(y){return d&&t(11,l=""),requestAnimationFrame(()=>{y.focus()}),{}}function M({currentTarget:y}){t(17,i=y.value),w("blur")}function j(y){y.key==="Enter"&&(o?(t(17,i=l),w("blur")):c||t(10,k=!k)),w("keydown",y)}function U(){!o&&!c&&t(10,k=!k)}function ee(y){Ge.call(this,n,y)}function te(y){Ge.call(this,n,y)}function Q(y){Ge.call(this,n,y)}function C(y){Ge.call(this,n,y)}function V(y){be[y?"unshift":"push"](()=>{v=y,t(0,v)})}function Z(){l=this.value,t(11,l),t(17,i)}return n.$$set=y=>{"edit"in y&&t(1,o=y.edit),"value"in y&&t(17,i=y.value),"display_value"in y&&t(2,r=y.display_value),"styling"in y&&t(3,_=y.styling),"header"in y&&t(4,c=y.header),"datatype"in y&&t(5,u=y.datatype),"latex_delimiters"in y&&t(6,h=y.latex_delimiters),"clear_on_focus"in y&&t(18,d=y.clear_on_focus),"line_breaks"in y&&t(7,g=y.line_breaks),"editable"in y&&t(8,z=y.editable),"root"in y&&t(9,F=y.root),"max_chars"in y&&t(19,O=y.max_chars),"el"in y&&t(0,v=y.el)},n.$$.update=()=>{n.$$.dirty&131072&&t(11,l=i),n.$$.dirty&656388&&t(12,s=k?i:is(r||i,O))},[v,o,r,_,c,u,h,g,z,F,k,l,s,T,M,j,U,i,d,O,ee,te,Q,C,V,Z]}class at extends De{constructor(e){super(),Te(this,e,rs,os,Re,{edit:1,value:17,display_value:2,styling:3,header:4,datatype:5,latex_delimiters:6,clear_on_focus:18,line_breaks:7,editable:8,root:9,max_chars:19,el:0})}get edit(){return this.$$.ctx[1]}set edit(e){this.$$set({edit:e}),q()}get value(){return this.$$.ctx[17]}set value(e){this.$$set({value:e}),q()}get display_value(){return this.$$.ctx[2]}set display_value(e){this.$$set({display_value:e}),q()}get styling(){return this.$$.ctx[3]}set styling(e){this.$$set({styling:e}),q()}get header(){return this.$$.ctx[4]}set header(e){this.$$set({header:e}),q()}get datatype(){return this.$$.ctx[5]}set datatype(e){this.$$set({datatype:e}),q()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),q()}get clear_on_focus(){return this.$$.ctx[18]}set clear_on_focus(e){this.$$set({clear_on_focus:e}),q()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),q()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),q()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),q()}get max_chars(){return this.$$.ctx[19]}set max_chars(e){this.$$set({max_chars:e}),q()}get el(){return this.$$.ctx[0]}set el(e){this.$$set({el:e}),q()}}const us=n=>({}),Ft=n=>({});function Ot(n,e,t){const l=n.slice();return l[37]=e[t],l}const as=n=>({item:n[0]&1024,index:n[0]&1024}),It=n=>({item:n[37].data,index:n[37].index}),fs=n=>({}),Kt=n=>({});function Ut(n){let e=[],t=new Map,l,s,o=Be(n[10]);const i=r=>r[37].data[0].id;for(let r=0;r<o.length;r+=1){let _=Ot(n,o,r),c=i(_);t.set(c,e[r]=Vt(c,_))}return{c(){for(let r=0;r<e.length;r+=1)e[r].c();l=Fe()},m(r,_){for(let c=0;c<e.length;c+=1)e[c]&&e[c].m(r,_);H(r,l,_),s=!0},p(r,_){_[0]&2098176&&(o=Be(r[10]),_e(),e=xe(e,_,i,1,r,o,t,l.parentNode,$e,Vt,l,Ot),ce())},i(r){if(!s){for(let _=0;_<o.length;_+=1)L(e[_]);s=!0}},o(r){for(let _=0;_<e.length;_+=1)R(e[_]);s=!1},d(r){r&&P(l);for(let _=0;_<e.length;_+=1)e[_].d(r)}}}function _s(n){let e;return{c(){e=ye(`Missing Table Row
						`)},m(t,l){H(t,e,l)},d(t){t&&P(e)}}}function Vt(n,e){let t,l;const s=e[22].tbody,o=gt(s,e,e[21],It),i=o||_s();return{key:n,first:null,c(){t=Fe(),i&&i.c(),this.first=t},m(r,_){H(r,t,_),i&&i.m(r,_),l=!0},p(r,_){e=r,o&&o.p&&(!l||_[0]&2098176)&&wt(o,s,e,e[21],l?pt(s,e[21],_,as):kt(e[21]),It)},i(r){l||(L(i,r),l=!0)},o(r){R(i,r),l=!1},d(r){r&&P(t),i&&i.d(r)}}}function cs(n){let e,t,l,s,o,i,r,_,c,u,h,d,g,z;const F=n[22].thead,O=gt(F,n,n[21],Kt);let w=n[10].length&&n[10][0].data.length&&Ut(n);const k=n[22].tfoot,v=gt(k,n,n[21],Ft);return{c(){e=N("svelte-virtual-table-viewport"),t=N("div"),l=N("table"),s=N("thead"),O&&O.c(),i=W(),r=N("tbody"),w&&w.c(),_=W(),c=N("tfoot"),v&&v.c(),f(s,"class","thead svelte-y11bhb"),Rt(()=>n[23].call(s)),f(r,"class","tbody svelte-y11bhb"),f(c,"class","tfoot svelte-y11bhb"),Rt(()=>n[25].call(c)),f(l,"class","table svelte-y11bhb"),se(l,"height",hs),se(l,"--bw-svt-p-top",n[8]+"px"),se(l,"--bw-svt-p-bottom",n[4]+"px"),se(l,"--bw-svt-head-height",n[6]+"px"),se(l,"--bw-svt-foot-height",n[7]+"px"),se(l,"--bw-svt-avg-row-height",n[2]+"px"),se(l,"--max-height",n[0]+"px"),Y(l,"disable-scroll",n[1])},m(T,M){H(T,e,M),B(e,t),B(t,l),B(l,s),O&&O.m(s,null),o=Ht(s,n[23].bind(s)),B(l,i),B(l,r),w&&w.m(r,null),n[24](r),B(l,_),B(l,c),v&&v.m(c,null),u=Ht(c,n[25].bind(c)),n[26](l),h=Ml.observe(l,n[27].bind(l)),d=!0,g||(z=J(l,"scroll",n[11]),g=!0)},p(T,M){O&&O.p&&(!d||M[0]&2097152)&&wt(O,F,T,T[21],d?pt(F,T[21],M,fs):kt(T[21]),Kt),T[10].length&&T[10][0].data.length?w?(w.p(T,M),M[0]&1024&&L(w,1)):(w=Ut(T),w.c(),L(w,1),w.m(r,null)):w&&(_e(),R(w,1,1,()=>{w=null}),ce()),v&&v.p&&(!d||M[0]&2097152)&&wt(v,k,T,T[21],d?pt(k,T[21],M,us):kt(T[21]),Ft),(!d||M[0]&256)&&se(l,"--bw-svt-p-top",T[8]+"px"),(!d||M[0]&16)&&se(l,"--bw-svt-p-bottom",T[4]+"px"),(!d||M[0]&64)&&se(l,"--bw-svt-head-height",T[6]+"px"),(!d||M[0]&128)&&se(l,"--bw-svt-foot-height",T[7]+"px"),(!d||M[0]&4)&&se(l,"--bw-svt-avg-row-height",T[2]+"px"),(!d||M[0]&1)&&se(l,"--max-height",T[0]+"px"),(!d||M[0]&2)&&Y(l,"disable-scroll",T[1])},i(T){d||(L(O,T),L(w),L(v,T),d=!0)},o(T){R(O,T),R(w),R(v,T),d=!1},d(T){T&&P(e),O&&O.d(T),o(),w&&w.d(),n[24](null),v&&v.d(T),u(),n[26](null),h(),g=!1,z()}}}let hs="100%";function ds(n,e){if(!n)return 0;const t=getComputedStyle(n);return parseInt(t.getPropertyValue(e))}function ms(n,e,t){let l,{$$slots:s={},$$scope:o}=e,{items:i=[]}=e,{max_height:r}=e,{actual_height:_}=e,{table_scrollbar_width:c}=e,{start:u=0}=e,{end:h=20}=e,{selected:d}=e,{disable_scroll:g=!1}=e,z=30,F=0,O,w=0,k=0,v=[],T,M,j=0,U,ee=200,te=[],Q;const C=typeof window<"u",V=C?window.requestAnimationFrame:A=>A();let Z=0;async function y(A){if(ee===0)return;t(6,w=U.querySelector(".thead")?.getBoundingClientRect().height||0),await je();const{scrollTop:b}=U;t(15,c=U.offsetWidth-U.clientWidth),Z=j-(b-w);let p=u;for(;Z<r&&p<A.length;){let ue=M[p-u];ue||(t(13,h=p+1),await je(),ue=M[p-u]);let pe=ue?.getBoundingClientRect().height;pe||(pe=z);const Ie=v[p]=pe;Z+=Ie,p+=1}t(13,h=p);const X=A.length-h,E=U.offsetHeight-U.clientHeight;E>0&&(Z+=E);let ge=v.filter(ue=>typeof ue=="number");t(2,z=ge.reduce((ue,pe)=>ue+pe,0)/ge.length),t(4,F=X*z),v.length=A.length,await je(),r?Z<r?t(14,_=Z+2):t(14,_=r):t(14,_=Z+1),await je()}async function I(A){V(async()=>{if(typeof A!="number")return;const b=typeof A!="number"?!1:we(A);b!==!0&&(b==="back"&&await he(A,{behavior:"instant"}),b==="forwards"&&await he(A,{behavior:"instant"},!0))})}function we(A){const b=M&&M[A-u];if(!b&&A<u)return"back";if(!b&&A>=h-1)return"forwards";const{top:p}=U.getBoundingClientRect(),{top:X,bottom:E}=b.getBoundingClientRect();return X-p<37?"back":E-p>ee?"forwards":!0}async function Ce(A){const b=U.scrollTop;M=O.children;const p=l.length<u,X=ds(M[1],"border-top-width"),E=0;p&&await he(l.length-1,{behavior:"auto"});let ge=0;for(let D=0;D<M.length;D+=1)v[u+D]=M[D].getBoundingClientRect().height;let ue=0,pe=w+X/2,Ie=[];for(;ue<l.length;){const D=v[ue]||z;if(Ie[ue]=D,pe+D+E>b){ge=ue,t(8,j=pe-(w+X/2));break}pe+=D,ue+=1}for(ge=Math.max(0,ge);ue<l.length;){const D=v[ue]||z;if(pe+=D,ue+=1,pe>b+ee)break}t(12,u=ge),t(13,h=ue);const fe=l.length-h;h===0&&t(13,h=10),t(2,z=(pe-w)/h);let He=fe*z;for(;ue<l.length;)ue+=1,v[ue]=z;t(4,F=He),isFinite(F)||t(4,F=2e5)}async function he(A,b,p=!1){await je();const X=z;let E=A*X;p&&(E=E-ee+X+w);const ge=U.offsetHeight-U.clientHeight;ge>0&&(E+=ge);const ue={top:E,behavior:"smooth",...b};U.scrollTo(ue)}rt(()=>{M=O.children,t(19,T=!0),y(i)});function G(){w=this.offsetHeight,t(6,w)}function ke(A){be[A?"unshift":"push"](()=>{O=A,t(5,O)})}function $(){k=this.offsetHeight,t(7,k)}function le(A){be[A?"unshift":"push"](()=>{U=A,t(9,U)})}function x(){Q=zl.entries.get(this)?.contentRect,t(3,Q)}return n.$$set=A=>{"items"in A&&t(16,i=A.items),"max_height"in A&&t(0,r=A.max_height),"actual_height"in A&&t(14,_=A.actual_height),"table_scrollbar_width"in A&&t(15,c=A.table_scrollbar_width),"start"in A&&t(12,u=A.start),"end"in A&&t(13,h=A.end),"selected"in A&&t(17,d=A.selected),"disable_scroll"in A&&t(1,g=A.disable_scroll),"$$scope"in A&&t(21,o=A.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&8&&(ee=Q?.height||200),n.$$.dirty[0]&65536&&t(20,l=i),n.$$.dirty[0]&1572864&&T&&V(()=>y(l)),n.$$.dirty[0]&131072&&I(d),n.$$.dirty[0]&1060869&&t(10,te=C?l.slice(u,h).map((A,b)=>({index:b+u,data:A})):l.slice(0,r/l.length*z+1).map((A,b)=>({index:b+u,data:A})))},[r,g,z,Q,F,O,w,k,j,U,te,Ce,u,h,_,c,i,d,he,T,l,o,s,G,ke,$,le,x]}class bs extends De{constructor(e){super(),Te(this,e,ms,cs,Re,{items:16,max_height:0,actual_height:14,table_scrollbar_width:15,start:12,end:13,selected:17,disable_scroll:1,scroll_to_index:18},null,[-1,-1])}get items(){return this.$$.ctx[16]}set items(e){this.$$set({items:e}),q()}get max_height(){return this.$$.ctx[0]}set max_height(e){this.$$set({max_height:e}),q()}get actual_height(){return this.$$.ctx[14]}set actual_height(e){this.$$set({actual_height:e}),q()}get table_scrollbar_width(){return this.$$.ctx[15]}set table_scrollbar_width(e){this.$$set({table_scrollbar_width:e}),q()}get start(){return this.$$.ctx[12]}set start(e){this.$$set({start:e}),q()}get end(){return this.$$.ctx[13]}set end(e){this.$$set({end:e}),q()}get selected(){return this.$$.ctx[17]}set selected(e){this.$$set({selected:e}),q()}get disable_scroll(){return this.$$.ctx[1]}set disable_scroll(e){this.$$set({disable_scroll:e}),q()}get scroll_to_index(){return this.$$.ctx[18]}}function gs(n){let e,t,l;return{c(){e=ae("svg"),t=ae("rect"),l=ae("path"),f(t,"x","10"),f(t,"y","5"),f(t,"width","4"),f(t,"height","14"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(l,"d","M7 8L17 16M17 8L7 16"),f(l,"stroke","currentColor"),f(l,"stroke-width","2"),f(l,"stroke-linecap","round"),f(e,"viewBox","0 0 24 24"),f(e,"width","16"),f(e,"height","16")},m(s,o){H(s,e,o),B(e,t),B(e,l)},d(s){s&&P(e)}}}function ws(n){let e,t,l;return{c(){e=ae("svg"),t=ae("rect"),l=ae("path"),f(t,"x","5"),f(t,"y","10"),f(t,"width","14"),f(t,"height","4"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(l,"d","M8 7L16 17M16 7L8 17"),f(l,"stroke","currentColor"),f(l,"stroke-width","2"),f(l,"stroke-linecap","round"),f(e,"viewBox","0 0 24 24"),f(e,"width","16"),f(e,"height","16")},m(s,o){H(s,e,o),B(e,t),B(e,l)},d(s){s&&P(e)}}}function ks(n){let e,t,l;return{c(){e=ae("svg"),t=ae("rect"),l=ae("path"),f(t,"x","6"),f(t,"y","4"),f(t,"width","12"),f(t,"height","4"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(l,"d","M12 12V19M8 16L12 19L16 16"),f(l,"stroke","currentColor"),f(l,"stroke-width","2"),f(l,"fill","none"),f(l,"stroke-linecap","round"),f(e,"viewBox","0 0 24 24"),f(e,"width","16"),f(e,"height","16")},m(s,o){H(s,e,o),B(e,t),B(e,l)},d(s){s&&P(e)}}}function ps(n){let e,t,l;return{c(){e=ae("svg"),t=ae("rect"),l=ae("path"),f(t,"x","6"),f(t,"y","16"),f(t,"width","12"),f(t,"height","4"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(l,"d","M12 12V5M8 8L12 5L16 8"),f(l,"stroke","currentColor"),f(l,"stroke-width","2"),f(l,"fill","none"),f(l,"stroke-linecap","round"),f(e,"viewBox","0 0 24 24"),f(e,"width","16"),f(e,"height","16")},m(s,o){H(s,e,o),B(e,t),B(e,l)},d(s){s&&P(e)}}}function vs(n){let e,t,l;return{c(){e=ae("svg"),t=ae("rect"),l=ae("path"),f(t,"x","16"),f(t,"y","6"),f(t,"width","4"),f(t,"height","12"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(t,"fill","none"),f(l,"d","M12 12H5M8 8L5 12L8 16"),f(l,"stroke","currentColor"),f(l,"stroke-width","2"),f(l,"fill","none"),f(l,"stroke-linecap","round"),f(e,"viewBox","0 0 24 24"),f(e,"width","16"),f(e,"height","16")},m(s,o){H(s,e,o),B(e,t),B(e,l)},d(s){s&&P(e)}}}function ys(n){let e,t,l;return{c(){e=ae("svg"),t=ae("rect"),l=ae("path"),f(t,"x","4"),f(t,"y","6"),f(t,"width","4"),f(t,"height","12"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(t,"fill","none"),f(l,"d","M12 12H19M16 8L19 12L16 16"),f(l,"stroke","currentColor"),f(l,"stroke-width","2"),f(l,"fill","none"),f(l,"stroke-linecap","round"),f(e,"viewBox","0 0 24 24"),f(e,"width","16"),f(e,"height","16")},m(s,o){H(s,e,o),B(e,t),B(e,l)},d(s){s&&P(e)}}}function qs(n){let e;function t(o,i){if(o[0]=="add-column-right")return ys;if(o[0]=="add-column-left")return vs;if(o[0]=="add-row-above")return ps;if(o[0]=="add-row-below")return ks;if(o[0]=="delete-row")return ws;if(o[0]=="delete-column")return gs}let l=t(n),s=l&&l(n);return{c(){s&&s.c(),e=Fe()},m(o,i){s&&s.m(o,i),H(o,e,i)},p(o,[i]){l!==(l=t(o))&&(s&&s.d(1),s=l&&l(o),s&&(s.c(),s.m(e.parentNode,e)))},i:ve,o:ve,d(o){o&&P(e),s&&s.d(o)}}}function Cs(n,e,t){let{icon:l}=e;return n.$$set=s=>{"icon"in s&&t(0,l=s.icon)},[l]}class Oe extends De{constructor(e){super(),Te(this,e,Cs,qs,Re,{icon:0})}get icon(){return this.$$.ctx[0]}set icon(e){this.$$set({icon:e}),q()}}function Yt(n){let e,t,l,s=n[8]("dataframe.add_row_above")+"",o,i,r,_,c,u=n[8]("dataframe.add_row_below")+"",h,d,g,z,F,O;t=new Oe({props:{icon:"add-row-above"}}),_=new Oe({props:{icon:"add-row-below"}});let w=n[6]&&Wt(n);return{c(){e=N("button"),oe(t.$$.fragment),l=W(),o=ye(s),i=W(),r=N("button"),oe(_.$$.fragment),c=W(),h=ye(u),d=W(),w&&w.c(),g=Fe(),f(e,"class","svelte-1d2u1p1"),f(r,"class","svelte-1d2u1p1")},m(k,v){H(k,e,v),ie(t,e,null),B(e,l),B(e,o),H(k,i,v),H(k,r,v),ie(_,r,null),B(r,c),B(r,h),H(k,d,v),w&&w.m(k,v),H(k,g,v),z=!0,F||(O=[J(e,"click",n[18]),J(r,"click",n[19])],F=!0)},p(k,v){(!z||v&256)&&s!==(s=k[8]("dataframe.add_row_above")+"")&&Se(o,s),(!z||v&256)&&u!==(u=k[8]("dataframe.add_row_below")+"")&&Se(h,u),k[6]?w?(w.p(k,v),v&64&&L(w,1)):(w=Wt(k),w.c(),L(w,1),w.m(g.parentNode,g)):w&&(_e(),R(w,1,1,()=>{w=null}),ce())},i(k){z||(L(t.$$.fragment,k),L(_.$$.fragment,k),L(w),z=!0)},o(k){R(t.$$.fragment,k),R(_.$$.fragment,k),R(w),z=!1},d(k){k&&(P(e),P(i),P(r),P(d),P(g)),re(t),re(_),w&&w.d(k),F=!1,qe(O)}}}function Wt(n){let e,t,l,s=n[8]("dataframe.delete_row")+"",o,i,r,_;return t=new Oe({props:{icon:"delete-row"}}),{c(){e=N("button"),oe(t.$$.fragment),l=W(),o=ye(s),f(e,"class","delete svelte-1d2u1p1")},m(c,u){H(c,e,u),ie(t,e,null),B(e,l),B(e,o),i=!0,r||(_=J(e,"click",function(){vt(n[4])&&n[4].apply(this,arguments)}),r=!0)},p(c,u){n=c,(!i||u&256)&&s!==(s=n[8]("dataframe.delete_row")+"")&&Se(o,s)},i(c){i||(L(t.$$.fragment,c),i=!0)},o(c){R(t.$$.fragment,c),i=!1},d(c){c&&P(e),re(t),r=!1,_()}}}function Xt(n){let e,t,l,s=n[8]("dataframe.add_column_left")+"",o,i,r,_,c,u=n[8]("dataframe.add_column_right")+"",h,d,g,z,F,O;t=new Oe({props:{icon:"add-column-left"}}),_=new Oe({props:{icon:"add-column-right"}});let w=n[7]&&Jt(n);return{c(){e=N("button"),oe(t.$$.fragment),l=W(),o=ye(s),i=W(),r=N("button"),oe(_.$$.fragment),c=W(),h=ye(u),d=W(),w&&w.c(),g=Fe(),f(e,"class","svelte-1d2u1p1"),f(r,"class","svelte-1d2u1p1")},m(k,v){H(k,e,v),ie(t,e,null),B(e,l),B(e,o),H(k,i,v),H(k,r,v),ie(_,r,null),B(r,c),B(r,h),H(k,d,v),w&&w.m(k,v),H(k,g,v),z=!0,F||(O=[J(e,"click",n[20]),J(r,"click",n[21])],F=!0)},p(k,v){(!z||v&256)&&s!==(s=k[8]("dataframe.add_column_left")+"")&&Se(o,s),(!z||v&256)&&u!==(u=k[8]("dataframe.add_column_right")+"")&&Se(h,u),k[7]?w?(w.p(k,v),v&128&&L(w,1)):(w=Jt(k),w.c(),L(w,1),w.m(g.parentNode,g)):w&&(_e(),R(w,1,1,()=>{w=null}),ce())},i(k){z||(L(t.$$.fragment,k),L(_.$$.fragment,k),L(w),z=!0)},o(k){R(t.$$.fragment,k),R(_.$$.fragment,k),R(w),z=!1},d(k){k&&(P(e),P(i),P(r),P(d),P(g)),re(t),re(_),w&&w.d(k),F=!1,qe(O)}}}function Jt(n){let e,t,l,s=n[8]("dataframe.delete_column")+"",o,i,r,_;return t=new Oe({props:{icon:"delete-column"}}),{c(){e=N("button"),oe(t.$$.fragment),l=W(),o=ye(s),f(e,"class","delete svelte-1d2u1p1")},m(c,u){H(c,e,u),ie(t,e,null),B(e,l),B(e,o),i=!0,r||(_=J(e,"click",function(){vt(n[5])&&n[5].apply(this,arguments)}),r=!0)},p(c,u){n=c,(!i||u&256)&&s!==(s=n[8]("dataframe.delete_column")+"")&&Se(o,s)},i(c){i||(L(t.$$.fragment,c),i=!0)},o(c){R(t.$$.fragment,c),i=!1},d(c){c&&P(e),re(t),r=!1,_()}}}function As(n){let e,t,l,s=!n[12]&&n[11]&&Yt(n),o=n[10]&&Xt(n);return{c(){e=N("div"),s&&s.c(),t=W(),o&&o.c(),f(e,"class","cell-menu svelte-1d2u1p1")},m(i,r){H(i,e,r),s&&s.m(e,null),B(e,t),o&&o.m(e,null),n[22](e),l=!0},p(i,[r]){!i[12]&&i[11]?s?(s.p(i,r),r&6144&&L(s,1)):(s=Yt(i),s.c(),L(s,1),s.m(e,t)):s&&(_e(),R(s,1,1,()=>{s=null}),ce()),i[10]?o?(o.p(i,r),r&1024&&L(o,1)):(o=Xt(i),o.c(),L(o,1),o.m(e,null)):o&&(_e(),R(o,1,1,()=>{o=null}),ce())},i(i){l||(L(s),L(o),l=!0)},o(i){R(s),R(o),l=!1},d(i){i&&P(e),s&&s.d(),o&&o.d(),n[22](null)}}}function Es(n,e,t){let l,s,o,{x:i}=e,{y:r}=e,{on_add_row_above:_}=e,{on_add_row_below:c}=e,{on_add_column_left:u}=e,{on_add_column_right:h}=e,{row:d}=e,{col_count:g}=e,{row_count:z}=e,{on_delete_row:F}=e,{on_delete_col:O}=e,{can_delete_rows:w}=e,{can_delete_cols:k}=e,{i18n:v}=e,T;rt(()=>{M()});function M(){if(!T)return;const C=window.innerWidth,V=window.innerHeight,Z=T.getBoundingClientRect();let y=i-30,I=r-20;y+Z.width>C&&(y=i-Z.width+10),I+Z.height>V&&(I=r-Z.height+10),t(9,T.style.left=`${y}px`,T),t(9,T.style.top=`${I}px`,T)}const j=()=>_(),U=()=>c(),ee=()=>u(),te=()=>h();function Q(C){be[C?"unshift":"push"](()=>{T=C,t(9,T)})}return n.$$set=C=>{"x"in C&&t(13,i=C.x),"y"in C&&t(14,r=C.y),"on_add_row_above"in C&&t(0,_=C.on_add_row_above),"on_add_row_below"in C&&t(1,c=C.on_add_row_below),"on_add_column_left"in C&&t(2,u=C.on_add_column_left),"on_add_column_right"in C&&t(3,h=C.on_add_column_right),"row"in C&&t(15,d=C.row),"col_count"in C&&t(16,g=C.col_count),"row_count"in C&&t(17,z=C.row_count),"on_delete_row"in C&&t(4,F=C.on_delete_row),"on_delete_col"in C&&t(5,O=C.on_delete_col),"can_delete_rows"in C&&t(6,w=C.can_delete_rows),"can_delete_cols"in C&&t(7,k=C.can_delete_cols),"i18n"in C&&t(8,v=C.i18n)},n.$$.update=()=>{n.$$.dirty&32768&&t(12,l=d===-1),n.$$.dirty&131072&&t(11,s=z[1]==="dynamic"),n.$$.dirty&65536&&t(10,o=g[1]==="dynamic")},[_,c,u,h,F,O,w,k,v,T,o,s,l,i,r,d,g,z,j,U,ee,te,Q]}class Ll extends De{constructor(e){super(),Te(this,e,Es,As,Re,{x:13,y:14,on_add_row_above:0,on_add_row_below:1,on_add_column_left:2,on_add_column_right:3,row:15,col_count:16,row_count:17,on_delete_row:4,on_delete_col:5,can_delete_rows:6,can_delete_cols:7,i18n:8})}get x(){return this.$$.ctx[13]}set x(e){this.$$set({x:e}),q()}get y(){return this.$$.ctx[14]}set y(e){this.$$set({y:e}),q()}get on_add_row_above(){return this.$$.ctx[0]}set on_add_row_above(e){this.$$set({on_add_row_above:e}),q()}get on_add_row_below(){return this.$$.ctx[1]}set on_add_row_below(e){this.$$set({on_add_row_below:e}),q()}get on_add_column_left(){return this.$$.ctx[2]}set on_add_column_left(e){this.$$set({on_add_column_left:e}),q()}get on_add_column_right(){return this.$$.ctx[3]}set on_add_column_right(e){this.$$set({on_add_column_right:e}),q()}get row(){return this.$$.ctx[15]}set row(e){this.$$set({row:e}),q()}get col_count(){return this.$$.ctx[16]}set col_count(e){this.$$set({col_count:e}),q()}get row_count(){return this.$$.ctx[17]}set row_count(e){this.$$set({row_count:e}),q()}get on_delete_row(){return this.$$.ctx[4]}set on_delete_row(e){this.$$set({on_delete_row:e}),q()}get on_delete_col(){return this.$$.ctx[5]}set on_delete_col(e){this.$$set({on_delete_col:e}),q()}get can_delete_rows(){return this.$$.ctx[6]}set can_delete_rows(e){this.$$set({can_delete_rows:e}),q()}get can_delete_cols(){return this.$$.ctx[7]}set can_delete_cols(e){this.$$set({can_delete_cols:e}),q()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),q()}}function Ms(n){let e,t;return{c(){e=ae("svg"),t=ae("path"),f(t,"d","M4 4h16v2.67l-6.67 6.67v8L9.33 19v-5.66L2.67 6.67V4h1.33z"),f(t,"stroke","currentColor"),f(t,"stroke-width","2"),f(t,"stroke-linecap","round"),f(t,"stroke-linejoin","round"),f(e,"viewBox","0 0 24 24"),f(e,"fill","none"),f(e,"xmlns","http://www.w3.org/2000/svg")},m(l,s){H(l,e,s),B(e,t)},p:ve,i:ve,o:ve,d(l){l&&P(e)}}}function zs(n){return[]}class Sl extends De{constructor(e){super(),Te(this,e,zs,Ms,Re,{})}}function Gt(n){let e,t,l,s,o,i,r=n[0]&&n[3]==="filter"&&Qt(n);return{c(){e=N("div"),t=N("input"),l=W(),r&&r.c(),f(t,"type","text"),f(t,"placeholder","Search..."),f(t,"class","search-input svelte-b1nr0g"),f(e,"class","search-container svelte-b1nr0g")},m(_,c){H(_,e,c),B(e,t),it(t,n[0]),B(e,l),r&&r.m(e,null),s=!0,o||(i=J(t,"input",n[10]),o=!0)},p(_,c){c&1&&t.value!==_[0]&&it(t,_[0]),_[0]&&_[3]==="filter"?r?(r.p(_,c),c&9&&L(r,1)):(r=Qt(_),r.c(),L(r,1),r.m(e,null)):r&&(_e(),R(r,1,1,()=>{r=null}),ce())},i(_){s||(L(r),s=!0)},o(_){R(r),s=!1},d(_){_&&P(e),r&&r.d(),o=!1,i()}}}function Qt(n){let e,t,l,s,o;return t=new Sl({}),{c(){e=N("button"),oe(t.$$.fragment),f(e,"class","toolbar-button check-button svelte-b1nr0g"),f(e,"aria-label","Apply filter and update dataframe values"),f(e,"title","Apply filter and update dataframe values")},m(i,r){H(i,e,r),ie(t,e,null),l=!0,s||(o=J(e,"click",function(){vt(n[5])&&n[5].apply(this,arguments)}),s=!0)},p(i,r){n=i},i(i){l||(L(t.$$.fragment,i),l=!0)},o(i){R(t.$$.fragment,i),l=!1},d(i){i&&P(e),re(t),s=!1,o()}}}function Zt(n){let e,t,l,s,o,i,r,_;const c=[Ss,Ls],u=[];function h(d,g){return d[6]?0:1}return t=h(n),l=u[t]=c[t](n),{c(){e=N("button"),l.c(),f(e,"class","toolbar-button svelte-b1nr0g"),f(e,"aria-label",s=n[6]?"Copied to clipboard":"Copy table data"),f(e,"title",o=n[6]?"Copied to clipboard":"Copy table data")},m(d,g){H(d,e,g),u[t].m(e,null),i=!0,r||(_=J(e,"click",n[7]),r=!0)},p(d,g){let z=t;t=h(d),t!==z&&(_e(),R(u[z],1,1,()=>{u[z]=null}),ce(),l=u[t],l||(l=u[t]=c[t](d),l.c()),L(l,1),l.m(e,null)),(!i||g&64&&s!==(s=d[6]?"Copied to clipboard":"Copy table data"))&&f(e,"aria-label",s),(!i||g&64&&o!==(o=d[6]?"Copied to clipboard":"Copy table data"))&&f(e,"title",o)},i(d){i||(L(l),i=!0)},o(d){R(l),i=!1},d(d){d&&P(e),u[t].d(),r=!1,_()}}}function Ls(n){let e,t;return e=new xn({}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function Ss(n){let e,t;return e=new Sl({}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function xt(n){let e,t,l,s,o,i,r,_;const c=[js,Bs],u=[];function h(d,g){return d[4]?0:1}return t=h(n),l=u[t]=c[t](n),{c(){e=N("button"),l.c(),f(e,"class","toolbar-button svelte-b1nr0g"),f(e,"aria-label",s=n[4]?"Exit fullscreen":"Enter fullscreen"),f(e,"title",o=n[4]?"Exit fullscreen":"Enter fullscreen")},m(d,g){H(d,e,g),u[t].m(e,null),i=!0,r||(_=J(e,"click",n[9]),r=!0)},p(d,g){let z=t;t=h(d),t!==z&&(_e(),R(u[z],1,1,()=>{u[z]=null}),ce(),l=u[t],l||(l=u[t]=c[t](d),l.c()),L(l,1),l.m(e,null)),(!i||g&16&&s!==(s=d[4]?"Exit fullscreen":"Enter fullscreen"))&&f(e,"aria-label",s),(!i||g&16&&o!==(o=d[4]?"Exit fullscreen":"Enter fullscreen"))&&f(e,"title",o)},i(d){i||(L(l),i=!0)},o(d){R(l),i=!1},d(d){d&&P(e),u[t].d(),r=!1,_()}}}function Bs(n){let e,t;return e=new $n({}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function js(n){let e,t;return e=new es({}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function Ds(n){let e,t,l,s,o,i=n[3]!=="none"&&Gt(n),r=n[2]&&Zt(n),_=n[1]&&xt(n);return{c(){e=N("div"),t=N("div"),i&&i.c(),l=W(),r&&r.c(),s=W(),_&&_.c(),f(t,"class","toolbar-buttons svelte-b1nr0g"),f(e,"class","toolbar svelte-b1nr0g"),f(e,"role","toolbar"),f(e,"aria-label","Table actions")},m(c,u){H(c,e,u),B(e,t),i&&i.m(t,null),B(t,l),r&&r.m(t,null),B(t,s),_&&_.m(t,null),o=!0},p(c,[u]){c[3]!=="none"?i?(i.p(c,u),u&8&&L(i,1)):(i=Gt(c),i.c(),L(i,1),i.m(t,l)):i&&(_e(),R(i,1,1,()=>{i=null}),ce()),c[2]?r?(r.p(c,u),u&4&&L(r,1)):(r=Zt(c),r.c(),L(r,1),r.m(t,s)):r&&(_e(),R(r,1,1,()=>{r=null}),ce()),c[1]?_?(_.p(c,u),u&2&&L(_,1)):(_=xt(c),_.c(),L(_,1),_.m(t,null)):_&&(_e(),R(_,1,1,()=>{_=null}),ce())},i(c){o||(L(i),L(r),L(_),o=!0)},o(c){R(i),R(r),R(_),o=!1},d(c){c&&P(e),i&&i.d(),r&&r.d(),_&&_.d()}}}function Ts(n,e,t){let{show_fullscreen_button:l=!1}=e,{show_copy_button:s=!1}=e,{show_search:o="none"}=e,{is_fullscreen:i=!1}=e,{on_copy:r}=e,{on_commit_filter:_}=e;const c=ut();let u=!1,h,{current_search_query:d=null}=e;function g(){t(6,u=!0),h&&clearTimeout(h),h=setTimeout(()=>{t(6,u=!1)},2e3)}async function z(){await r(),g()}Kn(()=>{h&&clearTimeout(h)});function F(w){Ge.call(this,n,w)}function O(){d=this.value,t(0,d)}return n.$$set=w=>{"show_fullscreen_button"in w&&t(1,l=w.show_fullscreen_button),"show_copy_button"in w&&t(2,s=w.show_copy_button),"show_search"in w&&t(3,o=w.show_search),"is_fullscreen"in w&&t(4,i=w.is_fullscreen),"on_copy"in w&&t(8,r=w.on_copy),"on_commit_filter"in w&&t(5,_=w.on_commit_filter),"current_search_query"in w&&t(0,d=w.current_search_query)},n.$$.update=()=>{n.$$.dirty&1&&c("search",d)},[d,l,s,o,i,_,u,z,r,F,O]}class Rs extends De{constructor(e){super(),Te(this,e,Ts,Ds,Re,{show_fullscreen_button:1,show_copy_button:2,show_search:3,is_fullscreen:4,on_copy:8,on_commit_filter:5,current_search_query:0})}get show_fullscreen_button(){return this.$$.ctx[1]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),q()}get show_copy_button(){return this.$$.ctx[2]}set show_copy_button(e){this.$$set({show_copy_button:e}),q()}get show_search(){return this.$$.ctx[3]}set show_search(e){this.$$set({show_search:e}),q()}get is_fullscreen(){return this.$$.ctx[4]}set is_fullscreen(e){this.$$set({is_fullscreen:e}),q()}get on_copy(){return this.$$.ctx[8]}set on_copy(e){this.$$set({on_copy:e}),q()}get on_commit_filter(){return this.$$.ctx[5]}set on_commit_filter(e){this.$$set({on_commit_filter:e}),q()}get current_search_query(){return this.$$.ctx[0]}set current_search_query(e){this.$$set({current_search_query:e}),q()}}function Hs(n){let e,t,l,s,o,i,r,_,c,u,h,d,g,z,F;return{c(){e=N("div"),t=N("button"),l=ae("svg"),s=ae("path"),r=W(),_=N("button"),c=ae("svg"),u=ae("path"),f(s,"d","M7 14l5-5 5 5"),f(s,"stroke","currentColor"),f(s,"stroke-width","2"),f(s,"stroke-linecap","round"),f(s,"stroke-linejoin","round"),f(l,"viewBox","0 0 24 24"),f(l,"fill","none"),f(l,"xmlns","http://www.w3.org/2000/svg"),f(l,"aria-hidden","true"),f(l,"focusable","false"),f(l,"class","svelte-a5uqm5"),f(t,"class","sort-button up svelte-a5uqm5"),f(t,"aria-label",o=n[1]("dataframe.sort_ascending")),f(t,"aria-pressed",i=n[0]==="asc"),Y(t,"active",n[0]==="asc"),f(u,"d","M7 10l5 5 5-5"),f(u,"stroke","currentColor"),f(u,"stroke-width","2"),f(u,"stroke-linecap","round"),f(u,"stroke-linejoin","round"),f(c,"viewBox","0 0 24 24"),f(c,"fill","none"),f(c,"xmlns","http://www.w3.org/2000/svg"),f(c,"aria-hidden","true"),f(c,"focusable","false"),f(c,"class","svelte-a5uqm5"),f(_,"class","sort-button down svelte-a5uqm5"),f(_,"aria-label",h=n[1]("dataframe.sort_descending")),f(_,"aria-pressed",d=n[0]==="des"),Y(_,"active",n[0]==="des"),f(e,"class","sort-icons svelte-a5uqm5"),f(e,"role","group"),f(e,"aria-label",g=n[1]("dataframe.sort_column"))},m(O,w){H(O,e,w),B(e,t),B(t,l),B(l,s),B(e,r),B(e,_),B(_,c),B(c,u),z||(F=[J(t,"click",n[3]),J(_,"click",n[4])],z=!0)},p(O,[w]){w&2&&o!==(o=O[1]("dataframe.sort_ascending"))&&f(t,"aria-label",o),w&1&&i!==(i=O[0]==="asc")&&f(t,"aria-pressed",i),w&1&&Y(t,"active",O[0]==="asc"),w&2&&h!==(h=O[1]("dataframe.sort_descending"))&&f(_,"aria-label",h),w&1&&d!==(d=O[0]==="des")&&f(_,"aria-pressed",d),w&1&&Y(_,"active",O[0]==="des"),w&2&&g!==(g=O[1]("dataframe.sort_column"))&&f(e,"aria-label",g)},i:ve,o:ve,d(O){O&&P(e),z=!1,qe(F)}}}function Ps(n,e,t){let{direction:l=null}=e,{i18n:s}=e;const o=ut(),i=()=>o("sort","asc"),r=()=>o("sort","des");return n.$$set=_=>{"direction"in _&&t(0,l=_.direction),"i18n"in _&&t(1,s=_.i18n)},[l,s,o,i,r]}class Bl extends De{constructor(e){super(),Te(this,e,Ps,Hs,Re,{direction:0,i18n:1})}get direction(){return this.$$.ctx[0]}set direction(e){this.$$set({direction:e}),q()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),q()}}function ot(n,e){const[t,l]=n;if(!e.some(([_,c])=>_===t&&c===l))return"";const s=e.some(([_,c])=>_===t-1&&c===l),o=e.some(([_,c])=>_===t+1&&c===l),i=e.some(([_,c])=>_===t&&c===l-1),r=e.some(([_,c])=>_===t&&c===l+1);return`cell-selected${s?" no-top":""}${o?" no-bottom":""}${i?" no-left":""}${r?" no-right":""}`}function jl(n,e){const[t,l]=n,[s,o]=e,i=Math.min(t,s),r=Math.max(t,s),_=Math.min(l,o),c=Math.max(l,o),u=[];for(let h=i;h<=r;h++)for(let d=_;d<=c;d++)u.push([h,d]);return u}function Ns(n,e,t){if(t.shiftKey&&e.length>0)return jl(e[e.length-1],n);if(t.metaKey||t.ctrlKey){const l=([o,i])=>o===n[0]&&i===n[1],s=e.findIndex(l);return s===-1?[...e,n]:e.filter((o,i)=>i!==s)}return[n]}function Fs(n,e){const t=n.map(l=>[...l]);return e.forEach(([l,s])=>{t[l]&&t[l][s]&&(t[l][s]={...t[l][s],value:""})}),t}function $t(n,e,t){const[l,s]=n;return t&&e.length===1&&e[0][0]===l&&e[0][1]===s}function Os(n,e,t){const[l,s]=n,o=t?-1:1;if(e[l]?.[s+o])return[l,s+o];const i=l+(o>0?1:0),r=l+(o<0?-1:0);return o>0&&e[i]?.[0]?[i,0]:o<0&&e[r]?.[e[0].length-1]?[r,e[0].length-1]:!1}function Is(n,e,t){const l={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[n],s=e[0]+l[0],o=e[1]+l[1];return s<0&&o<=0?!1:t[s]?.[o]?[s,o]:!1}function Ks(n,e){return e.reduce((t,l,s)=>{const o=l.reduce((i,r,_)=>n===r.id?_:i,-1);return o===-1?t:[s,o]},[-1,-1])}function Us(n,e){const[t]=n.composedPath();return!e.contains(t)}function Vs(n,e){return Array.from({length:n.length},(t,l)=>[l,e])}function Ys(n,e){return Array.from({length:n[0].length},(t,l)=>[e,l])}function Ws(n,e,t,l,s){const[o,i]=n;if(!e[o]?.[i])return{col_pos:"0px",row_pos:void 0};let r=0;for(let F=0;F<i;F++)r+=parseFloat(getComputedStyle(l).getPropertyValue(`--cell-width-${F}`));const _=e[o][i].id,c=t[_]?.cell;if(!c)return{col_pos:"0px",row_pos:void 0};const u=c.getBoundingClientRect(),h=s.getBoundingClientRect(),d=`${u.left-h.left+u.width/2}px`,z=`${u.top-h.top+u.height/2}px`;return{col_pos:d,row_pos:z}}function Xs(n,e,t){if(!n||!n.length||!n[0])return[];if(typeof e=="number"&&t&&e>=0&&e<n[0].length){const l=[...Array(n.length)].map((s,o)=>o);return l.sort((s,o)=>{const i=n[s],r=n[o];if(!i||!r||e>=i.length||e>=r.length)return 0;const _=i[e].value,c=r[e].value,u=_<c?-1:_>c?1:0;return t==="asc"?u:-u}),l}return[...Array(n.length)].map((l,s)=>s)}function Js(n){if(!n||!n.length)return[];let e=n[0].slice();for(let t=0;t<n.length;t++)for(let l=0;l<n[t].length;l++)`${e[l].value}`.length<`${n[t][l].value}`.length&&(e[l]=n[t][l]);return e}function Gs(n,e,t,l,s){const o=Xs(n,l,s),i=o.map(r=>n[r]);if(n.splice(0,n.length,...i),e){const r=o.map(_=>e[_]);e.splice(0,e.length,...r)}if(t){const r=o.map(_=>t[_]);t.splice(0,t.length,...r)}}async function Qs(n,e){const t=e.reduce((i,[r,_])=>{i[r]=i[r]||{};const c=String(n[r][_].value);return i[r][_]=c.includes(",")||c.includes('"')||c.includes(`
`)?`"${c.replace(/"/g,'""')}"`:c,i},{}),l=Object.keys(t).sort((i,r)=>+i-+r),s=Object.keys(t[l[0]]).sort((i,r)=>+i-+r),o=l.map(i=>s.map(r=>t[i][r]||"").join(",")).join(`
`);try{await navigator.clipboard.writeText(o)}catch(i){throw console.error("Copy failed:",i),new Error("Failed to copy to clipboard: "+i.message)}}function Zs(n,e){return e.filter(t);function t(l){var s=-1;return n.split(`
`).every(o);function o(i){if(!i)return!0;var r=i.split(l).length;return s<0&&(s=r),s===r&&r>1}}}function xs(n){const e=atob(n.split(",")[1]),t=n.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),s=new Uint8Array(l);for(let o=0;o<e.length;o++)s[o]=e.charCodeAt(o);return new Blob([l],{type:t})}function $s(n,e,t){const l=xs(n),s=new FileReader;s.addEventListener("loadend",o=>{if(!o?.target?.result||typeof o.target.result!="string")return;const[i]=Zs(o.target.result,[",","	"]),[r,..._]=ts(i).parseRows(o.target.result);e(r),t(_)}),s.readAsText(l)}const{window:eo}=Vn;function el(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[150]=e,l[151]=t,l}function tl(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[148]=e,l[149]=t,l}function ll(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[152]=e,l[149]=t,l}function nl(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[153]=e,l[151]=t,l}function sl(n){let e,t,l,s,o=n[2]&&n[2].length!==0&&n[3]&&ol(n);return l=new Rs({props:{show_fullscreen_button:n[17],is_fullscreen:n[41],on_copy:n[62],show_copy_button:n[18],show_search:n[20],on_commit_filter:n[69],current_search_query:n[47]}}),l.$on("click",n[61]),l.$on("search",n[77]),{c(){e=N("div"),o&&o.c(),t=W(),oe(l.$$.fragment),f(e,"class","header-row svelte-v7qhxw")},m(i,r){H(i,e,r),o&&o.m(e,null),B(e,t),ie(l,e,null),s=!0},p(i,r){i[2]&&i[2].length!==0&&i[3]?o?o.p(i,r):(o=ol(i),o.c(),o.m(e,t)):o&&(o.d(1),o=null);const _={};r[0]&131072&&(_.show_fullscreen_button=i[17]),r[1]&1024&&(_.is_fullscreen=i[41]),r[0]&262144&&(_.show_copy_button=i[18]),r[0]&1048576&&(_.show_search=i[20]),r[1]&65536&&(_.current_search_query=i[47]),l.$set(_)},i(i){s||(L(l.$$.fragment,i),s=!0)},o(i){R(l.$$.fragment,i),s=!1},d(i){i&&P(e),o&&o.d(),re(l)}}}function ol(n){let e,t,l;return{c(){e=N("div"),t=N("p"),l=ye(n[2]),f(t,"class","svelte-v7qhxw"),f(e,"class","label svelte-v7qhxw")},m(s,o){H(s,e,o),B(e,t),B(t,l)},p(s,o){o[0]&4&&Se(l,s[2])},d(s){s&&P(e)}}}function il(n){let e,t,l,s,o;return{c(){e=N("button"),e.textContent="⋮",t=W(),l=N("button"),l.textContent="⋮",f(e,"class","selection-button selection-button-column svelte-v7qhxw"),f(e,"aria-label","Select column"),f(l,"class","selection-button selection-button-row svelte-v7qhxw"),f(l,"aria-label","Select row")},m(i,r){H(i,e,r),H(i,t,r),H(i,l,r),s||(o=[J(e,"click",Qe(n[78])),J(l,"click",Qe(n[79]))],s=!0)},p:ve,d(i){i&&(P(e),P(t),P(l)),s=!1,qe(o)}}}function rl(n){let e,t;return{c(){e=N("caption"),t=ye(n[2]),f(e,"class","sr-only")},m(l,s){H(l,e,s),B(e,t)},p(l,s){s[0]&4&&Se(t,l[2])},d(l){l&&P(e)}}}function ul(n){let e;return{c(){e=N("th"),e.innerHTML='<div class="cell-wrap svelte-v7qhxw"><div class="header-content svelte-v7qhxw"><div class="header-text"></div></div></div>',f(e,"class","row-number-header frozen-column always-frozen svelte-v7qhxw"),se(e,"left","0")},m(t,l){H(t,e,l)},d(t){t&&P(e)}}}function al(n){let e,t,l;function s(...o){return n[82](n[151],...o)}return t=new Bl({props:{direction:n[29]===n[151]?n[28]:null,i18n:n[10]}}),t.$on("sort",s),{c(){e=N("div"),oe(t.$$.fragment),f(e,"class","sort-buttons svelte-v7qhxw")},m(o,i){H(o,e,i),ie(t,e,null),l=!0},p(o,i){n=o;const r={};i[0]&872415232&&(r.direction=n[29]===n[151]?n[28]:null),i[0]&1024&&(r.i18n=n[10]),t.$set(r)},i(o){l||(L(t.$$.fragment,o),l=!0)},o(o){R(t.$$.fragment,o),l=!1},d(o){o&&P(e),re(t)}}}function fl(n){let e,t,l;function s(...i){return n[83](n[151],...i)}function o(...i){return n[84](n[151],...i)}return{c(){e=N("button"),e.textContent="⋮",f(e,"class","cell-menu-button svelte-v7qhxw")},m(i,r){H(i,e,r),t||(l=[J(e,"click",s),J(e,"touchstart",o)],t=!0)},p(i,r){n=i},d(i){i&&P(e),t=!1,qe(l)}}}function _l(n,e){let t,l,s,o,i,r,_,c,u,h,d,g,z;function F(M){e[80](M,e[151])}function O(M){e[81](M,e[147])}let w={max_chars:e[19],latex_delimiters:e[6],line_breaks:e[12],edit:e[37]===e[151],header:!0,root:e[9],editable:e[7]};e[26][e[151]].value!==void 0&&(w.value=e[26][e[151]].value),e[25][e[147]].input!==void 0&&(w.el=e[25][e[147]].input),o=new at({props:w}),be.push(()=>ze(o,"value",F)),be.push(()=>ze(o,"el",O)),o.$on("keydown",e[53]);let k=e[37]!==e[151]&&al(e),v=e[7]&&fl(e);function T(...M){return e[85](e[151],...M)}return{key:n,first:null,c(){t=N("th"),l=N("div"),s=N("div"),oe(o.$$.fragment),_=W(),k&&k.c(),c=W(),v&&v.c(),u=W(),f(s,"class","header-content svelte-v7qhxw"),f(l,"class","cell-wrap svelte-v7qhxw"),f(t,"aria-sort",h=e[50](e[146],e[29],e[28])),se(t,"width",e[56](e[151])),se(t,"left",e[151]<e[33]?e[151]===0?e[14]?"var(--cell-width-row-number)":"0":`calc(${e[14]?"var(--cell-width-row-number) + ":""}${Array(e[151]).fill(0).map(Cl).join(" + ")})`:"auto"),f(t,"class","svelte-v7qhxw"),Y(t,"frozen-column",e[151]<e[33]),Y(t,"last-frozen",e[151]===e[33]-1),Y(t,"focus",e[37]===e[151]||e[38]===e[151]),this.first=t},m(M,j){H(M,t,j),B(t,l),B(l,s),ie(o,s,null),B(s,_),k&&k.m(s,null),B(l,c),v&&v.m(l,null),B(t,u),d=!0,g||(z=[J(t,"click",T),J(t,"mousedown",io)],g=!0)},p(M,j){e=M;const U={};j[0]&524288&&(U.max_chars=e[19]),j[0]&64&&(U.latex_delimiters=e[6]),j[0]&4096&&(U.line_breaks=e[12]),j[0]&67108864|j[1]&64&&(U.edit=e[37]===e[151]),j[0]&512&&(U.root=e[9]),j[0]&128&&(U.editable=e[7]),!i&&j[0]&67108864&&(i=!0,U.value=e[26][e[151]].value,Le(()=>i=!1)),!r&&j[0]&100663296&&(r=!0,U.el=e[25][e[147]].input,Le(()=>r=!1)),o.$set(U),e[37]!==e[151]?k?(k.p(e,j),j[0]&67108864|j[1]&64&&L(k,1)):(k=al(e),k.c(),L(k,1),k.m(s,null)):k&&(_e(),R(k,1,1,()=>{k=null}),ce()),e[7]?v?v.p(e,j):(v=fl(e),v.c(),v.m(l,null)):v&&(v.d(1),v=null),(!d||j[0]&872415232&&h!==(h=e[50](e[146],e[29],e[28])))&&f(t,"aria-sort",h),(!d||j[0]&67108864)&&se(t,"width",e[56](e[151])),(!d||j[0]&67125248|j[1]&4)&&se(t,"left",e[151]<e[33]?e[151]===0?e[14]?"var(--cell-width-row-number)":"0":`calc(${e[14]?"var(--cell-width-row-number) + ":""}${Array(e[151]).fill(0).map(Cl).join(" + ")})`:"auto"),(!d||j[0]&67108864|j[1]&4)&&Y(t,"frozen-column",e[151]<e[33]),(!d||j[0]&67108864|j[1]&4)&&Y(t,"last-frozen",e[151]===e[33]-1),(!d||j[0]&67108864|j[1]&192)&&Y(t,"focus",e[37]===e[151]||e[38]===e[151])},i(M){d||(L(o.$$.fragment,M),L(k),d=!0)},o(M){R(o.$$.fragment,M),R(k),d=!1},d(M){M&&P(t),re(o),k&&k.d(),v&&v.d(),g=!1,qe(z)}}}function cl(n,e){let t,l,s,o,i=e[149],r;s=new at({props:{value:e[146],latex_delimiters:e[6],line_breaks:e[12],datatype:Array.isArray(e[1])?e[1][e[149]]:e[1],edit:!1,el:null,root:e[9],editable:e[7]}});const _=()=>e[86](t,i),c=()=>e[86](null,i);return{key:n,first:null,c(){t=N("td"),l=N("div"),oe(s.$$.fragment),o=W(),f(l,"class","cell-wrap svelte-v7qhxw"),f(t,"tabindex","-1"),f(t,"class","svelte-v7qhxw"),this.first=t},m(u,h){H(u,t,h),B(t,l),ie(s,l,null),B(t,o),_(),r=!0},p(u,h){e=u;const d={};h[1]&262144&&(d.value=e[146]),h[0]&64&&(d.latex_delimiters=e[6]),h[0]&4096&&(d.line_breaks=e[12]),h[0]&2|h[1]&262144&&(d.datatype=Array.isArray(e[1])?e[1][e[149]]:e[1]),h[0]&512&&(d.root=e[9]),h[0]&128&&(d.editable=e[7]),s.$set(d),i!==e[149]&&(c(),i=e[149],_())},i(u){r||(L(s.$$.fragment,u),r=!0)},o(u){R(s.$$.fragment,u),r=!1},d(u){u&&P(t),re(s),c()}}}function hl(n){let e,t;return{c(){e=N("caption"),t=ye(n[2]),f(e,"class","sr-only")},m(l,s){H(l,e,s),B(e,t)},p(l,s){s[0]&4&&Se(t,l[2])},d(l){l&&P(e)}}}function to(n){let e,t=n[2]&&n[2].length!==0&&hl(n);return{c(){t&&t.c(),e=Fe()},m(l,s){t&&t.m(l,s),H(l,e,s)},p(l,s){l[2]&&l[2].length!==0?t?t.p(l,s):(t=hl(l),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(l){l&&P(e),t&&t.d(l)}}}function dl(n){let e;return{c(){e=N("th"),e.innerHTML='<div class="cell-wrap svelte-v7qhxw"><div class="header-content svelte-v7qhxw"><div class="header-text"></div></div></div>',f(e,"class","row-number-header frozen-column always-frozen svelte-v7qhxw"),se(e,"left","0")},m(t,l){H(t,e,l)},d(t){t&&P(e)}}}function ml(n){let e,t,l;function s(...o){return n[99](n[151],...o)}return t=new Bl({props:{direction:n[29]===n[151]?n[28]:null,i18n:n[10]}}),t.$on("sort",s),{c(){e=N("div"),oe(t.$$.fragment),f(e,"class","sort-buttons svelte-v7qhxw")},m(o,i){H(o,e,i),ie(t,e,null),l=!0},p(o,i){n=o;const r={};i[0]&872415232&&(r.direction=n[29]===n[151]?n[28]:null),i[0]&1024&&(r.i18n=n[10]),t.$set(r)},i(o){l||(L(t.$$.fragment,o),l=!0)},o(o){R(t.$$.fragment,o),l=!1},d(o){o&&P(e),re(t)}}}function bl(n){let e,t,l;function s(...i){return n[100](n[151],...i)}function o(...i){return n[101](n[151],...i)}return{c(){e=N("button"),e.textContent="⋮",f(e,"class","cell-menu-button svelte-v7qhxw")},m(i,r){H(i,e,r),t||(l=[J(e,"click",s),J(e,"touchstart",o)],t=!0)},p(i,r){n=i},d(i){i&&P(e),t=!1,qe(l)}}}function gl(n,e){let t,l,s,o,i,r,_,c,u,h,d,g,z;function F(M){e[97](M,e[151])}function O(M){e[98](M,e[147])}let w={max_chars:e[19],latex_delimiters:e[6],line_breaks:e[12],edit:e[37]===e[151],header:!0,root:e[9],editable:e[7]};e[26][e[151]].value!==void 0&&(w.value=e[26][e[151]].value),e[25][e[147]].input!==void 0&&(w.el=e[25][e[147]].input),o=new at({props:w}),be.push(()=>ze(o,"value",F)),be.push(()=>ze(o,"el",O));let k=e[37]!==e[151]&&ml(e),v=e[7]&&bl(e);function T(...M){return e[102](e[151],...M)}return{key:n,first:null,c(){t=N("th"),l=N("div"),s=N("div"),oe(o.$$.fragment),_=W(),k&&k.c(),c=W(),v&&v.c(),u=W(),f(s,"class","header-content svelte-v7qhxw"),f(l,"class","cell-wrap svelte-v7qhxw"),f(t,"aria-sort",h=e[50](e[146],e[29],e[28])),se(t,"width",e[56](e[151])),se(t,"left",e[151]<e[33]?e[151]===0?e[14]?"var(--cell-width-row-number)":"0":`calc(${e[14]?"var(--cell-width-row-number) + ":""}${Array(e[151]).fill(0).map(El).join(" + ")})`:"auto"),f(t,"class","svelte-v7qhxw"),Y(t,"frozen-column",e[151]<e[33]),Y(t,"last-frozen",e[151]===e[33]-1),Y(t,"focus",e[37]===e[151]||e[38]===e[151]),this.first=t},m(M,j){H(M,t,j),B(t,l),B(l,s),ie(o,s,null),B(s,_),k&&k.m(s,null),B(l,c),v&&v.m(l,null),B(t,u),d=!0,g||(z=[J(t,"click",T),J(t,"mousedown",uo)],g=!0)},p(M,j){e=M;const U={};j[0]&524288&&(U.max_chars=e[19]),j[0]&64&&(U.latex_delimiters=e[6]),j[0]&4096&&(U.line_breaks=e[12]),j[0]&67108864|j[1]&64&&(U.edit=e[37]===e[151]),j[0]&512&&(U.root=e[9]),j[0]&128&&(U.editable=e[7]),!i&&j[0]&67108864&&(i=!0,U.value=e[26][e[151]].value,Le(()=>i=!1)),!r&&j[0]&100663296&&(r=!0,U.el=e[25][e[147]].input,Le(()=>r=!1)),o.$set(U),e[37]!==e[151]?k?(k.p(e,j),j[0]&67108864|j[1]&64&&L(k,1)):(k=ml(e),k.c(),L(k,1),k.m(s,null)):k&&(_e(),R(k,1,1,()=>{k=null}),ce()),e[7]?v?v.p(e,j):(v=bl(e),v.c(),v.m(l,null)):v&&(v.d(1),v=null),(!d||j[0]&872415232&&h!==(h=e[50](e[146],e[29],e[28])))&&f(t,"aria-sort",h),(!d||j[0]&67108864)&&se(t,"width",e[56](e[151])),(!d||j[0]&67125248|j[1]&4)&&se(t,"left",e[151]<e[33]?e[151]===0?e[14]?"var(--cell-width-row-number)":"0":`calc(${e[14]?"var(--cell-width-row-number) + ":""}${Array(e[151]).fill(0).map(El).join(" + ")})`:"auto"),(!d||j[0]&67108864|j[1]&4)&&Y(t,"frozen-column",e[151]<e[33]),(!d||j[0]&67108864|j[1]&4)&&Y(t,"last-frozen",e[151]===e[33]-1),(!d||j[0]&67108864|j[1]&192)&&Y(t,"focus",e[37]===e[151]||e[38]===e[151])},i(M){d||(L(o.$$.fragment,M),L(k),d=!0)},o(M){R(o.$$.fragment,M),R(k),d=!1},d(M){M&&P(t),re(o),k&&k.d(),v&&v.d(),g=!1,qe(z)}}}function lo(n){let e,t,l=[],s=new Map,o,i=n[14]&&dl(),r=Be(n[26]);const _=c=>c[147];for(let c=0;c<r.length;c+=1){let u=el(n,r,c),h=_(u);s.set(h,l[c]=gl(h,u))}return{c(){e=N("tr"),i&&i.c(),t=W();for(let c=0;c<l.length;c+=1)l[c].c();f(e,"slot","thead"),f(e,"class","svelte-v7qhxw")},m(c,u){H(c,e,u),i&&i.m(e,null),B(e,t);for(let h=0;h<l.length;h+=1)l[h]&&l[h].m(e,null);o=!0},p(c,u){c[14]?i||(i=dl(),i.c(),i.m(e,t)):i&&(i.d(1),i=null),u[0]&906516160|u[1]&36176068|u[2]&258&&(r=Be(c[26]),_e(),l=xe(l,u,_,1,c,r,s,e,$e,gl,null,el),ce())},i(c){if(!o){for(let u=0;u<r.length;u+=1)L(l[u]);o=!0}},o(c){for(let u=0;u<l.length;u+=1)R(l[u]);o=!1},d(c){c&&P(e),i&&i.d();for(let u=0;u<l.length;u+=1)l[u].d()}}}function wl(n){let e,t=n[144]+1+"",l;return{c(){e=N("td"),l=ye(t),f(e,"class","row-number frozen-column always-frozen svelte-v7qhxw"),se(e,"left","0"),f(e,"tabindex","-1")},m(s,o){H(s,e,o),B(e,l)},p(s,o){o[4]&1048576&&t!==(t=s[144]+1+"")&&Se(l,t)},d(s){s&&P(e)}}}function kl(n){let e,t,l;function s(...o){return n[93](n[144],n[149],...o)}return{c(){e=N("button"),e.textContent="⋮",f(e,"class","cell-menu-button svelte-v7qhxw")},m(o,i){H(o,e,i),t||(l=J(e,"click",s),t=!0)},p(o,i){n=o},d(o){o&&P(e),t=!1,l()}}}function pl(n,e){let t,l,s,o,i,r,_=e[7]&&$t([e[144],e[149]],e[23],e[7]),c,u,h,d,g=e[147],z,F,O;function w(Q){e[89](Q,e[144],e[149])}function k(Q){e[90](Q,e[147])}function v(){return e[92](e[144],e[149])}let T={display_value:e[21]?.[e[144]]?.[e[149]],latex_delimiters:e[6],line_breaks:e[12],editable:e[7],edit:Ne(e[35],[e[144],e[149]]),datatype:Array.isArray(e[1])?e[1][e[149]]:e[1],clear_on_focus:e[36],root:e[9],max_chars:e[19]};e[27][e[144]][e[149]].value!==void 0&&(T.value=e[27][e[144]][e[149]].value),e[25][e[147]].input!==void 0&&(T.el=e[25][e[147]].input),s=new at({props:T}),be.push(()=>ze(s,"value",w)),be.push(()=>ze(s,"el",k)),s.$on("blur",e[91]),s.$on("focus",v);let M=_&&kl(e);const j=()=>e[94](t,g),U=()=>e[94](null,g);function ee(...Q){return e[95](e[144],e[149],...Q)}function te(...Q){return e[96](e[144],e[149],...Q)}return{key:n,first:null,c(){t=N("td"),l=N("div"),oe(s.$$.fragment),r=W(),M&&M.c(),c=W(),f(l,"class","cell-wrap svelte-v7qhxw"),f(t,"tabindex",u=e[14]&&e[149]===0?-1:0),f(t,"style",h="width: "+e[56](e[149])+"; left: "+(e[149]<e[33]?e[149]===0?e[14]?"var(--cell-width-row-number)":"0":`calc(${e[14]?"var(--cell-width-row-number) + ":""}${Array(e[149]).fill(0).map(Al).join(" + ")})`:"auto")+"; "+(e[22]?.[e[144]]?.[e[149]]||"")),f(t,"class",d=Pt(ot([e[144],e[149]],e[23]))+" svelte-v7qhxw"),Y(t,"frozen-column",e[149]<e[33]),Y(t,"last-frozen",e[149]===e[33]-1),Y(t,"flash",e[43]&&ot([e[144],e[149]],e[23])),Y(t,"menu-active",e[39]&&e[39].row===e[144]&&e[39].col===e[149]),this.first=t},m(Q,C){H(Q,t,C),B(t,l),ie(s,l,null),B(l,r),M&&M.m(l,null),B(t,c),j(),z=!0,F||(O=[J(t,"touchstart",ee),J(t,"mousedown",ro),J(t,"click",te)],F=!0)},p(Q,C){e=Q;const V={};C[0]&2097152|C[4]&3145728&&(V.display_value=e[21]?.[e[144]]?.[e[149]]),C[0]&64&&(V.latex_delimiters=e[6]),C[0]&4096&&(V.line_breaks=e[12]),C[0]&128&&(V.editable=e[7]),C[1]&16|C[4]&3145728&&(V.edit=Ne(e[35],[e[144],e[149]])),C[0]&2|C[4]&2097152&&(V.datatype=Array.isArray(e[1])?e[1][e[149]]:e[1]),C[1]&32&&(V.clear_on_focus=e[36]),C[0]&512&&(V.root=e[9]),C[0]&524288&&(V.max_chars=e[19]),!o&&C[0]&134217728|C[4]&3145728&&(o=!0,V.value=e[27][e[144]][e[149]].value,Le(()=>o=!1)),!i&&C[0]&33554432|C[4]&2097152&&(i=!0,V.el=e[25][e[147]].input,Le(()=>i=!1)),s.$set(V),C[0]&8388736|C[4]&3145728&&(_=e[7]&&$t([e[144],e[149]],e[23],e[7])),_?M?M.p(e,C):(M=kl(e),M.c(),M.m(l,null)):M&&(M.d(1),M=null),(!z||C[0]&16384|C[4]&2097152&&u!==(u=e[14]&&e[149]===0?-1:0))&&f(t,"tabindex",u),(!z||C[0]&4210688|C[1]&4|C[4]&3145728&&h!==(h="width: "+e[56](e[149])+"; left: "+(e[149]<e[33]?e[149]===0?e[14]?"var(--cell-width-row-number)":"0":`calc(${e[14]?"var(--cell-width-row-number) + ":""}${Array(e[149]).fill(0).map(Al).join(" + ")})`:"auto")+"; "+(e[22]?.[e[144]]?.[e[149]]||"")))&&f(t,"style",h),(!z||C[0]&8388608|C[4]&3145728&&d!==(d=Pt(ot([e[144],e[149]],e[23]))+" svelte-v7qhxw"))&&f(t,"class",d),g!==e[147]&&(U(),g=e[147],j()),(!z||C[0]&8388608|C[1]&4|C[4]&3145728)&&Y(t,"frozen-column",e[149]<e[33]),(!z||C[0]&8388608|C[1]&4|C[4]&3145728)&&Y(t,"last-frozen",e[149]===e[33]-1),(!z||C[0]&8388608|C[1]&4096|C[4]&3145728)&&Y(t,"flash",e[43]&&ot([e[144],e[149]],e[23])),(!z||C[0]&8388608|C[1]&256|C[4]&3145728)&&Y(t,"menu-active",e[39]&&e[39].row===e[144]&&e[39].col===e[149])},i(Q){z||(L(s.$$.fragment,Q),z=!0)},o(Q){R(s.$$.fragment,Q),z=!1},d(Q){Q&&P(t),re(s),M&&M.d(),U(),F=!1,qe(O)}}}function no(n){let e,t,l=[],s=new Map,o,i=n[14]&&wl(n),r=Be(n[145]);const _=c=>c[147];for(let c=0;c<r.length;c+=1){let u=tl(n,r,c),h=_(u);s.set(h,l[c]=pl(h,u))}return{c(){e=N("tr"),i&&i.c(),t=W();for(let c=0;c<l.length;c+=1)l[c].c();f(e,"slot","tbody"),f(e,"class","svelte-v7qhxw"),Y(e,"row_odd",n[144]%2===0)},m(c,u){H(c,e,u),i&&i.m(e,null),B(e,t);for(let h=0;h<l.length;h+=1)l[h]&&l[h].m(e,null);o=!0},p(c,u){c[14]?i?i.p(c,u):(i=wl(c),i.c(),i.m(e,t)):i&&(i.d(1),i=null),u[0]&182997698|u[1]&234885429|u[4]&3145728&&(r=Be(c[145]),_e(),l=xe(l,u,_,1,c,r,s,e,$e,pl,null,tl),ce()),(!o||u[4]&1048576)&&Y(e,"row_odd",c[144]%2===0)},i(c){if(!o){for(let u=0;u<r.length;u+=1)L(l[u]);o=!0}},o(c){for(let u=0;u<l.length;u+=1)R(l[u]);o=!1},d(c){c&&P(e),i&&i.d();for(let u=0;u<l.length;u+=1)l[u].d()}}}function so(n){let e,t,l,s,o,i;function r(h){n[103](h)}function _(h){n[104](h)}function c(h){n[105](h)}let u={max_height:n[11],selected:n[48],disable_scroll:n[39]!==null||n[40]!==null,$$slots:{tbody:[no,({index:h,item:d})=>({144:h,145:d}),({index:h,item:d})=>[0,0,0,0,(h?1048576:0)|(d?2097152:0)]],thead:[lo],default:[to]},$$scope:{ctx:n}};return n[27]!==void 0&&(u.items=n[27]),n[44]!==void 0&&(u.actual_height=n[44]),n[45]!==void 0&&(u.table_scrollbar_width=n[45]),t=new bs({props:u}),be.push(()=>ze(t,"items",r)),be.push(()=>ze(t,"actual_height",_)),be.push(()=>ze(t,"table_scrollbar_width",c)),{c(){e=N("div"),oe(t.$$.fragment),f(e,"class","table-wrap svelte-v7qhxw")},m(h,d){H(h,e,d),ie(t,e,null),i=!0},p(h,d){const g={};d[0]&2048&&(g.max_height=h[11]),d[1]&131072&&(g.selected=h[48]),d[1]&768&&(g.disable_scroll=h[39]!==null||h[40]!==null),d[0]&1055413958|d[1]&4597|d[4]&1076887552&&(g.$$scope={dirty:d,ctx:h}),!l&&d[0]&134217728&&(l=!0,g.items=h[27],Le(()=>l=!1)),!s&&d[1]&8192&&(s=!0,g.actual_height=h[44],Le(()=>s=!1)),!o&&d[1]&16384&&(o=!0,g.table_scrollbar_width=h[45],Le(()=>o=!1)),t.$set(g)},i(h){i||(L(t.$$.fragment,h),i=!0)},o(h){R(t.$$.fragment,h),i=!1},d(h){h&&P(e),re(t)}}}function vl(n){let e,t,l,s;return{c(){e=N("div"),t=N("button"),t.innerHTML="<span>+</span>",f(t,"class","add-row-button svelte-v7qhxw"),f(e,"class","add-row-container svelte-v7qhxw")},m(o,i){H(o,e,i),B(e,t),l||(s=J(t,"click",n[110]),l=!0)},p:ve,d(o){o&&P(e),l=!1,s()}}}function yl(n){let e,t;return e=new Ll({props:{x:n[39].x,y:n[39].y,row:n[39].row,col_count:n[4],row_count:n[5],on_add_row_above:n[111],on_add_row_below:n[112],on_add_column_left:n[113],on_add_column_right:n[114],on_delete_row:n[115],on_delete_col:n[116],can_delete_rows:n[27].length>1,can_delete_cols:n[27][0].length>1,i18n:n[10]}}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},p(l,s){const o={};s[1]&256&&(o.x=l[39].x),s[1]&256&&(o.y=l[39].y),s[1]&256&&(o.row=l[39].row),s[0]&16&&(o.col_count=l[4]),s[0]&32&&(o.row_count=l[5]),s[1]&256&&(o.on_add_row_above=l[111]),s[1]&256&&(o.on_add_row_below=l[112]),s[1]&256&&(o.on_add_column_left=l[113]),s[1]&256&&(o.on_add_column_right=l[114]),s[1]&256&&(o.on_delete_row=l[115]),s[1]&256&&(o.on_delete_col=l[116]),s[0]&134217728&&(o.can_delete_rows=l[27].length>1),s[0]&134217728&&(o.can_delete_cols=l[27][0].length>1),s[0]&1024&&(o.i18n=l[10]),e.$set(o)},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function ql(n){let e,t;return e=new Ll({props:{i18n:n[10],x:n[40].x,y:n[40].y,row:-1,col_count:n[4],row_count:n[5],on_add_row_above:n[117],on_add_row_below:n[118],on_add_column_left:n[119],on_add_column_right:n[120],on_delete_row:n[121],on_delete_col:n[122],can_delete_rows:!1,can_delete_cols:n[26].length>1}}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},p(l,s){const o={};s[0]&1024&&(o.i18n=l[10]),s[1]&512&&(o.x=l[40].x),s[1]&512&&(o.y=l[40].y),s[0]&16&&(o.col_count=l[4]),s[0]&32&&(o.row_count=l[5]),s[1]&256&&(o.on_add_row_above=l[117]),s[1]&256&&(o.on_add_row_below=l[118]),s[1]&512&&(o.on_add_column_left=l[119]),s[1]&512&&(o.on_add_column_right=l[120]),s[1]&256&&(o.on_delete_row=l[121]),s[1]&512&&(o.on_delete_col=l[122]),s[0]&67108864&&(o.can_delete_cols=l[26].length>1),e.$set(o)},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function oo(n){let e,t,l,s,o,i,r,_,c,u=[],h=new Map,d,g,z,F=[],O=new Map,w,k,v,T,M,j,U,ee,te,Q,C,V=(n[2]&&n[2].length!==0&&n[3]||n[17]||n[18]||n[20]!=="none")&&sl(n),Z=n[24]!==!1&&n[23].length===1&&il(n),y=n[2]&&n[2].length!==0&&rl(n),I=n[14]&&ul(),we=Be(n[26]);const Ce=b=>b[147];for(let b=0;b<we.length;b+=1){let p=nl(n,we,b),X=Ce(p);h.set(X,u[b]=_l(X,p))}let he=Be(n[49]);const G=b=>b[147];for(let b=0;b<he.length;b+=1){let p=ll(n,he,b),X=G(p);O.set(X,F[b]=cl(X,p))}function ke(b){n[106](b)}let $={upload:n[15],stream_handler:n[16],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:n[9],aria_label:n[10]("dataframe.drop_to_upload"),$$slots:{default:[so]},$$scope:{ctx:n}};n[42]!==void 0&&($.dragging=n[42]),v=new Qn({props:$}),be.push(()=>ze(v,"dragging",ke)),v.$on("load",n[107]);let le=n[27].length===0&&n[7]&&n[5][1]==="dynamic"&&vl(n),x=n[39]&&yl(n),A=n[40]!==null&&ql(n);return{c(){e=N("div"),V&&V.c(),t=W(),l=N("div"),Z&&Z.c(),s=W(),o=N("table"),y&&y.c(),i=W(),r=N("thead"),_=N("tr"),I&&I.c(),c=W();for(let b=0;b<u.length;b+=1)u[b].c();d=W(),g=N("tbody"),z=N("tr");for(let b=0;b<F.length;b+=1)F[b].c();k=W(),oe(v.$$.fragment),M=W(),le&&le.c(),j=W(),x&&x.c(),U=W(),A&&A.c(),ee=Fe(),f(_,"class","svelte-v7qhxw"),f(r,"class","svelte-v7qhxw"),f(z,"class","svelte-v7qhxw"),f(o,"class","svelte-v7qhxw"),Y(o,"fixed-layout",n[13].length!=0),f(l,"class","table-wrap svelte-v7qhxw"),se(l,"height",n[44]+"px"),f(l,"role","grid"),f(l,"tabindex","0"),Y(l,"dragging",n[42]),Y(l,"no-wrap",!n[8]),Y(l,"menu-open",n[39]||n[40]),f(e,"class","table-container svelte-v7qhxw")},m(b,p){H(b,e,p),V&&V.m(e,null),B(e,t),B(e,l),Z&&Z.m(l,null),B(l,s),B(l,o),y&&y.m(o,null),B(o,i),B(o,r),B(r,_),I&&I.m(_,null),B(_,c);for(let X=0;X<u.length;X+=1)u[X]&&u[X].m(_,null);B(o,d),B(o,g),B(g,z);for(let X=0;X<F.length;X+=1)F[X]&&F[X].m(z,null);w=Ml.observe(o,n[87].bind(o)),n[88](o),B(l,k),ie(v,l,null),n[108](l),H(b,M,p),le&&le.m(b,p),H(b,j,p),x&&x.m(b,p),H(b,U,p),A&&A.m(b,p),H(b,ee,p),te=!0,Q||(C=[J(eo,"resize",n[76]),J(l,"keydown",n[109])],Q=!0)},p(b,p){b[2]&&b[2].length!==0&&b[3]||b[17]||b[18]||b[20]!=="none"?V?(V.p(b,p),p[0]&1441804&&L(V,1)):(V=sl(b),V.c(),L(V,1),V.m(e,t)):V&&(_e(),R(V,1,1,()=>{V=null}),ce()),b[24]!==!1&&b[23].length===1?Z?Z.p(b,p):(Z=il(b),Z.c(),Z.m(l,s)):Z&&(Z.d(1),Z=null),b[2]&&b[2].length!==0?y?y.p(b,p):(y=rl(b),y.c(),y.m(o,i)):y&&(y.d(1),y=null),b[14]?I||(I=ul(),I.c(),I.m(_,c)):I&&(I.d(1),I=null),p[0]&906516160|p[1]&40370372|p[2]&258&&(we=Be(b[26]),_e(),u=xe(u,p,Ce,1,b,we,h,_,$e,_l,null,nl),ce()),p[0]&1073746626|p[1]&262144&&(he=Be(b[49]),_e(),F=xe(F,p,G,1,b,he,O,z,$e,cl,null,ll),ce()),(!te||p[0]&8192)&&Y(o,"fixed-layout",b[13].length!=0);const X={};p[0]&32768&&(X.upload=b[15]),p[0]&65536&&(X.stream_handler=b[16]),p[0]&512&&(X.root=b[9]),p[0]&1024&&(X.aria_label=b[10]("dataframe.drop_to_upload")),p[0]&1055416006|p[1]&160757|p[4]&1073741824&&(X.$$scope={dirty:p,ctx:b}),!T&&p[1]&2048&&(T=!0,X.dragging=b[42],Le(()=>T=!1)),v.$set(X),(!te||p[1]&8192)&&se(l,"height",b[44]+"px"),(!te||p[1]&2048)&&Y(l,"dragging",b[42]),(!te||p[0]&256)&&Y(l,"no-wrap",!b[8]),(!te||p[1]&768)&&Y(l,"menu-open",b[39]||b[40]),b[27].length===0&&b[7]&&b[5][1]==="dynamic"?le?le.p(b,p):(le=vl(b),le.c(),le.m(j.parentNode,j)):le&&(le.d(1),le=null),b[39]?x?(x.p(b,p),p[1]&256&&L(x,1)):(x=yl(b),x.c(),L(x,1),x.m(U.parentNode,U)):x&&(_e(),R(x,1,1,()=>{x=null}),ce()),b[40]!==null?A?(A.p(b,p),p[1]&512&&L(A,1)):(A=ql(b),A.c(),L(A,1),A.m(ee.parentNode,ee)):A&&(_e(),R(A,1,1,()=>{A=null}),ce())},i(b){if(!te){L(V);for(let p=0;p<we.length;p+=1)L(u[p]);for(let p=0;p<he.length;p+=1)L(F[p]);L(v.$$.fragment,b),L(x),L(A),te=!0}},o(b){R(V);for(let p=0;p<u.length;p+=1)R(u[p]);for(let p=0;p<F.length;p+=1)R(F[p]);R(v.$$.fragment,b),R(x),R(A),te=!1},d(b){b&&(P(e),P(M),P(j),P(U),P(ee)),V&&V.d(),Z&&Z.d(),y&&y.d(),I&&I.d();for(let p=0;p<u.length;p+=1)u[p].d();for(let p=0;p<F.length;p+=1)F[p].d();w(),n[88](null),re(v),n[108](null),le&&le.d(b),x&&x.d(b),A&&A.d(b),Q=!1,qe(C)}}}function Ze(){return Math.random().toString(36).substring(2,15)}function bt(n,e,t){let l=n||[];if(e[1]==="fixed"&&l.length<e[0]){const s=Array(e[0]-l.length).fill("").map((o,i)=>`${i+l.length}`);l=l.concat(s)}return!l||l.length===0?Array(e[0]).fill(0).map((s,o)=>{const i=Ze();return t[i]={cell:null,input:null},{id:i,value:JSON.stringify(o+1)}}):l.map((s,o)=>{const i=Ze();return t[i]={cell:null,input:null},{id:i,value:s??""}})}const Cl=(n,e)=>`var(--cell-width-${e})`,io=n=>{n.preventDefault(),n.stopPropagation()},Al=(n,e)=>`var(--cell-width-${e})`,ro=n=>{n.preventDefault(),n.stopPropagation()},El=(n,e)=>`var(--cell-width-${e})`,uo=n=>{n.preventDefault(),n.stopPropagation()};function ao(n,e,t){let l,s,{datatype:o}=e,{label:i=null}=e,{show_label:r=!0}=e,{headers:_=[]}=e,{values:c=[]}=e,{col_count:u}=e,{row_count:h}=e,{latex_delimiters:d}=e,{editable:g=!0}=e,{wrap:z=!1}=e,{root:F}=e,{i18n:O}=e,{max_height:w=500}=e,{line_breaks:k=!0}=e,{column_widths:v=[]}=e,{show_row_numbers:T=!1}=e,{upload:M}=e,{stream_handler:j}=e,{show_fullscreen_button:U=!1}=e,{show_copy_button:ee=!1}=e,{value_is_output:te=!1}=e,{max_chars:Q=void 0}=e,{show_search:C="none"}=e,{pinned_columns:V=0}=e,Z=0,y=[],I=!1,{display_value:we=null}=e,{styling:Ce=null}=e,he,G={};const ke=ut();let $=!1,le=!1,x=!1,A=!1,b=null,p=null,X=!1,E=!1,ge=!1,ue;rt(()=>{ue=getComputedStyle(document.documentElement).getPropertyValue("--color-accent").trim()+"40",document.documentElement.style.setProperty("--color-accent-copied",ue)});const pe=(a,m)=>D?.[a]?.[m]?.value;function Ie(a){const m=a.length;return m===0?[]:Array(h[1]==="fixed"?h[0]:m).fill(0).map((S,K)=>Array(u[1]==="fixed"?u[0]:a[0].length||_.length).fill(0).map((ne,de)=>{const Me=Ze();return t(25,G[Me]=G[Me]||{input:null,cell:null},G),{value:a?.[K]?.[de]??"",id:Me}}))}let fe=bt(_,u,G),He=_,D=[[]],Ae,yt=fe.map(a=>a.value),qt=D.map(a=>a.map(m=>String(m.value)));async function Dl(){if(Xe)return;const a=fe.map(S=>S.value),m=D.map(S=>S.map(K=>String(K.value)));(!Ne(m,qt)||!Ne(a,yt))&&(ke("change",{data:D.map(S=>S.map(K=>K.value)),headers:fe.map(S=>S.value),metadata:null}),te||ke("input"),qt=m,yt=a)}function Tl(a,m,S){if(!m)return"none";if(_[m]===a){if(S==="asc")return"ascending";if(S==="des")return"descending"}return"none"}async function Ct(a){if(A!==!1&&x===!1)switch(a.key){case"ArrowDown":t(24,I=[0,A]),t(23,y=[[0,A]]),t(38,A=!1);return;case"ArrowLeft":t(38,A=A>0?A-1:A);return;case"ArrowRight":t(38,A=A<fe.length-1?A+1:A);return;case"Escape":a.preventDefault(),t(38,A=!1);break;case"Enter":a.preventDefault(),g&&t(37,x=A);break}if(a.key==="Delete"||a.key==="Backspace"){if(!g)return;if($){const[K,ne]=$,de=G[D[K][ne].id].input;if(de&&de.selectionStart!==de.selectionEnd||a.key==="Delete"&&de?.selectionStart!==de?.value.length||a.key==="Backspace"&&de?.selectionStart!==0)return}a.preventDefault(),y.length>0&&(t(27,D=Fs(D,y)),ke("change",{data:D.map(K=>K.map(ne=>ne.value)),headers:fe.map(K=>K.value),metadata:null}),te||ke("input"));return}if(a.key==="c"&&(a.metaKey||a.ctrlKey)){a.preventDefault(),y.length>0&&await St();return}if(!I)return;const[m,S]=I;switch(a.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":if($)break;a.preventDefault();const K=Is(a.key,[m,S],D);K?(a.shiftKey?(t(23,y=jl(y.length>0?y[0]:[m,S],K)),t(35,$=!1)):(t(23,y=[K]),t(35,$=!1)),t(24,I=K)):K===!1&&a.key==="ArrowUp"&&m===0&&(t(38,A=S),t(24,I=!1),t(23,y=[]),t(35,$=!1));break;case"Escape":if(!g)break;a.preventDefault(),t(35,$=!1);break;case"Enter":if(a.preventDefault(),g)if(a.shiftKey)et(m),await je(),t(24,I=[m+1,S]);else if(Ne($,[m,S])){const de=D[m][S].id,Me=G[de].input;Me&&t(27,D[m][S].value=Me.value,D),t(35,$=!1),await je(),t(24,I=[m,S])}else t(35,$=[m,S]),t(36,le=!1);break;case"Tab":a.preventDefault(),t(35,$=!1);const ne=Os([m,S],D,a.shiftKey);ne&&(t(23,y=[ne]),t(24,I=ne),g&&(t(35,$=ne),t(36,le=!1)));break;default:if(!g)break;(!$||$&&Ne($,[m,S]))&&a.key.length===1&&(t(36,le=!0),t(35,$=[m,S]))}}let Pe,Ee;function ft(a,m){typeof Ee!="number"||Ee!==a?(t(28,Pe=m),t(29,Ee=a)):Ee===a&&(Pe===m?(t(28,Pe=void 0),t(29,Ee=void 0)):t(28,Pe=m))}async function Rl(a,m=!1){!g||x===a||(t(24,I=!1),t(23,y=[]),t(38,A=a),t(37,x=a))}function Hl(a){if(g)switch(a.detail.key){case"Escape":case"Enter":case"Tab":a.preventDefault(),t(24,I=!1),t(38,A=x),t(37,x=!1),me.focus();break}}async function et(a){if(me.focus(),h[1]!=="dynamic")return;const m=Array(D[0]?.length||_.length).fill(0).map((S,K)=>{const ne=Ze();return t(25,G[ne]={cell:null,input:null},G),{id:ne,value:""}});D.length===0?t(27,D=[m]):a!==void 0&&a>=0&&a<=D.length?D.splice(a,0,m):D.push(m),t(27,D),t(0,c),t(75,Ae),t(24,I=[a!==void 0?a:D.length-1,0])}async function Pl(a){if(me.focus(),u[1]!=="dynamic")return;const m=a!==void 0?a:D[0].length;for(let S=0;S<D.length;S++){const K=Ze();t(25,G[K]={cell:null,input:null},G),D[S].splice(m,0,{id:K,value:""})}_.splice(m,0,`Header ${_.length+1}`),t(27,D),t(0,c),t(75,Ae),t(71,_),await je(),requestAnimationFrame(()=>{Rl(m,!0);const S=me.querySelectorAll("tbody")[1].offsetWidth;me.querySelectorAll("table")[1].scrollTo({left:S})})}function At(a){Us(a,me)&&(t(35,$=!1),t(23,y=[]),t(37,x=!1),t(38,A=!1),t(39,b=null),t(40,p=null))}let Ke=[],me,tt;function Ue(){const a=Ke.map(S=>S?.clientWidth||0);if(a.length===0)return;T&&me.style.setProperty("--cell-width-row-number",`${a[0]}px`);const m=T?a.slice(1):a;m.forEach((S,K)=>{v[K]||me.style.setProperty(`--cell-width-${K}`,`${S-lt/m.length}px`)})}function Nl(a){return v[a]||`var(--cell-width-${a})`}let _t=c.slice(0,w/c.length*37).length*37+37,lt=0;function Fl(a,m,S,K,ne){let de=null;if(I&&I[0]in a&&I[1]in a[I[0]]&&(de=a[I[0]][I[1]].id),!(typeof K!="number"||!ne)&&(Gs(a,m,S,K,ne),t(27,D),t(0,c),t(75,Ae),de)){const[Me,Je]=Ks(de,D);t(24,I=[Me,Je])}}let Et=!1;rt(()=>{const a=new IntersectionObserver(m=>{m.forEach(S=>{S.isIntersecting&&!Et&&(Ue(),t(27,D),t(0,c),t(75,Ae)),Et=S.isIntersecting})});return a.observe(me),document.addEventListener("click",At),window.addEventListener("resize",zt),document.addEventListener("fullscreenchange",Lt),()=>{a.disconnect(),document.removeEventListener("click",At),window.removeEventListener("resize",zt),document.removeEventListener("fullscreenchange",Lt)}});function ct(a,m,S){a.target instanceof HTMLAnchorElement||(a.preventDefault(),a.stopPropagation(),!(T&&S===-1)&&(t(36,le=!1),t(39,b=null),t(40,p=null),t(38,A=!1),t(37,x=!1),t(23,y=Ns([m,S],y,a)),me.focus(),g&&(y.length===1?(t(35,$=[m,S]),je().then(()=>{const K=G[D[m][S].id].input;K&&(K.focus(),K.selectionStart=K.selectionEnd=K.value.length)})):t(35,$=!1)),Ol(m,S),ke("select",{index:[m,S],value:pe(m,S),row_value:D[m].map(K=>K.value)})))}function Mt(a,m,S){if(a.stopPropagation(),b&&b.row===m&&b.col===S)t(39,b=null);else{const K=a.target.closest("td");if(K){const ne=K.getBoundingClientRect();t(39,b={row:m,col:S,x:ne.right,y:ne.bottom})}}}function Ve(a,m){const S=m==="above"?a:a+1;et(S),t(39,b=null),t(40,p=null)}function Ye(a,m){const S=m==="left"?a:a+1;Pl(S),t(39,b=null),t(40,p=null)}function zt(){t(39,b=null),t(40,p=null),t(23,y=[]),t(24,I=!1),t(35,$=!1),Ue()}let nt=null;function Ol(a,m){nt=nt?.type==="cell"&&nt.row===a&&nt.col===m?null:{type:"cell",row:a,col:m}}function Il(){document.fullscreenElement?(document.exitFullscreen(),t(41,X=!1)):(me.requestFullscreen(),t(41,X=!0))}function Lt(){t(41,X=!!document.fullscreenElement)}async function St(){await Qs(D,y),t(43,ge=!0),setTimeout(()=>{t(43,ge=!1)},800)}function We(a,m){if(a.stopPropagation(),p&&p.col===m)t(40,p=null);else{const S=a.target.closest("th");if(S){const K=S.getBoundingClientRect();t(40,p={col:m,x:K.right,y:K.bottom})}}}Un(()=>{t(72,te=!1)});async function Kl(a){me.focus(),h[1]==="dynamic"&&(D.length<=1||(D.splice(a,1),t(27,D),t(0,c),t(75,Ae),t(24,I=!1)))}async function Ul(a){me.focus(),u[1]==="dynamic"&&(fe.length<=1||(fe.splice(a,1),t(26,fe),t(71,_),t(74,He),t(4,u),t(25,G),D.length>0&&(D.forEach(m=>{m.splice(a,1)}),t(27,D),t(0,c),t(75,Ae)),t(24,I=!1)))}function ht(a){Kl(a),t(39,b=null),t(40,p=null)}function dt(a){Ul(a),t(39,b=null),t(40,p=null)}function Bt(a){t(23,y=Vs(D,a)),t(24,I=y[0]),t(35,$=!1)}function jt(a){t(23,y=Ys(D,a)),t(24,I=y[0]),t(35,$=!1)}let st,Xe=null;function Dt(a){t(47,Xe=a),ke("search",a)}function Vl(){Xe&&C==="filter"&&(ke("change",{data:D.map(a=>a.map(m=>m.value)),headers:fe.map(a=>a.value),metadata:null}),te||ke("input"),t(47,Xe=null))}function mt(a,m){a.target instanceof HTMLAnchorElement||(a.preventDefault(),a.stopPropagation(),g&&(t(36,le=!1),t(39,b=null),t(40,p=null),t(24,I=!1),t(23,y=[]),t(38,A=m),t(37,x=m),me.focus()))}const Yl=()=>Ue(),Wl=a=>Dt(a.detail),Xl=()=>Bt(st[1]),Jl=()=>jt(st[0]);function Gl(a,m){n.$$.not_equal(fe[m].value,a)&&(fe[m].value=a,t(26,fe),t(71,_),t(74,He),t(4,u),t(25,G))}function Ql(a,m){n.$$.not_equal(G[m].input,a)&&(G[m].input=a,t(25,G))}const Zl=(a,{detail:m})=>ft(a,m),xl=(a,m)=>We(m,a),$l=(a,m)=>{m.preventDefault();const S=m.touches[0],K=new MouseEvent("click",{clientX:S.clientX,clientY:S.clientY,bubbles:!0,cancelable:!0,view:window});We(K,a)},en=(a,m)=>mt(m,a);function tn(a,m){be[a?"unshift":"push"](()=>{Ke[m]=a,t(30,Ke)})}function ln(){he=zl.entries.get(this)?.contentRect,t(34,he)}function nn(a){be[a?"unshift":"push"](()=>{tt=a,t(32,tt)})}function sn(a,m,S){n.$$.not_equal(D[m][S].value,a)&&(D[m][S].value=a,t(27,D),t(0,c),t(75,Ae))}function on(a,m){n.$$.not_equal(G[m].input,a)&&(G[m].input=a,t(25,G))}const rn=()=>{t(36,le=!1),me.focus()},un=(a,m)=>{const S=a,K=m;y.some(([ne,de])=>ne===S&&de===K)||t(23,y=[[S,K]])},an=(a,m,S)=>Mt(S,a,m);function fn(a,m){be[a?"unshift":"push"](()=>{G[m].cell=a,t(25,G)})}const _n=(a,m,S)=>{const K=S.touches[0],ne=new MouseEvent("click",{clientX:K.clientX,clientY:K.clientY,bubbles:!0,cancelable:!0,view:window});ct(ne,a,m)},cn=(a,m,S)=>ct(S,a,m);function hn(a,m){n.$$.not_equal(fe[m].value,a)&&(fe[m].value=a,t(26,fe),t(71,_),t(74,He),t(4,u),t(25,G))}function dn(a,m){n.$$.not_equal(G[m].input,a)&&(G[m].input=a,t(25,G))}const mn=(a,{detail:m})=>ft(a,m),bn=(a,m)=>We(m,a),gn=(a,m)=>{m.preventDefault();const S=m.touches[0],K=new MouseEvent("click",{clientX:S.clientX,clientY:S.clientY,bubbles:!0,cancelable:!0,view:window});We(K,a)},wn=(a,m)=>mt(m,a);function kn(a){D=a,t(27,D),t(0,c),t(75,Ae)}function pn(a){_t=a,t(44,_t)}function vn(a){lt=a,t(45,lt)}function yn(a){E=a,t(42,E)}const qn=({detail:a})=>$s(a.data,m=>(t(26,fe=bt(m.map(S=>S??""),u,G)),fe),m=>{t(0,c=m)});function Cn(a){be[a?"unshift":"push"](()=>{me=a,t(31,me)})}const An=a=>Ct(a),En=()=>et(),Mn=()=>Ve(b?.row||0,"above"),zn=()=>Ve(b?.row||0,"below"),Ln=()=>Ye(b?.col||0,"left"),Sn=()=>Ye(b?.col||0,"right"),Bn=()=>ht(b?.row||0),jn=()=>dt(b?.col||0),Dn=()=>Ve(b?.row??-1,"above"),Tn=()=>Ve(b?.row??-1,"below"),Rn=()=>Ye(p?.col??-1,"left"),Hn=()=>Ye(p?.col??-1,"right"),Pn=()=>ht(b?.row??-1),Nn=()=>dt(p?.col??-1);return n.$$set=a=>{"datatype"in a&&t(1,o=a.datatype),"label"in a&&t(2,i=a.label),"show_label"in a&&t(3,r=a.show_label),"headers"in a&&t(71,_=a.headers),"values"in a&&t(0,c=a.values),"col_count"in a&&t(4,u=a.col_count),"row_count"in a&&t(5,h=a.row_count),"latex_delimiters"in a&&t(6,d=a.latex_delimiters),"editable"in a&&t(7,g=a.editable),"wrap"in a&&t(8,z=a.wrap),"root"in a&&t(9,F=a.root),"i18n"in a&&t(10,O=a.i18n),"max_height"in a&&t(11,w=a.max_height),"line_breaks"in a&&t(12,k=a.line_breaks),"column_widths"in a&&t(13,v=a.column_widths),"show_row_numbers"in a&&t(14,T=a.show_row_numbers),"upload"in a&&t(15,M=a.upload),"stream_handler"in a&&t(16,j=a.stream_handler),"show_fullscreen_button"in a&&t(17,U=a.show_fullscreen_button),"show_copy_button"in a&&t(18,ee=a.show_copy_button),"value_is_output"in a&&t(72,te=a.value_is_output),"max_chars"in a&&t(19,Q=a.max_chars),"show_search"in a&&t(20,C=a.show_search),"pinned_columns"in a&&t(73,V=a.pinned_columns),"display_value"in a&&t(21,we=a.display_value),"styling"in a&&t(22,Ce=a.styling)},n.$$.update=()=>{if(n.$$.dirty[0]&1|n.$$.dirty[2]&8192&&(Ne(c,Ae)||(t(27,D=Ie(c)),t(75,Ae=JSON.parse(JSON.stringify(c))))),n.$$.dirty[0]&134217728|n.$$.dirty[2]&2048&&t(33,Z=V&&D?.[0]?.length?Math.min(V,D[0].length):0),n.$$.dirty[0]&8388608&&t(23,y=[...y]),n.$$.dirty[0]&8388608&&t(24,I=y.length>0?y[y.length-1]:!1),n.$$.dirty[0]&33554448|n.$$.dirty[2]&4608&&(Ne(_,He)||(t(26,fe=bt(_,u,G)),t(74,He=JSON.parse(JSON.stringify(_))))),n.$$.dirty[0]&201326592&&(D||fe)&&Dl(),n.$$.dirty[0]&134217728&&t(49,l=Js(D)),n.$$.dirty[0]&1073741824&&Ke[0]&&Ue(),n.$$.dirty[0]&945815552&&Fl(D,we,Ce,Ee,Pe),n.$$.dirty[0]&16777216&&t(48,s=!!I&&I[0]),n.$$.dirty[0]&939524096)if(typeof Ee=="number"&&Pe&&Ee>=0&&Ee<D[0].length){const a=[...Array(D.length)].map((S,K)=>K),m=Ee;a.sort((S,K)=>{const ne=D[S],de=D[K];if(!ne||!de||m>=ne.length||m>=de.length)return 0;const Me=ne[m].value,Je=de[m].value,Tt=Me<Je?-1:Me>Je?1:0;return Pe==="asc"?Tt:-Tt})}else[...Array(D.length)].map((a,m)=>m);if(n.$$.dirty[0]&16777216&&I!==!1&&t(46,st=I),n.$$.dirty[0]&184549376|n.$$.dirty[1]&3&&I!==!1){const a=Ws(I,D,G,me,tt);document.documentElement.style.setProperty("--selected-col-pos",a.col_pos),a.row_pos&&document.documentElement.style.setProperty("--selected-row-pos",a.row_pos)}},[c,o,i,r,u,h,d,g,z,F,O,w,k,v,T,M,j,U,ee,Q,C,we,Ce,y,I,G,fe,D,Pe,Ee,Ke,me,tt,Z,he,$,le,x,A,b,p,X,E,ge,_t,lt,st,Xe,s,l,Tl,Ct,ft,Hl,et,Ue,Nl,ct,Mt,Ve,Ye,Il,St,We,ht,dt,Bt,jt,Dt,Vl,mt,_,te,V,He,Ae,Yl,Wl,Xl,Jl,Gl,Ql,Zl,xl,$l,en,tn,ln,nn,sn,on,rn,un,an,fn,_n,cn,hn,dn,mn,bn,gn,wn,kn,pn,vn,yn,qn,Cn,An,En,Mn,zn,Ln,Sn,Bn,jn,Dn,Tn,Rn,Hn,Pn,Nn]}class fo extends De{constructor(e){super(),Te(this,e,ao,oo,Re,{datatype:1,label:2,show_label:3,headers:71,values:0,col_count:4,row_count:5,latex_delimiters:6,editable:7,wrap:8,root:9,i18n:10,max_height:11,line_breaks:12,column_widths:13,show_row_numbers:14,upload:15,stream_handler:16,show_fullscreen_button:17,show_copy_button:18,value_is_output:72,max_chars:19,show_search:20,pinned_columns:73,display_value:21,styling:22},null,[-1,-1,-1,-1,-1])}get datatype(){return this.$$.ctx[1]}set datatype(e){this.$$set({datatype:e}),q()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),q()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),q()}get headers(){return this.$$.ctx[71]}set headers(e){this.$$set({headers:e}),q()}get values(){return this.$$.ctx[0]}set values(e){this.$$set({values:e}),q()}get col_count(){return this.$$.ctx[4]}set col_count(e){this.$$set({col_count:e}),q()}get row_count(){return this.$$.ctx[5]}set row_count(e){this.$$set({row_count:e}),q()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),q()}get editable(){return this.$$.ctx[7]}set editable(e){this.$$set({editable:e}),q()}get wrap(){return this.$$.ctx[8]}set wrap(e){this.$$set({wrap:e}),q()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),q()}get i18n(){return this.$$.ctx[10]}set i18n(e){this.$$set({i18n:e}),q()}get max_height(){return this.$$.ctx[11]}set max_height(e){this.$$set({max_height:e}),q()}get line_breaks(){return this.$$.ctx[12]}set line_breaks(e){this.$$set({line_breaks:e}),q()}get column_widths(){return this.$$.ctx[13]}set column_widths(e){this.$$set({column_widths:e}),q()}get show_row_numbers(){return this.$$.ctx[14]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),q()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),q()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),q()}get show_fullscreen_button(){return this.$$.ctx[17]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),q()}get show_copy_button(){return this.$$.ctx[18]}set show_copy_button(e){this.$$set({show_copy_button:e}),q()}get value_is_output(){return this.$$.ctx[72]}set value_is_output(e){this.$$set({value_is_output:e}),q()}get max_chars(){return this.$$.ctx[19]}set max_chars(e){this.$$set({max_chars:e}),q()}get show_search(){return this.$$.ctx[20]}set show_search(e){this.$$set({show_search:e}),q()}get pinned_columns(){return this.$$.ctx[73]}set pinned_columns(e){this.$$set({pinned_columns:e}),q()}get display_value(){return this.$$.ctx[21]}set display_value(e){this.$$set({display_value:e}),q()}get styling(){return this.$$.ctx[22]}set styling(e){this.$$set({styling:e}),q()}}const _o=fo;function co(n){let e,t,l,s,o;const i=[{autoscroll:n[16].autoscroll},{i18n:n[16].i18n},n[19]];let r={};for(let u=0;u<i.length;u+=1)r=Wn(r,i[u]);e=new Xn({props:r}),e.$on("clear_status",n[33]);function _(u){n[36](u)}let c={root:n[13],label:n[7],show_label:n[8],row_count:n[6],col_count:n[5],values:n[31]||n[0].data,display_value:n[29],styling:n[28],headers:n[30],wrap:n[9],datatype:n[10],latex_delimiters:n[17],editable:n[20],max_height:n[18],i18n:n[16].i18n,line_breaks:n[14],column_widths:n[15],upload:n[34],stream_handler:n[35],show_fullscreen_button:n[21],max_chars:n[22],show_copy_button:n[23],show_row_numbers:n[24],show_search:n[25],pinned_columns:n[26]};return n[1]!==void 0&&(c.value_is_output=n[1]),l=new _o({props:c}),be.push(()=>ze(l,"value_is_output",_)),l.$on("change",n[37]),l.$on("input",n[38]),l.$on("select",n[39]),l.$on("search",n[40]),{c(){oe(e.$$.fragment),t=W(),oe(l.$$.fragment)},m(u,h){ie(e,u,h),H(u,t,h),ie(l,u,h),o=!0},p(u,h){const d=h[0]&589824?Jn(i,[h[0]&65536&&{autoscroll:u[16].autoscroll},h[0]&65536&&{i18n:u[16].i18n},h[0]&524288&&Gn(u[19])]):{};e.$set(d);const g={};h[0]&8192&&(g.root=u[13]),h[0]&128&&(g.label=u[7]),h[0]&256&&(g.show_label=u[8]),h[0]&64&&(g.row_count=u[6]),h[0]&32&&(g.col_count=u[5]),h[0]&1|h[1]&1&&(g.values=u[31]||u[0].data),h[0]&536870912&&(g.display_value=u[29]),h[0]&268435456&&(g.styling=u[28]),h[0]&1073741824&&(g.headers=u[30]),h[0]&512&&(g.wrap=u[9]),h[0]&1024&&(g.datatype=u[10]),h[0]&131072&&(g.latex_delimiters=u[17]),h[0]&1048576&&(g.editable=u[20]),h[0]&262144&&(g.max_height=u[18]),h[0]&65536&&(g.i18n=u[16].i18n),h[0]&16384&&(g.line_breaks=u[14]),h[0]&32768&&(g.column_widths=u[15]),h[0]&65536&&(g.upload=u[34]),h[0]&65536&&(g.stream_handler=u[35]),h[0]&2097152&&(g.show_fullscreen_button=u[21]),h[0]&4194304&&(g.max_chars=u[22]),h[0]&8388608&&(g.show_copy_button=u[23]),h[0]&16777216&&(g.show_row_numbers=u[24]),h[0]&33554432&&(g.show_search=u[25]),h[0]&67108864&&(g.pinned_columns=u[26]),!s&&h[0]&2&&(s=!0,g.value_is_output=u[1],Le(()=>s=!1)),l.$set(g)},i(u){o||(L(e.$$.fragment,u),L(l.$$.fragment,u),o=!0)},o(u){R(e.$$.fragment,u),R(l.$$.fragment,u),o=!1},d(u){u&&P(t),re(e,u),re(l,u)}}}function ho(n){let e,t;return e=new Yn({props:{visible:n[4],padding:!1,elem_id:n[2],elem_classes:n[3],container:!1,scale:n[11],min_width:n[12],overflow_behavior:"visible",$$slots:{default:[co]},$$scope:{ctx:n}}}),{c(){oe(e.$$.fragment)},m(l,s){ie(e,l,s),t=!0},p(l,s){const o={};s[0]&16&&(o.visible=l[4]),s[0]&4&&(o.elem_id=l[2]),s[0]&8&&(o.elem_classes=l[3]),s[0]&2048&&(o.scale=l[11]),s[0]&4096&&(o.min_width=l[12]),s[0]&2147477475|s[1]&1025&&(o.$$scope={dirty:s,ctx:l}),e.$set(o)},i(l){t||(L(e.$$.fragment,l),t=!0)},o(l){R(e.$$.fragment,l),t=!1},d(l){re(e,l)}}}function mo(n,e,t){let l,s,o,i,{headers:r=[]}=e,{elem_id:_=""}=e,{elem_classes:c=[]}=e,{visible:u=!0}=e,{value:h={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,{value_is_output:d=!1}=e,{col_count:g}=e,{row_count:z}=e,{label:F=null}=e,{show_label:O=!0}=e,{wrap:w}=e,{datatype:k}=e,{scale:v=null}=e,{min_width:T=void 0}=e,{root:M}=e,{line_breaks:j=!0}=e,{column_widths:U=[]}=e,{gradio:ee}=e,{latex_delimiters:te}=e,{max_height:Q=void 0}=e,{loading_status:C}=e,{interactive:V}=e,{show_fullscreen_button:Z=!1}=e,{max_chars:y=void 0}=e,{show_copy_button:I=!1}=e,{show_row_numbers:we=!1}=e,{show_search:Ce="none"}=e,he=null,{pinned_columns:G=0}=e;const ke=()=>ee.dispatch("clear_status",C),$=(...E)=>ee.client.upload(...E),le=(...E)=>ee.client.stream(...E);function x(E){d=E,t(1,d)}const A=E=>{t(0,h.data=E.detail.data,h),t(0,h.headers=E.detail.headers,h),ee.dispatch("change")},b=E=>ee.dispatch("input"),p=E=>ee.dispatch("select",E.detail),X=E=>t(27,he=E.detail);return n.$$set=E=>{"headers"in E&&t(32,r=E.headers),"elem_id"in E&&t(2,_=E.elem_id),"elem_classes"in E&&t(3,c=E.elem_classes),"visible"in E&&t(4,u=E.visible),"value"in E&&t(0,h=E.value),"value_is_output"in E&&t(1,d=E.value_is_output),"col_count"in E&&t(5,g=E.col_count),"row_count"in E&&t(6,z=E.row_count),"label"in E&&t(7,F=E.label),"show_label"in E&&t(8,O=E.show_label),"wrap"in E&&t(9,w=E.wrap),"datatype"in E&&t(10,k=E.datatype),"scale"in E&&t(11,v=E.scale),"min_width"in E&&t(12,T=E.min_width),"root"in E&&t(13,M=E.root),"line_breaks"in E&&t(14,j=E.line_breaks),"column_widths"in E&&t(15,U=E.column_widths),"gradio"in E&&t(16,ee=E.gradio),"latex_delimiters"in E&&t(17,te=E.latex_delimiters),"max_height"in E&&t(18,Q=E.max_height),"loading_status"in E&&t(19,C=E.loading_status),"interactive"in E&&t(20,V=E.interactive),"show_fullscreen_button"in E&&t(21,Z=E.show_fullscreen_button),"max_chars"in E&&t(22,y=E.max_chars),"show_copy_button"in E&&t(23,I=E.show_copy_button),"show_row_numbers"in E&&t(24,we=E.show_row_numbers),"show_search"in E&&t(25,Ce=E.show_search),"pinned_columns"in E&&t(26,G=E.pinned_columns)},n.$$.update=()=>{n.$$.dirty[0]&134217729&&t(31,l=he?h.data?.filter(E=>E.some(ge=>he&&String(ge).toLowerCase().includes(he.toLowerCase()))):null),n.$$.dirty[0]&1|n.$$.dirty[1]&2&&t(30,s=[...h.headers||r]),n.$$.dirty[0]&1&&t(29,o=h?.metadata?.display_value?[...h?.metadata?.display_value]:null),n.$$.dirty[0]&1048577&&t(28,i=!V&&h?.metadata?.styling?[...h?.metadata?.styling]:null)},[h,d,_,c,u,g,z,F,O,w,k,v,T,M,j,U,ee,te,Q,C,V,Z,y,I,we,Ce,G,he,i,o,s,l,r,ke,$,le,x,A,b,p,X]}class Co extends De{constructor(e){super(),Te(this,e,mo,ho,Re,{headers:32,elem_id:2,elem_classes:3,visible:4,value:0,value_is_output:1,col_count:5,row_count:6,label:7,show_label:8,wrap:9,datatype:10,scale:11,min_width:12,root:13,line_breaks:14,column_widths:15,gradio:16,latex_delimiters:17,max_height:18,loading_status:19,interactive:20,show_fullscreen_button:21,max_chars:22,show_copy_button:23,show_row_numbers:24,show_search:25,pinned_columns:26},null,[-1,-1])}get headers(){return this.$$.ctx[32]}set headers(e){this.$$set({headers:e}),q()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),q()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),q()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),q()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),q()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),q()}get col_count(){return this.$$.ctx[5]}set col_count(e){this.$$set({col_count:e}),q()}get row_count(){return this.$$.ctx[6]}set row_count(e){this.$$set({row_count:e}),q()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),q()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),q()}get wrap(){return this.$$.ctx[9]}set wrap(e){this.$$set({wrap:e}),q()}get datatype(){return this.$$.ctx[10]}set datatype(e){this.$$set({datatype:e}),q()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),q()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),q()}get root(){return this.$$.ctx[13]}set root(e){this.$$set({root:e}),q()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),q()}get column_widths(){return this.$$.ctx[15]}set column_widths(e){this.$$set({column_widths:e}),q()}get gradio(){return this.$$.ctx[16]}set gradio(e){this.$$set({gradio:e}),q()}get latex_delimiters(){return this.$$.ctx[17]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),q()}get max_height(){return this.$$.ctx[18]}set max_height(e){this.$$set({max_height:e}),q()}get loading_status(){return this.$$.ctx[19]}set loading_status(e){this.$$set({loading_status:e}),q()}get interactive(){return this.$$.ctx[20]}set interactive(e){this.$$set({interactive:e}),q()}get show_fullscreen_button(){return this.$$.ctx[21]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),q()}get max_chars(){return this.$$.ctx[22]}set max_chars(e){this.$$set({max_chars:e}),q()}get show_copy_button(){return this.$$.ctx[23]}set show_copy_button(e){this.$$set({show_copy_button:e}),q()}get show_row_numbers(){return this.$$.ctx[24]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),q()}get show_search(){return this.$$.ctx[25]}set show_search(e){this.$$set({show_search:e}),q()}get pinned_columns(){return this.$$.ctx[26]}set pinned_columns(e){this.$$set({pinned_columns:e}),q()}}export{_o as BaseDataFrame,Mo as BaseExample,Co as default};
//# sourceMappingURL=Index-BBHH-CoQ.js.map
