import{a as Oe,i as Pe,s as Qe,f as w,y as E,b as V,c as J,z as a,A as D,d as H,C as A,m as M,k as y,h as L,t as T,j,l as S,n as O,o as Re,J as Ve,K as Ye,e as he,w as te,x as le,M as d,N as ae,p as F,O as Y,D as K,P as Ge,Q as Ie,R as N,T as We,U as Xe,V as G}from"../lite.js";import{B as Ze}from"./BlockTitle-DvFB_De3.js";import{C as xe}from"./Check-DbzZ-PD_.js";import{C as $e}from"./Copy-DcTA0nce.js";import{S as et}from"./Send-DPp49sBe.js";import{S as tt}from"./Square-CkbFMpLj.js";import{f as lt}from"./index-B9I6rkKj.js";/* empty css                                              */function fe(l){let e,t,n,s;const o=[it,nt],u=[];function f(r,c){return r[19]?0:1}return e=f(l),t=u[e]=o[e](l),{c(){t.c(),n=he()},m(r,c){u[e].m(r,c),H(r,n,c),s=!0},p(r,c){let _=e;e=f(r),e===_?u[e].p(r,c):(L(),T(u[_],1,1,()=>{u[_]=null}),j(),t=u[e],t?t.p(r,c):(t=u[e]=o[e](r),t.c()),y(t,1),t.m(n.parentNode,n))},i(r){s||(y(t),s=!0)},o(r){T(t),s=!1},d(r){r&&S(n),u[e].d(r)}}}function nt(l){let e,t,n,s,o;return t=new $e({}),{c(){e=E("button"),J(t.$$.fragment),a(e,"class","copy-button svelte-173056l"),a(e,"aria-label","Copy"),a(e,"aria-roledescription","Copy text")},m(u,f){H(u,e,f),M(t,e,null),n=!0,s||(o=d(e,"click",l[21]),s=!0)},p:K,i(u){n||(y(t.$$.fragment,u),n=!0)},o(u){T(t.$$.fragment,u),n=!1},d(u){u&&S(e),O(t),s=!1,o()}}}function it(l){let e,t,n,s;return t=new xe({}),{c(){e=E("button"),J(t.$$.fragment),a(e,"class","copy-button svelte-173056l"),a(e,"aria-label","Copied"),a(e,"aria-roledescription","Text copied")},m(o,u){H(o,e,u),M(t,e,null),s=!0},p:K,i(o){s||(y(t.$$.fragment,o),o&&(n||Ge(()=>{n=Ie(e,lt,{duration:300}),n.start()})),s=!0)},o(o){T(t.$$.fragment,o),s=!1},d(o){o&&S(e),O(t)}}}function st(l){let e;return{c(){e=te(l[3])},m(t,n){H(t,e,n)},p(t,n){n[0]&8&&le(e,t[3])},d(t){t&&S(e)}}}function ot(l){let e,t,n,s,o,u;return{c(){e=E("textarea"),a(e,"data-testid","textbox"),a(e,"class","scroll-hide svelte-173056l"),a(e,"dir",t=l[13]?"rtl":"ltr"),a(e,"placeholder",l[2]),a(e,"rows",l[1]),e.disabled=l[5],e.autofocus=l[14],a(e,"maxlength",l[16]),a(e,"style",n=l[15]?"text-align: "+l[15]:""),D(e,"no-label",!l[6]&&(l[11]||l[12]))},m(f,r){H(f,e,r),N(e,l[0]),l[45](e),l[14]&&e.focus(),o||(u=[We(s=l[27].call(null,e,l[0])),d(e,"input",l[44]),d(e,"keypress",l[23]),d(e,"blur",l[36]),d(e,"select",l[22]),d(e,"focus",l[37]),d(e,"scroll",l[24])],o=!0)},p(f,r){r[0]&8192&&t!==(t=f[13]?"rtl":"ltr")&&a(e,"dir",t),r[0]&4&&a(e,"placeholder",f[2]),r[0]&2&&a(e,"rows",f[1]),r[0]&32&&(e.disabled=f[5]),r[0]&16384&&(e.autofocus=f[14]),r[0]&65536&&a(e,"maxlength",f[16]),r[0]&32768&&n!==(n=f[15]?"text-align: "+f[15]:"")&&a(e,"style",n),s&&Xe(s.update)&&r[0]&1&&s.update.call(null,f[0]),r[0]&1&&N(e,f[0]),r[0]&6208&&D(e,"no-label",!f[6]&&(f[11]||f[12]))},d(f){f&&S(e),l[45](null),o=!1,G(u)}}}function ut(l){let e;function t(o,u){if(o[9]==="text")return ft;if(o[9]==="password")return at;if(o[9]==="email")return rt}let n=t(l),s=n&&n(l);return{c(){s&&s.c(),e=he()},m(o,u){s&&s.m(o,u),H(o,e,u)},p(o,u){n===(n=t(o))&&s?s.p(o,u):(s&&s.d(1),s=n&&n(o),s&&(s.c(),s.m(e.parentNode,e)))},d(o){o&&S(e),s&&s.d(o)}}}function rt(l){let e,t,n;return{c(){e=E("input"),a(e,"data-testid","textbox"),a(e,"type","email"),a(e,"class","scroll-hide svelte-173056l"),a(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[14],a(e,"maxlength",l[16]),a(e,"autocomplete","email")},m(s,o){H(s,e,o),N(e,l[0]),l[43](e),l[14]&&e.focus(),t||(n=[d(e,"input",l[42]),d(e,"keypress",l[23]),d(e,"blur",l[34]),d(e,"select",l[22]),d(e,"focus",l[35])],t=!0)},p(s,o){o[0]&4&&a(e,"placeholder",s[2]),o[0]&32&&(e.disabled=s[5]),o[0]&16384&&(e.autofocus=s[14]),o[0]&65536&&a(e,"maxlength",s[16]),o[0]&1&&e.value!==s[0]&&N(e,s[0])},d(s){s&&S(e),l[43](null),t=!1,G(n)}}}function at(l){let e,t,n;return{c(){e=E("input"),a(e,"data-testid","password"),a(e,"type","password"),a(e,"class","scroll-hide svelte-173056l"),a(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[14],a(e,"maxlength",l[16]),a(e,"autocomplete","")},m(s,o){H(s,e,o),N(e,l[0]),l[41](e),l[14]&&e.focus(),t||(n=[d(e,"input",l[40]),d(e,"keypress",l[23]),d(e,"blur",l[32]),d(e,"select",l[22]),d(e,"focus",l[33])],t=!0)},p(s,o){o[0]&4&&a(e,"placeholder",s[2]),o[0]&32&&(e.disabled=s[5]),o[0]&16384&&(e.autofocus=s[14]),o[0]&65536&&a(e,"maxlength",s[16]),o[0]&1&&e.value!==s[0]&&N(e,s[0])},d(s){s&&S(e),l[41](null),t=!1,G(n)}}}function ft(l){let e,t,n,s,o;return{c(){e=E("input"),a(e,"data-testid","textbox"),a(e,"type","text"),a(e,"class","scroll-hide svelte-173056l"),a(e,"dir",t=l[13]?"rtl":"ltr"),a(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[14],a(e,"maxlength",l[16]),a(e,"style",n=l[15]?"text-align: "+l[15]:"")},m(u,f){H(u,e,f),N(e,l[0]),l[39](e),l[14]&&e.focus(),s||(o=[d(e,"input",l[38]),d(e,"keypress",l[23]),d(e,"blur",l[30]),d(e,"select",l[22]),d(e,"focus",l[31])],s=!0)},p(u,f){f[0]&8192&&t!==(t=u[13]?"rtl":"ltr")&&a(e,"dir",t),f[0]&4&&a(e,"placeholder",u[2]),f[0]&32&&(e.disabled=u[5]),f[0]&16384&&(e.autofocus=u[14]),f[0]&65536&&a(e,"maxlength",u[16]),f[0]&32768&&n!==(n=u[15]?"text-align: "+u[15]:"")&&a(e,"style",n),f[0]&1&&e.value!==u[0]&&N(e,u[0])},d(u){u&&S(e),l[39](null),s=!1,G(o)}}}function _e(l){let e,t,n,s,o,u;const f=[ct,_t],r=[];function c(_,m){return _[11]===!0?0:1}return t=c(l),n=r[t]=f[t](l),{c(){e=E("button"),n.c(),a(e,"class","submit-button svelte-173056l"),D(e,"padded-button",l[11]!==!0)},m(_,m){H(_,e,m),r[t].m(e,null),s=!0,o||(u=d(e,"click",l[26]),o=!0)},p(_,m){let b=t;t=c(_),t===b?r[t].p(_,m):(L(),T(r[b],1,1,()=>{r[b]=null}),j(),n=r[t],n?n.p(_,m):(n=r[t]=f[t](_),n.c()),y(n,1),n.m(e,null)),(!s||m[0]&2048)&&D(e,"padded-button",_[11]!==!0)},i(_){s||(y(n),s=!0)},o(_){T(n),s=!1},d(_){_&&S(e),r[t].d(),o=!1,u()}}}function _t(l){let e;return{c(){e=te(l[11])},m(t,n){H(t,e,n)},p(t,n){n[0]&2048&&le(e,t[11])},i:K,o:K,d(t){t&&S(e)}}}function ct(l){let e,t;return e=new et({}),{c(){J(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p:K,i(n){t||(y(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){O(e,n)}}}function ce(l){let e,t,n,s,o,u;const f=[bt,ht],r=[];function c(_,m){return _[12]===!0?0:1}return t=c(l),n=r[t]=f[t](l),{c(){e=E("button"),n.c(),a(e,"class","stop-button svelte-173056l"),D(e,"padded-button",l[12]!==!0)},m(_,m){H(_,e,m),r[t].m(e,null),s=!0,o||(u=d(e,"click",l[25]),o=!0)},p(_,m){let b=t;t=c(_),t===b?r[t].p(_,m):(L(),T(r[b],1,1,()=>{r[b]=null}),j(),n=r[t],n?n.p(_,m):(n=r[t]=f[t](_),n.c()),y(n,1),n.m(e,null)),(!s||m[0]&4096)&&D(e,"padded-button",_[12]!==!0)},i(_){s||(y(n),s=!0)},o(_){T(n),s=!1},d(_){_&&S(e),r[t].d(),o=!1,u()}}}function ht(l){let e;return{c(){e=te(l[12])},m(t,n){H(t,e,n)},p(t,n){n[0]&4096&&le(e,t[12])},i:K,o:K,d(t){t&&S(e)}}}function bt(l){let e,t;return e=new tt({props:{fill:"none",stroke_width:2.5}}),{c(){J(e.$$.fragment)},m(n,s){M(e,n,s),t=!0},p:K,i(n){t||(y(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){O(e,n)}}}function dt(l){let e,t,n,s,o,u,f,r,c=l[6]&&l[10]&&fe(l);n=new Ze({props:{root:l[17],show_label:l[6],info:l[4],$$slots:{default:[st]},$$scope:{ctx:l}}});function _(h,k){return h[1]===1&&h[8]===1?ut:ot}let m=_(l),b=m(l),g=l[11]&&_e(l),p=l[12]&&ce(l);return{c(){e=E("label"),c&&c.c(),t=V(),J(n.$$.fragment),s=V(),o=E("div"),b.c(),u=V(),g&&g.c(),f=V(),p&&p.c(),a(o,"class","input-container svelte-173056l"),a(e,"class","svelte-173056l"),D(e,"container",l[7]),D(e,"show_textbox_border",l[20])},m(h,k){H(h,e,k),c&&c.m(e,null),A(e,t),M(n,e,null),A(e,s),A(e,o),b.m(o,null),A(o,u),g&&g.m(o,null),A(o,f),p&&p.m(o,null),r=!0},p(h,k){h[6]&&h[10]?c?(c.p(h,k),k[0]&1088&&y(c,1)):(c=fe(h),c.c(),y(c,1),c.m(e,t)):c&&(L(),T(c,1,1,()=>{c=null}),j());const q={};k[0]&131072&&(q.root=h[17]),k[0]&64&&(q.show_label=h[6]),k[0]&16&&(q.info=h[4]),k[0]&8|k[1]&16777216&&(q.$$scope={dirty:k,ctx:h}),n.$set(q),m===(m=_(h))&&b?b.p(h,k):(b.d(1),b=m(h),b&&(b.c(),b.m(o,u))),h[11]?g?(g.p(h,k),k[0]&2048&&y(g,1)):(g=_e(h),g.c(),y(g,1),g.m(o,f)):g&&(L(),T(g,1,1,()=>{g=null}),j()),h[12]?p?(p.p(h,k),k[0]&4096&&y(p,1)):(p=ce(h),p.c(),y(p,1),p.m(o,null)):p&&(L(),T(p,1,1,()=>{p=null}),j()),(!r||k[0]&128)&&D(e,"container",h[7])},i(h){r||(y(c),y(n.$$.fragment,h),y(g),y(p),r=!0)},o(h){T(c),T(n.$$.fragment,h),T(g),T(p),r=!1},d(h){h&&S(e),c&&c.d(),O(n),b.d(),g&&g.d(),p&&p.d()}}}function mt(l,e,t){let{value:n=""}=e,{value_is_output:s=!1}=e,{lines:o=1}=e,{placeholder:u="Type here..."}=e,{label:f}=e,{info:r=void 0}=e,{disabled:c=!1}=e,{show_label:_=!0}=e,{container:m=!0}=e,{max_lines:b}=e,{type:g="text"}=e,{show_copy_button:p=!1}=e,{submit_btn:h=null}=e,{stop_btn:k=null}=e,{rtl:q=!1}=e,{autofocus:I=!1}=e,{text_align:ne=void 0}=e,{autoscroll:P=!0}=e,{max_length:ie=void 0}=e,{root:se}=e,v,W=!1,X,Z,oe=0,x=!1;const be=!h,B=Re();Ve(()=>{Z=v&&v.offsetHeight+v.scrollTop>v.scrollHeight-100});const de=()=>{Z&&P&&!x&&v.scrollTo(0,v.scrollHeight)};function me(){B("change",n),s||B("input")}Ye(()=>{I&&v.focus(),Z&&P&&de(),t(28,s=!1)});async function ge(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),B("copy",{value:n}),pe())}function pe(){t(19,W=!0),X&&clearTimeout(X),X=setTimeout(()=>{t(19,W=!1)},1e3)}function ke(i){const C=i.target,z=C.value,U=[C.selectionStart,C.selectionEnd];B("select",{value:z.substring(...U),index:U})}async function we(i){await ae(),(i.key==="Enter"&&i.shiftKey&&o>1||i.key==="Enter"&&!i.shiftKey&&o===1&&b>=1)&&(i.preventDefault(),B("submit"))}function ye(i){const C=i.target,z=C.scrollTop;z<oe&&(x=!0),oe=z;const U=C.scrollHeight-C.clientHeight;z>=U&&(x=!1)}function ve(){B("stop")}function Te(){B("submit")}async function Q(i){if(await ae(),o===b)return;const C=i.target,z=window.getComputedStyle(C),U=parseFloat(z.paddingTop),$=parseFloat(z.paddingBottom),ue=parseFloat(z.lineHeight);let ee=b===void 0?!1:U+$+ue*b,re=U+$+o*ue;C.style.height="1px";let R;ee&&C.scrollHeight>ee?R=ee:C.scrollHeight<re?R=re:R=C.scrollHeight,C.style.height=`${R}px`}function Ce(i,C){if(o!==b&&(i.style.overflowY="scroll",i.addEventListener("input",Q),!!C.trim()))return Q({target:i}),{destroy:()=>i.removeEventListener("input",Q)}}function He(i){F.call(this,l,i)}function Se(i){F.call(this,l,i)}function Ee(i){F.call(this,l,i)}function ze(i){F.call(this,l,i)}function De(i){F.call(this,l,i)}function qe(i){F.call(this,l,i)}function Be(i){F.call(this,l,i)}function Fe(i){F.call(this,l,i)}function Ke(){n=this.value,t(0,n)}function Ne(i){Y[i?"unshift":"push"](()=>{v=i,t(18,v)})}function Ue(){n=this.value,t(0,n)}function Le(i){Y[i?"unshift":"push"](()=>{v=i,t(18,v)})}function je(){n=this.value,t(0,n)}function Ae(i){Y[i?"unshift":"push"](()=>{v=i,t(18,v)})}function Je(){n=this.value,t(0,n)}function Me(i){Y[i?"unshift":"push"](()=>{v=i,t(18,v)})}return l.$$set=i=>{"value"in i&&t(0,n=i.value),"value_is_output"in i&&t(28,s=i.value_is_output),"lines"in i&&t(1,o=i.lines),"placeholder"in i&&t(2,u=i.placeholder),"label"in i&&t(3,f=i.label),"info"in i&&t(4,r=i.info),"disabled"in i&&t(5,c=i.disabled),"show_label"in i&&t(6,_=i.show_label),"container"in i&&t(7,m=i.container),"max_lines"in i&&t(8,b=i.max_lines),"type"in i&&t(9,g=i.type),"show_copy_button"in i&&t(10,p=i.show_copy_button),"submit_btn"in i&&t(11,h=i.submit_btn),"stop_btn"in i&&t(12,k=i.stop_btn),"rtl"in i&&t(13,q=i.rtl),"autofocus"in i&&t(14,I=i.autofocus),"text_align"in i&&t(15,ne=i.text_align),"autoscroll"in i&&t(29,P=i.autoscroll),"max_length"in i&&t(16,ie=i.max_length),"root"in i&&t(17,se=i.root)},l.$$.update=()=>{l.$$.dirty[0]&1&&n===null&&t(0,n=""),l.$$.dirty[0]&262403&&v&&o!==b&&Q({target:v}),l.$$.dirty[0]&1&&me()},[n,o,u,f,r,c,_,m,b,g,p,h,k,q,I,ne,ie,se,v,W,be,ge,ke,we,ye,ve,Te,Ce,s,P,He,Se,Ee,ze,De,qe,Be,Fe,Ke,Ne,Ue,Le,je,Ae,Je,Me]}class Ht extends Oe{constructor(e){super(),Pe(this,e,mt,dt,Qe,{value:0,value_is_output:28,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:8,type:9,show_copy_button:10,submit_btn:11,stop_btn:12,rtl:13,autofocus:14,text_align:15,autoscroll:29,max_length:16,root:17},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[28]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get lines(){return this.$$.ctx[1]}set lines(e){this.$$set({lines:e}),w()}get placeholder(){return this.$$.ctx[2]}set placeholder(e){this.$$set({placeholder:e}),w()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),w()}get disabled(){return this.$$.ctx[5]}set disabled(e){this.$$set({disabled:e}),w()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),w()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),w()}get max_lines(){return this.$$.ctx[8]}set max_lines(e){this.$$set({max_lines:e}),w()}get type(){return this.$$.ctx[9]}set type(e){this.$$set({type:e}),w()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),w()}get submit_btn(){return this.$$.ctx[11]}set submit_btn(e){this.$$set({submit_btn:e}),w()}get stop_btn(){return this.$$.ctx[12]}set stop_btn(e){this.$$set({stop_btn:e}),w()}get rtl(){return this.$$.ctx[13]}set rtl(e){this.$$set({rtl:e}),w()}get autofocus(){return this.$$.ctx[14]}set autofocus(e){this.$$set({autofocus:e}),w()}get text_align(){return this.$$.ctx[15]}set text_align(e){this.$$set({text_align:e}),w()}get autoscroll(){return this.$$.ctx[29]}set autoscroll(e){this.$$set({autoscroll:e}),w()}get max_length(){return this.$$.ctx[16]}set max_length(e){this.$$set({max_length:e}),w()}get root(){return this.$$.ctx[17]}set root(e){this.$$set({root:e}),w()}}export{Ht as T};
//# sourceMappingURL=Textbox-GwvoYeHL.js.map
