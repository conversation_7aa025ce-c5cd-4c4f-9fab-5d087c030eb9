{"version": 3, "file": "Index-BkGbjVqe.js", "sources": ["../../../colorpicker/shared/events.ts", "../../../colorpicker/shared/utils.ts", "../../../colorpicker/shared/Colorpicker.svelte", "../../../colorpicker/Index.svelte"], "sourcesContent": ["/**\n * Svelte action to handle clicks outside of a DOM node\n * @param node DOM node to check the click is outside of\n * @param callback callback function to call if click is outside\n * @returns svelte action return object with destroy method to remove event listener\n */\nexport function click_outside(\n\tnode: Node,\n\tcallback: (arg: MouseEvent) => void\n): any {\n\tconst handle_click = (event: MouseEvent): void => {\n\t\tif (\n\t\t\tnode &&\n\t\t\t!node.contains(event.target as Node) &&\n\t\t\t!event.defaultPrevented\n\t\t) {\n\t\t\tcallback(event);\n\t\t}\n\t};\n\n\tdocument.addEventListener(\"mousedown\", handle_click, true);\n\n\treturn {\n\t\tdestroy() {\n\t\t\tdocument.removeEventListener(\"mousedown\", handle_click, true);\n\t\t}\n\t};\n}\n", "import tinycolor from \"tinycolor2\";\n\nexport function hsva_to_rgba(hsva: {\n\th: number;\n\ts: number;\n\tv: number;\n\ta: number;\n}): string {\n\tconst saturation = hsva.s;\n\tconst value = hsva.v;\n\tlet chroma = saturation * value;\n\tconst hue_by_60 = hsva.h / 60;\n\tlet x = chroma * (1 - Math.abs((hue_by_60 % 2) - 1));\n\tconst m = value - chroma;\n\n\tchroma = chroma + m;\n\tx = x + m;\n\n\tconst index = Math.floor(hue_by_60) % 6;\n\tconst red = [chroma, x, m, m, x, chroma][index];\n\tconst green = [x, chroma, chroma, x, m, m][index];\n\tconst blue = [m, m, x, chroma, chroma, x][index];\n\n\treturn `rgba(${red * 255}, ${green * 255}, ${blue * 255}, ${hsva.a})`;\n}\n\nexport function format_color(\n\tcolor: string,\n\tmode: \"hex\" | \"rgb\" | \"hsl\"\n): string {\n\tif (mode === \"hex\") {\n\t\treturn tinycolor(color).toHexString();\n\t} else if (mode === \"rgb\") {\n\t\treturn tinycolor(color).toRgbString();\n\t}\n\treturn tinycolor(color).toHslString();\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, afterUpdate, onMount, tick } from \"svelte\";\n\timport tinycolor from \"tinycolor2\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { click_outside } from \"./events\";\n\timport { Eyedropper } from \"@gradio/icons\";\n\timport { hsva_to_rgba, format_color } from \"./utils\";\n\n\texport let value = \"#000000\";\n\texport let value_is_output = false;\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let disabled = false;\n\texport let show_label = true;\n\texport let root: string;\n\n\texport let current_mode: \"hex\" | \"rgb\" | \"hsl\" = \"hex\";\n\texport let dialog_open = false;\n\n\tlet eyedropper_supported = false;\n\n\tlet sl_wrap: HTMLDivElement;\n\tlet hue_wrap: HTMLDivElement;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tclick_outside: void;\n\t\tinput: undefined;\n\t\tsubmit: undefined;\n\t\tblur: undefined;\n\t\tfocus: undefined;\n\t\tselected: string;\n\t\tclose: void;\n\t}>();\n\n\tlet sl_marker_pos = [0, 0];\n\tlet sl_rect: DOMRect | null = null;\n\tlet sl_moving = false;\n\tlet sl = [0, 0];\n\n\tlet hue = 0;\n\tlet hue_marker_pos = 0;\n\tlet hue_rect: DOMRect | null = null;\n\tlet hue_moving = false;\n\n\tfunction handle_hue_down(\n\t\tevent: MouseEvent & { currentTarget: HTMLDivElement }\n\t): void {\n\t\thue_rect = event.currentTarget.getBoundingClientRect();\n\t\thue_moving = true;\n\t\tupdate_hue_from_mouse(event.clientX);\n\t}\n\n\tfunction update_hue_from_mouse(x: number): void {\n\t\tif (!hue_rect) return;\n\t\tconst _x = Math.max(0, Math.min(x - hue_rect.left, hue_rect.width)); // Get the x-coordinate relative to the box\n\t\thue_marker_pos = _x;\n\t\tconst _hue = (_x / hue_rect.width) * 360; // Scale the x position to a hue value (0-360)\n\n\t\thue = _hue;\n\n\t\tvalue = hsva_to_rgba({ h: _hue, s: sl[0], v: sl[1], a: 1 });\n\t}\n\n\tfunction update_color_from_mouse(x: number, y: number): void {\n\t\tif (!sl_rect) return;\n\t\tconst _x = Math.max(0, Math.min(x - sl_rect.left, sl_rect.width));\n\t\tconst _y = Math.max(0, Math.min(y - sl_rect.top, sl_rect.height));\n\t\tsl_marker_pos = [_x, _y];\n\t\tconst _hsva = {\n\t\t\th: hue * 1,\n\t\t\ts: _x / sl_rect.width,\n\t\t\tv: 1 - _y / sl_rect.height,\n\t\t\ta: 1\n\t\t};\n\n\t\tsl = [_hsva.s, _hsva.v];\n\n\t\tvalue = hsva_to_rgba(_hsva);\n\t}\n\n\tfunction handle_sl_down(\n\t\tevent: MouseEvent & { currentTarget: HTMLDivElement }\n\t): void {\n\t\tsl_moving = true;\n\t\tsl_rect = event.currentTarget.getBoundingClientRect();\n\t\tupdate_color_from_mouse(event.clientX, event.clientY);\n\t}\n\n\tfunction handle_move(event: MouseEvent): void {\n\t\tif (sl_moving) update_color_from_mouse(event.clientX, event.clientY);\n\t\tif (hue_moving) update_hue_from_mouse(event.clientX);\n\t}\n\n\tfunction handle_end(): void {\n\t\tsl_moving = false;\n\t\thue_moving = false;\n\t}\n\n\tasync function update_mouse_from_color(color: string): Promise<void> {\n\t\tif (sl_moving || hue_moving) return;\n\t\tawait tick();\n\t\tif (!color) return;\n\n\t\tif (!sl_rect && sl_wrap) {\n\t\t\tsl_rect = sl_wrap.getBoundingClientRect();\n\t\t}\n\n\t\tif (!hue_rect && hue_wrap) {\n\t\t\thue_rect = hue_wrap.getBoundingClientRect();\n\t\t}\n\n\t\t// Exit if we still don't have valid rectangles\n\t\tif (!sl_rect || !hue_rect) return;\n\n\t\tconst hsva = tinycolor(color).toHsv();\n\t\tconst _x = hsva.s * sl_rect.width;\n\t\tconst _y = (1 - hsva.v) * sl_rect.height;\n\t\tsl_marker_pos = [_x, _y];\n\t\tsl = [hsva.s, hsva.v];\n\t\thue = hsva.h;\n\t\thue_marker_pos = (hsva.h / 360) * hue_rect.width;\n\t}\n\n\tfunction request_eyedropper(): void {\n\t\t// @ts-ignore\n\t\tconst eyeDropper = new EyeDropper();\n\n\t\teyeDropper.open().then((result: { sRGBHex: string }) => {\n\t\t\tvalue = result.sRGBHex;\n\t\t});\n\t}\n\n\tconst modes = [\n\t\t[\"Hex\", \"hex\"],\n\t\t[\"RGB\", \"rgb\"],\n\t\t[\"HSL\", \"hsl\"]\n\t] as const;\n\n\t$: color_string = format_color(value, current_mode);\n\t$: color_string && dispatch(\"selected\", color_string);\n\n\tonMount(async () => {\n\t\t// @ts-ignore\n\t\teyedropper_supported = window !== undefined && !!window.EyeDropper;\n\t});\n\n\tfunction handle_click_outside(): void {\n\t\tdialog_open = false;\n\t}\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t$: update_mouse_from_color(value);\n\t$: value, handle_change();\n\n\tfunction handle_click(): void {\n\t\tdispatch(\"selected\", color_string);\n\t\tdispatch(\"close\");\n\t}\n</script>\n\n<BlockTitle {root} {show_label} {info}>{label}</BlockTitle>\n<button\n\tclass=\"dialog-button\"\n\tstyle:background={value}\n\t{disabled}\n\ton:click={() => {\n\t\tupdate_mouse_from_color(value);\n\t\tdialog_open = !dialog_open;\n\t}}\n/>\n\n<svelte:window on:mousemove={handle_move} on:mouseup={handle_end} />\n\n{#if dialog_open}\n\t<div\n\t\tclass=\"color-picker\"\n\t\ton:focus\n\t\ton:blur\n\t\tuse:click_outside={handle_click_outside}\n\t>\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t<div\n\t\t\tclass=\"color-gradient\"\n\t\t\ton:mousedown={handle_sl_down}\n\t\t\tstyle=\"--hue:{hue}\"\n\t\t\tbind:this={sl_wrap}\n\t\t>\n\t\t\t<div\n\t\t\t\tclass=\"marker\"\n\t\t\t\tstyle:transform=\"translate({sl_marker_pos[0]}px,{sl_marker_pos[1]}px)\"\n\t\t\t\tstyle:background={value}\n\t\t\t/>\n\t\t</div>\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t<div class=\"hue-slider\" on:mousedown={handle_hue_down} bind:this={hue_wrap}>\n\t\t\t<div\n\t\t\t\tclass=\"marker\"\n\t\t\t\tstyle:background={\"hsl(\" + hue + \", 100%, 50%)\"}\n\t\t\t\tstyle:transform=\"translateX({hue_marker_pos}px)\"\n\t\t\t/>\n\t\t</div>\n\n\t\t<div class=\"input\">\n\t\t\t<button class=\"swatch\" style:background={value} on:click={handle_click}\n\t\t\t></button>\n\t\t\t<div>\n\t\t\t\t<div class=\"input-wrap\">\n\t\t\t\t\t<input\n\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\tbind:value={color_string}\n\t\t\t\t\t\ton:change={(e) => (value = e.currentTarget.value)}\n\t\t\t\t\t/>\n\t\t\t\t\t<button class=\"eyedropper\" on:click={request_eyedropper}>\n\t\t\t\t\t\t{#if eyedropper_supported}\n\t\t\t\t\t\t\t<Eyedropper />\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"buttons\">\n\t\t\t\t\t{#each modes as [label, value]}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button\"\n\t\t\t\t\t\t\tclass:active={current_mode === value}\n\t\t\t\t\t\t\ton:click={() => (current_mode = value)}>{label}</button\n\t\t\t\t\t\t>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t</div>\n{/if}\n\n<style>\n\t.dialog-button {\n\t\tdisplay: block;\n\t\twidth: var(--size-10);\n\t\theight: var(--size-5);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t}\n\n\t.dialog-button:disabled {\n\t\tcursor: not-allowed;\n\t}\n\n\t.input {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 10px 15px;\n\t}\n\n\t.input input {\n\t\theight: 30px;\n\t\twidth: 100%;\n\t\tflex-shrink: 1;\n\t\tborder-bottom-left-radius: 0;\n\t\tborder: 1px solid var(--block-border-color);\n\t\tletter-spacing: -0.05rem;\n\t\tborder-left: none;\n\t\tborder-right: none;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--scale-000);\n\t\tpadding-left: 15px;\n\t\tpadding-right: 0;\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\t.swatch {\n\t\twidth: 50px;\n\t\theight: 50px;\n\t\tborder-top-left-radius: 15px;\n\t\tborder-bottom-left-radius: 15px;\n\t\tflex-shrink: 0;\n\t\tborder: 1px solid var(--block-border-color);\n\t}\n\n\t.color-picker {\n\t\twidth: 230px;\n\t\tbackground: var(--background-fill-secondary);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\tmargin: var(--spacing-sm) 0;\n\t}\n\n\t.buttons {\n\t\theight: 20px;\n\t\tdisplay: flex;\n\t\tjustify-content: stretch;\n\t\tgap: 0px;\n\t}\n\n\t.buttons button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1px solid var(--block-border-color);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: 3px 6px;\n\t\tfont-size: var(--scale-000);\n\t\tcursor: pointer;\n\t\tborder-right: none;\n\t\twidth: 100%;\n\t\tborder-top: none;\n\t}\n\n\t.buttons button:first-child {\n\t\tborder-left: none;\n\t}\n\n\t.buttons button:last-child {\n\t\tborder-bottom-right-radius: 15px;\n\t\tborder-right: 1px solid var(--block-border-color);\n\t}\n\n\t.buttons button:hover {\n\t\tbackground: var(--background-fill-secondary-hover);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.buttons button.active {\n\t\tbackground: var(--background-fill-secondary);\n\t\tfont-weight: var(--weight-bold);\n\t}\n\n\t.input-wrap {\n\t\tdisplay: flex;\n\t}\n\n\t.color-gradient {\n\t\tposition: relative;\n\t\t--hue: white;\n\t\tbackground: linear-gradient(rgba(0, 0, 0, 0), #000),\n\t\t\tlinear-gradient(90deg, #fff, hsl(var(--hue), 100%, 50%));\n\t\twidth: 100%;\n\t\theight: 150px;\n\t\tborder-radius: var(--radius-sm) var(--radius-sm) 0 0;\n\t}\n\n\t.hue-slider {\n\t\tposition: relative;\n\t\twidth: 90%;\n\t\tmargin: 10px auto;\n\t\theight: 10px;\n\t\tborder-radius: 5px;\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\thsl(0, 100%, 50%) 0%,\n\t\t\t#ff0 17%,\n\t\t\tlime 33%,\n\t\t\tcyan 50%,\n\t\t\tblue 67%,\n\t\t\tmagenta 83%,\n\t\t\tred 100%\n\t\t);\n\t}\n\n\t.swatch {\n\t\twidth: 50px;\n\t\theight: 50px;\n\t\tborder-top-left-radius: 15px;\n\t\tborder-bottom-left-radius: 15px;\n\t\tflex-shrink: 0;\n\t\tborder: 1px solid var(--block-border-color);\n\t}\n\n\t.eyedropper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 25px;\n\t\theight: 30px;\n\t\tborder-top-right-radius: 15px;\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-left: none;\n\t\tbackground: var(--background-fill-secondary);\n\t\theight: 30px;\n\t\tpadding: 7px 7px 5px 0px;\n\t\tcursor: pointer;\n\t}\n\n\t.marker {\n\t\tposition: absolute;\n\t\twidth: 14px;\n\t\theight: 14px;\n\t\tborder-radius: 50%;\n\t\tborder: 2px solid white;\n\t\ttop: -2px;\n\t\tleft: -7px;\n\t\tbox-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);\n\t\tpointer-events: none;\n\t}\n\n\tinput {\n\t\twidth: 100%;\n\t\theight: 30px;\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: 0 var(--size-2);\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--scale-000);\n\t\tcolor: var(--block-label-text-color);\n\t\tbackground-color: var(--background-fill-primary);\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseColorPicker } from \"./shared/Colorpicker.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport Colorpicker from \"./shared/Colorpicker.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let label = \"ColorPicker\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string;\n\texport let value_is_output = false;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let root: string;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\tsubmit: never;\n\t\tblur: never;\n\t\tfocus: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let interactive: boolean;\n\texport let disabled = false;\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t<Colorpicker\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{root}\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\tdisabled={!interactive || disabled}\n\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:submit={() => gradio.dispatch(\"submit\")}\n\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t/>\n</Block>\n"], "names": ["click_outside", "node", "callback", "handle_click", "event", "hsva_to_rgba", "hsva", "saturation", "value", "chroma", "hue_by_60", "x", "m", "index", "red", "green", "blue", "format_color", "color", "mode", "tinycolor", "ctx", "style_transform", "create_if_block_1", "i", "insert", "target", "div8", "anchor", "append", "div1", "div0", "div3", "div2", "div7", "button0", "div6", "div4", "input", "button1", "div5", "dirty", "toggle_class", "button", "create_if_block", "$$props", "value_is_output", "label", "info", "disabled", "show_label", "root", "current_mode", "dialog_open", "eyedropper_supported", "sl_wrap", "hue_wrap", "dispatch", "createEventDispatcher", "sl_marker_pos", "sl_rect", "sl_moving", "sl", "hue", "hue_marker_pos", "hue_rect", "hue_moving", "handle_hue_down", "update_hue_from_mouse", "_x", "_hue", "update_color_from_mouse", "y", "_y", "$$invalidate", "_hsva", "handle_sl_down", "handle_move", "handle_end", "update_mouse_from_color", "tick", "request_eyedropper", "result", "modes", "onMount", "handle_click_outside", "handle_change", "afterUpdate", "color_string", "$$value", "change_handler", "e", "click_handler_1", "colorpicker_changes", "elem_id", "elem_classes", "visible", "container", "scale", "min_width", "loading_status", "gradio", "interactive", "clear_status_handler"], "mappings": "gjBAMgB,SAAAA,GACfC,EACAC,EACM,CACA,MAAAC,EAAgBC,GAA4B,CAEhDH,GACA,CAACA,EAAK,SAASG,EAAM,MAAc,GACnC,CAACA,EAAM,kBAEPF,EAASE,CAAK,CACf,EAGQ,gBAAA,iBAAiB,YAAaD,EAAc,EAAI,EAElD,CACN,SAAU,CACA,SAAA,oBAAoB,YAAaA,EAAc,EAAI,CAC7D,CAAA,CAEF,CCzBO,SAASE,GAAaC,EAKlB,CACV,MAAMC,EAAaD,EAAK,EAClBE,EAAQF,EAAK,EACnB,IAAIG,EAASF,EAAaC,EACpB,MAAAE,EAAYJ,EAAK,EAAI,GAC3B,IAAIK,EAAIF,GAAU,EAAI,KAAK,IAAKC,EAAY,EAAK,CAAC,GAClD,MAAME,EAAIJ,EAAQC,EAElBA,EAASA,EAASG,EAClBD,EAAIA,EAAIC,EAER,MAAMC,EAAQ,KAAK,MAAMH,CAAS,EAAI,EAChCI,EAAM,CAACL,EAAQE,EAAGC,EAAGA,EAAGD,EAAGF,CAAM,EAAEI,CAAK,EACxCE,EAAQ,CAACJ,EAAGF,EAAQA,EAAQE,EAAGC,EAAGA,CAAC,EAAEC,CAAK,EAC1CG,EAAO,CAACJ,EAAGA,EAAGD,EAAGF,EAAQA,EAAQE,CAAC,EAAEE,CAAK,EAExC,MAAA,QAAQC,EAAM,GAAG,KAAKC,EAAQ,GAAG,KAAKC,EAAO,GAAG,KAAKV,EAAK,CAAC,GACnE,CAEgB,SAAAW,GACfC,EACAC,EACS,CACT,OAAIA,IAAS,MACLC,EAAUF,CAAK,EAAE,cACdC,IAAS,MACZC,EAAUF,CAAK,EAAE,cAElBE,EAAUF,CAAK,EAAE,aACzB,kICuIwCG,EAAK,CAAA,CAAA,0CAALA,EAAK,CAAA,CAAA,2CA6BbC,EAAA,aAAAD,EAAc,EAAA,EAAA,CAAC,CAAM,MAAAA,MAAc,CAAC,CAAA,4BASnCA,EAAc,EAAA,CAAA,oCAepCA,EAAoB,CAAA,GAAAE,GAAA,MAOnBF,EAAK,EAAA,CAAA,uBAAV,OAAIG,GAAA,iUA9BWH,EAAK,CAAA,CAAA,2DANVA,EAAG,EAAA,CAAA,wDAaE,OAASA,EAAG,EAAA,EAAG,cAAc,oHAMPA,EAAK,CAAA,CAAA,kQA7BhDI,EAwDKC,EAAAC,EAAAC,CAAA,EAjDJC,EAWKF,EAAAG,CAAA,EALJD,EAICC,EAAAC,CAAA,kBAGFF,EAMKF,EAAAK,CAAA,EALJH,EAICG,EAAAC,CAAA,kBAGFJ,EA2BKF,EAAAO,CAAA,EA1BJL,EACSK,EAAAC,CAAA,SACTN,EAuBKK,EAAAE,CAAA,EAtBJP,EAWKO,EAAAC,CAAA,EAVJR,EAICQ,EAAAC,CAAA,MAFYjB,EAAY,CAAA,CAAA,SAGzBQ,EAIQQ,EAAAE,CAAA,wBAGTV,EAQKO,EAAAI,CAAA,+EA5CQnB,EAAc,EAAA,CAAA,kBAWSA,EAAe,EAAA,CAAA,cASMA,EAAY,EAAA,CAAA,qDAS/BA,EAAkB,EAAA,CAAA,yDAlCvCA,EAAoB,EAAA,CAAA,CAAA,oBAWToB,EAAA,CAAA,EAAA,MAAAnB,KAAAA,EAAA,aAAAD,EAAc,EAAA,EAAA,CAAC,CAAM,MAAAA,MAAc,CAAC,CAAA,oDAC9CA,EAAK,CAAA,CAAA,+BANVA,EAAG,EAAA,CAAA,8BAaE,OAASA,EAAG,EAAA,EAAG,cAAc,mCAClBA,EAAc,EAAA,CAAA,oDAKHA,EAAK,CAAA,CAAA,sBAM/BA,EAAY,CAAA,OAAZA,EAAY,CAAA,CAAA,EAInBA,EAAoB,CAAA,8GAOnBA,EAAK,EAAA,CAAA,oBAAV,OAAIG,GAAA,EAAA,mHAAJ,2XAIyCH,EAAK,CAAA,CAAA,wCADhCqB,GAAAC,EAAA,SAAAtB,OAAiBA,EAAK,CAAA,CAAA,UAFrCI,EAIAC,EAAAiB,EAAAf,CAAA,uDAFec,GAAAC,EAAA,SAAAtB,OAAiBA,EAAK,CAAA,CAAA,mKAlDtCA,EAAW,CAAA,GAAAuB,GAAAvB,CAAA,qJAVGA,EAAK,CAAA,CAAA,4BAFxBI,EAQCC,EAAAiB,EAAAf,CAAA,6DAE4BP,EAAW,EAAA,CAAA,iBAAcA,EAAU,EAAA,CAAA,6OAR7CA,EAAK,CAAA,CAAA,EAUnBA,EAAW,CAAA,gRAhLJ,MAAAb,EAAQ,SAAA,EAAAqC,GACR,gBAAAC,EAAkB,EAAA,EAAAD,EAClB,CAAA,MAAAE,CAAA,EAAAF,GACA,KAAAG,EAA2B,MAAA,EAAAH,GAC3B,SAAAI,EAAW,EAAA,EAAAJ,GACX,WAAAK,EAAa,EAAA,EAAAL,EACb,CAAA,KAAAM,CAAA,EAAAN,GAEA,aAAAO,EAAsC,KAAA,EAAAP,GACtC,YAAAQ,EAAc,EAAA,EAAAR,EAErBS,EAAuB,GAEvBC,EACAC,QAEEC,EAAWC,KAWb,IAAAC,EAAA,CAAiB,EAAG,CAAC,EACrBC,EAA0B,KAC1BC,EAAY,GACZC,EAAA,CAAM,EAAG,CAAC,EAEVC,EAAM,EACNC,EAAiB,EACjBC,EAA2B,KAC3BC,EAAa,YAERC,EACR/D,EAAA,CAEA6D,EAAW7D,EAAM,cAAc,wBAC/B8D,EAAa,GACbE,EAAsBhE,EAAM,OAAO,WAG3BgE,EAAsBzD,EAAA,CACzB,GAAA,CAAAsD,EAAA,aACCI,EAAK,KAAK,IAAI,EAAG,KAAK,IAAI1D,EAAIsD,EAAS,KAAMA,EAAS,KAAK,CAAA,OACjED,EAAiBK,CAAA,QACXC,EAAQD,EAAKJ,EAAS,MAAS,SAErCF,EAAMO,CAAA,MAEN9D,EAAQH,GAAA,CAAe,EAAGiE,EAAM,EAAGR,EAAG,CAAC,EAAG,EAAGA,EAAG,CAAC,EAAG,EAAG,CAAA,CAAA,CAAA,EAG/C,SAAAS,EAAwB5D,EAAW6D,EAAA,CACtC,GAAA,CAAAZ,EAAA,aACCS,EAAK,KAAK,IAAI,EAAG,KAAK,IAAI1D,EAAIiD,EAAQ,KAAMA,EAAQ,KAAK,CAAA,EACzDa,EAAK,KAAK,IAAI,EAAG,KAAK,IAAID,EAAIZ,EAAQ,IAAKA,EAAQ,MAAM,CAAA,EAC/Dc,EAAA,GAAAf,EAAA,CAAiBU,EAAII,CAAE,CAAA,EACjB,MAAAE,EAAA,CACL,EAAGZ,EAAM,EACT,EAAGM,EAAKT,EAAQ,MAChB,EAAG,EAAIa,EAAKb,EAAQ,OACpB,EAAG,GAGJE,EAAM,CAAAa,EAAM,EAAGA,EAAM,CAAC,EAEtBD,EAAA,EAAAlE,EAAQH,GAAasE,CAAK,CAAA,WAGlBC,EACRxE,EAAA,CAEAyD,EAAY,GACZD,EAAUxD,EAAM,cAAc,wBAC9BmE,EAAwBnE,EAAM,QAASA,EAAM,OAAO,WAG5CyE,EAAYzE,EAAA,CAChByD,GAAWU,EAAwBnE,EAAM,QAASA,EAAM,OAAO,EAC/D8D,GAAYE,EAAsBhE,EAAM,OAAO,EAG3C,SAAA0E,IAAA,CACRjB,EAAY,GACZK,EAAa,kBAGCa,EAAwB7D,EAAA,IAClC2C,GAAaK,IACX,MAAAc,GAAA,EACD,CAAA9D,MAEA0C,GAAWL,IACfK,EAAUL,EAAQ,0BAGdU,GAAYT,IAChBS,EAAWT,EAAS,0BAIhBI,GAAY,CAAAK,GAAA,aAEX3D,EAAOc,EAAUF,CAAK,EAAE,MAAA,EACxBmD,EAAK/D,EAAK,EAAIsD,EAAQ,MACtBa,GAAM,EAAInE,EAAK,GAAKsD,EAAQ,OAClCc,EAAA,GAAAf,EAAA,CAAiBU,EAAII,CAAE,CAAA,EACvBX,EAAM,CAAAxD,EAAK,EAAGA,EAAK,CAAC,EACpBoE,EAAA,GAAAX,EAAMzD,EAAK,CAAA,OACX0D,EAAkB1D,EAAK,EAAI,IAAO2D,EAAS,KAAA,EAGnC,SAAAgB,IAAA,CAEe,IAAA,aAEZ,OAAO,KAAMC,GAAA,CACvBR,EAAA,EAAAlE,EAAQ0E,EAAO,OAAA,IAIX,MAAAC,GAAA,CAAA,CACJ,MAAO,KAAK,EAAA,CACZ,MAAO,KAAK,EAAA,CACZ,MAAO,KAAK,CAAA,EAMdC,GAAA,SAAA,KAEC9B,EAAuB,SAAA,UAA0B,OAAO,UAAA,IAGhD,SAAA+B,IAAA,KACRhC,EAAc,EAAA,EAGN,SAAAiC,IAAA,CACR7B,EAAS,SAAUjD,CAAK,EACnBsC,GACJW,EAAS,OAAO,EAIlB8B,GAAA,IAAA,MACCzC,EAAkB,EAAA,IAMV,SAAA3C,IAAA,CACRsD,EAAS,WAAY+B,CAAY,EACjC/B,EAAS,OAAO,kFAUhBsB,EAAwBvE,CAAK,EAC7BkE,EAAA,EAAArB,GAAeA,CAAW,6CAkBdE,EAAOkC,sDAS+CjC,EAAQiC,0BAe1DD,EAAY,KAAA,2BACZ,MAAAE,GAAAC,OAAOnF,EAAQmF,EAAE,cAAc,KAAK,EAc9BC,GAAApF,GAAAkE,EAAA,EAAAtB,EAAe5C,CAAK,+WAhGxCkE,EAAA,EAAAc,EAAevE,GAAaT,EAAO4C,CAAY,CAAA,qBAC/CoC,GAAgB/B,EAAS,WAAY+B,CAAY,mBAsBjDT,EAAwBvE,CAAK,mBACtB8E,GAAA,uiCC1HG,WAAAjE,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,0MAWP,SAAA,CAAAA,OAAeA,EAAQ,EAAA,4YAbtB,WAAAA,MAAO,YACboB,EAAA,MAAA,CAAA,KAAApB,MAAO,IAAI,aACbA,EAAc,EAAA,CAAA,qHAWPoB,EAAA,QAAAoD,EAAA,SAAA,CAAAxE,OAAeA,EAAQ,EAAA,+wBAxCxB,MAAA0B,EAAQ,aAAA,EAAAF,GACR,KAAAG,EAA2B,MAAA,EAAAH,GAC3B,QAAAiD,EAAU,EAAA,EAAAjD,EACV,CAAA,aAAAkD,EAAA,EAAA,EAAAlD,GACA,QAAAmD,EAAU,EAAA,EAAAnD,EACV,CAAA,MAAArC,CAAA,EAAAqC,GACA,gBAAAC,EAAkB,EAAA,EAAAD,EAClB,CAAA,WAAAK,CAAA,EAAAL,GACA,UAAAoD,EAAY,EAAA,EAAApD,GACZ,MAAAqD,EAAuB,IAAA,EAAArD,GACvB,UAAAsD,EAAgC,MAAA,EAAAtD,EAChC,CAAA,eAAAuD,CAAA,EAAAvD,EACA,CAAA,KAAAM,CAAA,EAAAN,EACA,CAAA,OAAAwD,CAAA,EAAAxD,EAQA,CAAA,YAAAyD,CAAA,EAAAzD,GACA,SAAAI,EAAW,EAAA,EAAAJ,EAQE,MAAA0D,EAAA,IAAAF,EAAO,SAAS,eAAgBD,CAAc,gEAWpDC,EAAO,SAAS,QAAQ,QACzBA,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,QAC1BA,EAAO,SAAS,MAAM,QACrBA,EAAO,SAAS,OAAO"}