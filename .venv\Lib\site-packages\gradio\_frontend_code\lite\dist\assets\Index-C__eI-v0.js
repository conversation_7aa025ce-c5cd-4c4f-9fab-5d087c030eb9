import{a as E,i as F,s as G,f as h,B as H,c as w,m as d,k,t as v,n as B,Y as J,S as K,b as C,d as L,a0 as M,a6 as N,l as Y}from"../lite.js";import{P as O,a as Q}from"./Plot-BAjuLK03.js";import{B as R}from"./BlockLabel-DWW9BWN3.js";import"./Empty-Bzq0Ew6m.js";function T(l){let e,n,i,o,_,m;e=new R({props:{show_label:l[6],label:l[5]||l[13].i18n("plot.plot"),Icon:O}});const c=[{autoscroll:l[13].autoscroll},{i18n:l[13].i18n},l[4]];let f={};for(let t=0;t<c.length;t+=1)f=J(f,c[t]);return i=new K({props:f}),i.$on("clear_status",l[17]),_=new Q({props:{value:l[0],theme_mode:l[10],caption:l[11],bokeh_version:l[12],show_actions_button:l[14],gradio:l[13],show_label:l[6],_selectable:l[15],x_lim:l[16]}}),_.$on("change",l[18]),_.$on("select",l[19]),{c(){w(e.$$.fragment),n=C(),w(i.$$.fragment),o=C(),w(_.$$.fragment)},m(t,a){d(e,t,a),L(t,n,a),d(i,t,a),L(t,o,a),d(_,t,a),m=!0},p(t,a){const b={};a&64&&(b.show_label=t[6]),a&8224&&(b.label=t[5]||t[13].i18n("plot.plot")),e.$set(b);const g=a&8208?M(c,[a&8192&&{autoscroll:t[13].autoscroll},a&8192&&{i18n:t[13].i18n},a&16&&N(t[4])]):{};i.$set(g);const u={};a&1&&(u.value=t[0]),a&1024&&(u.theme_mode=t[10]),a&2048&&(u.caption=t[11]),a&4096&&(u.bokeh_version=t[12]),a&16384&&(u.show_actions_button=t[14]),a&8192&&(u.gradio=t[13]),a&64&&(u.show_label=t[6]),a&32768&&(u._selectable=t[15]),a&65536&&(u.x_lim=t[16]),_.$set(u)},i(t){m||(k(e.$$.fragment,t),k(i.$$.fragment,t),k(_.$$.fragment,t),m=!0)},o(t){v(e.$$.fragment,t),v(i.$$.fragment,t),v(_.$$.fragment,t),m=!1},d(t){t&&(Y(n),Y(o)),B(e,t),B(i,t),B(_,t)}}}function U(l){let e,n;return e=new H({props:{padding:!1,elem_id:l[1],elem_classes:l[2],visible:l[3],container:l[7],scale:l[8],min_width:l[9],allow_overflow:!1,$$slots:{default:[T]},$$scope:{ctx:l}}}),{c(){w(e.$$.fragment)},m(i,o){d(e,i,o),n=!0},p(i,[o]){const _={};o&2&&(_.elem_id=i[1]),o&4&&(_.elem_classes=i[2]),o&8&&(_.visible=i[3]),o&128&&(_.container=i[7]),o&256&&(_.scale=i[8]),o&512&&(_.min_width=i[9]),o&1178737&&(_.$$scope={dirty:o,ctx:i}),e.$set(_)},i(i){n||(k(e.$$.fragment,i),n=!0)},o(i){v(e.$$.fragment,i),n=!1},d(i){B(e,i)}}}function V(l,e,n){let{value:i=null}=e,{elem_id:o=""}=e,{elem_classes:_=[]}=e,{visible:m=!0}=e,{loading_status:c}=e,{label:f}=e,{show_label:t}=e,{container:a=!0}=e,{scale:b=null}=e,{min_width:g=void 0}=e,{theme_mode:u}=e,{caption:P}=e,{bokeh_version:S}=e,{gradio:r}=e,{show_actions_button:I=!1}=e,{_selectable:j=!1}=e,{x_lim:q=null}=e;const z=()=>r.dispatch("clear_status",c),A=()=>r.dispatch("change"),D=s=>r.dispatch("select",s.detail);return l.$$set=s=>{"value"in s&&n(0,i=s.value),"elem_id"in s&&n(1,o=s.elem_id),"elem_classes"in s&&n(2,_=s.elem_classes),"visible"in s&&n(3,m=s.visible),"loading_status"in s&&n(4,c=s.loading_status),"label"in s&&n(5,f=s.label),"show_label"in s&&n(6,t=s.show_label),"container"in s&&n(7,a=s.container),"scale"in s&&n(8,b=s.scale),"min_width"in s&&n(9,g=s.min_width),"theme_mode"in s&&n(10,u=s.theme_mode),"caption"in s&&n(11,P=s.caption),"bokeh_version"in s&&n(12,S=s.bokeh_version),"gradio"in s&&n(13,r=s.gradio),"show_actions_button"in s&&n(14,I=s.show_actions_button),"_selectable"in s&&n(15,j=s._selectable),"x_lim"in s&&n(16,q=s.x_lim)},[i,o,_,m,c,f,t,a,b,g,u,P,S,r,I,j,q,z,A,D]}class y extends E{constructor(e){super(),F(this,e,V,U,G,{value:0,elem_id:1,elem_classes:2,visible:3,loading_status:4,label:5,show_label:6,container:7,scale:8,min_width:9,theme_mode:10,caption:11,bokeh_version:12,gradio:13,show_actions_button:14,_selectable:15,x_lim:16})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),h()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),h()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),h()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),h()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),h()}get theme_mode(){return this.$$.ctx[10]}set theme_mode(e){this.$$set({theme_mode:e}),h()}get caption(){return this.$$.ctx[11]}set caption(e){this.$$set({caption:e}),h()}get bokeh_version(){return this.$$.ctx[12]}set bokeh_version(e){this.$$set({bokeh_version:e}),h()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),h()}get show_actions_button(){return this.$$.ctx[14]}set show_actions_button(e){this.$$set({show_actions_button:e}),h()}get _selectable(){return this.$$.ctx[15]}set _selectable(e){this.$$set({_selectable:e}),h()}get x_lim(){return this.$$.ctx[16]}set x_lim(e){this.$$set({x_lim:e}),h()}}export{Q as BasePlot,y as default};
//# sourceMappingURL=Index-C__eI-v0.js.map
