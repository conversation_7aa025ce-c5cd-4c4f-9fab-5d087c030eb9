import{a as ke,i as ve,s as Be,f as w,B as Ie,c as S,m as j,k as v,t as B,n as C,Y as Me,S as qe,b as N,y as P,z as k,d as I,a0 as ze,a6 as Se,h as ne,j as se,l as M,O as je,aq as O,e as ie,ar as V,A as z,C as A,as as ae,w as Ce,$ as X,M as p,x as Ee,V as Fe}from"../lite.js";import{B as Ne}from"./BlockLabel-DWW9BWN3.js";import{E as Pe}from"./Empty-Bzq0Ew6m.js";import{I as oe,F as pe}from"./FullscreenButton-DsVuMC2h.js";import{I as Ae}from"./IconButtonWrapper-BqpIgNIH.js";import{r as Z}from"./file-url-CoOyVRgq.js";import"./Minimize-DOBO88I3.js";function $(t,e,n){const l=t.slice();return l[32]=e[n],l[34]=n,l}function y(t,e,n){const l=t.slice();return l[32]=e[n],l[34]=n,l}function Le(t){let e,n,l,s,i,a,h,b,g;n=new Ae({props:{$$slots:{default:[Ve]},$$scope:{ctx:t}}});let m=O(t[15]?t[15]?.annotations:[]),f=[];for(let r=0;r<m.length;r+=1)f[r]=ee(y(t,m,r));let _=t[6]&&t[15]&&le(t);return{c(){e=P("div"),S(n.$$.fragment),l=N(),s=P("img"),a=N();for(let r=0;r<f.length;r+=1)f[r].c();h=N(),_&&_.c(),b=ie(),k(s,"class","base-image svelte-303fln"),V(s.src,i=t[15]?t[15].image.url:null)||k(s,"src",i),k(s,"alt","the base file that is annotated"),z(s,"fit-height",t[7]&&!t[18]),k(e,"class","image-container svelte-303fln")},m(r,u){I(r,e,u),j(n,e,null),A(e,l),A(e,s),A(e,a);for(let c=0;c<f.length;c+=1)f[c]&&f[c].m(e,null);t[26](e),I(r,h,u),_&&_.m(r,u),I(r,b,u),g=!0},p(r,u){const c={};if(u[0]&409600|u[1]&32&&(c.$$scope={dirty:u,ctx:r}),n.$set(c),(!g||u[0]&32768&&!V(s.src,i=r[15]?r[15].image.url:null))&&k(s,"src",i),(!g||u[0]&262272)&&z(s,"fit-height",r[7]&&!r[18]),u[0]&360976){m=O(r[15]?r[15]?.annotations:[]);let d;for(d=0;d<m.length;d+=1){const q=y(r,m,d);f[d]?f[d].p(q,u):(f[d]=ee(q),f[d].c(),f[d].m(e,null))}for(;d<f.length;d+=1)f[d].d(1);f.length=m.length}r[6]&&r[15]?_?_.p(r,u):(_=le(r),_.c(),_.m(b.parentNode,b)):_&&(_.d(1),_=null)},i(r){g||(v(n.$$.fragment,r),g=!0)},o(r){B(n.$$.fragment,r),g=!1},d(r){r&&(M(e),M(h),M(b)),C(n),ae(f,r),t[26](null),_&&_.d(r)}}}function Oe(t){let e,n;return e=new Pe({props:{size:"large",unpadded_box:!0,$$slots:{default:[We]},$$scope:{ctx:t}}}),{c(){S(e.$$.fragment)},m(l,s){j(e,l,s),n=!0},p(l,s){const i={};s[1]&32&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){B(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function x(t){let e,n;return e=new pe({props:{container:t[17]}}),e.$on("fullscreenchange",t[25]),{c(){S(e.$$.fragment)},m(l,s){j(e,l,s),n=!0},p(l,s){const i={};s[0]&131072&&(i.container=l[17]),e.$set(i)},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){B(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function Ve(t){let e,n,l=t[14]&&x(t);return{c(){l&&l.c(),e=ie()},m(s,i){l&&l.m(s,i),I(s,e,i),n=!0},p(s,i){s[14]?l?(l.p(s,i),i[0]&16384&&v(l,1)):(l=x(s),l.c(),v(l,1),l.m(e.parentNode,e)):l&&(ne(),B(l,1,1,()=>{l=null}),se())},i(s){n||(v(l),n=!0)},o(s){B(l),n=!1},d(s){s&&M(e),l&&l.d(s)}}}function ee(t){let e,n,l,s;return{c(){e=P("img"),k(e,"alt",n="segmentation mask identifying "+t[4]+" within the uploaded file"),k(e,"class","mask fit-height svelte-303fln"),V(e.src,l=t[32].image.url)||k(e,"src",l),k(e,"style",s=t[9]&&t[32].label in t[9]?null:`filter: hue-rotate(${Math.round(t[34]*360/t[15]?.annotations.length)}deg);`),z(e,"fit-height",!t[18]),z(e,"active",t[16]==t[32].label),z(e,"inactive",t[16]!=t[32].label&&t[16]!=null)},m(i,a){I(i,e,a)},p(i,a){a[0]&16&&n!==(n="segmentation mask identifying "+i[4]+" within the uploaded file")&&k(e,"alt",n),a[0]&32768&&!V(e.src,l=i[32].image.url)&&k(e,"src",l),a[0]&33280&&s!==(s=i[9]&&i[32].label in i[9]?null:`filter: hue-rotate(${Math.round(i[34]*360/i[15]?.annotations.length)}deg);`)&&k(e,"style",s),a[0]&262144&&z(e,"fit-height",!i[18]),a[0]&98304&&z(e,"active",i[16]==i[32].label),a[0]&98304&&z(e,"inactive",i[16]!=i[32].label&&i[16]!=null)},d(i){i&&M(e)}}}function le(t){let e,n=O(t[15].annotations),l=[];for(let s=0;s<n.length;s+=1)l[s]=te($(t,n,s));return{c(){e=P("div");for(let s=0;s<l.length;s+=1)l[s].c();k(e,"class","legend svelte-303fln")},m(s,i){I(s,e,i);for(let a=0;a<l.length;a+=1)l[a]&&l[a].m(e,null)},p(s,i){if(i[0]&3703296){n=O(s[15].annotations);let a;for(a=0;a<n.length;a+=1){const h=$(s,n,a);l[a]?l[a].p(h,i):(l[a]=te(h),l[a].c(),l[a].m(e,null))}for(;a<l.length;a+=1)l[a].d(1);l.length=n.length}},d(s){s&&M(e),ae(l,s)}}}function te(t){let e,n=t[32].label+"",l,s,i,a;function h(){return t[27](t[32])}function b(){return t[28](t[32])}function g(){return t[31](t[34],t[32])}return{c(){e=P("button"),l=Ce(n),s=N(),k(e,"class","legend-item svelte-303fln"),X(e,"background-color",t[9]&&t[32].label in t[9]?t[9][t[32].label]+"88":`hsla(${Math.round(t[34]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},m(m,f){I(m,e,f),A(e,l),A(e,s),i||(a=[p(e,"mouseover",h),p(e,"focus",b),p(e,"mouseout",t[29]),p(e,"blur",t[30]),p(e,"click",g)],i=!0)},p(m,f){t=m,f[0]&32768&&n!==(n=t[32].label+"")&&Ee(l,n),f[0]&33280&&X(e,"background-color",t[9]&&t[32].label in t[9]?t[9][t[32].label]+"88":`hsla(${Math.round(t[34]*360/t[15].annotations.length)}, 100%, 50%, 0.3)`)},d(m){m&&M(e),i=!1,Fe(a)}}}function We(t){let e,n;return e=new oe({}),{c(){S(e.$$.fragment)},m(l,s){j(e,l,s),n=!0},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){B(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function Ye(t){let e,n,l,s,i,a,h,b;const g=[{autoscroll:t[3].autoscroll},{i18n:t[3].i18n},t[13]];let m={};for(let u=0;u<g.length;u+=1)m=Me(m,g[u]);e=new qe({props:m}),l=new Ne({props:{show_label:t[5],Icon:oe,label:t[4]||t[3].i18n("image.image")}});const f=[Oe,Le],_=[];function r(u,c){return u[15]==null?0:1}return a=r(t),h=_[a]=f[a](t),{c(){S(e.$$.fragment),n=N(),S(l.$$.fragment),s=N(),i=P("div"),h.c(),k(i,"class","container svelte-303fln")},m(u,c){j(e,u,c),I(u,n,c),j(l,u,c),I(u,s,c),I(u,i,c),_[a].m(i,null),b=!0},p(u,c){const d=c[0]&8200?ze(g,[c[0]&8&&{autoscroll:u[3].autoscroll},c[0]&8&&{i18n:u[3].i18n},c[0]&8192&&Se(u[13])]):{};e.$set(d);const q={};c[0]&32&&(q.show_label=u[5]),c[0]&24&&(q.label=u[4]||u[3].i18n("image.image")),l.$set(q);let E=a;a=r(u),a===E?_[a].p(u,c):(ne(),B(_[E],1,1,()=>{_[E]=null}),se(),h=_[a],h?h.p(u,c):(h=_[a]=f[a](u),h.c()),v(h,1),h.m(i,null))},i(u){b||(v(e.$$.fragment,u),v(l.$$.fragment,u),v(h),b=!0)},o(u){B(e.$$.fragment,u),B(l.$$.fragment,u),B(h),b=!1},d(u){u&&(M(n),M(s),M(i)),C(e,u),C(l,u),_[a].d()}}}function De(t){let e,n;return e=new Ie({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],padding:!1,height:t[7],width:t[8],allow_overflow:!1,container:t[10],scale:t[11],min_width:t[12],$$slots:{default:[Ye]},$$scope:{ctx:t}}}),{c(){S(e.$$.fragment)},m(l,s){j(e,l,s),n=!0},p(l,s){const i={};s[0]&4&&(i.visible=l[2]),s[0]&1&&(i.elem_id=l[0]),s[0]&2&&(i.elem_classes=l[1]),s[0]&128&&(i.height=l[7]),s[0]&256&&(i.width=l[8]),s[0]&1024&&(i.container=l[10]),s[0]&2048&&(i.scale=l[11]),s[0]&4096&&(i.min_width=l[12]),s[0]&516856|s[1]&32&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){n||(v(e.$$.fragment,l),n=!0)},o(l){B(e.$$.fragment,l),n=!1},d(l){C(e,l)}}}function Ge(t,e,n){let{elem_id:l=""}=e,{elem_classes:s=[]}=e,{visible:i=!0}=e,{value:a=null}=e,h=null,b=null,{gradio:g}=e,{label:m=g.i18n("annotated_image.annotated_image")}=e,{show_label:f=!0}=e,{show_legend:_=!0}=e,{height:r}=e,{width:u}=e,{color_map:c}=e,{container:d=!0}=e,{scale:q=null}=e,{min_width:E=void 0}=e,W=null,{loading_status:K}=e,{show_fullscreen_button:Q=!0}=e,Y,R=!1,D=null;function G(o){n(16,W=o)}function H(){n(16,W=null)}function T(o,L){g.dispatch("select",{value:m,index:o})}const ue=o=>n(18,R=o.detail);function re(o){je[o?"unshift":"push"](()=>{Y=o,n(17,Y)})}const fe=o=>G(o.label),_e=o=>G(o.label),ce=()=>H(),he=()=>H(),me=(o,L)=>T(o,L.label);return t.$$set=o=>{"elem_id"in o&&n(0,l=o.elem_id),"elem_classes"in o&&n(1,s=o.elem_classes),"visible"in o&&n(2,i=o.visible),"value"in o&&n(22,a=o.value),"gradio"in o&&n(3,g=o.gradio),"label"in o&&n(4,m=o.label),"show_label"in o&&n(5,f=o.show_label),"show_legend"in o&&n(6,_=o.show_legend),"height"in o&&n(7,r=o.height),"width"in o&&n(8,u=o.width),"color_map"in o&&n(9,c=o.color_map),"container"in o&&n(10,d=o.container),"scale"in o&&n(11,q=o.scale),"min_width"in o&&n(12,E=o.min_width),"loading_status"in o&&n(13,K=o.loading_status),"show_fullscreen_button"in o&&n(14,Q=o.show_fullscreen_button)},t.$$.update=()=>{if(t.$$.dirty[0]&29360136)if(a!==h&&(n(23,h=a),g.dispatch("change")),a){const o={image:a.image,annotations:a.annotations.map(F=>({image:F.image,label:F.label}))};n(15,b=o);const L=Z(o.image.url),ge=Promise.all(o.annotations.map(F=>Z(F.image.url))),J=Promise.all([L,ge]);n(24,D=J),J.then(([F,be])=>{if(D!==J)return;const de={image:{...o.image,url:F??void 0},annotations:o.annotations.map((U,we)=>({...U,image:{...U.image,url:be[we]??void 0}}))};n(15,b=de)})}else n(15,b=null)},[l,s,i,g,m,f,_,r,u,c,d,q,E,K,Q,b,W,Y,R,G,H,T,a,h,D,ue,re,fe,_e,ce,he,me]}class Xe extends ke{constructor(e){super(),ve(this,e,Ge,De,Be,{elem_id:0,elem_classes:1,visible:2,value:22,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13,show_fullscreen_button:14},null,[-1,-1])}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[22]}set value(e){this.$$set({value:e}),w()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),w()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),w()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),w()}get show_legend(){return this.$$.ctx[6]}set show_legend(e){this.$$set({show_legend:e}),w()}get height(){return this.$$.ctx[7]}set height(e){this.$$set({height:e}),w()}get width(){return this.$$.ctx[8]}set width(e){this.$$set({width:e}),w()}get color_map(){return this.$$.ctx[9]}set color_map(e){this.$$set({color_map:e}),w()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),w()}get loading_status(){return this.$$.ctx[13]}set loading_status(e){this.$$set({loading_status:e}),w()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),w()}}export{Xe as default};
//# sourceMappingURL=Index-DCCCVwmE.js.map
