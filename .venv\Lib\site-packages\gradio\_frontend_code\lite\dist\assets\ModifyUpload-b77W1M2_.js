import{a as M,i as j,s as L,f as w,c as $,m as b,k as c,t as d,n as p,o as P,q as S,I as D,a2 as W,b as k,d as h,h as E,j as N,u as z,r as A,v as F,l as I}from"../lite.js";import{D as G}from"./Download-RUpc9r8A.js";import{E as H}from"./Edit-fMGAgLsI.js";import{U as J}from"./Undo-50qkik3g.js";import{I as K}from"./IconButtonWrapper-BqpIgNIH.js";import{D as O}from"./DownloadLink-dHe4pFcz.js";function q(a){let e,o;return e=new D({props:{Icon:H,label:a[3]("common.edit")}}),e.$on("click",a[6]),{c(){$(e.$$.fragment)},m(n,s){b(e,n,s),o=!0},p(n,s){const i={};s&8&&(i.label=n[3]("common.edit")),e.$set(i)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){d(e.$$.fragment,n),o=!1},d(n){p(e,n)}}}function B(a){let e,o;return e=new D({props:{Icon:J,label:a[3]("common.undo")}}),e.$on("click",a[7]),{c(){$(e.$$.fragment)},m(n,s){b(e,n,s),o=!0},p(n,s){const i={};s&8&&(i.label=n[3]("common.undo")),e.$set(i)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){d(e.$$.fragment,n),o=!1},d(n){p(e,n)}}}function C(a){let e,o;return e=new O({props:{href:a[2],download:!0,$$slots:{default:[Q]},$$scope:{ctx:a}}}),{c(){$(e.$$.fragment)},m(n,s){b(e,n,s),o=!0},p(n,s){const i={};s&4&&(i.href=n[2]),s&520&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){d(e.$$.fragment,n),o=!1},d(n){p(e,n)}}}function Q(a){let e,o;return e=new D({props:{Icon:G,label:a[3]("common.download")}}),{c(){$(e.$$.fragment)},m(n,s){b(e,n,s),o=!0},p(n,s){const i={};s&8&&(i.label=n[3]("common.download")),e.$set(i)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){d(e.$$.fragment,n),o=!1},d(n){p(e,n)}}}function R(a){let e,o,n,s,i,_,r=a[0]&&q(a),u=a[1]&&B(a),f=a[2]&&C(a);const g=a[5].default,m=S(g,a,a[9],null);return i=new D({props:{Icon:W,label:a[3]("common.clear")}}),i.$on("click",a[8]),{c(){r&&r.c(),e=k(),u&&u.c(),o=k(),f&&f.c(),n=k(),m&&m.c(),s=k(),$(i.$$.fragment)},m(t,l){r&&r.m(t,l),h(t,e,l),u&&u.m(t,l),h(t,o,l),f&&f.m(t,l),h(t,n,l),m&&m.m(t,l),h(t,s,l),b(i,t,l),_=!0},p(t,l){t[0]?r?(r.p(t,l),l&1&&c(r,1)):(r=q(t),r.c(),c(r,1),r.m(e.parentNode,e)):r&&(E(),d(r,1,1,()=>{r=null}),N()),t[1]?u?(u.p(t,l),l&2&&c(u,1)):(u=B(t),u.c(),c(u,1),u.m(o.parentNode,o)):u&&(E(),d(u,1,1,()=>{u=null}),N()),t[2]?f?(f.p(t,l),l&4&&c(f,1)):(f=C(t),f.c(),c(f,1),f.m(n.parentNode,n)):f&&(E(),d(f,1,1,()=>{f=null}),N()),m&&m.p&&(!_||l&512)&&z(m,g,t,t[9],_?F(g,t[9],l,null):A(t[9]),null);const U={};l&8&&(U.label=t[3]("common.clear")),i.$set(U)},i(t){_||(c(r),c(u),c(f),c(m,t),c(i.$$.fragment,t),_=!0)},o(t){d(r),d(u),d(f),d(m,t),d(i.$$.fragment,t),_=!1},d(t){t&&(I(e),I(o),I(n),I(s)),r&&r.d(t),u&&u.d(t),f&&f.d(t),m&&m.d(t),p(i,t)}}}function T(a){let e,o;return e=new K({props:{$$slots:{default:[R]},$$scope:{ctx:a}}}),{c(){$(e.$$.fragment)},m(n,s){b(e,n,s),o=!0},p(n,[s]){const i={};s&527&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){d(e.$$.fragment,n),o=!1},d(n){p(e,n)}}}function V(a,e,o){let{$$slots:n={},$$scope:s}=e,{editable:i=!1}=e,{undoable:_=!1}=e,{download:r=null}=e,{i18n:u}=e;const f=P(),g=()=>f("edit"),m=()=>f("undo"),t=l=>{f("clear"),l.stopPropagation()};return a.$$set=l=>{"editable"in l&&o(0,i=l.editable),"undoable"in l&&o(1,_=l.undoable),"download"in l&&o(2,r=l.download),"i18n"in l&&o(3,u=l.i18n),"$$scope"in l&&o(9,s=l.$$scope)},[i,_,r,u,f,n,g,m,t,s]}class ee extends M{constructor(e){super(),j(this,e,V,T,L,{editable:0,undoable:1,download:2,i18n:3})}get editable(){return this.$$.ctx[0]}set editable(e){this.$$set({editable:e}),w()}get undoable(){return this.$$.ctx[1]}set undoable(e){this.$$set({undoable:e}),w()}get download(){return this.$$.ctx[2]}set download(e){this.$$set({download:e}),w()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),w()}}export{ee as M};
//# sourceMappingURL=ModifyUpload-b77W1M2_.js.map
